#!/usr/bin/env node

/**
 * BioStar 2 Connection Test Script
 * 
 * This script tests the connection to BioStar 2 API and validates
 * the configuration before deployment.
 * 
 * Usage:
 *   node scripts/test-biostar-connection.js
 *   node scripts/test-biostar-connection.js --verbose
 *   node scripts/test-biostar-connection.js --config production
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  apiUrl: process.env.NEXT_PUBLIC_BIOSTAR_API_URL || 'https://bs2api.biostar2.com',
  username: process.env.NEXT_PUBLIC_BIOSTAR_USERNAME || '',
  password: process.env.NEXT_PUBLIC_BIOSTAR_PASSWORD || '',
  timeout: parseInt(process.env.BIOSTAR_TIMEOUT) || 20000,
  retryAttempts: parseInt(process.env.BIOSTAR_RETRY_ATTEMPTS) || 3,
  verbose: process.argv.includes('--verbose'),
  configType: process.argv.includes('--config') ? 
    process.argv[process.argv.indexOf('--config') + 1] : 'default'
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Logging functions
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  verbose: (msg) => config.verbose && console.log(`${colors.cyan}→${colors.reset} ${msg}`)
};

// Test results
const results = {
  configValidation: { passed: false, errors: [] },
  networkConnectivity: { passed: false, responseTime: 0, error: null },
  authentication: { passed: false, error: null },
  apiEndpoints: { passed: 0, total: 0, details: [] },
  deviceConnectivity: { passed: false, devices: [] },
  overall: { passed: false, score: 0 }
};

// Validate configuration
function validateConfiguration() {
  log.info('Validating BioStar configuration...');
  
  const errors = [];
  
  if (!config.apiUrl) {
    errors.push('NEXT_PUBLIC_BIOSTAR_API_URL is required');
  } else {
    try {
      new URL(config.apiUrl);
      log.verbose(`API URL: ${config.apiUrl}`);
    } catch {
      errors.push('NEXT_PUBLIC_BIOSTAR_API_URL must be a valid URL');
    }
  }
  
  if (!config.username) {
    errors.push('NEXT_PUBLIC_BIOSTAR_USERNAME is required');
  } else {
    log.verbose(`Username: ${config.username}`);
  }
  
  if (!config.password) {
    errors.push('NEXT_PUBLIC_BIOSTAR_PASSWORD is required');
  } else {
    log.verbose('Password: [HIDDEN]');
  }
  
  results.configValidation = {
    passed: errors.length === 0,
    errors
  };
  
  if (results.configValidation.passed) {
    log.success('Configuration validation passed');
  } else {
    log.error('Configuration validation failed:');
    errors.forEach(error => log.error(`  - ${error}`));
  }
  
  return results.configValidation.passed;
}

// Test network connectivity
async function testNetworkConnectivity() {
  log.info('Testing network connectivity...');
  
  return new Promise((resolve) => {
    const startTime = Date.now();
    const url = new URL(config.apiUrl);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: '/health',
      method: 'GET',
      timeout: config.timeout,
      headers: {
        'User-Agent': 'WorkFlo-BioStar-Test/1.0'
      }
    };
    
    log.verbose(`Connecting to ${url.hostname}:${options.port}`);
    
    const req = client.request(options, (res) => {
      const responseTime = Date.now() - startTime;
      
      log.verbose(`Response status: ${res.statusCode}`);
      log.verbose(`Response time: ${responseTime}ms`);
      
      results.networkConnectivity = {
        passed: res.statusCode < 500,
        responseTime,
        error: res.statusCode >= 400 ? `HTTP ${res.statusCode}` : null
      };
      
      if (results.networkConnectivity.passed) {
        log.success(`Network connectivity test passed (${responseTime}ms)`);
      } else {
        log.error(`Network connectivity test failed: ${results.networkConnectivity.error}`);
      }
      
      resolve(results.networkConnectivity.passed);
    });
    
    req.on('error', (error) => {
      const responseTime = Date.now() - startTime;
      
      results.networkConnectivity = {
        passed: false,
        responseTime,
        error: error.message
      };
      
      log.error(`Network connectivity test failed: ${error.message}`);
      resolve(false);
    });
    
    req.on('timeout', () => {
      req.destroy();
      const responseTime = Date.now() - startTime;
      
      results.networkConnectivity = {
        passed: false,
        responseTime,
        error: 'Connection timeout'
      };
      
      log.error('Network connectivity test failed: Connection timeout');
      resolve(false);
    });
    
    req.end();
  });
}

// Test authentication
async function testAuthentication() {
  log.info('Testing BioStar authentication...');
  
  return new Promise((resolve) => {
    const url = new URL(config.apiUrl);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const postData = JSON.stringify({
      username: config.username,
      password: config.password
    });
    
    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: '/login',
      method: 'POST',
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'User-Agent': 'WorkFlo-BioStar-Test/1.0'
      }
    };
    
    log.verbose('Attempting authentication...');
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          
          results.authentication = {
            passed: res.statusCode === 200 && response.access_token,
            error: res.statusCode !== 200 ? `HTTP ${res.statusCode}` : null
          };
          
          if (results.authentication.passed) {
            log.success('Authentication test passed');
            log.verbose(`Token received: ${response.access_token.substring(0, 20)}...`);
          } else {
            log.error(`Authentication test failed: ${results.authentication.error}`);
          }
        } catch (error) {
          results.authentication = {
            passed: false,
            error: 'Invalid JSON response'
          };
          log.error('Authentication test failed: Invalid JSON response');
        }
        
        resolve(results.authentication.passed);
      });
    });
    
    req.on('error', (error) => {
      results.authentication = {
        passed: false,
        error: error.message
      };
      
      log.error(`Authentication test failed: ${error.message}`);
      resolve(false);
    });
    
    req.on('timeout', () => {
      req.destroy();
      results.authentication = {
        passed: false,
        error: 'Authentication timeout'
      };
      
      log.error('Authentication test failed: Timeout');
      resolve(false);
    });
    
    req.write(postData);
    req.end();
  });
}

// Test API endpoints
async function testApiEndpoints() {
  log.info('Testing API endpoints...');
  
  const endpoints = [
    { path: '/users', name: 'Users API' },
    { path: '/devices', name: 'Devices API' },
    { path: '/events', name: 'Events API' },
    { path: '/health', name: 'Health Check' }
  ];
  
  results.apiEndpoints.total = endpoints.length;
  
  for (const endpoint of endpoints) {
    log.verbose(`Testing ${endpoint.name}...`);
    
    const success = await testEndpoint(endpoint.path);
    
    results.apiEndpoints.details.push({
      name: endpoint.name,
      path: endpoint.path,
      passed: success
    });
    
    if (success) {
      results.apiEndpoints.passed++;
      log.verbose(`${endpoint.name} - OK`);
    } else {
      log.verbose(`${endpoint.name} - FAILED`);
    }
  }
  
  const allPassed = results.apiEndpoints.passed === results.apiEndpoints.total;
  
  if (allPassed) {
    log.success(`All API endpoints accessible (${results.apiEndpoints.passed}/${results.apiEndpoints.total})`);
  } else {
    log.warning(`Some API endpoints failed (${results.apiEndpoints.passed}/${results.apiEndpoints.total})`);
  }
  
  return allPassed;
}

// Test individual endpoint
async function testEndpoint(path) {
  return new Promise((resolve) => {
    const url = new URL(config.apiUrl);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: path,
      method: 'GET',
      timeout: 5000,
      headers: {
        'User-Agent': 'WorkFlo-BioStar-Test/1.0'
      }
    };
    
    const req = client.request(options, (res) => {
      resolve(res.statusCode < 500);
    });
    
    req.on('error', () => resolve(false));
    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

// Generate test report
function generateReport() {
  log.info('Generating test report...');
  
  const totalTests = 4;
  let passedTests = 0;
  
  if (results.configValidation.passed) passedTests++;
  if (results.networkConnectivity.passed) passedTests++;
  if (results.authentication.passed) passedTests++;
  if (results.apiEndpoints.passed === results.apiEndpoints.total) passedTests++;
  
  results.overall = {
    passed: passedTests === totalTests,
    score: Math.round((passedTests / totalTests) * 100)
  };
  
  console.log('\n' + '='.repeat(60));
  console.log('BIOSTAR CONNECTION TEST REPORT');
  console.log('='.repeat(60));
  
  console.log(`\nOverall Score: ${results.overall.score}% (${passedTests}/${totalTests} tests passed)`);
  
  console.log('\nTest Results:');
  console.log(`  Configuration Validation: ${results.configValidation.passed ? '✓ PASS' : '✗ FAIL'}`);
  console.log(`  Network Connectivity: ${results.networkConnectivity.passed ? '✓ PASS' : '✗ FAIL'} (${results.networkConnectivity.responseTime}ms)`);
  console.log(`  Authentication: ${results.authentication.passed ? '✓ PASS' : '✗ FAIL'}`);
  console.log(`  API Endpoints: ${results.apiEndpoints.passed}/${results.apiEndpoints.total} accessible`);
  
  if (!results.configValidation.passed) {
    console.log('\nConfiguration Errors:');
    results.configValidation.errors.forEach(error => console.log(`  - ${error}`));
  }
  
  if (!results.networkConnectivity.passed && results.networkConnectivity.error) {
    console.log(`\nNetwork Error: ${results.networkConnectivity.error}`);
  }
  
  if (!results.authentication.passed && results.authentication.error) {
    console.log(`\nAuthentication Error: ${results.authentication.error}`);
  }
  
  console.log('\n' + '='.repeat(60));
  
  if (results.overall.passed) {
    log.success('All tests passed! BioStar integration is ready for production.');
  } else {
    log.error('Some tests failed. Please resolve the issues before deploying.');
    process.exit(1);
  }
}

// Main test function
async function runTests() {
  console.log(`${colors.magenta}BioStar 2 Connection Test${colors.reset}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Config Type: ${config.configType}\n`);
  
  try {
    // Run tests sequentially
    const configValid = validateConfiguration();
    if (!configValid) {
      generateReport();
      return;
    }
    
    await testNetworkConnectivity();
    await testAuthentication();
    await testApiEndpoints();
    
    generateReport();
    
  } catch (error) {
    log.error(`Test execution failed: ${error.message}`);
    process.exit(1);
  }
}

// Run tests if script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  validateConfiguration,
  testNetworkConnectivity,
  testAuthentication,
  testApiEndpoints
};
