{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/lib/websocket.ts"], "sourcesContent": ["import { TokenManager } from './api';\n\nexport interface NotificationData {\n  id: string;\n  type: 'leave_update' | 'payroll_update' | 'performance_update' | 'company_announcement' | 'system_maintenance' | 'ticket_update';\n  title: string;\n  message: string;\n  data?: any;\n  timestamp: string;\n  read: boolean;\n  priority: 'low' | 'medium' | 'high';\n  user_id: number;\n}\n\nexport interface WebSocketMessage {\n  type: 'notification' | 'status' | 'error' | 'ping' | 'pong';\n  data?: any;\n  timestamp?: string;\n}\n\ntype WebSocketEventHandler = (data: any) => void;\n\nclass WebSocketService {\n  private ws: WebSocket | null = null;\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n  private reconnectInterval = 1000; // Start with 1 second\n  private maxReconnectInterval = 30000; // Max 30 seconds\n  private reconnectTimer: NodeJS.Timeout | null = null;\n  private pingTimer: NodeJS.Timeout | null = null;\n  private isConnecting = false;\n  private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map();\n  private notifications: NotificationData[] = [];\n\n  private getWebSocketUrl(): string {\n    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n    const host = process.env.NEXT_PUBLIC_WS_HOST || window.location.host;\n    return `${protocol}//${host}/ws/notifications/`;\n  }\n\n  private isWebSocketEnabled(): boolean {\n    // Check if WebSocket is enabled via environment variable\n    const wsEnabled = process.env.NEXT_PUBLIC_ENABLE_WEBSOCKET !== 'false';\n    const isDevelopment = process.env.NODE_ENV === 'development';\n\n    // In development, only connect if explicitly enabled\n    if (isDevelopment && process.env.NEXT_PUBLIC_WS_HOST === undefined) {\n      return false;\n    }\n\n    return wsEnabled;\n  }\n\n  connect(): void {\n    if (!this.isWebSocketEnabled()) {\n      console.log('WebSocket connection disabled in current environment');\n      return;\n    }\n\n    if (this.ws?.readyState === WebSocket.OPEN || this.isConnecting) {\n      return;\n    }\n\n    this.isConnecting = true;\n    const token = TokenManager.getAccessToken();\n\n    if (!token) {\n      console.warn('No access token available for WebSocket connection');\n      this.isConnecting = false;\n      return;\n    }\n\n    try {\n      const wsUrl = `${this.getWebSocketUrl()}?token=${token}`;\n      this.ws = new WebSocket(wsUrl);\n\n      this.ws.onopen = this.handleOpen.bind(this);\n      this.ws.onmessage = this.handleMessage.bind(this);\n      this.ws.onclose = this.handleClose.bind(this);\n      this.ws.onerror = this.handleError.bind(this);\n    } catch (error) {\n      console.warn('WebSocket connection error (this is normal in development):', error);\n      this.isConnecting = false;\n      // Don't schedule reconnect in development if WebSocket server is not available\n      if (this.isWebSocketEnabled() && process.env.NODE_ENV !== 'development') {\n        this.scheduleReconnect();\n      }\n    }\n  }\n\n  disconnect(): void {\n    this.clearTimers();\n\n    if (this.ws) {\n      this.ws.close(1000, 'Client disconnect');\n      this.ws = null;\n    }\n\n    this.isConnecting = false;\n    this.reconnectAttempts = 0;\n  }\n\n  private handleOpen(): void {\n    console.log('WebSocket connected');\n    this.isConnecting = false;\n    this.reconnectAttempts = 0;\n    this.reconnectInterval = 1000;\n\n    // Start ping/pong to keep connection alive\n    this.startPing();\n\n    // Emit connection event\n    this.emit('connected', { timestamp: new Date().toISOString() });\n  }\n\n  private handleMessage(event: MessageEvent): void {\n    try {\n      const message: WebSocketMessage = JSON.parse(event.data);\n\n      switch (message.type) {\n        case 'notification':\n          this.handleNotification(message.data);\n          break;\n        case 'status':\n          this.emit('status', message.data);\n          break;\n        case 'error':\n          this.emit('error', message.data);\n          break;\n        case 'pong':\n          // Pong received, connection is alive\n          break;\n        default:\n          console.log('Unknown WebSocket message type:', message.type);\n      }\n    } catch (error) {\n      console.error('Error parsing WebSocket message:', error);\n    }\n  }\n\n  private handleClose(event: CloseEvent): void {\n    console.log('WebSocket disconnected:', event.code, event.reason);\n    this.isConnecting = false;\n    this.clearTimers();\n\n    // Emit disconnection event\n    this.emit('disconnected', {\n      code: event.code,\n      reason: event.reason,\n      timestamp: new Date().toISOString()\n    });\n\n    // Attempt to reconnect unless it was a clean close\n    if (event.code !== 1000) {\n      this.scheduleReconnect();\n    }\n  }\n\n  private handleError(error: Event): void {\n    // Only log errors in production or when WebSocket is explicitly enabled\n    if (process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_WS_HOST) {\n      console.error('WebSocket error:', error);\n    } else {\n      console.log('WebSocket connection failed (this is normal in development without WebSocket server)');\n    }\n    this.isConnecting = false;\n    this.emit('error', { error, timestamp: new Date().toISOString() });\n  }\n\n  private handleNotification(notification: NotificationData): void {\n    // Add to notifications list\n    this.notifications.unshift(notification);\n\n    // Keep only last 100 notifications\n    if (this.notifications.length > 100) {\n      this.notifications = this.notifications.slice(0, 100);\n    }\n\n    // Emit notification event\n    this.emit('notification', notification);\n\n    // Show browser notification if permission granted\n    this.showBrowserNotification(notification);\n  }\n\n  private showBrowserNotification(notification: NotificationData): void {\n    if ('Notification' in window && Notification.permission === 'granted') {\n      const browserNotification = new Notification(notification.title, {\n        body: notification.message,\n        icon: '/favicon.ico',\n        tag: notification.id,\n        requireInteraction: notification.priority === 'high'\n      });\n\n      browserNotification.onclick = () => {\n        window.focus();\n        this.emit('notificationClick', notification);\n        browserNotification.close();\n      };\n\n      // Auto close after 5 seconds for non-high priority\n      if (notification.priority !== 'high') {\n        setTimeout(() => {\n          browserNotification.close();\n        }, 5000);\n      }\n    }\n  }\n\n  private scheduleReconnect(): void {\n    // Don't attempt reconnection in development without WebSocket server\n    if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_WS_HOST) {\n      return;\n    }\n\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\n      if (process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_WS_HOST) {\n        console.error('Max reconnection attempts reached');\n      }\n      this.emit('maxReconnectAttemptsReached', {\n        attempts: this.reconnectAttempts,\n        timestamp: new Date().toISOString()\n      });\n      return;\n    }\n\n    this.reconnectTimer = setTimeout(() => {\n      this.reconnectAttempts++;\n      if (process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_WS_HOST) {\n        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n      }\n\n      this.connect();\n\n      // Exponential backoff\n      this.reconnectInterval = Math.min(\n        this.reconnectInterval * 2,\n        this.maxReconnectInterval\n      );\n    }, this.reconnectInterval);\n  }\n\n  private startPing(): void {\n    this.pingTimer = setInterval(() => {\n      if (this.ws?.readyState === WebSocket.OPEN) {\n        this.send({ type: 'ping', timestamp: new Date().toISOString() });\n      }\n    }, 30000); // Ping every 30 seconds\n  }\n\n  private clearTimers(): void {\n    if (this.reconnectTimer) {\n      clearTimeout(this.reconnectTimer);\n      this.reconnectTimer = null;\n    }\n\n    if (this.pingTimer) {\n      clearInterval(this.pingTimer);\n      this.pingTimer = null;\n    }\n  }\n\n  private send(message: WebSocketMessage): void {\n    if (this.ws?.readyState === WebSocket.OPEN) {\n      this.ws.send(JSON.stringify(message));\n    }\n  }\n\n  // Event system\n  on(event: string, handler: WebSocketEventHandler): void {\n    if (!this.eventHandlers.has(event)) {\n      this.eventHandlers.set(event, []);\n    }\n    this.eventHandlers.get(event)!.push(handler);\n  }\n\n  off(event: string, handler: WebSocketEventHandler): void {\n    const handlers = this.eventHandlers.get(event);\n    if (handlers) {\n      const index = handlers.indexOf(handler);\n      if (index > -1) {\n        handlers.splice(index, 1);\n      }\n    }\n  }\n\n  private emit(event: string, data: any): void {\n    const handlers = this.eventHandlers.get(event);\n    if (handlers) {\n      handlers.forEach(handler => {\n        try {\n          handler(data);\n        } catch (error) {\n          console.error('Error in WebSocket event handler:', error);\n        }\n      });\n    }\n  }\n\n  // Public methods\n  getNotifications(): NotificationData[] {\n    return [...this.notifications];\n  }\n\n  getUnreadNotifications(): NotificationData[] {\n    return this.notifications.filter(n => !n.read);\n  }\n\n  markNotificationAsRead(notificationId: string): void {\n    const notification = this.notifications.find(n => n.id === notificationId);\n    if (notification) {\n      notification.read = true;\n      this.emit('notificationRead', notification);\n    }\n  }\n\n  markAllNotificationsAsRead(): void {\n    this.notifications.forEach(n => n.read = true);\n    this.emit('allNotificationsRead', {});\n  }\n\n  clearNotifications(): void {\n    this.notifications = [];\n    this.emit('notificationsCleared', {});\n  }\n\n  isConnected(): boolean {\n    return this.ws?.readyState === WebSocket.OPEN;\n  }\n\n  getConnectionState(): string {\n    if (!this.ws) return 'disconnected';\n\n    switch (this.ws.readyState) {\n      case WebSocket.CONNECTING: return 'connecting';\n      case WebSocket.OPEN: return 'connected';\n      case WebSocket.CLOSING: return 'closing';\n      case WebSocket.CLOSED: return 'disconnected';\n      default: return 'unknown';\n    }\n  }\n\n  // Request browser notification permission\n  static async requestNotificationPermission(): Promise<NotificationPermission> {\n    if ('Notification' in window) {\n      return await Notification.requestPermission();\n    }\n    return 'denied';\n  }\n}\n\n// Create singleton instance\nexport const webSocketService = new WebSocketService();\n\nexport default webSocketService;\n"], "names": ["webSocketService", "WebSocketService", "getWebSocketUrl", "protocol", "window", "location", "host", "process", "env", "NEXT_PUBLIC_WS_HOST", "isWebSocketEnabled", "wsEnabled", "NEXT_PUBLIC_ENABLE_WEBSOCKET", "isDevelopment", "NODE_ENV", "undefined", "connect", "console", "log", "ws", "readyState", "WebSocket", "OPEN", "isConnecting", "token", "TokenManager", "getAccessToken", "warn", "wsUrl", "onopen", "handleOpen", "bind", "onmessage", "handleMessage", "onclose", "handleClose", "onerror", "handleError", "error", "scheduleReconnect", "disconnect", "clearTimers", "close", "reconnectAttempts", "reconnectInterval", "startPing", "emit", "timestamp", "Date", "toISOString", "event", "message", "JSON", "parse", "data", "type", "handleNotification", "code", "reason", "notification", "notifications", "unshift", "length", "slice", "showBrowserNotification", "Notification", "permission", "browserNotification", "title", "body", "icon", "tag", "id", "requireInteraction", "priority", "onclick", "focus", "setTimeout", "maxReconnectAttempts", "attempts", "reconnectTimer", "Math", "min", "maxReconnectInterval", "pingTimer", "setInterval", "send", "clearTimeout", "clearInterval", "stringify", "on", "handler", "eventHandlers", "has", "set", "get", "push", "off", "handlers", "index", "indexOf", "splice", "for<PERSON>ach", "getNotifications", "getUnreadNotifications", "filter", "n", "read", "markNotificationAsRead", "notificationId", "find", "markAllNotificationsAsRead", "clearNotifications", "isConnected", "getConnectionState", "CONNECTING", "CLOSING", "CLOSED", "requestNotificationPermission", "requestPermission", "Map"], "mappings": ";;;;;;;;;;;IAkWA,OAAgC;eAAhC;;IAFaA,gBAAgB;eAAhBA;;;qBAhWgB;AAsB7B,MAAMC;IAYIC,kBAA0B;QAChC,MAAMC,WAAWC,OAAOC,QAAQ,CAACF,QAAQ,KAAK,WAAW,SAAS;QAClE,MAAMG,OAAOC,QAAQC,GAAG,CAACC,mBAAmB,IAAIL,OAAOC,QAAQ,CAACC,IAAI;QACpE,OAAO,GAAGH,SAAS,EAAE,EAAEG,KAAK,kBAAkB,CAAC;IACjD;IAEQI,qBAA8B;QACpC,yDAAyD;QACzD,MAAMC,YAAYJ,QAAQC,GAAG,CAACI,4BAA4B,KAAK;QAC/D,MAAMC,gBAAgBN,QAAQC,GAAG,CAACM,QAAQ,KAAK;QAE/C,qDAAqD;QACrD,IAAID,iBAAiBN,QAAQC,GAAG,CAACC,mBAAmB,KAAKM,WAAW;YAClE,OAAO;QACT;QAEA,OAAOJ;IACT;IAEAK,UAAgB;QACd,IAAI,CAAC,IAAI,CAACN,kBAAkB,IAAI;YAC9BO,QAAQC,GAAG,CAAC;YACZ;QACF;QAEA,IAAI,IAAI,CAACC,EAAE,EAAEC,eAAeC,UAAUC,IAAI,IAAI,IAAI,CAACC,YAAY,EAAE;YAC/D;QACF;QAEA,IAAI,CAACA,YAAY,GAAG;QACpB,MAAMC,QAAQC,iBAAY,CAACC,cAAc;QAEzC,IAAI,CAACF,OAAO;YACVP,QAAQU,IAAI,CAAC;YACb,IAAI,CAACJ,YAAY,GAAG;YACpB;QACF;QAEA,IAAI;YACF,MAAMK,QAAQ,GAAG,IAAI,CAAC1B,eAAe,GAAG,OAAO,EAAEsB,OAAO;YACxD,IAAI,CAACL,EAAE,GAAG,IAAIE,UAAUO;YAExB,IAAI,CAACT,EAAE,CAACU,MAAM,GAAG,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC,IAAI;YAC1C,IAAI,CAACZ,EAAE,CAACa,SAAS,GAAG,IAAI,CAACC,aAAa,CAACF,IAAI,CAAC,IAAI;YAChD,IAAI,CAACZ,EAAE,CAACe,OAAO,GAAG,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC,IAAI;YAC5C,IAAI,CAACZ,EAAE,CAACiB,OAAO,GAAG,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,IAAI;QAC9C,EAAE,OAAOO,OAAO;YACdrB,QAAQU,IAAI,CAAC,+DAA+DW;YAC5E,IAAI,CAACf,YAAY,GAAG;YACpB,+EAA+E;YAC/E,IAAI,IAAI,CAACb,kBAAkB,MAAMH,QAAQC,GAAG,CAACM,QAAQ,KAAK,eAAe;gBACvE,IAAI,CAACyB,iBAAiB;YACxB;QACF;IACF;IAEAC,aAAmB;QACjB,IAAI,CAACC,WAAW;QAEhB,IAAI,IAAI,CAACtB,EAAE,EAAE;YACX,IAAI,CAACA,EAAE,CAACuB,KAAK,CAAC,MAAM;YACpB,IAAI,CAACvB,EAAE,GAAG;QACZ;QAEA,IAAI,CAACI,YAAY,GAAG;QACpB,IAAI,CAACoB,iBAAiB,GAAG;IAC3B;IAEQb,aAAmB;QACzBb,QAAQC,GAAG,CAAC;QACZ,IAAI,CAACK,YAAY,GAAG;QACpB,IAAI,CAACoB,iBAAiB,GAAG;QACzB,IAAI,CAACC,iBAAiB,GAAG;QAEzB,2CAA2C;QAC3C,IAAI,CAACC,SAAS;QAEd,wBAAwB;QACxB,IAAI,CAACC,IAAI,CAAC,aAAa;YAAEC,WAAW,IAAIC,OAAOC,WAAW;QAAG;IAC/D;IAEQhB,cAAciB,KAAmB,EAAQ;QAC/C,IAAI;YACF,MAAMC,UAA4BC,KAAKC,KAAK,CAACH,MAAMI,IAAI;YAEvD,OAAQH,QAAQI,IAAI;gBAClB,KAAK;oBACH,IAAI,CAACC,kBAAkB,CAACL,QAAQG,IAAI;oBACpC;gBACF,KAAK;oBACH,IAAI,CAACR,IAAI,CAAC,UAAUK,QAAQG,IAAI;oBAChC;gBACF,KAAK;oBACH,IAAI,CAACR,IAAI,CAAC,SAASK,QAAQG,IAAI;oBAC/B;gBACF,KAAK;oBAEH;gBACF;oBACErC,QAAQC,GAAG,CAAC,mCAAmCiC,QAAQI,IAAI;YAC/D;QACF,EAAE,OAAOjB,OAAO;YACdrB,QAAQqB,KAAK,CAAC,oCAAoCA;QACpD;IACF;IAEQH,YAAYe,KAAiB,EAAQ;QAC3CjC,QAAQC,GAAG,CAAC,2BAA2BgC,MAAMO,IAAI,EAAEP,MAAMQ,MAAM;QAC/D,IAAI,CAACnC,YAAY,GAAG;QACpB,IAAI,CAACkB,WAAW;QAEhB,2BAA2B;QAC3B,IAAI,CAACK,IAAI,CAAC,gBAAgB;YACxBW,MAAMP,MAAMO,IAAI;YAChBC,QAAQR,MAAMQ,MAAM;YACpBX,WAAW,IAAIC,OAAOC,WAAW;QACnC;QAEA,mDAAmD;QACnD,IAAIC,MAAMO,IAAI,KAAK,MAAM;YACvB,IAAI,CAAClB,iBAAiB;QACxB;IACF;IAEQF,YAAYC,KAAY,EAAQ;QACtC,wEAAwE;QACxE,IAAI/B,QAAQC,GAAG,CAACM,QAAQ,KAAK,gBAAgBP,QAAQC,GAAG,CAACC,mBAAmB,EAAE;YAC5EQ,QAAQqB,KAAK,CAAC,oBAAoBA;QACpC,OAAO;YACLrB,QAAQC,GAAG,CAAC;QACd;QACA,IAAI,CAACK,YAAY,GAAG;QACpB,IAAI,CAACuB,IAAI,CAAC,SAAS;YAAER;YAAOS,WAAW,IAAIC,OAAOC,WAAW;QAAG;IAClE;IAEQO,mBAAmBG,YAA8B,EAAQ;QAC/D,4BAA4B;QAC5B,IAAI,CAACC,aAAa,CAACC,OAAO,CAACF;QAE3B,mCAAmC;QACnC,IAAI,IAAI,CAACC,aAAa,CAACE,MAAM,GAAG,KAAK;YACnC,IAAI,CAACF,aAAa,GAAG,IAAI,CAACA,aAAa,CAACG,KAAK,CAAC,GAAG;QACnD;QAEA,0BAA0B;QAC1B,IAAI,CAACjB,IAAI,CAAC,gBAAgBa;QAE1B,kDAAkD;QAClD,IAAI,CAACK,uBAAuB,CAACL;IAC/B;IAEQK,wBAAwBL,YAA8B,EAAQ;QACpE,IAAI,kBAAkBvD,UAAU6D,aAAaC,UAAU,KAAK,WAAW;YACrE,MAAMC,sBAAsB,IAAIF,aAAaN,aAAaS,KAAK,EAAE;gBAC/DC,MAAMV,aAAaR,OAAO;gBAC1BmB,MAAM;gBACNC,KAAKZ,aAAaa,EAAE;gBACpBC,oBAAoBd,aAAae,QAAQ,KAAK;YAChD;YAEAP,oBAAoBQ,OAAO,GAAG;gBAC5BvE,OAAOwE,KAAK;gBACZ,IAAI,CAAC9B,IAAI,CAAC,qBAAqBa;gBAC/BQ,oBAAoBzB,KAAK;YAC3B;YAEA,mDAAmD;YACnD,IAAIiB,aAAae,QAAQ,KAAK,QAAQ;gBACpCG,WAAW;oBACTV,oBAAoBzB,KAAK;gBAC3B,GAAG;YACL;QACF;IACF;IAEQH,oBAA0B;QAChC,qEAAqE;QACrE,IAAIhC,QAAQC,GAAG,CAACM,QAAQ,KAAK,iBAAiB,CAACP,QAAQC,GAAG,CAACC,mBAAmB,EAAE;YAC9E;QACF;QAEA,IAAI,IAAI,CAACkC,iBAAiB,IAAI,IAAI,CAACmC,oBAAoB,EAAE;YACvD,IAAIvE,QAAQC,GAAG,CAACM,QAAQ,KAAK,gBAAgBP,QAAQC,GAAG,CAACC,mBAAmB,EAAE;gBAC5EQ,QAAQqB,KAAK,CAAC;YAChB;YACA,IAAI,CAACQ,IAAI,CAAC,+BAA+B;gBACvCiC,UAAU,IAAI,CAACpC,iBAAiB;gBAChCI,WAAW,IAAIC,OAAOC,WAAW;YACnC;YACA;QACF;QAEA,IAAI,CAAC+B,cAAc,GAAGH,WAAW;YAC/B,IAAI,CAAClC,iBAAiB;YACtB,IAAIpC,QAAQC,GAAG,CAACM,QAAQ,KAAK,gBAAgBP,QAAQC,GAAG,CAACC,mBAAmB,EAAE;gBAC5EQ,QAAQC,GAAG,CAAC,CAAC,yBAAyB,EAAE,IAAI,CAACyB,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACmC,oBAAoB,CAAC,CAAC,CAAC;YAChG;YAEA,IAAI,CAAC9D,OAAO;YAEZ,sBAAsB;YACtB,IAAI,CAAC4B,iBAAiB,GAAGqC,KAAKC,GAAG,CAC/B,IAAI,CAACtC,iBAAiB,GAAG,GACzB,IAAI,CAACuC,oBAAoB;QAE7B,GAAG,IAAI,CAACvC,iBAAiB;IAC3B;IAEQC,YAAkB;QACxB,IAAI,CAACuC,SAAS,GAAGC,YAAY;YAC3B,IAAI,IAAI,CAAClE,EAAE,EAAEC,eAAeC,UAAUC,IAAI,EAAE;gBAC1C,IAAI,CAACgE,IAAI,CAAC;oBAAE/B,MAAM;oBAAQR,WAAW,IAAIC,OAAOC,WAAW;gBAAG;YAChE;QACF,GAAG,QAAQ,wBAAwB;IACrC;IAEQR,cAAoB;QAC1B,IAAI,IAAI,CAACuC,cAAc,EAAE;YACvBO,aAAa,IAAI,CAACP,cAAc;YAChC,IAAI,CAACA,cAAc,GAAG;QACxB;QAEA,IAAI,IAAI,CAACI,SAAS,EAAE;YAClBI,cAAc,IAAI,CAACJ,SAAS;YAC5B,IAAI,CAACA,SAAS,GAAG;QACnB;IACF;IAEQE,KAAKnC,OAAyB,EAAQ;QAC5C,IAAI,IAAI,CAAChC,EAAE,EAAEC,eAAeC,UAAUC,IAAI,EAAE;YAC1C,IAAI,CAACH,EAAE,CAACmE,IAAI,CAAClC,KAAKqC,SAAS,CAACtC;QAC9B;IACF;IAEA,eAAe;IACfuC,GAAGxC,KAAa,EAAEyC,OAA8B,EAAQ;QACtD,IAAI,CAAC,IAAI,CAACC,aAAa,CAACC,GAAG,CAAC3C,QAAQ;YAClC,IAAI,CAAC0C,aAAa,CAACE,GAAG,CAAC5C,OAAO,EAAE;QAClC;QACA,IAAI,CAAC0C,aAAa,CAACG,GAAG,CAAC7C,OAAQ8C,IAAI,CAACL;IACtC;IAEAM,IAAI/C,KAAa,EAAEyC,OAA8B,EAAQ;QACvD,MAAMO,WAAW,IAAI,CAACN,aAAa,CAACG,GAAG,CAAC7C;QACxC,IAAIgD,UAAU;YACZ,MAAMC,QAAQD,SAASE,OAAO,CAACT;YAC/B,IAAIQ,QAAQ,CAAC,GAAG;gBACdD,SAASG,MAAM,CAACF,OAAO;YACzB;QACF;IACF;IAEQrD,KAAKI,KAAa,EAAEI,IAAS,EAAQ;QAC3C,MAAM4C,WAAW,IAAI,CAACN,aAAa,CAACG,GAAG,CAAC7C;QACxC,IAAIgD,UAAU;YACZA,SAASI,OAAO,CAACX,CAAAA;gBACf,IAAI;oBACFA,QAAQrC;gBACV,EAAE,OAAOhB,OAAO;oBACdrB,QAAQqB,KAAK,CAAC,qCAAqCA;gBACrD;YACF;QACF;IACF;IAEA,iBAAiB;IACjBiE,mBAAuC;QACrC,OAAO;eAAI,IAAI,CAAC3C,aAAa;SAAC;IAChC;IAEA4C,yBAA6C;QAC3C,OAAO,IAAI,CAAC5C,aAAa,CAAC6C,MAAM,CAACC,CAAAA,IAAK,CAACA,EAAEC,IAAI;IAC/C;IAEAC,uBAAuBC,cAAsB,EAAQ;QACnD,MAAMlD,eAAe,IAAI,CAACC,aAAa,CAACkD,IAAI,CAACJ,CAAAA,IAAKA,EAAElC,EAAE,KAAKqC;QAC3D,IAAIlD,cAAc;YAChBA,aAAagD,IAAI,GAAG;YACpB,IAAI,CAAC7D,IAAI,CAAC,oBAAoBa;QAChC;IACF;IAEAoD,6BAAmC;QACjC,IAAI,CAACnD,aAAa,CAAC0C,OAAO,CAACI,CAAAA,IAAKA,EAAEC,IAAI,GAAG;QACzC,IAAI,CAAC7D,IAAI,CAAC,wBAAwB,CAAC;IACrC;IAEAkE,qBAA2B;QACzB,IAAI,CAACpD,aAAa,GAAG,EAAE;QACvB,IAAI,CAACd,IAAI,CAAC,wBAAwB,CAAC;IACrC;IAEAmE,cAAuB;QACrB,OAAO,IAAI,CAAC9F,EAAE,EAAEC,eAAeC,UAAUC,IAAI;IAC/C;IAEA4F,qBAA6B;QAC3B,IAAI,CAAC,IAAI,CAAC/F,EAAE,EAAE,OAAO;QAErB,OAAQ,IAAI,CAACA,EAAE,CAACC,UAAU;YACxB,KAAKC,UAAU8F,UAAU;gBAAE,OAAO;YAClC,KAAK9F,UAAUC,IAAI;gBAAE,OAAO;YAC5B,KAAKD,UAAU+F,OAAO;gBAAE,OAAO;YAC/B,KAAK/F,UAAUgG,MAAM;gBAAE,OAAO;YAC9B;gBAAS,OAAO;QAClB;IACF;IAEA,0CAA0C;IAC1C,aAAaC,gCAAiE;QAC5E,IAAI,kBAAkBlH,QAAQ;YAC5B,OAAO,MAAM6D,aAAasD,iBAAiB;QAC7C;QACA,OAAO;IACT;;aArUQpG,KAAuB;aACvBwB,oBAAoB;aACpBmC,uBAAuB;aACvBlC,oBAAoB,KAAM,sBAAsB;;aAChDuC,uBAAuB,MAAO,iBAAiB;;aAC/CH,iBAAwC;aACxCI,YAAmC;aACnC7D,eAAe;aACfqE,gBAAsD,IAAI4B;aAC1D5D,gBAAoC,EAAE;;AA6ThD;AAGO,MAAM5D,mBAAmB,IAAIC;MAEpC,WAAeD"}