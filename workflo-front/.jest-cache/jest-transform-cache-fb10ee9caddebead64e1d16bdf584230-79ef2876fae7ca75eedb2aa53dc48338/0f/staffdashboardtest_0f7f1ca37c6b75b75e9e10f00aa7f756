429d68130a3a5c18a38e74a3107e86ff
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _react1 = require("@testing-library/react");
const _userevent = /*#__PURE__*/ _interop_require_default(require("@testing-library/user-event"));
const _testutils = require("../utils/test-utils");
const _page = /*#__PURE__*/ _interop_require_default(require("../../app/(auth)/(staff)/staff/page"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Mock the current time for consistent testing
const mockDate = new Date('2024-12-20T10:30:00Z');
jest.useFakeTimers();
jest.setSystemTime(mockDate);
describe('Staff Dashboard Integration', ()=>{
    beforeEach(()=>{
        jest.clearAllMocks();
    });
    afterAll(()=>{
        jest.useRealTimers();
    });
    describe('dashboard rendering', ()=>{
        it('renders welcome message with user name', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            expect(_react1.screen.getByText(/Good morning, John!/)).toBeInTheDocument();
            expect(_react1.screen.getByText(/Friday, December 20, 2024/)).toBeInTheDocument();
        });
        it('displays current time', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            // The time should be displayed (mocked to 10:30:00)
            expect(_react1.screen.getByText(/10:30/)).toBeInTheDocument();
        });
        it('shows today\'s stats cards', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            expect(_react1.screen.getByText('Check-in Time')).toBeInTheDocument();
            expect(_react1.screen.getByText('Hours Worked')).toBeInTheDocument();
            expect(_react1.screen.getByText('Tasks Completed')).toBeInTheDocument();
            expect(_react1.screen.getByText('Leave Balance')).toBeInTheDocument();
        });
    });
    describe('quick actions', ()=>{
        it('renders all quick action buttons', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            expect(_react1.screen.getByText('Check In')).toBeInTheDocument();
            expect(_react1.screen.getByText('Apply Leave')).toBeInTheDocument();
            expect(_react1.screen.getByText('View Payslip')).toBeInTheDocument();
            expect(_react1.screen.getByText('Submit Ticket')).toBeInTheDocument();
        });
        it('handles check-in action', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            const checkInButton = _react1.screen.getByText('Check In');
            await user.click(checkInButton);
        // Should show some feedback or change state
        // This would depend on the actual implementation
        });
    });
    describe('recent activities', ()=>{
        it('displays recent activities section', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            expect(_react1.screen.getByText('Recent Activities')).toBeInTheDocument();
            // Check for some sample activities
            expect(_react1.screen.getByText('Checked in at 08:30 AM')).toBeInTheDocument();
            expect(_react1.screen.getByText('Submitted leave application')).toBeInTheDocument();
            expect(_react1.screen.getByText('Completed project milestone')).toBeInTheDocument();
        });
        it('shows view all activities link', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            expect(_react1.screen.getByText('View All Activities')).toBeInTheDocument();
        });
    });
    describe('upcoming events', ()=>{
        it('displays upcoming events section', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            expect(_react1.screen.getByText('Upcoming Events')).toBeInTheDocument();
            // Check for sample events
            expect(_react1.screen.getByText('Team Meeting')).toBeInTheDocument();
            expect(_react1.screen.getByText('Performance Review')).toBeInTheDocument();
            expect(_react1.screen.getByText('Company Holiday')).toBeInTheDocument();
        });
        it('shows event details', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            expect(_react1.screen.getByText('Weekly team sync meeting')).toBeInTheDocument();
            expect(_react1.screen.getByText('Q4 performance review session')).toBeInTheDocument();
            expect(_react1.screen.getByText('Christmas Day - Office Closed')).toBeInTheDocument();
        });
    });
    describe('announcements', ()=>{
        it('displays announcements section', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            expect(_react1.screen.getByText('Announcements')).toBeInTheDocument();
            // Check for sample announcements
            expect(_react1.screen.getByText('New Health Insurance Policy')).toBeInTheDocument();
            expect(_react1.screen.getByText('Office Renovation Update')).toBeInTheDocument();
        });
        it('shows announcement details', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            expect(_react1.screen.getByText(/Enhanced health insurance coverage/)).toBeInTheDocument();
            expect(_react1.screen.getByText(/Office renovation will begin/)).toBeInTheDocument();
        });
    });
    describe('responsive behavior', ()=>{
        it('adapts layout for mobile screens', ()=>{
            // Mock mobile viewport
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: 375
            });
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            // Check that grid layouts adapt to mobile
            const statsGrid = _react1.screen.getByText('Check-in Time').closest('.grid');
            expect(statsGrid).toHaveClass('grid-cols-1');
        });
    });
    describe('real-time updates', ()=>{
        it('updates time every second', async ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            // Initial time
            expect(_react1.screen.getByText(/10:30/)).toBeInTheDocument();
            // Advance time by 1 minute
            jest.advanceTimersByTime(60000);
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText(/10:31/)).toBeInTheDocument();
            });
        });
        it('updates working hours when checked in', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            // Simulate check-in
            const checkInButton = _react1.screen.getByText('Check In');
            await user.click(checkInButton);
            // Advance time to simulate working
            jest.advanceTimersByTime(3600000); // 1 hour
            // Should show updated working hours
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText(/1:00/)).toBeInTheDocument(); // 1 hour worked
            });
        });
    });
    describe('error handling', ()=>{
        it('handles missing user data gracefully', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}), {
                authContextValue: {
                    user: null,
                    isAuthenticated: true,
                    isLoading: false,
                    login: jest.fn(),
                    logout: jest.fn(),
                    getCurrentUser: jest.fn(),
                    updateProfile: jest.fn(),
                    changePassword: jest.fn()
                }
            });
            // Should still render without crashing
            expect(_react1.screen.getByText(/Good morning/)).toBeInTheDocument();
        });
        it('shows loading state when user data is loading', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}), {
                authContextValue: {
                    user: null,
                    isAuthenticated: false,
                    isLoading: true,
                    login: jest.fn(),
                    logout: jest.fn(),
                    getCurrentUser: jest.fn(),
                    updateProfile: jest.fn(),
                    changePassword: jest.fn()
                }
            });
            // Should show loading state
            expect(_react1.screen.getByText('Checking permissions...')).toBeInTheDocument();
        });
    });
    describe('accessibility', ()=>{
        it('has proper heading structure', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            // Check for proper heading hierarchy
            const mainHeading = _react1.screen.getByRole('heading', {
                level: 1
            });
            expect(mainHeading).toBeInTheDocument();
            const sectionHeadings = _react1.screen.getAllByRole('heading', {
                level: 2
            });
            expect(sectionHeadings.length).toBeGreaterThan(0);
        });
        it('has accessible buttons', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            const buttons = _react1.screen.getAllByRole('button');
            buttons.forEach((button)=>{
                expect(button).toBeVisible();
                // Each button should have accessible text
                expect(button).toHaveTextContent(/.+/);
            });
        });
        it('has proper ARIA labels where needed', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
            // Check for ARIA labels on interactive elements
            const timeDisplay = _react1.screen.getByText(/10:30/);
            expect(timeDisplay.closest('[role="timer"]')).toBeInTheDocument();
        });
    });
    describe('performance', ()=>{
        it('renders efficiently without unnecessary re-renders', ()=>{
            const renderSpy = jest.fn();
            const TestWrapper = ()=>{
                renderSpy();
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {});
            };
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {}));
            // Should only render once initially
            expect(renderSpy).toHaveBeenCalledTimes(1);
            // Advance time slightly
            jest.advanceTimersByTime(1000);
            // Should not cause unnecessary re-renders
            expect(renderSpy).toHaveBeenCalledTimes(1);
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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