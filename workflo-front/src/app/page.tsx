'use client';

import React from 'react';

export default function Home() {
  // AuthGuard will handle all authentication logic and redirections
  // This component should be minimal to prevent glitching
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="w-16 h-16 bg-orange-500 rounded-lg flex items-center justify-center mx-auto mb-4">
          <span className="text-white font-bold text-xl">W</span>
        </div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Welcome to WorkFlow
        </h1>
        <p className="text-gray-600 mb-6">
          HR Management System
        </p>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">
          Initializing...
        </p>
      </div>
    </div>
  );
}
