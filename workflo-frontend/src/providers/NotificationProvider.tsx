'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useAuth } from './AuthProvider';
import webSocketService, { NotificationData, WebSocketService } from '@/lib/websocket';

interface NotificationContextType {
  notifications: NotificationData[];
  unreadCount: number;
  isConnected: boolean;
  connectionState: string;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  clearNotifications: () => void;
  requestPermission: () => Promise<NotificationPermission>;
}

const NotificationContext = createContext<NotificationContextType | null>(null);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: React.ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState('disconnected');

  // Update notifications from WebSocket service
  const updateNotifications = useCallback(() => {
    setNotifications(webSocketService.getNotifications());
  }, []);

  // Update connection state
  const updateConnectionState = useCallback(() => {
    setIsConnected(webSocketService.isConnected());
    setConnectionState(webSocketService.getConnectionState());
  }, []);

  // WebSocket event handlers
  useEffect(() => {
    if (!isAuthenticated) return;

    const handleNotification = (notification: NotificationData) => {
      updateNotifications();

      // Show toast notification (you can integrate with a toast library here)
      console.log('New notification:', notification);
    };

    const handleConnected = () => {
      updateConnectionState();
      console.log('WebSocket connected');
    };

    const handleDisconnected = () => {
      updateConnectionState();
      console.log('WebSocket disconnected');
    };

    const handleError = (error: any) => {
      updateConnectionState();
      console.error('WebSocket error:', error);
    };

    const handleMaxReconnectAttempts = () => {
      console.error('WebSocket max reconnection attempts reached');
      // You could show a user notification here
    };

    // Register event handlers
    webSocketService.on('notification', handleNotification);
    webSocketService.on('connected', handleConnected);
    webSocketService.on('disconnected', handleDisconnected);
    webSocketService.on('error', handleError);
    webSocketService.on('maxReconnectAttemptsReached', handleMaxReconnectAttempts);

    // Connect WebSocket
    webSocketService.connect();

    // Initial state update
    updateNotifications();
    updateConnectionState();

    // Cleanup
    return () => {
      webSocketService.off('notification', handleNotification);
      webSocketService.off('connected', handleConnected);
      webSocketService.off('disconnected', handleDisconnected);
      webSocketService.off('error', handleError);
      webSocketService.off('maxReconnectAttemptsReached', handleMaxReconnectAttempts);
    };
  }, [isAuthenticated, updateNotifications, updateConnectionState]);

  // Disconnect when user logs out
  useEffect(() => {
    if (!isAuthenticated) {
      webSocketService.disconnect();
      setNotifications([]);
      setIsConnected(false);
      setConnectionState('disconnected');
    }
  }, [isAuthenticated]);

  // Mark notification as read
  const markAsRead = useCallback((notificationId: string) => {
    webSocketService.markNotificationAsRead(notificationId);
    updateNotifications();
  }, [updateNotifications]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(() => {
    webSocketService.markAllNotificationsAsRead();
    updateNotifications();
  }, [updateNotifications]);

  // Clear all notifications
  const clearNotifications = useCallback(() => {
    webSocketService.clearNotifications();
    updateNotifications();
  }, [updateNotifications]);

  // Request browser notification permission
  const requestPermission = useCallback(async (): Promise<NotificationPermission> => {
    return await WebSocketService.requestNotificationPermission();
  }, []);

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.read).length;

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    isConnected,
    connectionState,
    markAsRead,
    markAllAsRead,
    clearNotifications,
    requestPermission
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export default NotificationProvider;
