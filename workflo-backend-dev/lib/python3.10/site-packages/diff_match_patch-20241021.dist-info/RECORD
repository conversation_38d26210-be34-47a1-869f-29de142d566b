diff_match_patch-20241021.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
diff_match_patch-20241021.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
diff_match_patch-20241021.dist-info/METADATA,sha256=p8k7y-F4E_M5XeeVvex8Wft-YzmYk2-MCcHw5HzABTI,5463
diff_match_patch-20241021.dist-info/RECORD,,
diff_match_patch-20241021.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
diff_match_patch-20241021.dist-info/WHEEL,sha256=EZbGkh7Ie4PoZfRQ8I0ZuP9VklN_TvcZ6DSE5Uar4z4,81
diff_match_patch/__init__.py,sha256=jKUEv4huO366n_3m6c8reljrUajK1G6uahPnPLiN0Mo,322
diff_match_patch/__pycache__/__init__.cpython-310.pyc,,
diff_match_patch/__pycache__/__version__.cpython-310.pyc,,
diff_match_patch/__pycache__/diff_match_patch.cpython-310.pyc,,
diff_match_patch/__version__.py,sha256=aBt4Imloj_7N4lcjbWLg_iQgAaAQAvs8OoFNWPDxC8s,159
diff_match_patch/diff_match_patch.py,sha256=xa7MKUVEH1PN_kE7WcI-iOtDv6YeREyo_w7uWCMjKnE,80575
diff_match_patch/tests/__init__.py,sha256=1lpyGcGhylYS6wrtIbqPxTRfHgO9Z3LQyBjtmn7ps1Y,66
diff_match_patch/tests/__pycache__/__init__.cpython-310.pyc,,
diff_match_patch/tests/__pycache__/diff_match_patch_test.cpython-310.pyc,,
diff_match_patch/tests/__pycache__/speedtest.cpython-310.pyc,,
diff_match_patch/tests/diff_match_patch_test.py,sha256=mJigqiSDN5MZD6Y_fi9BziW4kDvY0AeIVOuLzfVmNpk,57365
diff_match_patch/tests/speedtest.py,sha256=UniivpFUA9mlRZLbLh61gfN_rWfOO07MgZtLUgZ_DlU,1438
diff_match_patch/tests/speedtest1.txt,sha256=kqb1w5ktmGMu6npsYmHPGiauSEs0NYd4t3Si1Y_TVtM,12979
diff_match_patch/tests/speedtest2.txt,sha256=jRBt26i9SoWou0tZ5IG4j1Nt6YBG3I5PdvdVHCZcXdM,11918
