{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/hooks/useBiostarAttendance.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { attendanceService } from '@/lib/attendanceService';\nimport { AttendanceRecord, RealTimeAttendance, BiometricDevice } from '@/types';\n\ninterface UseBiostarAttendanceOptions {\n  employeeId?: string;\n  autoRefresh?: boolean;\n  refreshInterval?: number;\n  enableRealTime?: boolean;\n}\n\ninterface UseBiostarAttendanceReturn {\n  // Data\n  todayAttendance: AttendanceRecord | null;\n  attendanceRecords: AttendanceRecord[];\n  devices: BiometricDevice[];\n  realtimeUpdates: RealTimeAttendance[];\n  \n  // Status\n  loading: boolean;\n  connected: boolean;\n  error: string | null;\n  \n  // Actions\n  refresh: () => Promise<void>;\n  getAttendanceRange: (startDate: string, endDate: string) => Promise<AttendanceRecord[]>;\n  \n  // Summary data\n  summary: {\n    todayStatus: 'PRESENT' | 'ABSENT' | 'LATE' | 'EARLY_OUT';\n    checkInTime?: string;\n    checkOutTime?: string;\n    hoursWorked: number;\n    breakTime: number;\n    overtime: number;\n    weeklyHours: number;\n    monthlyAttendance: number;\n  } | null;\n}\n\nexport const useBiostarAttendance = (\n  options: UseBiostarAttendanceOptions = {}\n): UseBiostarAttendanceReturn => {\n  const {\n    employeeId,\n    autoRefresh = true,\n    refreshInterval = 300000, // 5 minutes\n    enableRealTime = true\n  } = options;\n\n  // State\n  const [todayAttendance, setTodayAttendance] = useState<AttendanceRecord | null>(null);\n  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);\n  const [devices, setDevices] = useState<BiometricDevice[]>([]);\n  const [realtimeUpdates, setRealtimeUpdates] = useState<RealTimeAttendance[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [connected, setConnected] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [summary, setSummary] = useState<any>(null);\n\n  // Load attendance data\n  const loadAttendanceData = useCallback(async () => {\n    if (!employeeId) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Get today's attendance\n      const today = await attendanceService.getTodayAttendance(employeeId);\n      setTodayAttendance(today);\n\n      // Get attendance summary\n      const summaryData = await attendanceService.getAttendanceSummary(employeeId);\n      setSummary(summaryData);\n\n      // Get this month's records\n      const monthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1)\n        .toISOString().split('T')[0];\n      const today_date = new Date().toISOString().split('T')[0];\n      \n      const records = await attendanceService.getAttendanceRange(employeeId, monthStart, today_date);\n      setAttendanceRecords(records);\n\n      // Get devices\n      const deviceList = await attendanceService.getDevices();\n      setDevices(deviceList);\n\n      setConnected(true);\n    } catch (err) {\n      console.error('Failed to load attendance data:', err);\n      setError(err instanceof Error ? err.message : 'Failed to load attendance data');\n      setConnected(false);\n    } finally {\n      setLoading(false);\n    }\n  }, [employeeId]);\n\n  // Refresh function\n  const refresh = useCallback(async () => {\n    await loadAttendanceData();\n  }, [loadAttendanceData]);\n\n  // Get attendance range\n  const getAttendanceRange = useCallback(async (startDate: string, endDate: string) => {\n    if (!employeeId) return [];\n    \n    try {\n      const records = await attendanceService.getAttendanceRange(employeeId, startDate, endDate);\n      return records;\n    } catch (err) {\n      console.error('Failed to get attendance range:', err);\n      setError(err instanceof Error ? err.message : 'Failed to get attendance range');\n      return [];\n    }\n  }, [employeeId]);\n\n  // Real-time updates handler\n  const handleRealTimeUpdate = useCallback((update: RealTimeAttendance) => {\n    setRealtimeUpdates(prev => {\n      // Add new update and keep only last 10\n      const newUpdates = [update, ...prev];\n      return newUpdates.slice(0, 10);\n    });\n\n    // If it's for current employee, refresh today's data\n    if (update.employee_id === employeeId) {\n      // Debounce the refresh to avoid too many calls\n      setTimeout(() => {\n        loadAttendanceData();\n      }, 2000);\n    }\n  }, [employeeId, loadAttendanceData]);\n\n  // Initial load\n  useEffect(() => {\n    if (employeeId) {\n      loadAttendanceData();\n    }\n  }, [employeeId, loadAttendanceData]);\n\n  // Auto refresh\n  useEffect(() => {\n    if (!autoRefresh || !employeeId) return;\n\n    const interval = setInterval(() => {\n      loadAttendanceData();\n    }, refreshInterval);\n\n    return () => clearInterval(interval);\n  }, [autoRefresh, refreshInterval, employeeId, loadAttendanceData]);\n\n  // Real-time monitoring\n  useEffect(() => {\n    if (!enableRealTime) return;\n\n    attendanceService.startRealTimeMonitoring(handleRealTimeUpdate);\n\n    return () => {\n      attendanceService.stopRealTimeMonitoring(handleRealTimeUpdate);\n    };\n  }, [enableRealTime, handleRealTimeUpdate]);\n\n  return {\n    // Data\n    todayAttendance,\n    attendanceRecords,\n    devices,\n    realtimeUpdates,\n    \n    // Status\n    loading,\n    connected,\n    error,\n    \n    // Actions\n    refresh,\n    getAttendanceRange,\n    \n    // Summary\n    summary\n  };\n};\n\n// Hook for device monitoring\nexport const useBiostarDevices = () => {\n  const [devices, setDevices] = useState<BiometricDevice[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const loadDevices = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const deviceList = await attendanceService.getDevices();\n      setDevices(deviceList);\n    } catch (err) {\n      console.error('Failed to load devices:', err);\n      setError(err instanceof Error ? err.message : 'Failed to load devices');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  useEffect(() => {\n    loadDevices();\n    \n    // Refresh devices every 2 minutes\n    const interval = setInterval(loadDevices, 120000);\n    return () => clearInterval(interval);\n  }, [loadDevices]);\n\n  return {\n    devices,\n    loading,\n    error,\n    refresh: loadDevices,\n    onlineDevices: devices.filter(d => d.status === 'ONLINE'),\n    offlineDevices: devices.filter(d => d.status === 'OFFLINE')\n  };\n};\n\n// Hook for real-time monitoring only\nexport const useBiostarRealTime = () => {\n  const [updates, setUpdates] = useState<RealTimeAttendance[]>([]);\n  const [isMonitoring, setIsMonitoring] = useState(false);\n\n  const startMonitoring = useCallback(() => {\n    if (isMonitoring) return;\n\n    const handleUpdate = (update: RealTimeAttendance) => {\n      setUpdates(prev => [update, ...prev.slice(0, 19)]); // Keep last 20 updates\n    };\n\n    attendanceService.startRealTimeMonitoring(handleUpdate);\n    setIsMonitoring(true);\n\n    return () => {\n      attendanceService.stopRealTimeMonitoring(handleUpdate);\n      setIsMonitoring(false);\n    };\n  }, [isMonitoring]);\n\n  const stopMonitoring = useCallback(() => {\n    attendanceService.stopRealTimeMonitoring();\n    setIsMonitoring(false);\n  }, []);\n\n  const clearUpdates = useCallback(() => {\n    setUpdates([]);\n  }, []);\n\n  return {\n    updates,\n    isMonitoring,\n    startMonitoring,\n    stopMonitoring,\n    clearUpdates\n  };\n};\n"], "names": ["useBiostarAttendance", "useBiostarDevices", "useBiostarRealTime", "options", "employeeId", "autoRefresh", "refreshInterval", "enableRealTime", "todayAttendance", "setTodayAttendance", "useState", "attendanceRecords", "setAttendanceRecords", "devices", "setDevices", "realtimeUpdates", "setRealtimeUpdates", "loading", "setLoading", "connected", "setConnected", "error", "setError", "summary", "set<PERSON>ummary", "loadAttendanceData", "useCallback", "today", "attendanceService", "getTodayAttendance", "summaryData", "getAttendanceSummary", "monthStart", "Date", "getFullYear", "getMonth", "toISOString", "split", "today_date", "records", "getAttendanceRange", "deviceList", "getDevices", "err", "console", "Error", "message", "refresh", "startDate", "endDate", "handleRealTimeUpdate", "update", "prev", "newUpdates", "slice", "employee_id", "setTimeout", "useEffect", "interval", "setInterval", "clearInterval", "startRealTimeMonitoring", "stopRealTimeMonitoring", "loadDevices", "onlineDevices", "filter", "d", "status", "offlineDevices", "updates", "setUpdates", "isMonitoring", "setIsMonitoring", "startMonitoring", "handleUpdate", "stopMonitoring", "clearUpdates"], "mappings": ";;;;;;;;;;;IAwCaA,oBAAoB;eAApBA;;IAiJAC,iBAAiB;eAAjBA;;IAsCAC,kBAAkB;eAAlBA;;;uBA/NoC;mCACf;AAuC3B,MAAMF,uBAAuB,CAClCG,UAAuC,CAAC,CAAC;IAEzC,MAAM,EACJC,UAAU,EACVC,cAAc,IAAI,EAClBC,kBAAkB,MAAM,EACxBC,iBAAiB,IAAI,EACtB,GAAGJ;IAEJ,QAAQ;IACR,MAAM,CAACK,iBAAiBC,mBAAmB,GAAGC,IAAAA,eAAQ,EAA0B;IAChF,MAAM,CAACC,mBAAmBC,qBAAqB,GAAGF,IAAAA,eAAQ,EAAqB,EAAE;IACjF,MAAM,CAACG,SAASC,WAAW,GAAGJ,IAAAA,eAAQ,EAAoB,EAAE;IAC5D,MAAM,CAACK,iBAAiBC,mBAAmB,GAAGN,IAAAA,eAAQ,EAAuB,EAAE;IAC/E,MAAM,CAACO,SAASC,WAAW,GAAGR,IAAAA,eAAQ,EAAC;IACvC,MAAM,CAACS,WAAWC,aAAa,GAAGV,IAAAA,eAAQ,EAAC;IAC3C,MAAM,CAACW,OAAOC,SAAS,GAAGZ,IAAAA,eAAQ,EAAgB;IAClD,MAAM,CAACa,SAASC,WAAW,GAAGd,IAAAA,eAAQ,EAAM;IAE5C,uBAAuB;IACvB,MAAMe,qBAAqBC,IAAAA,kBAAW,EAAC;QACrC,IAAI,CAACtB,YAAY;QAEjB,IAAI;YACFc,WAAW;YACXI,SAAS;YAET,yBAAyB;YACzB,MAAMK,QAAQ,MAAMC,oCAAiB,CAACC,kBAAkB,CAACzB;YACzDK,mBAAmBkB;YAEnB,yBAAyB;YACzB,MAAMG,cAAc,MAAMF,oCAAiB,CAACG,oBAAoB,CAAC3B;YACjEoB,WAAWM;YAEX,2BAA2B;YAC3B,MAAME,aAAa,IAAIC,KAAK,IAAIA,OAAOC,WAAW,IAAI,IAAID,OAAOE,QAAQ,IAAI,GAC1EC,WAAW,GAAGC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC9B,MAAMC,aAAa,IAAIL,OAAOG,WAAW,GAAGC,KAAK,CAAC,IAAI,CAAC,EAAE;YAEzD,MAAME,UAAU,MAAMX,oCAAiB,CAACY,kBAAkB,CAACpC,YAAY4B,YAAYM;YACnF1B,qBAAqB2B;YAErB,cAAc;YACd,MAAME,aAAa,MAAMb,oCAAiB,CAACc,UAAU;YACrD5B,WAAW2B;YAEXrB,aAAa;QACf,EAAE,OAAOuB,KAAK;YACZC,QAAQvB,KAAK,CAAC,mCAAmCsB;YACjDrB,SAASqB,eAAeE,QAAQF,IAAIG,OAAO,GAAG;YAC9C1B,aAAa;QACf,SAAU;YACRF,WAAW;QACb;IACF,GAAG;QAACd;KAAW;IAEf,mBAAmB;IACnB,MAAM2C,UAAUrB,IAAAA,kBAAW,EAAC;QAC1B,MAAMD;IACR,GAAG;QAACA;KAAmB;IAEvB,uBAAuB;IACvB,MAAMe,qBAAqBd,IAAAA,kBAAW,EAAC,OAAOsB,WAAmBC;QAC/D,IAAI,CAAC7C,YAAY,OAAO,EAAE;QAE1B,IAAI;YACF,MAAMmC,UAAU,MAAMX,oCAAiB,CAACY,kBAAkB,CAACpC,YAAY4C,WAAWC;YAClF,OAAOV;QACT,EAAE,OAAOI,KAAK;YACZC,QAAQvB,KAAK,CAAC,mCAAmCsB;YACjDrB,SAASqB,eAAeE,QAAQF,IAAIG,OAAO,GAAG;YAC9C,OAAO,EAAE;QACX;IACF,GAAG;QAAC1C;KAAW;IAEf,4BAA4B;IAC5B,MAAM8C,uBAAuBxB,IAAAA,kBAAW,EAAC,CAACyB;QACxCnC,mBAAmBoC,CAAAA;YACjB,uCAAuC;YACvC,MAAMC,aAAa;gBAACF;mBAAWC;aAAK;YACpC,OAAOC,WAAWC,KAAK,CAAC,GAAG;QAC7B;QAEA,qDAAqD;QACrD,IAAIH,OAAOI,WAAW,KAAKnD,YAAY;YACrC,+CAA+C;YAC/CoD,WAAW;gBACT/B;YACF,GAAG;QACL;IACF,GAAG;QAACrB;QAAYqB;KAAmB;IAEnC,eAAe;IACfgC,IAAAA,gBAAS,EAAC;QACR,IAAIrD,YAAY;YACdqB;QACF;IACF,GAAG;QAACrB;QAAYqB;KAAmB;IAEnC,eAAe;IACfgC,IAAAA,gBAAS,EAAC;QACR,IAAI,CAACpD,eAAe,CAACD,YAAY;QAEjC,MAAMsD,WAAWC,YAAY;YAC3BlC;QACF,GAAGnB;QAEH,OAAO,IAAMsD,cAAcF;IAC7B,GAAG;QAACrD;QAAaC;QAAiBF;QAAYqB;KAAmB;IAEjE,uBAAuB;IACvBgC,IAAAA,gBAAS,EAAC;QACR,IAAI,CAAClD,gBAAgB;QAErBqB,oCAAiB,CAACiC,uBAAuB,CAACX;QAE1C,OAAO;YACLtB,oCAAiB,CAACkC,sBAAsB,CAACZ;QAC3C;IACF,GAAG;QAAC3C;QAAgB2C;KAAqB;IAEzC,OAAO;QACL,OAAO;QACP1C;QACAG;QACAE;QACAE;QAEA,SAAS;QACTE;QACAE;QACAE;QAEA,UAAU;QACV0B;QACAP;QAEA,UAAU;QACVjB;IACF;AACF;AAGO,MAAMtB,oBAAoB;IAC/B,MAAM,CAACY,SAASC,WAAW,GAAGJ,IAAAA,eAAQ,EAAoB,EAAE;IAC5D,MAAM,CAACO,SAASC,WAAW,GAAGR,IAAAA,eAAQ,EAAC;IACvC,MAAM,CAACW,OAAOC,SAAS,GAAGZ,IAAAA,eAAQ,EAAgB;IAElD,MAAMqD,cAAcrC,IAAAA,kBAAW,EAAC;QAC9B,IAAI;YACFR,WAAW;YACXI,SAAS;YACT,MAAMmB,aAAa,MAAMb,oCAAiB,CAACc,UAAU;YACrD5B,WAAW2B;QACb,EAAE,OAAOE,KAAK;YACZC,QAAQvB,KAAK,CAAC,2BAA2BsB;YACzCrB,SAASqB,eAAeE,QAAQF,IAAIG,OAAO,GAAG;QAChD,SAAU;YACR5B,WAAW;QACb;IACF,GAAG,EAAE;IAELuC,IAAAA,gBAAS,EAAC;QACRM;QAEA,kCAAkC;QAClC,MAAML,WAAWC,YAAYI,aAAa;QAC1C,OAAO,IAAMH,cAAcF;IAC7B,GAAG;QAACK;KAAY;IAEhB,OAAO;QACLlD;QACAI;QACAI;QACA0B,SAASgB;QACTC,eAAenD,QAAQoD,MAAM,CAACC,CAAAA,IAAKA,EAAEC,MAAM,KAAK;QAChDC,gBAAgBvD,QAAQoD,MAAM,CAACC,CAAAA,IAAKA,EAAEC,MAAM,KAAK;IACnD;AACF;AAGO,MAAMjE,qBAAqB;IAChC,MAAM,CAACmE,SAASC,WAAW,GAAG5D,IAAAA,eAAQ,EAAuB,EAAE;IAC/D,MAAM,CAAC6D,cAAcC,gBAAgB,GAAG9D,IAAAA,eAAQ,EAAC;IAEjD,MAAM+D,kBAAkB/C,IAAAA,kBAAW,EAAC;QAClC,IAAI6C,cAAc;QAElB,MAAMG,eAAe,CAACvB;YACpBmB,WAAWlB,CAAAA,OAAQ;oBAACD;uBAAWC,KAAKE,KAAK,CAAC,GAAG;iBAAI,GAAG,uBAAuB;QAC7E;QAEA1B,oCAAiB,CAACiC,uBAAuB,CAACa;QAC1CF,gBAAgB;QAEhB,OAAO;YACL5C,oCAAiB,CAACkC,sBAAsB,CAACY;YACzCF,gBAAgB;QAClB;IACF,GAAG;QAACD;KAAa;IAEjB,MAAMI,iBAAiBjD,IAAAA,kBAAW,EAAC;QACjCE,oCAAiB,CAACkC,sBAAsB;QACxCU,gBAAgB;IAClB,GAAG,EAAE;IAEL,MAAMI,eAAelD,IAAAA,kBAAW,EAAC;QAC/B4C,WAAW,EAAE;IACf,GAAG,EAAE;IAEL,OAAO;QACLD;QACAE;QACAE;QACAE;QACAC;IACF;AACF"}