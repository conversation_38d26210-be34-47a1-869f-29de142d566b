'use client';

import React from 'react';
import { AccountantLayout } from '@/components/layout/AccountantLayout';
import RoleGuard from '@/components/auth/RoleGuard';

interface AccountantLayoutWrapperProps {
  children: React.ReactNode;
}

const AccountantLayoutWrapper: React.FC<AccountantLayoutWrapperProps> = ({ children }) => {
  return (
    <RoleGuard allowedRoles={['accountant', 'admin', 'hr']}>
      <AccountantLayout>
        {children}
      </AccountantLayout>
    </RoleGuard>
  );
};

export default AccountantLayoutWrapper;
