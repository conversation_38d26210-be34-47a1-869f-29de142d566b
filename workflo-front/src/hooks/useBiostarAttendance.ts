import { useState, useEffect, useCallback } from 'react';
import { attendanceService } from '@/lib/attendanceService';
import { AttendanceRecord, RealTimeAttendance, BiometricDevice } from '@/types';

interface UseBiostarAttendanceOptions {
  employeeId?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableRealTime?: boolean;
}

interface UseBiostarAttendanceReturn {
  // Data
  todayAttendance: AttendanceRecord | null;
  attendanceRecords: AttendanceRecord[];
  devices: BiometricDevice[];
  realtimeUpdates: RealTimeAttendance[];

  // Status
  loading: boolean;
  connected: boolean;
  error: string | null;

  // Actions
  refresh: () => Promise<void>;
  getAttendanceRange: (startDate: string, endDate: string) => Promise<AttendanceRecord[]>;

  // Summary data
  summary: {
    todayStatus: 'PRESENT' | 'ABSENT' | 'LATE' | 'EARLY_OUT';
    checkInTime?: string;
    checkOutTime?: string;
    hoursWorked: number;
    breakTime: number;
    overtime: number;
    weeklyHours: number;
    monthlyAttendance: number;
  } | null;
}

export const useBiostarAttendance = (
  options: UseBiostarAttendanceOptions = {}
): UseBiostarAttendanceReturn => {
  const {
    employeeId,
    autoRefresh = true,
    refreshInterval = 300000, // 5 minutes
    enableRealTime = true
  } = options;

  // State
  const [todayAttendance, setTodayAttendance] = useState<AttendanceRecord | null>(null);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [devices, setDevices] = useState<BiometricDevice[]>([]);
  const [realtimeUpdates, setRealtimeUpdates] = useState<RealTimeAttendance[]>([]);
  const [loading, setLoading] = useState(true);
  const [connected, setConnected] = useState(true); // Always connected for mock data
  const [error, setError] = useState<string | null>(null);
  const [summary, setSummary] = useState<any>(null);

  // Load attendance data (using mock data instead of BioStar API)
  const loadAttendanceData = useCallback(async () => {
    if (!employeeId) return;

    try {
      setLoading(true);
      setError(null);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Mock today's attendance
      const today: AttendanceRecord = {
        id: 'att-today',
        employee_id: employeeId,
        employee_name: 'Current User',
        date: new Date().toISOString().split('T')[0],
        first_in: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        last_out: undefined,
        total_hours: 2,
        break_time: 0,
        overtime: 0,
        status: 'PRESENT',
        events: [
          {
            id: 'event-1',
            user_id: employeeId,
            device_id: 'dev-001',
            event_type: 'ENTRY',
            datetime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            user_name: 'Current User',
            device_name: 'Main Entrance'
          }
        ],
        biostar_synced: true
      };
      setTodayAttendance(today);

      // Mock attendance summary
      const summaryData = {
        todayStatus: 'PRESENT' as const,
        checkInTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        hoursWorked: 2,
        breakTime: 0,
        overtime: 0,
        weeklyHours: 32,
        monthlyAttendance: 20
      };
      setSummary(summaryData);

      // Mock recent attendance records
      const records: AttendanceRecord[] = Array.from({ length: 10 }, (_, i) => ({
        id: `att-${i}`,
        employee_id: employeeId,
        employee_name: 'Current User',
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        first_in: new Date(Date.now() - i * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000).toISOString(),
        last_out: new Date(Date.now() - i * 24 * 60 * 60 * 1000 + 17 * 60 * 60 * 1000).toISOString(),
        total_hours: 8,
        break_time: 1,
        overtime: 0,
        status: i === 0 ? 'PRESENT' : 'PRESENT',
        events: [],
        biostar_synced: true
      }));
      setAttendanceRecords(records);

      // Mock devices
      const deviceList: BiometricDevice[] = [
        {
          id: 'dev-001',
          name: 'Main Entrance',
          ip: '*************',
          port: 8080,
          status: 'ONLINE',
          type: 'Fingerprint Scanner',
          location: 'Main Building'
        },
        {
          id: 'dev-002',
          name: 'Office Floor',
          ip: '*************',
          port: 8080,
          status: 'ONLINE',
          type: 'Face Recognition',
          location: 'Second Floor'
        },
        {
          id: 'dev-003',
          name: 'Back Entrance',
          ip: '*************',
          port: 8080,
          status: 'OFFLINE',
          type: 'Fingerprint Scanner',
          location: 'Back Building'
        }
      ];
      setDevices(deviceList);

      setConnected(true);
    } catch (err) {
      console.error('Failed to load attendance data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load attendance data');
      setConnected(false);
    } finally {
      setLoading(false);
    }
  }, [employeeId]);

  // Refresh function
  const refresh = useCallback(async () => {
    await loadAttendanceData();
  }, [loadAttendanceData]);

  // Get attendance range (using mock data)
  const getAttendanceRange = useCallback(async (startDate: string, endDate: string) => {
    if (!employeeId) return [];

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      // Generate mock records for the date range
      const start = new Date(startDate);
      const end = new Date(endDate);
      const records: AttendanceRecord[] = [];

      for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
        // Skip weekends
        if (d.getDay() === 0 || d.getDay() === 6) continue;

        records.push({
          id: `att-${d.toISOString().split('T')[0]}`,
          employee_id: employeeId,
          employee_name: 'Current User',
          date: d.toISOString().split('T')[0],
          first_in: new Date(d.getTime() + 8 * 60 * 60 * 1000).toISOString(),
          last_out: new Date(d.getTime() + 17 * 60 * 60 * 1000).toISOString(),
          total_hours: 8,
          break_time: 1,
          overtime: 0,
          status: 'PRESENT',
          events: [],
          biostar_synced: true
        });
      }

      return records;
    } catch (err) {
      console.error('Failed to get attendance range:', err);
      setError(err instanceof Error ? err.message : 'Failed to get attendance range');
      return [];
    }
  }, [employeeId]);

  // Real-time updates handler
  const handleRealTimeUpdate = useCallback((update: RealTimeAttendance) => {
    setRealtimeUpdates(prev => {
      // Add new update and keep only last 10
      const newUpdates = [update, ...prev];
      return newUpdates.slice(0, 10);
    });

    // If it's for current employee, refresh today's data
    if (update.employee_id === employeeId) {
      // Debounce the refresh to avoid too many calls
      setTimeout(() => {
        loadAttendanceData();
      }, 2000);
    }
  }, [employeeId, loadAttendanceData]);

  // Initial load
  useEffect(() => {
    if (employeeId) {
      loadAttendanceData();
    }
  }, [employeeId, loadAttendanceData]);

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh || !employeeId) return;

    const interval = setInterval(() => {
      loadAttendanceData();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, employeeId, loadAttendanceData]);

  // Real-time monitoring (disabled - using mock data)
  useEffect(() => {
    if (!enableRealTime) return;

    // Generate mock real-time updates periodically
    const interval = setInterval(() => {
      const mockUpdate: RealTimeAttendance = {
        employee_id: `EMP${Math.floor(Math.random() * 100).toString().padStart(3, '0')}`,
        employee_name: `Employee ${Math.floor(Math.random() * 100)}`,
        event_type: Math.random() > 0.5 ? 'CHECK_IN' : 'CHECK_OUT',
        device_name: ['Main Entrance', 'Office Floor', 'Back Entrance'][Math.floor(Math.random() * 3)],
        timestamp: new Date().toISOString(),
        location: 'Office Building'
      };

      handleRealTimeUpdate(mockUpdate);
    }, 30000); // Every 30 seconds

    return () => clearInterval(interval);
  }, [enableRealTime, handleRealTimeUpdate]);

  return {
    // Data
    todayAttendance,
    attendanceRecords,
    devices,
    realtimeUpdates,

    // Status
    loading,
    connected,
    error,

    // Actions
    refresh,
    getAttendanceRange,

    // Summary
    summary
  };
};

// Hook for device monitoring (using mock data)
export const useBiostarDevices = () => {
  const [devices, setDevices] = useState<BiometricDevice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadDevices = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      // Mock device list
      const deviceList: BiometricDevice[] = [
        {
          id: 'dev-001',
          name: 'Main Entrance',
          ip: '*************',
          port: 8080,
          status: 'ONLINE',
          type: 'Fingerprint Scanner',
          location: 'Main Building'
        },
        {
          id: 'dev-002',
          name: 'Office Floor',
          ip: '*************',
          port: 8080,
          status: 'ONLINE',
          type: 'Face Recognition',
          location: 'Second Floor'
        },
        {
          id: 'dev-003',
          name: 'Back Entrance',
          ip: '*************',
          port: 8080,
          status: Math.random() > 0.5 ? 'ONLINE' : 'OFFLINE', // Random status
          type: 'Fingerprint Scanner',
          location: 'Back Building'
        },
        {
          id: 'dev-004',
          name: 'Conference Room',
          ip: '*************',
          port: 8080,
          status: 'ONLINE',
          type: 'Card Reader',
          location: 'Third Floor'
        }
      ];

      setDevices(deviceList);
    } catch (err) {
      console.error('Failed to load devices:', err);
      setError(err instanceof Error ? err.message : 'Failed to load devices');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadDevices();

    // Refresh devices every 2 minutes
    const interval = setInterval(loadDevices, 120000);
    return () => clearInterval(interval);
  }, [loadDevices]);

  return {
    devices,
    loading,
    error,
    refresh: loadDevices,
    onlineDevices: devices.filter(d => d.status === 'ONLINE'),
    offlineDevices: devices.filter(d => d.status === 'OFFLINE')
  };
};

// Hook for real-time monitoring only
export const useBiostarRealTime = () => {
  const [updates, setUpdates] = useState<RealTimeAttendance[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  const startMonitoring = useCallback(() => {
    if (isMonitoring) return;

    const handleUpdate = (update: RealTimeAttendance) => {
      setUpdates(prev => [update, ...prev.slice(0, 19)]); // Keep last 20 updates
    };

    attendanceService.startRealTimeMonitoring(handleUpdate);
    setIsMonitoring(true);

    return () => {
      attendanceService.stopRealTimeMonitoring(handleUpdate);
      setIsMonitoring(false);
    };
  }, [isMonitoring]);

  const stopMonitoring = useCallback(() => {
    attendanceService.stopRealTimeMonitoring();
    setIsMonitoring(false);
  }, []);

  const clearUpdates = useCallback(() => {
    setUpdates([]);
  }, []);

  return {
    updates,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    clearUpdates
  };
};
