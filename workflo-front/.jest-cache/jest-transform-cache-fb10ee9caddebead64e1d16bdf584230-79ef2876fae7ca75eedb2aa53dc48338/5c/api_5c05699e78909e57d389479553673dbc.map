{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport { AuthTokens, ApiResponse } from '@/types';\nimport { mockApi, setMockUserEmail, clearMockUserEmail } from './mockApi';\n\n// API Configuration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';\nconst USE_MOCK_API = process.env.NEXT_PUBLIC_USE_MOCK_API === 'true' || true; // Default to true for development\n\n// Create axios instance\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Token management\nclass TokenManager {\n  private static readonly ACCESS_TOKEN_KEY = 'access_token';\n  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';\n\n  static getAccessToken(): string | null {\n    if (typeof window === 'undefined') return null;\n    return localStorage.getItem(this.ACCESS_TOKEN_KEY);\n  }\n\n  static getRefreshToken(): string | null {\n    if (typeof window === 'undefined') return null;\n    return localStorage.getItem(this.REFRESH_TOKEN_KEY);\n  }\n\n  static setTokens(tokens: AuthTokens): void {\n    if (typeof window === 'undefined') return;\n    localStorage.setItem(this.ACCESS_TOKEN_KEY, tokens.access);\n    localStorage.setItem(this.REFRESH_TOKEN_KEY, tokens.refresh);\n  }\n\n  static clearTokens(): void {\n    if (typeof window === 'undefined') return;\n    localStorage.removeItem(this.ACCESS_TOKEN_KEY);\n    localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n  }\n\n  static isTokenExpired(token: string): boolean {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Date.now() / 1000;\n      return payload.exp < currentTime;\n    } catch {\n      return true;\n    }\n  }\n}\n\n// Request interceptor to add auth token\napiClient.interceptors.request.use(\n  (config) => {\n    const token = TokenManager.getAccessToken();\n    if (token && !TokenManager.isTokenExpired(token)) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle token refresh\napiClient.interceptors.response.use(\n  (response) => response,\n  async (error) => {\n    const originalRequest = error.config;\n\n    if (error.response?.status === 401 && !originalRequest._retry) {\n      originalRequest._retry = true;\n\n      const refreshToken = TokenManager.getRefreshToken();\n      if (refreshToken && !TokenManager.isTokenExpired(refreshToken)) {\n        try {\n          const response = await axios.post(`${API_BASE_URL}/token/refresh/`, {\n            refresh: refreshToken,\n          });\n\n          const newTokens: AuthTokens = response.data;\n          TokenManager.setTokens(newTokens);\n\n          // Retry original request with new token\n          originalRequest.headers.Authorization = `Bearer ${newTokens.access}`;\n          return apiClient(originalRequest);\n        } catch (refreshError) {\n          // Refresh failed, redirect to login\n          TokenManager.clearTokens();\n          if (typeof window !== 'undefined') {\n            window.location.href = '/login';\n          }\n          return Promise.reject(refreshError);\n        }\n      } else {\n        // No valid refresh token, redirect to login\n        TokenManager.clearTokens();\n        if (typeof window !== 'undefined') {\n          window.location.href = '/login';\n        }\n      }\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// API wrapper class\nclass ApiService {\n  // Generic request method\n  private async request<T>(config: AxiosRequestConfig): Promise<T> {\n    try {\n      const response: AxiosResponse<T> = await apiClient(config);\n      return response.data;\n    } catch (error) {\n      console.error('API Request Error:', error);\n      throw error;\n    }\n  }\n\n  // GET request\n  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    return this.request<T>({ ...config, method: 'GET', url });\n  }\n\n  // POST request\n  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    return this.request<T>({ ...config, method: 'POST', url, data });\n  }\n\n  // PUT request\n  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    return this.request<T>({ ...config, method: 'PUT', url, data });\n  }\n\n  // PATCH request\n  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    return this.request<T>({ ...config, method: 'PATCH', url, data });\n  }\n\n  // DELETE request\n  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    return this.request<T>({ ...config, method: 'DELETE', url });\n  }\n\n  // File upload\n  async uploadFile<T>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    return this.request<T>({\n      method: 'POST',\n      url,\n      data: formData,\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      onUploadProgress: (progressEvent) => {\n        if (onProgress && progressEvent.total) {\n          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n          onProgress(progress);\n        }\n      },\n    });\n  }\n}\n\n// Create API service instance\nexport const api = new ApiService();\n\n// Authentication API\nexport const authApi = {\n  login: async (credentials: { email: string; password: string }) => {\n    if (USE_MOCK_API) {\n      const tokens = await mockApi.login(credentials);\n      setMockUserEmail(credentials.email);\n      return tokens;\n    }\n    return api.post<AuthTokens>('/token/', credentials);\n  },\n\n  refreshToken: (refreshToken: string) =>\n    api.post<AuthTokens>('/token/refresh/', { refresh: refreshToken }),\n\n  logout: () => {\n    TokenManager.clearTokens();\n    if (USE_MOCK_API) {\n      clearMockUserEmail();\n    }\n    if (typeof window !== 'undefined') {\n      window.location.href = '/login';\n    }\n  },\n\n  getCurrentUser: () => {\n    if (USE_MOCK_API) {\n      return mockApi.getCurrentUser();\n    }\n    return api.get('/employees/me/');\n  },\n};\n\n// Employee API\nexport const employeeApi = {\n  getAll: () => {\n    if (USE_MOCK_API) {\n      return mockApi.getEmployees();\n    }\n    return api.get('/get_employees/');\n  },\n  getById: (id: number) => {\n    if (USE_MOCK_API) {\n      return mockApi.getEmployeeById(id);\n    }\n    return api.get(`/employees/${id}/`);\n  },\n  create: (data: any) => api.post('/new_employee/', data),\n  update: (id: number, data: any) => api.put(`/new_employee/${id}/`, data),\n  delete: (id: number) => api.delete(`/employees/${id}/`),\n  getProfile: () => {\n    if (USE_MOCK_API) {\n      return mockApi.getCurrentUser();\n    }\n    return api.get('/employees/me/');\n  },\n};\n\n// Department API\nexport const departmentApi = {\n  getAll: () => api.get('/get_departments/'),\n  create: (data: any) => api.post('/departments/', data),\n  update: (id: number, data: any) => api.put(`/departments/${id}/`, data),\n  delete: (id: number) => api.delete(`/departments/${id}/`),\n};\n\n// Attendance API\nexport const attendanceApi = {\n  getMyAttendance: () => api.get('/attendance/'),\n  clockIn: () => api.post('/attendance/', { action: 'clock_in' }),\n  clockOut: () => api.post('/attendance/', { action: 'clock_out' }),\n  getByEmployee: (employeeId: number) => api.get(`/attendance/?employee=${employeeId}`),\n};\n\n// Leave API\nexport const leaveApi = {\n  getMyLeaves: () => api.get('/leave_applications/'),\n  getPendingLeaves: () => api.get('/pending_leave/'),\n  getRejectedLeaves: () => api.get('/my_rejected_leaves/'),\n  apply: (data: any) => api.post('/leave_applications/', data),\n  approve: (id: number, data: any) => api.put(`/pending_leave/${id}/`, data),\n  reject: (id: number, data: any) => api.put(`/pending_leave/${id}/`, data),\n};\n\n// Payroll API\nexport const payrollApi = {\n  getMyPayroll: () => api.get('/payroll/'),\n  getPayCycles: () => api.get('/paycycle/'),\n  createPayCycle: (data: any) => api.post('/paycycle/', data),\n  markPaid: (id: number) => api.patch(`/paycycle/${id}/mark-paid/`),\n  bulkCreatePayroll: (data: any) => api.post('/payroll_create/bulk-create/', data),\n  getEmployeePayroll: (employeeId: number, payCycleId: number) =>\n    api.get(`/payroll_create/employee-detail/?employee=${employeeId}&pay_cycle=${payCycleId}`),\n};\n\n// Performance API\nexport const performanceApi = {\n  getMyReviews: () => api.get('/performance_reviews/'),\n  getTeamReviews: () => api.get('/reviews/'),\n  create: (data: any) => api.post('/performance_reviews/', data),\n  update: (id: number, data: any) => api.put(`/performance_reviews/${id}/`, data),\n};\n\n// Training API\nexport const trainingApi = {\n  getModules: () => api.get('/training_modules/'),\n  getMyTrainings: () => api.get('/employee_trainings/'),\n  assignTraining: (data: any) => api.post('/employee_trainings/', data),\n  updateProgress: (id: number, data: any) => api.put(`/employee_trainings/${id}/`, data),\n};\n\n// Job API\nexport const jobApi = {\n  getPostings: () => api.get('/get_job_postings/'),\n  getCandidates: () => api.get('/candidates/'),\n  createPosting: (data: any) => api.post('/job_postings/', data),\n  apply: (data: any) => api.post('/candidates/', data),\n};\n\n// Document API\nexport const documentApi = {\n  getMyDocuments: () => api.get('/user_documents/'),\n  getAllDocuments: () => api.get('/admin_documents/'),\n  getCompanyDocuments: () => api.get('/company_documents/'),\n  upload: (file: File, onProgress?: (progress: number) => void) =>\n    api.uploadFile('/employee_documents/', file, onProgress),\n};\n\n// Benefits API\nexport const benefitApi = {\n  getAll: () => api.get('/benefits/'),\n  create: (data: any) => api.post('/benefits/', data),\n  update: (id: number, data: any) => api.put(`/benefits/${id}/`, data),\n  delete: (id: number) => api.delete(`/benefits/${id}/`),\n};\n\n// Export token manager for use in components\nexport { TokenManager };\n\n// Export the configured axios instance for custom requests\nexport { apiClient };\n"], "names": ["TokenManager", "api", "apiClient", "attendanceApi", "authApi", "benefitApi", "departmentApi", "documentApi", "employeeApi", "jobApi", "leaveApi", "payrollApi", "performanceApi", "trainingApi", "API_BASE_URL", "process", "env", "NEXT_PUBLIC_API_URL", "USE_MOCK_API", "NEXT_PUBLIC_USE_MOCK_API", "axios", "create", "baseURL", "timeout", "headers", "ACCESS_TOKEN_KEY", "REFRESH_TOKEN_KEY", "getAccessToken", "window", "localStorage", "getItem", "getRefreshToken", "setTokens", "tokens", "setItem", "access", "refresh", "clearTokens", "removeItem", "isTokenExpired", "token", "payload", "JSON", "parse", "atob", "split", "currentTime", "Date", "now", "exp", "interceptors", "request", "use", "config", "Authorization", "error", "Promise", "reject", "response", "originalRequest", "status", "_retry", "refreshToken", "post", "newTokens", "data", "refreshError", "location", "href", "ApiService", "console", "get", "url", "method", "put", "patch", "delete", "uploadFile", "file", "onProgress", "formData", "FormData", "append", "onUploadProgress", "progressEvent", "total", "progress", "Math", "round", "loaded", "login", "credentials", "mockApi", "setMockUserEmail", "email", "logout", "clearMockUserEmail", "getCurrentUser", "getAll", "getEmployees", "getById", "id", "getEmployeeById", "update", "getProfile", "getMyAttendance", "clockIn", "action", "clockOut", "getByEmployee", "employeeId", "getMyLeaves", "getPendingLeaves", "getRejectedLeaves", "apply", "approve", "getMyPayroll", "getPayCycles", "createPayCycle", "markPaid", "bulkCreatePayroll", "getEmployeePayroll", "payCycleId", "getMyReviews", "getTeamReviews", "getModules", "getMyTrainings", "assignTraining", "updateProgress", "getPostings", "getCandidates", "createPosting", "getMyDocuments", "getAllDocuments", "getCompanyDocuments", "upload"], "mappings": ";;;;;;;;;;;IAuTSA,YAAY;eAAZA;;IA1IIC,GAAG;eAAHA;;IA6IJC,SAAS;eAATA;;IAzEIC,aAAa;eAAbA;;IAjEAC,OAAO;eAAPA;;IA+HAC,UAAU;eAAVA;;IAtEAC,aAAa;eAAbA;;IA6DAC,WAAW;eAAXA;;IAtFAC,WAAW;eAAXA;;IA8EAC,MAAM;eAANA;;IArCAC,QAAQ;eAARA;;IAUAC,UAAU;eAAVA;;IAWAC,cAAc;eAAdA;;IAQAC,WAAW;eAAXA;;;8DAtR2D;yBAEV;;;;;;AAE9D,oBAAoB;AACpB,MAAMC,eAAeC,QAAQC,GAAG,CAACC,mBAAmB,IAAI;AACxD,MAAMC,eAAeH,QAAQC,GAAG,CAACG,wBAAwB,KAAK,UAAU,MAAM,kCAAkC;AAEhH,wBAAwB;AACxB,MAAMjB,YAA2BkB,cAAK,CAACC,MAAM,CAAC;IAC5CC,SAASR;IACTS,SAAS;IACTC,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,mBAAmB;AACnB,MAAMxB;;aACoByB,mBAAmB;;;aACnBC,oBAAoB;;IAE5C,OAAOC,iBAAgC;QACrC,IAAI,OAAOC,WAAW,aAAa,OAAO;QAC1C,OAAOC,aAAaC,OAAO,CAAC,IAAI,CAACL,gBAAgB;IACnD;IAEA,OAAOM,kBAAiC;QACtC,IAAI,OAAOH,WAAW,aAAa,OAAO;QAC1C,OAAOC,aAAaC,OAAO,CAAC,IAAI,CAACJ,iBAAiB;IACpD;IAEA,OAAOM,UAAUC,MAAkB,EAAQ;QACzC,IAAI,OAAOL,WAAW,aAAa;QACnCC,aAAaK,OAAO,CAAC,IAAI,CAACT,gBAAgB,EAAEQ,OAAOE,MAAM;QACzDN,aAAaK,OAAO,CAAC,IAAI,CAACR,iBAAiB,EAAEO,OAAOG,OAAO;IAC7D;IAEA,OAAOC,cAAoB;QACzB,IAAI,OAAOT,WAAW,aAAa;QACnCC,aAAaS,UAAU,CAAC,IAAI,CAACb,gBAAgB;QAC7CI,aAAaS,UAAU,CAAC,IAAI,CAACZ,iBAAiB;IAChD;IAEA,OAAOa,eAAeC,KAAa,EAAW;QAC5C,IAAI;YACF,MAAMC,UAAUC,KAAKC,KAAK,CAACC,KAAKJ,MAAMK,KAAK,CAAC,IAAI,CAAC,EAAE;YACnD,MAAMC,cAAcC,KAAKC,GAAG,KAAK;YACjC,OAAOP,QAAQQ,GAAG,GAAGH;QACvB,EAAE,OAAM;YACN,OAAO;QACT;IACF;AACF;AAEA,wCAAwC;AACxC5C,UAAUgD,YAAY,CAACC,OAAO,CAACC,GAAG,CAChC,CAACC;IACC,MAAMb,QAAQxC,aAAa2B,cAAc;IACzC,IAAIa,SAAS,CAACxC,aAAauC,cAAc,CAACC,QAAQ;QAChDa,OAAO7B,OAAO,CAAC8B,aAAa,GAAG,CAAC,OAAO,EAAEd,OAAO;IAClD;IACA,OAAOa;AACT,GACA,CAACE;IACC,OAAOC,QAAQC,MAAM,CAACF;AACxB;AAGF,+CAA+C;AAC/CrD,UAAUgD,YAAY,CAACQ,QAAQ,CAACN,GAAG,CACjC,CAACM,WAAaA,UACd,OAAOH;IACL,MAAMI,kBAAkBJ,MAAMF,MAAM;IAEpC,IAAIE,MAAMG,QAAQ,EAAEE,WAAW,OAAO,CAACD,gBAAgBE,MAAM,EAAE;QAC7DF,gBAAgBE,MAAM,GAAG;QAEzB,MAAMC,eAAe9D,aAAa+B,eAAe;QACjD,IAAI+B,gBAAgB,CAAC9D,aAAauC,cAAc,CAACuB,eAAe;YAC9D,IAAI;gBACF,MAAMJ,WAAW,MAAMtC,cAAK,CAAC2C,IAAI,CAAC,GAAGjD,aAAa,eAAe,CAAC,EAAE;oBAClEsB,SAAS0B;gBACX;gBAEA,MAAME,YAAwBN,SAASO,IAAI;gBAC3CjE,aAAagC,SAAS,CAACgC;gBAEvB,wCAAwC;gBACxCL,gBAAgBnC,OAAO,CAAC8B,aAAa,GAAG,CAAC,OAAO,EAAEU,UAAU7B,MAAM,EAAE;gBACpE,OAAOjC,UAAUyD;YACnB,EAAE,OAAOO,cAAc;gBACrB,oCAAoC;gBACpClE,aAAaqC,WAAW;gBACxB,IAAI,OAAOT,WAAW,aAAa;oBACjCA,OAAOuC,QAAQ,CAACC,IAAI,GAAG;gBACzB;gBACA,OAAOZ,QAAQC,MAAM,CAACS;YACxB;QACF,OAAO;YACL,4CAA4C;YAC5ClE,aAAaqC,WAAW;YACxB,IAAI,OAAOT,WAAW,aAAa;gBACjCA,OAAOuC,QAAQ,CAACC,IAAI,GAAG;YACzB;QACF;IACF;IAEA,OAAOZ,QAAQC,MAAM,CAACF;AACxB;AAGF,oBAAoB;AACpB,MAAMc;IACJ,yBAAyB;IACzB,MAAclB,QAAWE,MAA0B,EAAc;QAC/D,IAAI;YACF,MAAMK,WAA6B,MAAMxD,UAAUmD;YACnD,OAAOK,SAASO,IAAI;QACtB,EAAE,OAAOV,OAAO;YACde,QAAQf,KAAK,CAAC,sBAAsBA;YACpC,MAAMA;QACR;IACF;IAEA,cAAc;IACd,MAAMgB,IAAOC,GAAW,EAAEnB,MAA2B,EAAc;QACjE,OAAO,IAAI,CAACF,OAAO,CAAI;YAAE,GAAGE,MAAM;YAAEoB,QAAQ;YAAOD;QAAI;IACzD;IAEA,eAAe;IACf,MAAMT,KAAQS,GAAW,EAAEP,IAAU,EAAEZ,MAA2B,EAAc;QAC9E,OAAO,IAAI,CAACF,OAAO,CAAI;YAAE,GAAGE,MAAM;YAAEoB,QAAQ;YAAQD;YAAKP;QAAK;IAChE;IAEA,cAAc;IACd,MAAMS,IAAOF,GAAW,EAAEP,IAAU,EAAEZ,MAA2B,EAAc;QAC7E,OAAO,IAAI,CAACF,OAAO,CAAI;YAAE,GAAGE,MAAM;YAAEoB,QAAQ;YAAOD;YAAKP;QAAK;IAC/D;IAEA,gBAAgB;IAChB,MAAMU,MAASH,GAAW,EAAEP,IAAU,EAAEZ,MAA2B,EAAc;QAC/E,OAAO,IAAI,CAACF,OAAO,CAAI;YAAE,GAAGE,MAAM;YAAEoB,QAAQ;YAASD;YAAKP;QAAK;IACjE;IAEA,iBAAiB;IACjB,MAAMW,OAAUJ,GAAW,EAAEnB,MAA2B,EAAc;QACpE,OAAO,IAAI,CAACF,OAAO,CAAI;YAAE,GAAGE,MAAM;YAAEoB,QAAQ;YAAUD;QAAI;IAC5D;IAEA,cAAc;IACd,MAAMK,WAAcL,GAAW,EAAEM,IAAU,EAAEC,UAAuC,EAAc;QAChG,MAAMC,WAAW,IAAIC;QACrBD,SAASE,MAAM,CAAC,QAAQJ;QAExB,OAAO,IAAI,CAAC3B,OAAO,CAAI;YACrBsB,QAAQ;YACRD;YACAP,MAAMe;YACNxD,SAAS;gBACP,gBAAgB;YAClB;YACA2D,kBAAkB,CAACC;gBACjB,IAAIL,cAAcK,cAAcC,KAAK,EAAE;oBACrC,MAAMC,WAAWC,KAAKC,KAAK,CAAC,AAACJ,cAAcK,MAAM,GAAG,MAAOL,cAAcC,KAAK;oBAC9EN,WAAWO;gBACb;YACF;QACF;IACF;AACF;AAGO,MAAMrF,MAAM,IAAIoE;AAGhB,MAAMjE,UAAU;IACrBsF,OAAO,OAAOC;QACZ,IAAIzE,cAAc;YAChB,MAAMe,SAAS,MAAM2D,gBAAO,CAACF,KAAK,CAACC;YACnCE,IAAAA,yBAAgB,EAACF,YAAYG,KAAK;YAClC,OAAO7D;QACT;QACA,OAAOhC,IAAI8D,IAAI,CAAa,WAAW4B;IACzC;IAEA7B,cAAc,CAACA,eACb7D,IAAI8D,IAAI,CAAa,mBAAmB;YAAE3B,SAAS0B;QAAa;IAElEiC,QAAQ;QACN/F,aAAaqC,WAAW;QACxB,IAAInB,cAAc;YAChB8E,IAAAA,2BAAkB;QACpB;QACA,IAAI,OAAOpE,WAAW,aAAa;YACjCA,OAAOuC,QAAQ,CAACC,IAAI,GAAG;QACzB;IACF;IAEA6B,gBAAgB;QACd,IAAI/E,cAAc;YAChB,OAAO0E,gBAAO,CAACK,cAAc;QAC/B;QACA,OAAOhG,IAAIsE,GAAG,CAAC;IACjB;AACF;AAGO,MAAM/D,cAAc;IACzB0F,QAAQ;QACN,IAAIhF,cAAc;YAChB,OAAO0E,gBAAO,CAACO,YAAY;QAC7B;QACA,OAAOlG,IAAIsE,GAAG,CAAC;IACjB;IACA6B,SAAS,CAACC;QACR,IAAInF,cAAc;YAChB,OAAO0E,gBAAO,CAACU,eAAe,CAACD;QACjC;QACA,OAAOpG,IAAIsE,GAAG,CAAC,CAAC,WAAW,EAAE8B,GAAG,CAAC,CAAC;IACpC;IACAhF,QAAQ,CAAC4C,OAAchE,IAAI8D,IAAI,CAAC,kBAAkBE;IAClDsC,QAAQ,CAACF,IAAYpC,OAAchE,IAAIyE,GAAG,CAAC,CAAC,cAAc,EAAE2B,GAAG,CAAC,CAAC,EAAEpC;IACnEW,QAAQ,CAACyB,KAAepG,IAAI2E,MAAM,CAAC,CAAC,WAAW,EAAEyB,GAAG,CAAC,CAAC;IACtDG,YAAY;QACV,IAAItF,cAAc;YAChB,OAAO0E,gBAAO,CAACK,cAAc;QAC/B;QACA,OAAOhG,IAAIsE,GAAG,CAAC;IACjB;AACF;AAGO,MAAMjE,gBAAgB;IAC3B4F,QAAQ,IAAMjG,IAAIsE,GAAG,CAAC;IACtBlD,QAAQ,CAAC4C,OAAchE,IAAI8D,IAAI,CAAC,iBAAiBE;IACjDsC,QAAQ,CAACF,IAAYpC,OAAchE,IAAIyE,GAAG,CAAC,CAAC,aAAa,EAAE2B,GAAG,CAAC,CAAC,EAAEpC;IAClEW,QAAQ,CAACyB,KAAepG,IAAI2E,MAAM,CAAC,CAAC,aAAa,EAAEyB,GAAG,CAAC,CAAC;AAC1D;AAGO,MAAMlG,gBAAgB;IAC3BsG,iBAAiB,IAAMxG,IAAIsE,GAAG,CAAC;IAC/BmC,SAAS,IAAMzG,IAAI8D,IAAI,CAAC,gBAAgB;YAAE4C,QAAQ;QAAW;IAC7DC,UAAU,IAAM3G,IAAI8D,IAAI,CAAC,gBAAgB;YAAE4C,QAAQ;QAAY;IAC/DE,eAAe,CAACC,aAAuB7G,IAAIsE,GAAG,CAAC,CAAC,sBAAsB,EAAEuC,YAAY;AACtF;AAGO,MAAMpG,WAAW;IACtBqG,aAAa,IAAM9G,IAAIsE,GAAG,CAAC;IAC3ByC,kBAAkB,IAAM/G,IAAIsE,GAAG,CAAC;IAChC0C,mBAAmB,IAAMhH,IAAIsE,GAAG,CAAC;IACjC2C,OAAO,CAACjD,OAAchE,IAAI8D,IAAI,CAAC,wBAAwBE;IACvDkD,SAAS,CAACd,IAAYpC,OAAchE,IAAIyE,GAAG,CAAC,CAAC,eAAe,EAAE2B,GAAG,CAAC,CAAC,EAAEpC;IACrER,QAAQ,CAAC4C,IAAYpC,OAAchE,IAAIyE,GAAG,CAAC,CAAC,eAAe,EAAE2B,GAAG,CAAC,CAAC,EAAEpC;AACtE;AAGO,MAAMtD,aAAa;IACxByG,cAAc,IAAMnH,IAAIsE,GAAG,CAAC;IAC5B8C,cAAc,IAAMpH,IAAIsE,GAAG,CAAC;IAC5B+C,gBAAgB,CAACrD,OAAchE,IAAI8D,IAAI,CAAC,cAAcE;IACtDsD,UAAU,CAAClB,KAAepG,IAAI0E,KAAK,CAAC,CAAC,UAAU,EAAE0B,GAAG,WAAW,CAAC;IAChEmB,mBAAmB,CAACvD,OAAchE,IAAI8D,IAAI,CAAC,gCAAgCE;IAC3EwD,oBAAoB,CAACX,YAAoBY,aACvCzH,IAAIsE,GAAG,CAAC,CAAC,0CAA0C,EAAEuC,WAAW,WAAW,EAAEY,YAAY;AAC7F;AAGO,MAAM9G,iBAAiB;IAC5B+G,cAAc,IAAM1H,IAAIsE,GAAG,CAAC;IAC5BqD,gBAAgB,IAAM3H,IAAIsE,GAAG,CAAC;IAC9BlD,QAAQ,CAAC4C,OAAchE,IAAI8D,IAAI,CAAC,yBAAyBE;IACzDsC,QAAQ,CAACF,IAAYpC,OAAchE,IAAIyE,GAAG,CAAC,CAAC,qBAAqB,EAAE2B,GAAG,CAAC,CAAC,EAAEpC;AAC5E;AAGO,MAAMpD,cAAc;IACzBgH,YAAY,IAAM5H,IAAIsE,GAAG,CAAC;IAC1BuD,gBAAgB,IAAM7H,IAAIsE,GAAG,CAAC;IAC9BwD,gBAAgB,CAAC9D,OAAchE,IAAI8D,IAAI,CAAC,wBAAwBE;IAChE+D,gBAAgB,CAAC3B,IAAYpC,OAAchE,IAAIyE,GAAG,CAAC,CAAC,oBAAoB,EAAE2B,GAAG,CAAC,CAAC,EAAEpC;AACnF;AAGO,MAAMxD,SAAS;IACpBwH,aAAa,IAAMhI,IAAIsE,GAAG,CAAC;IAC3B2D,eAAe,IAAMjI,IAAIsE,GAAG,CAAC;IAC7B4D,eAAe,CAAClE,OAAchE,IAAI8D,IAAI,CAAC,kBAAkBE;IACzDiD,OAAO,CAACjD,OAAchE,IAAI8D,IAAI,CAAC,gBAAgBE;AACjD;AAGO,MAAM1D,cAAc;IACzB6H,gBAAgB,IAAMnI,IAAIsE,GAAG,CAAC;IAC9B8D,iBAAiB,IAAMpI,IAAIsE,GAAG,CAAC;IAC/B+D,qBAAqB,IAAMrI,IAAIsE,GAAG,CAAC;IACnCgE,QAAQ,CAACzD,MAAYC,aACnB9E,IAAI4E,UAAU,CAAC,wBAAwBC,MAAMC;AACjD;AAGO,MAAM1E,aAAa;IACxB6F,QAAQ,IAAMjG,IAAIsE,GAAG,CAAC;IACtBlD,QAAQ,CAAC4C,OAAchE,IAAI8D,IAAI,CAAC,cAAcE;IAC9CsC,QAAQ,CAACF,IAAYpC,OAAchE,IAAIyE,GAAG,CAAC,CAAC,UAAU,EAAE2B,GAAG,CAAC,CAAC,EAAEpC;IAC/DW,QAAQ,CAACyB,KAAepG,IAAI2E,MAAM,CAAC,CAAC,UAAU,EAAEyB,GAAG,CAAC,CAAC;AACvD"}