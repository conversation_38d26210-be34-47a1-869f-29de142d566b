{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/app/(auth)/(staff)/staff/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Calendar,\n  Clock,\n  User,\n  FileText,\n  TrendingUp,\n  CheckCircle,\n  AlertCircle,\n  Bell,\n  Coffee,\n  Target,\n  Award,\n  Activity,\n  Users\n} from 'lucide-react';\nimport Card from '@/components/ui/Card';\nimport { useAuth } from '@/providers/AuthProvider';\nimport { cn, formatDate, formatCurrency } from '@/lib/utils';\n\nconst StaffDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  const todayStats = {\n    checkInTime: '08:30 AM',\n    hoursWorked: '7.5',\n    tasksCompleted: 4,\n    meetingsToday: 2,\n    leaveBalance: 15,\n    pendingApprovals: 1\n  };\n\n  const recentActivities = [\n    {\n      id: 1,\n      type: 'check_in',\n      description: 'Checked in for the day',\n      time: '08:30 AM',\n      icon: Clock,\n      color: 'text-green-500'\n    },\n    {\n      id: 2,\n      type: 'task',\n      description: 'Completed project review',\n      time: '10:15 AM',\n      icon: CheckCircle,\n      color: 'text-blue-500'\n    },\n    {\n      id: 3,\n      type: 'meeting',\n      description: 'Team standup meeting',\n      time: '11:00 AM',\n      icon: Users,\n      color: 'text-purple-500'\n    },\n    {\n      id: 4,\n      type: 'document',\n      description: 'Updated timesheet',\n      time: '02:30 PM',\n      icon: FileText,\n      color: 'text-orange-500'\n    }\n  ];\n\n  const upcomingEvents = [\n    {\n      id: 1,\n      title: 'Project Review Meeting',\n      time: '3:00 PM',\n      type: 'meeting',\n      participants: 5\n    },\n    {\n      id: 2,\n      title: 'Training Session: React Best Practices',\n      time: 'Tomorrow 10:00 AM',\n      type: 'training',\n      duration: '2 hours'\n    },\n    {\n      id: 3,\n      title: 'Performance Review',\n      time: 'Friday 2:00 PM',\n      type: 'review',\n      with: 'Manager'\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Header */}\n      <div className=\"bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold\">\n              Good {currentTime.getHours() < 12 ? 'Morning' : currentTime.getHours() < 18 ? 'Afternoon' : 'Evening'}, {user?.first_name}!\n            </h1>\n            <p className=\"text-orange-100 mt-1\">\n              {formatDate(new Date().toISOString(), 'EEEE, MMMM dd, yyyy')}\n            </p>\n          </div>\n          <div className=\"text-right\">\n            <div className=\"text-3xl font-bold\">\n              {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n            </div>\n            <div className=\"text-orange-100 text-sm\">\n              Current Time\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Today's Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <Card>\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <Clock className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Check-in Time</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{todayStats.checkInTime}</p>\n              </div>\n            </div>\n          </div>\n        </Card>\n\n        <Card>\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <Activity className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Hours Worked</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{todayStats.hoursWorked}h</p>\n              </div>\n            </div>\n          </div>\n        </Card>\n\n        <Card>\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <Target className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Tasks Completed</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{todayStats.tasksCompleted}</p>\n              </div>\n            </div>\n          </div>\n        </Card>\n\n        <Card>\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-orange-100 rounded-lg\">\n                <Calendar className=\"h-6 w-6 text-orange-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Leave Balance</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{todayStats.leaveBalance} days</p>\n              </div>\n            </div>\n          </div>\n        </Card>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Recent Activities */}\n        <Card>\n          <div className=\"p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Today's Activities</h3>\n            <div className=\"space-y-4\">\n              {recentActivities.map((activity) => {\n                const Icon = activity.icon;\n                return (\n                  <div key={activity.id} className=\"flex items-center space-x-3\">\n                    <div className={cn('p-2 rounded-full bg-gray-100', activity.color)}>\n                      <Icon className=\"h-4 w-4\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"text-sm font-medium text-gray-900\">{activity.description}</p>\n                      <p className=\"text-xs text-gray-500\">{activity.time}</p>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        </Card>\n\n        {/* Upcoming Events */}\n        <Card>\n          <div className=\"p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Upcoming Events</h3>\n            <div className=\"space-y-4\">\n              {upcomingEvents.map((event) => (\n                <div key={event.id} className=\"border-l-4 border-orange-500 pl-4\">\n                  <h4 className=\"text-sm font-medium text-gray-900\">{event.title}</h4>\n                  <p className=\"text-xs text-gray-500 mt-1\">{event.time}</p>\n                  {event.participants && (\n                    <p className=\"text-xs text-gray-500\">{event.participants} participants</p>\n                  )}\n                  {event.duration && (\n                    <p className=\"text-xs text-gray-500\">Duration: {event.duration}</p>\n                  )}\n                  {event.with && (\n                    <p className=\"text-xs text-gray-500\">With: {event.with}</p>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n        </Card>\n      </div>\n\n      {/* Quick Actions */}\n      <Card>\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Quick Actions</h3>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center\">\n              <FileText className=\"h-6 w-6 text-orange-500 mx-auto mb-2\" />\n              <span className=\"text-sm font-medium text-gray-900\">Apply Leave</span>\n            </button>\n            <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center\">\n              <Clock className=\"h-6 w-6 text-blue-500 mx-auto mb-2\" />\n              <span className=\"text-sm font-medium text-gray-900\">Check Out</span>\n            </button>\n            <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center\">\n              <User className=\"h-6 w-6 text-green-500 mx-auto mb-2\" />\n              <span className=\"text-sm font-medium text-gray-900\">Update Profile</span>\n            </button>\n            <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center\">\n              <TrendingUp className=\"h-6 w-6 text-purple-500 mx-auto mb-2\" />\n              <span className=\"text-sm font-medium text-gray-900\">View Performance</span>\n            </button>\n          </div>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default StaffDashboard;\n"], "names": ["StaffDashboard", "user", "useAuth", "currentTime", "setCurrentTime", "useState", "Date", "useEffect", "timer", "setInterval", "clearInterval", "todayStats", "checkInTime", "hoursWorked", "tasksCompleted", "meetingsToday", "leaveBalance", "pendingApprovals", "recentActivities", "id", "type", "description", "time", "icon", "Clock", "color", "CheckCircle", "Users", "FileText", "upcomingEvents", "title", "participants", "duration", "with", "div", "className", "h1", "getHours", "first_name", "p", "formatDate", "toISOString", "toLocaleTimeString", "hour", "minute", "Card", "Activity", "Target", "Calendar", "h3", "map", "activity", "Icon", "cn", "event", "h4", "button", "span", "User", "TrendingUp"], "mappings": "AAAA;;;;;+BAsQA;;;eAAA;;;;+DApQ2C;6BAepC;6DACU;8BACO;uBACuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE/C,MAAMA,iBAA2B;IAC/B,MAAM,EAAEC,IAAI,EAAE,GAAGC,IAAAA,qBAAO;IACxB,MAAM,CAACC,aAAaC,eAAe,GAAGC,IAAAA,eAAQ,EAAC,IAAIC;IAEnDC,IAAAA,gBAAS,EAAC;QACR,MAAMC,QAAQC,YAAY;YACxBL,eAAe,IAAIE;QACrB,GAAG;QAEH,OAAO,IAAMI,cAAcF;IAC7B,GAAG,EAAE;IAEL,MAAMG,aAAa;QACjBC,aAAa;QACbC,aAAa;QACbC,gBAAgB;QAChBC,eAAe;QACfC,cAAc;QACdC,kBAAkB;IACpB;IAEA,MAAMC,mBAAmB;QACvB;YACEC,IAAI;YACJC,MAAM;YACNC,aAAa;YACbC,MAAM;YACNC,MAAMC,kBAAK;YACXC,OAAO;QACT;QACA;YACEN,IAAI;YACJC,MAAM;YACNC,aAAa;YACbC,MAAM;YACNC,MAAMG,wBAAW;YACjBD,OAAO;QACT;QACA;YACEN,IAAI;YACJC,MAAM;YACNC,aAAa;YACbC,MAAM;YACNC,MAAMI,kBAAK;YACXF,OAAO;QACT;QACA;YACEN,IAAI;YACJC,MAAM;YACNC,aAAa;YACbC,MAAM;YACNC,MAAMK,qBAAQ;YACdH,OAAO;QACT;KACD;IAED,MAAMI,iBAAiB;QACrB;YACEV,IAAI;YACJW,OAAO;YACPR,MAAM;YACNF,MAAM;YACNW,cAAc;QAChB;QACA;YACEZ,IAAI;YACJW,OAAO;YACPR,MAAM;YACNF,MAAM;YACNY,UAAU;QACZ;QACA;YACEb,IAAI;YACJW,OAAO;YACPR,MAAM;YACNF,MAAM;YACNa,MAAM;QACR;KACD;IAED,qBACE,sBAACC;QAAIC,WAAU;;0BAEb,qBAACD;gBAAIC,WAAU;0BACb,cAAA,sBAACD;oBAAIC,WAAU;;sCACb,sBAACD;;8CACC,sBAACE;oCAAGD,WAAU;;wCAAqB;wCAC3BhC,YAAYkC,QAAQ,KAAK,KAAK,YAAYlC,YAAYkC,QAAQ,KAAK,KAAK,cAAc;wCAAU;wCAAGpC,MAAMqC;wCAAW;;;8CAE5H,qBAACC;oCAAEJ,WAAU;8CACVK,IAAAA,iBAAU,EAAC,IAAIlC,OAAOmC,WAAW,IAAI;;;;sCAG1C,sBAACP;4BAAIC,WAAU;;8CACb,qBAACD;oCAAIC,WAAU;8CACZhC,YAAYuC,kBAAkB,CAAC,EAAE,EAAE;wCAAEC,MAAM;wCAAWC,QAAQ;oCAAU;;8CAE3E,qBAACV;oCAAIC,WAAU;8CAA0B;;;;;;;0BAQ/C,sBAACD;gBAAIC,WAAU;;kCACb,qBAACU,aAAI;kCACH,cAAA,qBAACX;4BAAIC,WAAU;sCACb,cAAA,sBAACD;gCAAIC,WAAU;;kDACb,qBAACD;wCAAIC,WAAU;kDACb,cAAA,qBAACX,kBAAK;4CAACW,WAAU;;;kDAEnB,sBAACD;wCAAIC,WAAU;;0DACb,qBAACI;gDAAEJ,WAAU;0DAAoC;;0DACjD,qBAACI;gDAAEJ,WAAU;0DAAoCxB,WAAWC,WAAW;;;;;;;;kCAM/E,qBAACiC,aAAI;kCACH,cAAA,qBAACX;4BAAIC,WAAU;sCACb,cAAA,sBAACD;gCAAIC,WAAU;;kDACb,qBAACD;wCAAIC,WAAU;kDACb,cAAA,qBAACW,qBAAQ;4CAACX,WAAU;;;kDAEtB,sBAACD;wCAAIC,WAAU;;0DACb,qBAACI;gDAAEJ,WAAU;0DAAoC;;0DACjD,sBAACI;gDAAEJ,WAAU;;oDAAoCxB,WAAWE,WAAW;oDAAC;;;;;;;;;kCAMhF,qBAACgC,aAAI;kCACH,cAAA,qBAACX;4BAAIC,WAAU;sCACb,cAAA,sBAACD;gCAAIC,WAAU;;kDACb,qBAACD;wCAAIC,WAAU;kDACb,cAAA,qBAACY,mBAAM;4CAACZ,WAAU;;;kDAEpB,sBAACD;wCAAIC,WAAU;;0DACb,qBAACI;gDAAEJ,WAAU;0DAAoC;;0DACjD,qBAACI;gDAAEJ,WAAU;0DAAoCxB,WAAWG,cAAc;;;;;;;;kCAMlF,qBAAC+B,aAAI;kCACH,cAAA,qBAACX;4BAAIC,WAAU;sCACb,cAAA,sBAACD;gCAAIC,WAAU;;kDACb,qBAACD;wCAAIC,WAAU;kDACb,cAAA,qBAACa,qBAAQ;4CAACb,WAAU;;;kDAEtB,sBAACD;wCAAIC,WAAU;;0DACb,qBAACI;gDAAEJ,WAAU;0DAAoC;;0DACjD,sBAACI;gDAAEJ,WAAU;;oDAAoCxB,WAAWK,YAAY;oDAAC;;;;;;;;;;;0BAOnF,sBAACkB;gBAAIC,WAAU;;kCAEb,qBAACU,aAAI;kCACH,cAAA,sBAACX;4BAAIC,WAAU;;8CACb,qBAACc;oCAAGd,WAAU;8CAAyC;;8CACvD,qBAACD;oCAAIC,WAAU;8CACZjB,iBAAiBgC,GAAG,CAAC,CAACC;wCACrB,MAAMC,OAAOD,SAAS5B,IAAI;wCAC1B,qBACE,sBAACW;4CAAsBC,WAAU;;8DAC/B,qBAACD;oDAAIC,WAAWkB,IAAAA,SAAE,EAAC,gCAAgCF,SAAS1B,KAAK;8DAC/D,cAAA,qBAAC2B;wDAAKjB,WAAU;;;8DAElB,sBAACD;oDAAIC,WAAU;;sEACb,qBAACI;4DAAEJ,WAAU;sEAAqCgB,SAAS9B,WAAW;;sEACtE,qBAACkB;4DAAEJ,WAAU;sEAAyBgB,SAAS7B,IAAI;;;;;2CAN7C6B,SAAShC,EAAE;oCAUzB;;;;;kCAMN,qBAAC0B,aAAI;kCACH,cAAA,sBAACX;4BAAIC,WAAU;;8CACb,qBAACc;oCAAGd,WAAU;8CAAyC;;8CACvD,qBAACD;oCAAIC,WAAU;8CACZN,eAAeqB,GAAG,CAAC,CAACI,sBACnB,sBAACpB;4CAAmBC,WAAU;;8DAC5B,qBAACoB;oDAAGpB,WAAU;8DAAqCmB,MAAMxB,KAAK;;8DAC9D,qBAACS;oDAAEJ,WAAU;8DAA8BmB,MAAMhC,IAAI;;gDACpDgC,MAAMvB,YAAY,kBACjB,sBAACQ;oDAAEJ,WAAU;;wDAAyBmB,MAAMvB,YAAY;wDAAC;;;gDAE1DuB,MAAMtB,QAAQ,kBACb,sBAACO;oDAAEJ,WAAU;;wDAAwB;wDAAWmB,MAAMtB,QAAQ;;;gDAE/DsB,MAAMrB,IAAI,kBACT,sBAACM;oDAAEJ,WAAU;;wDAAwB;wDAAOmB,MAAMrB,IAAI;;;;2CAVhDqB,MAAMnC,EAAE;;;;;;;0BAoB5B,qBAAC0B,aAAI;0BACH,cAAA,sBAACX;oBAAIC,WAAU;;sCACb,qBAACc;4BAAGd,WAAU;sCAAyC;;sCACvD,sBAACD;4BAAIC,WAAU;;8CACb,sBAACqB;oCAAOrB,WAAU;;sDAChB,qBAACP,qBAAQ;4CAACO,WAAU;;sDACpB,qBAACsB;4CAAKtB,WAAU;sDAAoC;;;;8CAEtD,sBAACqB;oCAAOrB,WAAU;;sDAChB,qBAACX,kBAAK;4CAACW,WAAU;;sDACjB,qBAACsB;4CAAKtB,WAAU;sDAAoC;;;;8CAEtD,sBAACqB;oCAAOrB,WAAU;;sDAChB,qBAACuB,iBAAI;4CAACvB,WAAU;;sDAChB,qBAACsB;4CAAKtB,WAAU;sDAAoC;;;;8CAEtD,sBAACqB;oCAAOrB,WAAU;;sDAChB,qBAACwB,uBAAU;4CAACxB,WAAU;;sDACtB,qBAACsB;4CAAKtB,WAAU;sDAAoC;;;;;;;;;;;AAOlE;MAEA,WAAenC"}