9a7c0e997fa2e34dfd64d99c28f1c31c
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _lucidereact = require("lucide-react");
const _utils = require("../../lib/utils");
const _fileUpload = /*#__PURE__*/ _interop_require_default(require("../../lib/fileUpload"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const FileUpload = ({ onUpload, onProgress, multiple = false, accept, maxSize, maxFiles = 10, disabled = false, className, children, endpoint, folder })=>{
    const [files, setFiles] = (0, _react.useState)([]);
    const [isDragOver, setIsDragOver] = (0, _react.useState)(false);
    const [isUploading, setIsUploading] = (0, _react.useState)(false);
    const fileInputRef = (0, _react.useRef)(null);
    const handleFileSelect = (0, _react.useCallback)((selectedFiles)=>{
        if (!selectedFiles) return;
        const newFiles = Array.from(selectedFiles).map((file)=>{
            const fileWithPreview = file;
            if (_fileUpload.default.isImage(file)) {
                fileWithPreview.preview = _fileUpload.default.createPreviewUrl(file);
            }
            return fileWithPreview;
        });
        if (multiple) {
            setFiles((prev)=>[
                    ...prev,
                    ...newFiles
                ].slice(0, maxFiles));
        } else {
            // Clean up previous preview URLs
            files.forEach((file)=>{
                if (file.preview) {
                    _fileUpload.default.revokePreviewUrl(file.preview);
                }
            });
            setFiles(newFiles.slice(0, 1));
        }
    }, [
        files,
        multiple,
        maxFiles
    ]);
    const handleDrop = (0, _react.useCallback)((e)=>{
        e.preventDefault();
        setIsDragOver(false);
        if (disabled) return;
        handleFileSelect(e.dataTransfer.files);
    }, [
        disabled,
        handleFileSelect
    ]);
    const handleDragOver = (0, _react.useCallback)((e)=>{
        e.preventDefault();
        if (!disabled) {
            setIsDragOver(true);
        }
    }, [
        disabled
    ]);
    const handleDragLeave = (0, _react.useCallback)((e)=>{
        e.preventDefault();
        setIsDragOver(false);
    }, []);
    const handleClick = (0, _react.useCallback)(()=>{
        if (!disabled && fileInputRef.current) {
            fileInputRef.current.click();
        }
    }, [
        disabled
    ]);
    const removeFile = (0, _react.useCallback)((index)=>{
        setFiles((prev)=>{
            const newFiles = [
                ...prev
            ];
            const removedFile = newFiles[index];
            // Clean up preview URL
            if (removedFile.preview) {
                _fileUpload.default.revokePreviewUrl(removedFile.preview);
            }
            newFiles.splice(index, 1);
            return newFiles;
        });
    }, []);
    const uploadFiles = (0, _react.useCallback)(async ()=>{
        if (files.length === 0 || isUploading) return;
        setIsUploading(true);
        const results = [];
        try {
            for(let i = 0; i < files.length; i++){
                const file = files[i];
                // Update file state to show uploading
                setFiles((prev)=>prev.map((f, idx)=>idx === i ? {
                            ...f,
                            uploading: true,
                            progress: 0
                        } : f));
                const result = await _fileUpload.default.uploadFile(file, endpoint, {
                    folder,
                    maxSize,
                    onProgress: (progress)=>{
                        // Update individual file progress
                        setFiles((prev)=>prev.map((f, idx)=>idx === i ? {
                                    ...f,
                                    progress: progress.percentage
                                } : f));
                        // Calculate overall progress
                        if (onProgress) {
                            const overallProgress = {
                                loaded: i * 100 + progress.percentage,
                                total: files.length * 100,
                                percentage: Math.round((i * 100 + progress.percentage) / files.length)
                            };
                            onProgress(overallProgress);
                        }
                    }
                });
                // Update file state with result
                setFiles((prev)=>prev.map((f, idx)=>idx === i ? {
                            ...f,
                            uploading: false,
                            uploadResult: result
                        } : f));
                results.push(result);
            }
            if (onUpload) {
                onUpload(results);
            }
        } catch (error) {
            console.error('Upload error:', error);
        } finally{
            setIsUploading(false);
        }
    }, [
        files,
        isUploading,
        endpoint,
        folder,
        maxSize,
        onProgress,
        onUpload
    ]);
    const getFileIcon = (file)=>{
        if (_fileUpload.default.isImage(file)) {
            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Image, {
                className: "h-8 w-8 text-blue-500"
            });
        } else if (_fileUpload.default.isPDF(file)) {
            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.FileText, {
                className: "h-8 w-8 text-red-500"
            });
        } else {
            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.File, {
                className: "h-8 w-8 text-gray-500"
            });
        }
    };
    const getUploadStatus = (file)=>{
        if (file.uploading) {
            return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "flex items-center space-x-2",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Loader2, {
                        className: "h-4 w-4 animate-spin text-blue-500"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                        className: "text-sm text-blue-600",
                        children: [
                            file.progress || 0,
                            "%"
                        ]
                    })
                ]
            });
        } else if (file.uploadResult) {
            if (file.uploadResult.success) {
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                    className: "h-4 w-4 text-green-500"
                });
            } else {
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertCircle, {
                    className: "h-4 w-4 text-red-500"
                });
            }
        }
        return null;
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: (0, _utils.cn)('w-full', className),
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                onDrop: handleDrop,
                onDragOver: handleDragOver,
                onDragLeave: handleDragLeave,
                onClick: handleClick,
                className: (0, _utils.cn)('border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors', isDragOver ? 'border-orange-500 bg-orange-50' : 'border-gray-300 hover:border-gray-400', disabled && 'opacity-50 cursor-not-allowed'),
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("input", {
                        ref: fileInputRef,
                        type: "file",
                        multiple: multiple,
                        accept: accept,
                        onChange: (e)=>handleFileSelect(e.target.files),
                        className: "hidden",
                        disabled: disabled
                    }),
                    children || /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Upload, {
                                className: "h-12 w-12 text-gray-400 mx-auto"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                        className: "text-sm font-medium text-gray-900",
                                        children: "Click to upload or drag and drop"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                        className: "text-xs text-gray-500",
                                        children: accept ? `Accepted formats: ${accept}` : 'All file types accepted'
                                    }),
                                    maxSize && /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                        className: "text-xs text-gray-500",
                                        children: [
                                            "Max size: ",
                                            _fileUpload.default.formatFileSize(maxSize)
                                        ]
                                    })
                                ]
                            })
                        ]
                    })
                ]
            }),
            files.length > 0 && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "mt-4 space-y-2",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("h4", {
                                className: "text-sm font-medium text-gray-900",
                                children: [
                                    "Selected Files (",
                                    files.length,
                                    ")"
                                ]
                            }),
                            files.length > 0 && !isUploading && /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                onClick: uploadFiles,
                                className: "px-3 py-1 bg-orange-500 text-white text-sm rounded-md hover:bg-orange-600 transition-colors",
                                children: "Upload All"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "space-y-2",
                        children: files.map((file, index)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center space-x-3 p-3 border border-gray-200 rounded-lg",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "flex-shrink-0",
                                        children: file.preview ? /*#__PURE__*/ (0, _jsxruntime.jsx)("img", {
                                            src: file.preview,
                                            alt: file.name,
                                            className: "h-12 w-12 object-cover rounded"
                                        }) : getFileIcon(file)
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "flex-1 min-w-0",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-900 truncate",
                                                children: file.name
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-xs text-gray-500",
                                                children: _fileUpload.default.formatFileSize(file.size)
                                            }),
                                            file.uploadResult && !file.uploadResult.success && /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-xs text-red-500 mt-1",
                                                children: file.uploadResult.error
                                            })
                                        ]
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "flex items-center space-x-2",
                                        children: [
                                            getUploadStatus(file),
                                            !file.uploading && /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                                onClick: ()=>removeFile(index),
                                                className: "p-1 text-gray-400 hover:text-red-500 transition-colors",
                                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.X, {
                                                    className: "h-4 w-4"
                                                })
                                            })
                                        ]
                                    })
                                ]
                            }, `${file.name}-${index}`))
                    })
                ]
            })
        ]
    });
};
const _default = FileUpload;

//# sourceMappingURL=data:application/json;base64,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