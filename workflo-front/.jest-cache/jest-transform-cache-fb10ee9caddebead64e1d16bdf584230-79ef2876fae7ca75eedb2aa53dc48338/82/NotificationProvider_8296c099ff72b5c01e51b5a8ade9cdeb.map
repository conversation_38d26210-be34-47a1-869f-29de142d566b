{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/providers/NotificationProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState, useCallback } from 'react';\nimport { useAuth } from './AuthProvider';\nimport webSocketService, { NotificationData } from '@/lib/websocket';\n\ninterface NotificationContextType {\n  notifications: NotificationData[];\n  unreadCount: number;\n  isConnected: boolean;\n  connectionState: string;\n  markAsRead: (notificationId: string) => void;\n  markAllAsRead: () => void;\n  clearNotifications: () => void;\n  requestPermission: () => Promise<NotificationPermission>;\n}\n\nconst NotificationContext = createContext<NotificationContextType | null>(null);\n\nexport const useNotifications = () => {\n  const context = useContext(NotificationContext);\n  if (!context) {\n    throw new Error('useNotifications must be used within a NotificationProvider');\n  }\n  return context;\n};\n\ninterface NotificationProviderProps {\n  children: React.ReactNode;\n}\n\nexport const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {\n  const { isAuthenticated, user } = useAuth();\n  const [notifications, setNotifications] = useState<NotificationData[]>([]);\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionState, setConnectionState] = useState('disconnected');\n\n  // Update notifications from WebSocket service\n  const updateNotifications = useCallback(() => {\n    setNotifications(webSocketService.getNotifications());\n  }, []);\n\n  // Update connection state\n  const updateConnectionState = useCallback(() => {\n    setIsConnected(webSocketService.isConnected());\n    setConnectionState(webSocketService.getConnectionState());\n  }, []);\n\n  // WebSocket event handlers\n  useEffect(() => {\n    if (!isAuthenticated) return;\n\n    const handleNotification = (notification: NotificationData) => {\n      updateNotifications();\n      \n      // Show toast notification (you can integrate with a toast library here)\n      console.log('New notification:', notification);\n    };\n\n    const handleConnected = () => {\n      updateConnectionState();\n      console.log('WebSocket connected');\n    };\n\n    const handleDisconnected = () => {\n      updateConnectionState();\n      console.log('WebSocket disconnected');\n    };\n\n    const handleError = (error: any) => {\n      updateConnectionState();\n      console.error('WebSocket error:', error);\n    };\n\n    const handleMaxReconnectAttempts = () => {\n      console.error('WebSocket max reconnection attempts reached');\n      // You could show a user notification here\n    };\n\n    // Register event handlers\n    webSocketService.on('notification', handleNotification);\n    webSocketService.on('connected', handleConnected);\n    webSocketService.on('disconnected', handleDisconnected);\n    webSocketService.on('error', handleError);\n    webSocketService.on('maxReconnectAttemptsReached', handleMaxReconnectAttempts);\n\n    // Connect WebSocket\n    webSocketService.connect();\n\n    // Initial state update\n    updateNotifications();\n    updateConnectionState();\n\n    // Cleanup\n    return () => {\n      webSocketService.off('notification', handleNotification);\n      webSocketService.off('connected', handleConnected);\n      webSocketService.off('disconnected', handleDisconnected);\n      webSocketService.off('error', handleError);\n      webSocketService.off('maxReconnectAttemptsReached', handleMaxReconnectAttempts);\n    };\n  }, [isAuthenticated, updateNotifications, updateConnectionState]);\n\n  // Disconnect when user logs out\n  useEffect(() => {\n    if (!isAuthenticated) {\n      webSocketService.disconnect();\n      setNotifications([]);\n      setIsConnected(false);\n      setConnectionState('disconnected');\n    }\n  }, [isAuthenticated]);\n\n  // Mark notification as read\n  const markAsRead = useCallback((notificationId: string) => {\n    webSocketService.markNotificationAsRead(notificationId);\n    updateNotifications();\n  }, [updateNotifications]);\n\n  // Mark all notifications as read\n  const markAllAsRead = useCallback(() => {\n    webSocketService.markAllNotificationsAsRead();\n    updateNotifications();\n  }, [updateNotifications]);\n\n  // Clear all notifications\n  const clearNotifications = useCallback(() => {\n    webSocketService.clearNotifications();\n    updateNotifications();\n  }, [updateNotifications]);\n\n  // Request browser notification permission\n  const requestPermission = useCallback(async (): Promise<NotificationPermission> => {\n    return await webSocketService.constructor.requestNotificationPermission();\n  }, []);\n\n  // Calculate unread count\n  const unreadCount = notifications.filter(n => !n.read).length;\n\n  const value: NotificationContextType = {\n    notifications,\n    unreadCount,\n    isConnected,\n    connectionState,\n    markAsRead,\n    markAllAsRead,\n    clearNotifications,\n    requestPermission\n  };\n\n  return (\n    <NotificationContext.Provider value={value}>\n      {children}\n    </NotificationContext.Provider>\n  );\n};\n\nexport default NotificationProvider;\n"], "names": ["NotificationProvider", "useNotifications", "NotificationContext", "createContext", "context", "useContext", "Error", "children", "isAuthenticated", "user", "useAuth", "notifications", "setNotifications", "useState", "isConnected", "setIsConnected", "connectionState", "setConnectionState", "updateNotifications", "useCallback", "webSocketService", "getNotifications", "updateConnectionState", "getConnectionState", "useEffect", "handleNotification", "notification", "console", "log", "handleConnected", "handleDisconnected", "handleError", "error", "handleMaxReconnectAttempts", "on", "connect", "off", "disconnect", "mark<PERSON><PERSON><PERSON>", "notificationId", "markNotificationAsRead", "markAllAsRead", "markAllNotificationsAsRead", "clearNotifications", "requestPermission", "constructor", "requestNotificationPermission", "unreadCount", "filter", "n", "read", "length", "value", "Provider"], "mappings": "AAAA;;;;;;;;;;;;IA+Ba<PERSON>,oBAAoB;eAApBA;;IA8Hb,OAAoC;eAApC;;IA1IaC,gBAAgB;eAAhBA;;;;+DAjBsE;8BAC3D;kEAC2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAanD,MAAMC,oCAAsBC,IAAAA,oBAAa,EAAiC;AAEnE,MAAMF,mBAAmB;IAC9B,MAAMG,UAAUC,IAAAA,iBAAU,EAACH;IAC3B,IAAI,CAACE,SAAS;QACZ,MAAM,IAAIE,MAAM;IAClB;IACA,OAAOF;AACT;AAMO,MAAMJ,uBAA4D,CAAC,EAAEO,QAAQ,EAAE;IACpF,MAAM,EAAEC,eAAe,EAAEC,IAAI,EAAE,GAAGC,IAAAA,qBAAO;IACzC,MAAM,CAACC,eAAeC,iBAAiB,GAAGC,IAAAA,eAAQ,EAAqB,EAAE;IACzE,MAAM,CAACC,aAAaC,eAAe,GAAGF,IAAAA,eAAQ,EAAC;IAC/C,MAAM,CAACG,iBAAiBC,mBAAmB,GAAGJ,IAAAA,eAAQ,EAAC;IAEvD,8CAA8C;IAC9C,MAAMK,sBAAsBC,IAAAA,kBAAW,EAAC;QACtCP,iBAAiBQ,kBAAgB,CAACC,gBAAgB;IACpD,GAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAMC,wBAAwBH,IAAAA,kBAAW,EAAC;QACxCJ,eAAeK,kBAAgB,CAACN,WAAW;QAC3CG,mBAAmBG,kBAAgB,CAACG,kBAAkB;IACxD,GAAG,EAAE;IAEL,2BAA2B;IAC3BC,IAAAA,gBAAS,EAAC;QACR,IAAI,CAAChB,iBAAiB;QAEtB,MAAMiB,qBAAqB,CAACC;YAC1BR;YAEA,wEAAwE;YACxES,QAAQC,GAAG,CAAC,qBAAqBF;QACnC;QAEA,MAAMG,kBAAkB;YACtBP;YACAK,QAAQC,GAAG,CAAC;QACd;QAEA,MAAME,qBAAqB;YACzBR;YACAK,QAAQC,GAAG,CAAC;QACd;QAEA,MAAMG,cAAc,CAACC;YACnBV;YACAK,QAAQK,KAAK,CAAC,oBAAoBA;QACpC;QAEA,MAAMC,6BAA6B;YACjCN,QAAQK,KAAK,CAAC;QACd,0CAA0C;QAC5C;QAEA,0BAA0B;QAC1BZ,kBAAgB,CAACc,EAAE,CAAC,gBAAgBT;QACpCL,kBAAgB,CAACc,EAAE,CAAC,aAAaL;QACjCT,kBAAgB,CAACc,EAAE,CAAC,gBAAgBJ;QACpCV,kBAAgB,CAACc,EAAE,CAAC,SAASH;QAC7BX,kBAAgB,CAACc,EAAE,CAAC,+BAA+BD;QAEnD,oBAAoB;QACpBb,kBAAgB,CAACe,OAAO;QAExB,uBAAuB;QACvBjB;QACAI;QAEA,UAAU;QACV,OAAO;YACLF,kBAAgB,CAACgB,GAAG,CAAC,gBAAgBX;YACrCL,kBAAgB,CAACgB,GAAG,CAAC,aAAaP;YAClCT,kBAAgB,CAACgB,GAAG,CAAC,gBAAgBN;YACrCV,kBAAgB,CAACgB,GAAG,CAAC,SAASL;YAC9BX,kBAAgB,CAACgB,GAAG,CAAC,+BAA+BH;QACtD;IACF,GAAG;QAACzB;QAAiBU;QAAqBI;KAAsB;IAEhE,gCAAgC;IAChCE,IAAAA,gBAAS,EAAC;QACR,IAAI,CAAChB,iBAAiB;YACpBY,kBAAgB,CAACiB,UAAU;YAC3BzB,iBAAiB,EAAE;YACnBG,eAAe;YACfE,mBAAmB;QACrB;IACF,GAAG;QAACT;KAAgB;IAEpB,4BAA4B;IAC5B,MAAM8B,aAAanB,IAAAA,kBAAW,EAAC,CAACoB;QAC9BnB,kBAAgB,CAACoB,sBAAsB,CAACD;QACxCrB;IACF,GAAG;QAACA;KAAoB;IAExB,iCAAiC;IACjC,MAAMuB,gBAAgBtB,IAAAA,kBAAW,EAAC;QAChCC,kBAAgB,CAACsB,0BAA0B;QAC3CxB;IACF,GAAG;QAACA;KAAoB;IAExB,0BAA0B;IAC1B,MAAMyB,qBAAqBxB,IAAAA,kBAAW,EAAC;QACrCC,kBAAgB,CAACuB,kBAAkB;QACnCzB;IACF,GAAG;QAACA;KAAoB;IAExB,0CAA0C;IAC1C,MAAM0B,oBAAoBzB,IAAAA,kBAAW,EAAC;QACpC,OAAO,MAAMC,kBAAgB,CAACyB,WAAW,CAACC,6BAA6B;IACzE,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAMC,cAAcpC,cAAcqC,MAAM,CAACC,CAAAA,IAAK,CAACA,EAAEC,IAAI,EAAEC,MAAM;IAE7D,MAAMC,QAAiC;QACrCzC;QACAoC;QACAjC;QACAE;QACAsB;QACAG;QACAE;QACAC;IACF;IAEA,qBACE,qBAAC1C,oBAAoBmD,QAAQ;QAACD,OAAOA;kBAClC7C;;AAGP;MAEA,WAAeP"}