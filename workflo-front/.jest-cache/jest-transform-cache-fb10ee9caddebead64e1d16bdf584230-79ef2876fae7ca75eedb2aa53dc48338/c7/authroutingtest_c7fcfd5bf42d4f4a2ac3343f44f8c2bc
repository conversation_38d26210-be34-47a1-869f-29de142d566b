2d910c688302c6670348d4f9b7611460
/**
 * Test file for role-based authentication routing
 */ "use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _globals = require("@jest/globals");
// Mock the next/navigation module
const mockPush = _globals.jest.fn();
const mockReplace = _globals.jest.fn();
_globals.jest.mock('next/navigation', ()=>({
        useRouter: ()=>({
                push: mockPush,
                replace: mockReplace,
                back: _globals.jest.fn()
            }),
        usePathname: ()=>'/login'
    }));
// Mock the auth provider
const mockLogin = _globals.jest.fn();
const mockUser = {
    id: 1,
    email: '<EMAIL>',
    role: 'hr',
    first_name: 'Test',
    last_name: 'User'
};
_globals.jest.mock('@/providers/AuthProvider', ()=>({
        useAuth: ()=>({
                login: mockLogin,
                user: mockUser,
                isAuthenticated: false,
                isLoading: false,
                error: null,
                clearError: _globals.jest.fn()
            })
    }));
(0, _globals.describe)('Role-based Authentication Routing', ()=>{
    (0, _globals.beforeEach)(()=>{
        _globals.jest.clearAllMocks();
    });
    (0, _globals.describe)('Role-based redirect paths', ()=>{
        (0, _globals.it)('should redirect admin roles to /dashboard', ()=>{
            const adminRoles = [
                'hr',
                'supervisor',
                'admin',
                'accountant'
            ];
            adminRoles.forEach((role)=>{
                const user = {
                    ...mockUser,
                    role
                };
                const getRedirectPath = (user)=>{
                    if (!user) return '/login';
                    const adminRoles = [
                        'hr',
                        'supervisor',
                        'admin',
                        'accountant'
                    ];
                    if (adminRoles.includes(user.role)) {
                        return '/dashboard';
                    }
                    if (user.role === 'employee') {
                        return '/staff';
                    }
                    return '/dashboard';
                };
                (0, _globals.expect)(getRedirectPath(user)).toBe('/dashboard');
            });
        });
        (0, _globals.it)('should redirect employee role to /staff', ()=>{
            const user = {
                ...mockUser,
                role: 'employee'
            };
            const getRedirectPath = (user)=>{
                if (!user) return '/login';
                const adminRoles = [
                    'hr',
                    'supervisor',
                    'admin',
                    'accountant'
                ];
                if (adminRoles.includes(user.role)) {
                    return '/dashboard';
                }
                if (user.role === 'employee') {
                    return '/staff';
                }
                return '/dashboard';
            };
            (0, _globals.expect)(getRedirectPath(user)).toBe('/staff');
        });
        (0, _globals.it)('should redirect to /login for null user', ()=>{
            const getRedirectPath = (user)=>{
                if (!user) return '/login';
                const adminRoles = [
                    'hr',
                    'supervisor',
                    'admin',
                    'accountant'
                ];
                if (adminRoles.includes(user.role)) {
                    return '/dashboard';
                }
                if (user.role === 'employee') {
                    return '/staff';
                }
                return '/dashboard';
            };
            (0, _globals.expect)(getRedirectPath(null)).toBe('/login');
        });
    });
    (0, _globals.describe)('Login credentials', ()=>{
        (0, _globals.it)('should have correct demo credentials', ()=>{
            const demoCredentials = [
                {
                    email: '<EMAIL>',
                    password: 'admin123',
                    role: 'hr'
                },
                {
                    email: '<EMAIL>',
                    password: 'employee123',
                    role: 'employee'
                }
            ];
            (0, _globals.expect)(demoCredentials[0].email).toBe('<EMAIL>');
            (0, _globals.expect)(demoCredentials[0].password).toBe('admin123');
            (0, _globals.expect)(demoCredentials[1].email).toBe('<EMAIL>');
            (0, _globals.expect)(demoCredentials[1].password).toBe('employee123');
        });
    });
    (0, _globals.describe)('Password visibility toggle', ()=>{
        (0, _globals.it)('should toggle password visibility state', ()=>{
            let showPassword = false;
            const togglePassword = ()=>{
                showPassword = !showPassword;
            };
            (0, _globals.expect)(showPassword).toBe(false);
            togglePassword();
            (0, _globals.expect)(showPassword).toBe(true);
            togglePassword();
            (0, _globals.expect)(showPassword).toBe(false);
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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