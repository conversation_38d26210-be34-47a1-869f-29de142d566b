b53aaa5f44e00aa38b12226d39872b5e
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AdminGuard: function() {
        return AdminGuard;
    },
    HRGuard: function() {
        return HRGuard;
    },
    StaffGuard: function() {
        return StaffGuard;
    },
    SupervisorGuard: function() {
        return SupervisorGuard;
    },
    default: function() {
        return _default;
    },
    useRoleCheck: function() {
        return useRoleCheck;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _navigation = require("next/navigation");
const _AuthProvider = require("../../providers/AuthProvider");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Helper function to check if user has required role
const hasRequiredRole = (user, allowedRoles)=>{
    if (!user) return false;
    // Admin roles that can access admin routes
    const adminRoles = [
        'hr',
        'supervisor',
        'admin'
    ];
    // If allowed roles include 'admin', check if user has any admin role
    if (allowedRoles.includes('admin')) {
        return adminRoles.includes(user.role);
    }
    // If allowed roles include 'staff', check if user is employee
    if (allowedRoles.includes('staff')) {
        return user.role === 'employee';
    }
    // Direct role check
    return allowedRoles.includes(user.role);
};
const RoleGuard = ({ children, allowedRoles, fallback, redirectTo })=>{
    const { user, isAuthenticated, isLoading } = (0, _AuthProvider.useAuth)();
    const router = (0, _navigation.useRouter)();
    // Show loading while checking authentication
    if (isLoading) {
        return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
            className: "min-h-screen flex items-center justify-center bg-gray-50",
            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                        className: "mt-4 text-gray-600 text-sm",
                        children: "Checking permissions..."
                    })
                ]
            })
        });
    }
    // Redirect to login if not authenticated
    if (!isAuthenticated) {
        if (redirectTo) {
            router.replace(redirectTo);
            return null;
        }
        return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
            className: "min-h-screen flex items-center justify-center bg-gray-50",
            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h2", {
                        className: "text-xl font-semibold text-gray-900 mb-2",
                        children: "Authentication Required"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                        className: "text-gray-600 mb-4",
                        children: "Please log in to access this page."
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                        onClick: ()=>router.push('/login'),
                        className: "px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors",
                        children: "Go to Login"
                    })
                ]
            })
        });
    }
    // Check if user has required role
    if (!hasRequiredRole(user, allowedRoles)) {
        if (fallback) {
            return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {
                children: fallback
            });
        }
        // Default unauthorized access component
        return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
            className: "min-h-screen flex items-center justify-center bg-gray-50",
            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "text-center max-w-md mx-auto p-6",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("svg", {
                            className: "w-8 h-8 text-red-600",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h2", {
                        className: "text-xl font-semibold text-gray-900 mb-2",
                        children: "Access Denied"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                        className: "text-gray-600 mb-4",
                        children: [
                            "You don't have permission to access this page. Your current role (",
                            user?.role,
                            ") is not authorized for this section."
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                onClick: ()=>router.back(),
                                className: "w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors",
                                children: "Go Back"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                onClick: ()=>{
                                    // Redirect based on user role
                                    if (hasRequiredRole(user, [
                                        'admin'
                                    ])) {
                                        router.push('/dashboard');
                                    } else if (hasRequiredRole(user, [
                                        'staff'
                                    ])) {
                                        router.push('/staff');
                                    } else {
                                        router.push('/login');
                                    }
                                },
                                className: "w-full px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors",
                                children: "Go to Dashboard"
                            })
                        ]
                    })
                ]
            })
        });
    }
    // User has required role, render children
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {
        children: children
    });
};
const _default = RoleGuard;
const AdminGuard = (props)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(RoleGuard, {
        ...props,
        allowedRoles: [
            'admin'
        ]
    });
const StaffGuard = (props)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(RoleGuard, {
        ...props,
        allowedRoles: [
            'staff'
        ]
    });
const HRGuard = (props)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(RoleGuard, {
        ...props,
        allowedRoles: [
            'hr'
        ]
    });
const SupervisorGuard = (props)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(RoleGuard, {
        ...props,
        allowedRoles: [
            'supervisor'
        ]
    });
const useRoleCheck = ()=>{
    const { user } = (0, _AuthProvider.useAuth)();
    return {
        hasRole: (roles)=>hasRequiredRole(user, roles),
        isAdmin: ()=>hasRequiredRole(user, [
                'admin'
            ]),
        isStaff: ()=>hasRequiredRole(user, [
                'staff'
            ]),
        isHR: ()=>hasRequiredRole(user, [
                'hr'
            ]),
        isSupervisor: ()=>hasRequiredRole(user, [
                'supervisor'
            ]),
        userRole: user?.role || null
    };
};

//# sourceMappingURL=data:application/json;base64,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