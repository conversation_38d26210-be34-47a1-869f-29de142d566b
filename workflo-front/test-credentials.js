// Simple test script to verify supervisor credentials
const mockUsers = [
  {
    id: 1,
    email: '<EMAIL>',
    first_name: '<PERSON><PERSON>',
    last_name: 'User',
    employee_id: 'ADMIN001',
    role: 'hr',
  },
  {
    id: 2,
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    employee_id: 'SUP001',
    role: 'supervisor',
  },
  {
    id: 3,
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    employee_id: 'EMP001',
    role: 'employee',
  },
  {
    id: 4,
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    employee_id: 'ACC001',
    role: 'accountant',
  },
];

const validCredentials = [
  { email: '<EMAIL>', password: 'admin123' },
  { email: '<EMAIL>', password: 'supervisor123' },
  { email: '<EMAIL>', password: 'employee123' },
  { email: '<EMAIL>', password: 'accountant123' },
];

function testLogin(email, password) {
  console.log(`\n🧪 Testing login for: ${email}`);
  
  const isValid = validCredentials.some(
    cred => cred.email === email && cred.password === password
  );
  
  if (isValid) {
    const user = mockUsers.find(u => u.email === email);
    console.log(`✅ Login successful!`);
    console.log(`   Name: ${user.first_name} ${user.last_name}`);
    console.log(`   Role: ${user.role}`);
    console.log(`   Employee ID: ${user.employee_id}`);
    
    // Determine redirect path
    let redirectPath;
    switch (user.role) {
      case 'supervisor':
        redirectPath = '/supervisor';
        break;
      case 'employee':
        redirectPath = '/staff';
        break;
      case 'hr':
      case 'admin':
      case 'accountant':
        redirectPath = '/dashboard';
        break;
      default:
        redirectPath = '/dashboard';
    }
    console.log(`   Redirect to: ${redirectPath}`);
  } else {
    console.log(`❌ Login failed: Invalid email or password`);
  }
}

console.log('🎯 WorkFlo Supervisor Login Credentials Test');
console.log('='.repeat(50));

// Test all credentials
testLogin('<EMAIL>', 'supervisor123');
testLogin('<EMAIL>', 'admin123');
testLogin('<EMAIL>', 'employee123');
testLogin('<EMAIL>', 'accountant123');

// Test invalid credentials
testLogin('<EMAIL>', 'wrongpassword');
testLogin('<EMAIL>', 'supervisor123');

console.log('\n✅ All tests completed!');
console.log('\n📝 Instructions:');
console.log('1. Start the dev server: npm run dev');
console.log('2. Navigate to: http://localhost:3000/login');
console.log('3. Click "🎯 Quick Test - Login as Supervisor"');
console.log('4. Should redirect to: http://localhost:3000/supervisor');
