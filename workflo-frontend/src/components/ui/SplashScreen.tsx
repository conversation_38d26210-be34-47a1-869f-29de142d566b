'use client';

import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface SplashScreenProps {
  isVisible: boolean;
  onComplete?: () => void;
  duration?: number;
}

const SplashScreen: React.FC<SplashScreenProps> = ({
  isVisible,
  onComplete,
  duration = 2000,
}) => {
  const [progress, setProgress] = useState(0);
  const [fadeOut, setFadeOut] = useState(false);

  useEffect(() => {
    if (isVisible) {
      // Reset states
      setProgress(0);
      setFadeOut(false);

      // Animate progress bar
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            return 100;
          }
          return prev + 4; // Faster progress
        });
      }, duration / 25);

      // Start fade out before completion
      const fadeTimer = setTimeout(() => {
        setFadeOut(true);
      }, duration - 500);

      // Complete splash screen
      const completeTimer = setTimeout(() => {
        onComplete?.();
      }, duration);

      return () => {
        clearTimeout(fadeTimer);
        clearTimeout(completeTimer);
        clearInterval(progressInterval);
      };
    }
  }, [isVisible, duration, onComplete]);

  if (!isVisible) return null;

  return (
    <div
      className={cn(
        'fixed inset-0 z-50 bg-gradient-to-br from-orange-500 to-orange-600 flex items-center justify-center transition-opacity duration-500',
        fadeOut ? 'opacity-0' : 'opacity-100'
      )}
    >
      <div className="text-center text-white">
        {/* Logo */}
        <div className="mb-8">
          <div className="w-24 h-24 bg-white rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-2xl">
            <span className="text-orange-500 font-bold text-4xl">W</span>
          </div>
          <h1 className="text-4xl font-bold mb-2">WorkFlow</h1>
          <p className="text-orange-100 text-lg">HR Management System</p>
        </div>

        {/* Loading Animation */}
        <div className="mb-8">
          <div className="flex justify-center space-x-2 mb-4">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={cn(
                  'w-3 h-3 bg-white rounded-full animate-bounce',
                  `animation-delay-${i * 200}`
                )}
                style={{
                  animationDelay: `${i * 0.2}s`,
                }}
              />
            ))}
          </div>

          {/* Progress Bar */}
          <div className="w-64 h-2 bg-orange-400 rounded-full mx-auto overflow-hidden">
            <div
              className="h-full bg-white rounded-full transition-all duration-100 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>

          <p className="text-orange-100 text-sm mt-4">
            {progress < 30 && 'Initializing...'}
            {progress >= 30 && progress < 60 && 'Loading components...'}
            {progress >= 60 && progress < 90 && 'Setting up workspace...'}
            {progress >= 90 && 'Almost ready...'}
          </p>
        </div>

        {/* Features */}
        <div className="text-orange-100 text-sm space-y-1">
          <p>✓ Employee Management</p>
          <p>✓ Leave Management</p>
          <p>✓ Calendar Integration</p>
          <p>✓ Dashboard Analytics</p>
        </div>
      </div>
    </div>
  );
};

// Enhanced Loading Spinner
export const EnhancedLoadingSpinner: React.FC<{
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'white' | 'gray';
  className?: string;
}> = ({ size = 'md', color = 'primary', className }) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16',
  };

  const colorClasses = {
    primary: 'border-orange-500',
    white: 'border-white',
    gray: 'border-gray-400',
  };

  return (
    <div className="relative">
      <div
        className={cn(
          'animate-spin rounded-full border-2 border-transparent',
          sizeClasses[size],
          className
        )}
        style={{
          borderTopColor: color === 'primary' ? '#f97316' : color === 'white' ? '#ffffff' : '#9ca3af',
          borderRightColor: color === 'primary' ? '#f97316' : color === 'white' ? '#ffffff' : '#9ca3af',
        }}
      />
      <div
        className={cn(
          'absolute inset-0 animate-ping rounded-full opacity-20',
          sizeClasses[size],
          colorClasses[color]
        )}
      />
    </div>
  );
};

// Page Loading with Better UX
export const PageLoadingScreen: React.FC<{
  message?: string;
  showProgress?: boolean;
  fullScreen?: boolean;
}> = ({
  message = 'Loading...',
  showProgress = false,
  fullScreen = false
}) => {
  const [dots, setDots] = useState('');
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Delay showing the loading screen to prevent flashing
    const showTimer = setTimeout(() => {
      setIsVisible(true);
    }, 100);

    const interval = setInterval(() => {
      setDots(prev => prev.length >= 3 ? '' : prev + '.');
    }, 500);

    return () => {
      clearTimeout(showTimer);
      clearInterval(interval);
    };
  }, []);

  if (!isVisible) return null;

  const containerClass = fullScreen
    ? 'fixed inset-0 bg-gray-50 flex items-center justify-center z-40'
    : 'min-h-[400px] flex items-center justify-center';

  return (
    <div className={containerClass}>
      <div className="text-center">
        <div className="mb-6">
          <EnhancedLoadingSpinner size="xl" className="mx-auto" />
        </div>

        <div className="space-y-2">
          <h3 className="text-lg font-medium text-gray-900">
            {message}{dots}
          </h3>

          {showProgress && (
            <div className="w-48 h-1 bg-gray-200 rounded-full mx-auto overflow-hidden">
              <div className="h-full bg-orange-500 rounded-full animate-pulse" style={{ width: '60%' }} />
            </div>
          )}

          <p className="text-sm text-gray-500">
            Please wait while we prepare your workspace
          </p>
        </div>
      </div>
    </div>
  );
};

// Skeleton Loading Components
export const SkeletonCard: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className={cn('bg-white rounded-lg shadow-sm border border-gray-200 p-6', className)}>
      <div className="animate-pulse">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export const SkeletonTable: React.FC<{ rows?: number }> = ({ rows = 5 }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
        </div>
      </div>
      <div className="divide-y divide-gray-200">
        {Array.from({ length: rows }).map((_, index) => (
          <div key={index} className="p-6">
            <div className="animate-pulse flex items-center space-x-4">
              <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                <div className="h-3 bg-gray-200 rounded w-1/4"></div>
              </div>
              <div className="w-20 h-8 bg-gray-200 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SplashScreen;
