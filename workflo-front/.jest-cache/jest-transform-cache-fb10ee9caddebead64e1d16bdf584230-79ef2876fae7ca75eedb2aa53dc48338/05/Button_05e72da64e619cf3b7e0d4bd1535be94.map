{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\nimport { ButtonProps } from '@/types';\n\nconst Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  loading = false,\n  onClick,\n  children,\n  className,\n  type = 'button',\n  title,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n\n  const variantClasses = {\n    primary: 'bg-orange-500 hover:bg-orange-600 text-white focus:ring-orange-500 border-b-4 border-orange-600',\n    secondary: 'bg-gray-100 hover:bg-gray-200 text-gray-900 focus:ring-gray-500 border border-gray-300',\n    outline: 'border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 focus:ring-gray-500',\n    ghost: 'hover:bg-gray-100 text-gray-700 focus:ring-gray-500',\n    danger: 'bg-red-500 hover:bg-red-600 text-white focus:ring-red-500 border-b-4 border-red-600',\n    success: 'bg-green-500 hover:bg-green-600 text-white focus:ring-green-500 border-b-4 border-green-600',\n    warning: 'bg-yellow-500 hover:bg-yellow-600 text-white focus:ring-yellow-500 border-b-4 border-yellow-600',\n  };\n\n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base',\n  };\n\n  const classes = cn(\n    baseClasses,\n    variantClasses[variant],\n    sizeClasses[size],\n    className\n  );\n\n  return (\n    <button\n      type={type}\n      className={classes}\n      disabled={disabled || loading}\n      onClick={onClick}\n      title={title}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          ></circle>\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          ></path>\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "names": ["<PERSON><PERSON>", "variant", "size", "disabled", "loading", "onClick", "children", "className", "type", "title", "props", "baseClasses", "variantClasses", "primary", "secondary", "outline", "ghost", "danger", "success", "warning", "sizeClasses", "sm", "md", "lg", "classes", "cn", "button", "svg", "xmlns", "fill", "viewBox", "circle", "cx", "cy", "r", "stroke", "strokeWidth", "path", "d"], "mappings": ";;;;+BA6EA;;;eAAA;;;;8DA7EkB;uBACC;;;;;;AAGnB,MAAMA,SAAgC,CAAC,EACrCC,UAAU,SAAS,EACnBC,OAAO,IAAI,EACXC,WAAW,KAAK,EAChBC,UAAU,KAAK,EACfC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACTC,OAAO,QAAQ,EACfC,KAAK,EACL,GAAGC,OACJ;IACC,MAAMC,cAAc;IAEpB,MAAMC,iBAAiB;QACrBC,SAAS;QACTC,WAAW;QACXC,SAAS;QACTC,OAAO;QACPC,QAAQ;QACRC,SAAS;QACTC,SAAS;IACX;IAEA,MAAMC,cAAc;QAClBC,IAAI;QACJC,IAAI;QACJC,IAAI;IACN;IAEA,MAAMC,UAAUC,IAAAA,SAAE,EAChBd,aACAC,cAAc,CAACX,QAAQ,EACvBmB,WAAW,CAAClB,KAAK,EACjBK;IAGF,qBACE,sBAACmB;QACClB,MAAMA;QACND,WAAWiB;QACXrB,UAAUA,YAAYC;QACtBC,SAASA;QACTI,OAAOA;QACN,GAAGC,KAAK;;YAERN,yBACC,sBAACuB;gBACCpB,WAAU;gBACVqB,OAAM;gBACNC,MAAK;gBACLC,SAAQ;;kCAER,qBAACC;wBACCxB,WAAU;wBACVyB,IAAG;wBACHC,IAAG;wBACHC,GAAE;wBACFC,QAAO;wBACPC,aAAY;;kCAEd,qBAACC;wBACC9B,WAAU;wBACVsB,MAAK;wBACLS,GAAE;;;;YAIPhC;;;AAGP;MAEA,WAAeN"}