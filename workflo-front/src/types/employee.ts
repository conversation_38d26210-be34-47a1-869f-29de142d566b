export interface Employee {
  id: number;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  profile_picture?: string;
  department_id: number;
  department_name: string;
  position: string;
  hire_date: string;
  salary: number;
  employment_type: 'full_time' | 'part_time' | 'contract' | 'intern';
  status: 'active' | 'inactive' | 'on_leave' | 'terminated';
  role: 'employee' | 'supervisor' | 'hr' | 'accountant' | 'admin';
  address?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  date_of_birth?: string;
  gender?: 'male' | 'female' | 'other';
  marital_status?: 'single' | 'married' | 'divorced' | 'widowed';
  nationality?: string;
  created_at: string;
  updated_at: string;
}

export interface Department {
  id: number;
  name: string;
  description?: string;
  supervisor_id?: number;
  supervisor_name?: string;
  employee_count: number;
  created_at: string;
  updated_at: string;
}

export interface EmployeeFilters {
  search?: string;
  department?: number;
  status?: string;
  employment_type?: string;
  role?: string;
}

export interface EmployeeBulkAction {
  action: 'activate' | 'deactivate' | 'delete' | 'export';
  employee_ids: number[];
}

export interface CreateEmployeeData {
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  department_id: number;
  position: string;
  hire_date: string;
  salary: number;
  employment_type: 'full_time' | 'part_time' | 'contract' | 'intern';
  role: 'employee' | 'supervisor' | 'hr' | 'accountant' | 'admin';
  address?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  date_of_birth?: string;
  gender?: 'male' | 'female' | 'other';
  marital_status?: 'single' | 'married' | 'divorced' | 'widowed';
  nationality?: string;
}

export interface UpdateEmployeeData extends Partial<CreateEmployeeData> {
  id: number;
}
