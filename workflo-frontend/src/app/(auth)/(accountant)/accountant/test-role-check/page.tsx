'use client';

import React from 'react';
import { useAuth } from '@/providers/AuthProvider';
import { useRoleCheck } from '@/components/auth/RoleGuard';
import Card from '@/components/ui/Card';

const TestRoleCheckPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const roleCheck = useRoleCheck();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Role Check Test Page</h1>
        <p className="text-gray-600">Testing role checking functionality for accountant</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Current User Info</h3>
          <div className="space-y-2 text-sm">
            <div>
              <span className="font-medium">Authenticated:</span> 
              <span className={`ml-2 ${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
                {isAuthenticated ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="font-medium">Email:</span> 
              <span className="ml-2 text-gray-700">{user?.email || 'Not available'}</span>
            </div>
            <div>
              <span className="font-medium">Role:</span> 
              <span className="ml-2 text-gray-700">{user?.role || 'Not available'}</span>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Role Check Results</h3>
          <div className="space-y-2 text-sm">
            <div>
              <span className="font-medium">Is Accountant:</span> 
              <span className={`ml-2 ${roleCheck.isAccountant() ? 'text-green-600' : 'text-red-600'}`}>
                {roleCheck.isAccountant() ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="font-medium">Has Accountant Role:</span> 
              <span className={`ml-2 ${roleCheck.hasRole(['accountant']) ? 'text-green-600' : 'text-red-600'}`}>
                {roleCheck.hasRole(['accountant']) ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="font-medium">Has Multiple Roles (accountant, admin, hr):</span> 
              <span className={`ml-2 ${roleCheck.hasRole(['accountant', 'admin', 'hr']) ? 'text-green-600' : 'text-red-600'}`}>
                {roleCheck.hasRole(['accountant', 'admin', 'hr']) ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </Card>
      </div>

      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Status</h3>
        <div className="space-y-3">
          {isAuthenticated && user?.role === 'accountant' && roleCheck.isAccountant() ? (
            <div className="p-4 bg-green-100 border border-green-300 rounded-md">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                <span className="text-green-800 font-medium">✅ All role checks are working correctly!</span>
              </div>
              <p className="text-green-700 text-sm mt-2">
                You are successfully authenticated as an accountant and can access this page.
              </p>
            </div>
          ) : (
            <div className="p-4 bg-red-100 border border-red-300 rounded-md">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-red-500 rounded-full mr-3"></div>
                <span className="text-red-800 font-medium">❌ Role checking is not working properly</span>
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default TestRoleCheckPage;
