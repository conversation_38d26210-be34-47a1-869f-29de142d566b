# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>Szentkirályi, 2016
# Attila <PERSON> <>, 2012
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-11-18 09:29+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Hungarian (http://www.transifex.com/django/django/language/"
"hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Tartalom típusok"

msgid "python model class name"
msgstr "python modell osztály neve"

msgid "content type"
msgstr "tartalom típusa"

msgid "content types"
msgstr "tartalom típusok"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "A %(ct_id)s tartalomtípus-objektumhoz nincsenek modellek hozzárendelve"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "A(z) %(ct_id)s típusú %(obj_id)s objektum nem létezik"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr ""
"%(ct_name)s objektumok esetén nincs beállítva a get_absolute_url() metódus."
