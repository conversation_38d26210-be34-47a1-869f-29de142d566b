# WorkFlow HR Management System - System Documentation

## Project Overview
WorkFlow is a comprehensive Human Resources Management System built with Django REST Framework backend and React TypeScript frontend. The system provides complete HR functionality including employee management, payroll processing, attendance tracking, leave management, performance reviews, and biometric integration.

## System Architecture

### Backend (Django REST Framework)
- **Framework**: Django 5.2 with Django REST Framework 3.15.2
- **Database**: SQLite3 (development)
- **Authentication**: JWT-based authentication using SimpleJWT
- **API Design**: RESTful API with ViewSets and Serializers
- **Time Zone**: Africa/Nairobi
- **File Storage**: Local file system for media files

### Frontend (React TypeScript)
- **Framework**: React 19.0.0 with TypeScript 5.7.2
- **Build Tool**: Vite 6.2.6 (actual installed version)
- **Routing**: React Router DOM 7.4.0
- **HTTP Client**: Axios 1.8.4
- **UI Framework**: Bootstrap 5.3.3
- **PDF Generation**: jsPDF 3.0.1 with jsPDF-AutoTable 5.0.2

## Core Features & Modules

### 1. Employee Management
- **User Model**: Extended AbstractUser with comprehensive employee fields
- **Employee Registration**: Complete employee onboarding with personal, financial, and job details
- **Profile Management**: Employee profile pictures, documents, and information updates
- **Soft Delete**: Employees are soft-deleted to maintain data integrity
- **Role-based Access**: HR, Supervisor, and Accountant roles

### 2. Department Management
- **Department Structure**: Hierarchical department organization
- **Supervisor Assignment**: Each department can have a designated supervisor
- **Employee Assignment**: Employees are assigned to specific departments

### 3. Authentication & Authorization
- **JWT Authentication**: Secure token-based authentication
- **Token Refresh**: Automatic token refresh mechanism
- **Role-based Permissions**: Different access levels for different user roles
- **Protected Routes**: Frontend route protection based on authentication status

### 4. Attendance Management
- **Time Tracking**: Check-in/check-out functionality
- **Overtime Calculation**: Automatic overtime hours calculation
- **Attendance History**: Complete attendance records for employees
- **Biometric Integration**: Suprema biometric device integration for attendance

### 5. Leave Management
- **Leave Types**: Sick, Annual, Paternity, Maternity leave types
- **Leave Application**: Employee leave request submission
- **Approval Workflow**: Supervisor approval/rejection with comments
- **Automatic Calculation**: Business days calculation excluding weekends
- **Email Notifications**: Automated email notifications for leave status updates with formatted dates and reviewer comments

### 6. Payroll System
- **Pay Cycles**: Configurable pay periods with start/end dates
- **Salary Calculation**: Gross salary with automatic deductions
- **Tax Calculation**: Kenyan tax brackets implementation (10%, 25%, 30%, 32.5%, 35%)
- **Statutory Deductions**: NSSF (6% with tier system), NHIF/SHA (2.75%), Housing Levy (1.5%)
- **Banking Integration**: Support for 16 major Kenyan banks (Equity, KCB, Co-op, etc.)
- **Bulk Payroll**: Bulk payroll generation for pay cycles
- **Payslip Generation**: Individual payslip access for employees
- **Refunds & Deductions**: Support for additional refunds and other deductions

### 7. Performance Management
- **Performance Reviews**: Rating system (1-5 scale)
- **Review Documentation**: File upload for performance documents
- **Supervisor Reviews**: Supervisors can review their direct reports
- **Review History**: Complete performance review history

### 8. Training Management
- **Training Modules**: Course creation and management
- **Employee Training**: Training assignment and progress tracking
- **Completion Status**: Not Started, In Progress, Completed statuses
- **Training Venues**: Physical location tracking for training

### 9. Recruitment Management
- **Job Postings**: Job creation with requirements and descriptions
- **Application Management**: Resume upload and application tracking
- **Candidate Status**: Applied, Interview, Rejected, Hired workflow
- **Interview Scheduling**: Interview date and interviewer assignment (model exists but API currently disabled)

### 10. Document Management
- **Employee Documents**: Personal document storage per employee
- **Company Documents**: Centralized company document repository
- **File Upload**: Secure file upload and storage system
- **Document Access**: Role-based document access control

### 11. Benefits Management
- **Benefit Types**: Various employee benefit categories
- **Benefit Assignment**: Individual benefit assignment to employees
- **Monetary Benefits**: Amount-based benefit calculations
- **Benefit Descriptions**: Detailed benefit information

### 12. Biometric Integration (Suprema)
- **Device Connection**: Integration with Suprema biometric devices
- **Session Management**: Secure session handling for device communication
- **API Proxy**: Proxy service for Suprema API calls
- **Device Management**: Multiple device support and configuration

## Database Design
### Key Models
1. **User (Employee)**: Extended Django user model with HR-specific fields
2. **Department**: Organizational structure with supervisor relationships
3. **Attendance**: Time tracking with check-in/out timestamps
4. **LeaveApplication**: Leave requests with approval workflow
5. **Payroll**: Salary calculations with deductions and net pay
6. **PayCycle**: Pay period definitions for payroll processing
7. **PerformanceReview**: Employee performance evaluations
8. **JobPosting**: Recruitment job advertisements
9. **Candidate**: Job application management
10. **Benefit**: Employee benefit definitions
11. **TrainingModule**: Training course management
12. **EmployeeDocument**: Document storage per employee
13. **JobHistory**: Employee job history tracking
14. **InterviewSchedule**: Interview scheduling for candidates
15. **EmployeeTraining**: Training assignment and progress tracking
16. **CompanyDocuments**: Centralized company document storage
17. **BaseModel**: Abstract model providing soft delete functionality

### Relationships
- One-to-Many: Department → Employees
- One-to-Many: Employee → Attendance Records
- One-to-Many: Employee → Leave Applications
- One-to-Many: Employee → Job History Records
- One-to-Many: Employee → Employee Documents
- One-to-Many: PayCycle → Payroll Records
- One-to-Many: JobPosting → Candidates
- One-to-Many: Candidate → Interview Schedules
- One-to-Many: Employee → Training Assignments (EmployeeTraining)
- Many-to-One: Employee → Department
- Many-to-One: Employee → Benefit

## API Endpoints

### Authentication
- `POST /api/token/` - Login and obtain JWT tokens
- `POST /api/token/refresh/` - Refresh access token

### Employee Management
- `GET /api/employees/me/` - Get current user profile
- `GET /api/get_employees/` - List all employees (admin)
- `POST /api/new_employee/` - Create new employee
- `PUT /api/new_employee/{id}/` - Update employee

### Department Management
- `GET /api/get_departments/` - List departments
- `POST /api/departments/` - Create department

### Attendance
- `GET /api/attendance/` - Get user attendance
- `POST /api/attendance/` - Record attendance

### Leave Management
- `GET /api/leave_applications/` - Get approved leaves
- `POST /api/leave_applications/` - Submit leave application
- `GET /api/pending_leave/` - Get pending leaves (supervisor)
- `PUT /api/pending_leave/{id}/` - Approve/reject leave

### Payroll
- `GET /api/payroll/` - Get user payroll history
- `GET /api/paycycle/` - List pay cycles
- `POST /api/paycycle/` - Create new pay cycle
- `PATCH /api/paycycle/{id}/mark-paid/` - Mark pay cycle as paid
- `POST /api/payroll_create/bulk-create/` - Bulk create payroll
- `GET /api/payroll_create/submitted-users/` - Get submitted user IDs for pay cycle
- `GET /api/payroll_create/employee-detail/` - Get specific employee payroll details

### Job Management
- `GET /api/get_job_postings/` - List active job postings
- `POST /api/job_postings/` - Create job posting
- `GET /api/candidates/` - List job candidates
- `POST /api/candidates/` - Submit job application

### Training & Performance
- `GET /api/training_modules/` - List training modules
- `GET /api/employee_trainings/` - Get user training assignments
- `GET /api/performance_reviews/` - Get user performance reviews
- `POST /api/performance_reviews/` - Create performance review
- `GET /api/reviews/` - Get supervisor's direct reports reviews

### Documents & Benefits
- `GET /api/user_documents/` - Get user documents
- `GET /api/admin_documents/` - Get all employee documents (admin)
- `POST /api/employee_documents/` - Upload employee document
- `GET /api/benefits/` - List benefits
- `GET /api/company_documents/` - Get company documents

### Additional Employee Endpoints
- `GET /api/all_employees/` - List all employees for dropdowns
- `GET /api/supervisor_directs/` - Get supervisor's direct reports
- `GET /api/employees_pay/` - Get employee salary information
- `GET /api/job_histories/` - Get user job history
- `GET /api/my_rejected_leaves/` - Get user's rejected leaves
- `GET /api/my_pending_leaves/` - Get user's pending leaves

### Biometric Integration
- `POST /api/suprema/login` - Login to Suprema device
- `POST /api/suprema/proxy` - Proxy Suprema API calls

## Frontend Structure

### Page Categories
1. **Admin Pages**: Employee management, payroll, departments, jobs
2. **User Pages**: Personal info, leave, payslip, documents, benefits
3. **Manage Pages**: Supervisor functions for leave and review management
4. **Shared Components**: Navigation, headers, footers, company info

### Key Components
- **Dashboard**: Employee overview with profile, reviews, jobs, training
- **Employee Management**: CRUD operations for employee data
- **Payroll Administration**: Pay cycle and payroll management
- **Leave Management**: Leave application and approval interfaces
- **Attendance Tracking**: Time recording and history
- **Document Management**: File upload and document access

## Security Features
- JWT token-based authentication with 30-minute access tokens
- Automatic token refresh (3-day refresh token lifetime)
- Role-based access control (HR, Supervisor, Accountant)
- CORS configuration for frontend-backend communication
- Soft delete for data preservation
- File upload validation and secure storage
- Custom user manager for soft-deleted users
- Protected API endpoints with authentication requirements

## Configuration Notes & Known Issues
- **EasyAudit Middleware**: Currently commented out in INSTALLED_APPS but referenced in MIDDLEWARE (line 79 in settings.py)
- **Email Backend**: Set to console backend for development (production should use SMTP)
- **Debug Mode**: Currently enabled for development (should be disabled in production)
- **Secret Key**: Hardcoded in settings (should use environment variables in production)
- **Interview Scheduling**: Model and serializer exist but ViewSet is commented out (lines 111-118 in views.py)
- **Training Filtering**: Employee training only shows 'Not Started' and 'In Progress' statuses
- **Duplicate Prevention**: Payroll system prevents duplicate entries for same employee and pay cycle
- **Soft Delete**: Some models use soft delete (BaseModel) while others use hard delete

## Deployment Configuration
- **Development**: SQLite database, console email backend
- **CORS**: Configured for localhost:5173 (Vite dev server)
- **Static Files**: Configured for production deployment
- **Media Files**: Local storage with proper URL configuration
- **Time Zone**: Africa/Nairobi for Kenyan operations

## Technology Stack Summary
- **Backend**: Django 5.2, DRF 3.15.2, SQLite, JWT (SimpleJWT 5.5.0)
- **Frontend**: React 19, TypeScript 5.7.2, Vite 6.2.6, Bootstrap 5.3.3
- **Integration**: Suprema biometric devices, Email notifications, PDF generation
- **Development**: Hot reload, ESLint 9.21.0, TypeScript checking, Axios 1.8.4
- **Additional Libraries**: Pillow 11.1.0 (image processing), django-cors-headers 4.7.0
- **Deployment**: Static file serving, media handling, CORS configuration

This system provides a complete HR management solution with modern web technologies, comprehensive employee lifecycle management, and integration capabilities for biometric attendance systems.
hr, accountant, supervisor, staff