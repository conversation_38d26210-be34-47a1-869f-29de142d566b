'use client';

import React from 'react';
import { useAuth } from '@/providers/AuthProvider';
import { useRoleCheck } from '@/components/auth/RoleGuard';
import Card from '@/components/ui/Card';

const AccountantDebugPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const roleCheck = useRoleCheck();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Accountant Debug Page</h1>
        <p className="text-gray-600">Debug information for accountant role and permissions</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Authentication Status</h3>
          <div className="space-y-2 text-sm">
            <div>
              <span className="font-medium">Authenticated:</span>
              <span className={`ml-2 ${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
                {isAuthenticated ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="font-medium">User Email:</span>
              <span className="ml-2 text-gray-700">{user?.email || 'Not available'}</span>
            </div>
            <div>
              <span className="font-medium">User Name:</span>
              <span className="ml-2 text-gray-700">{user?.email || 'Not available'}</span>
            </div>
            <div>
              <span className="font-medium">User Role:</span>
              <span className="ml-2 text-gray-700">{user?.role || 'Not available'}</span>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Role Permissions</h3>
          <div className="space-y-2 text-sm">
            <div>
              <span className="font-medium">Is Admin:</span>
              <span className={`ml-2 ${roleCheck.isAdmin() ? 'text-green-600' : 'text-red-600'}`}>
                {roleCheck.isAdmin() ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="font-medium">Is Staff:</span>
              <span className={`ml-2 ${roleCheck.isStaff() ? 'text-green-600' : 'text-red-600'}`}>
                {roleCheck.isStaff() ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="font-medium">Is Supervisor:</span>
              <span className={`ml-2 ${roleCheck.isSupervisor() ? 'text-green-600' : 'text-red-600'}`}>
                {roleCheck.isSupervisor() ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="font-medium">Is Accountant:</span>
              <span className={`ml-2 ${roleCheck.isAccountant() ? 'text-green-600' : 'text-red-600'}`}>
                {roleCheck.isAccountant() ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="font-medium">Has Accountant Role:</span>
              <span className={`ml-2 ${roleCheck.hasRole(['accountant']) ? 'text-green-600' : 'text-red-600'}`}>
                {roleCheck.hasRole(['accountant']) ? 'Yes' : 'No'}
              </span>
            </div>
            <div>
              <span className="font-medium">Has Admin/HR/Accountant:</span>
              <span className={`ml-2 ${roleCheck.hasRole(['accountant', 'admin', 'hr']) ? 'text-green-600' : 'text-red-600'}`}>
                {roleCheck.hasRole(['accountant', 'admin', 'hr']) ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
        </Card>
      </div>

      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Raw User Object</h3>
        <pre className="bg-gray-100 p-4 rounded-md text-xs overflow-auto">
          {JSON.stringify(user, null, 2)}
        </pre>
      </Card>

      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Results</h3>
        <div className="space-y-3">
          {isAuthenticated && user?.role === 'accountant' ? (
            <div className="p-4 bg-green-100 border border-green-300 rounded-md">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                <span className="text-green-800 font-medium">✅ Accountant role is working correctly!</span>
              </div>
            </div>
          ) : (
            <div className="p-4 bg-red-100 border border-red-300 rounded-md">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-red-500 rounded-full mr-3"></div>
                <span className="text-red-800 font-medium">❌ Accountant role is not working properly</span>
              </div>
            </div>
          )}

          {roleCheck.isAccountant() ? (
            <div className="p-4 bg-green-100 border border-green-300 rounded-md">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                <span className="text-green-800 font-medium">✅ Role checking function is working!</span>
              </div>
            </div>
          ) : (
            <div className="p-4 bg-red-100 border border-red-300 rounded-md">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-red-500 rounded-full mr-3"></div>
                <span className="text-red-800 font-medium">❌ Role checking function is not working</span>
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default AccountantDebugPage;
