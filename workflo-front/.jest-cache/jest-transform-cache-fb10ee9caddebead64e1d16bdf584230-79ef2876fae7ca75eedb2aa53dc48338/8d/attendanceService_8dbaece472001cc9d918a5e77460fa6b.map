{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/lib/attendanceService.ts"], "sourcesContent": ["import { biostarApi } from './biostarApi';\nimport { \n  AttendanceRecord, \n  BiometricEvent, \n  RealTimeAttendance, \n  BiometricUser,\n  BiometricDevice \n} from '@/types';\nimport { format, startOfDay, endOfDay, parseISO, differenceInHours } from 'date-fns';\n\nexport class AttendanceService {\n  private static instance: AttendanceService;\n  private realtimeListeners: ((data: RealTimeAttendance) => void)[] = [];\n  private pollingInterval: NodeJS.Timeout | null = null;\n\n  static getInstance(): AttendanceService {\n    if (!AttendanceService.instance) {\n      AttendanceService.instance = new AttendanceService();\n    }\n    return AttendanceService.instance;\n  }\n\n  // Get today's attendance for current user\n  async getTodayAttendance(employeeId: string): Promise<AttendanceRecord | null> {\n    try {\n      const today = format(new Date(), 'yyyy-MM-dd');\n      const startDate = format(startOfDay(new Date()), \"yyyy-MM-dd'T'HH:mm:ss\");\n      const endDate = format(endOfDay(new Date()), \"yyyy-MM-dd'T'HH:mm:ss\");\n\n      // Get events for today\n      const eventsResponse = await biostarApi.getEvents(\n        startDate,\n        endDate,\n        employeeId,\n        undefined,\n        100,\n        0\n      );\n\n      if (!eventsResponse.results || eventsResponse.results.length === 0) {\n        return null;\n      }\n\n      return this.processAttendanceEvents(employeeId, today, eventsResponse.results);\n    } catch (error) {\n      console.error('Error fetching today\\'s attendance:', error);\n      return null;\n    }\n  }\n\n  // Get attendance for a date range\n  async getAttendanceRange(\n    employeeId: string, \n    startDate: string, \n    endDate: string\n  ): Promise<AttendanceRecord[]> {\n    try {\n      const eventsResponse = await biostarApi.getEvents(\n        `${startDate}T00:00:00`,\n        `${endDate}T23:59:59`,\n        employeeId,\n        undefined,\n        1000,\n        0\n      );\n\n      if (!eventsResponse.results) {\n        return [];\n      }\n\n      // Group events by date\n      const eventsByDate = this.groupEventsByDate(eventsResponse.results);\n      \n      // Process each date\n      const attendanceRecords: AttendanceRecord[] = [];\n      for (const [date, events] of Object.entries(eventsByDate)) {\n        const record = this.processAttendanceEvents(employeeId, date, events);\n        if (record) {\n          attendanceRecords.push(record);\n        }\n      }\n\n      return attendanceRecords.sort((a, b) => b.date.localeCompare(a.date));\n    } catch (error) {\n      console.error('Error fetching attendance range:', error);\n      return [];\n    }\n  }\n\n  // Get real-time attendance updates\n  async getRealTimeUpdates(): Promise<RealTimeAttendance[]> {\n    try {\n      const events = await biostarApi.getRealtimeEvents(20);\n      \n      return events.map(event => ({\n        employee_id: event.user_id,\n        employee_name: event.user_name || 'Unknown',\n        event_type: this.mapEventType(event.event_type),\n        timestamp: event.datetime,\n        device_name: event.device_name || 'Unknown Device',\n        location: 'Main Office' // Default location\n      }));\n    } catch (error) {\n      console.error('Error fetching real-time updates:', error);\n      return [];\n    }\n  }\n\n  // Start real-time monitoring\n  startRealTimeMonitoring(callback: (data: RealTimeAttendance) => void): void {\n    this.realtimeListeners.push(callback);\n    \n    if (!this.pollingInterval) {\n      this.pollingInterval = setInterval(async () => {\n        try {\n          const updates = await this.getRealTimeUpdates();\n          updates.forEach(update => {\n            this.realtimeListeners.forEach(listener => listener(update));\n          });\n        } catch (error) {\n          console.error('Real-time monitoring error:', error);\n        }\n      }, 30000); // Poll every 30 seconds\n    }\n  }\n\n  // Stop real-time monitoring\n  stopRealTimeMonitoring(callback?: (data: RealTimeAttendance) => void): void {\n    if (callback) {\n      this.realtimeListeners = this.realtimeListeners.filter(listener => listener !== callback);\n    } else {\n      this.realtimeListeners = [];\n    }\n\n    if (this.realtimeListeners.length === 0 && this.pollingInterval) {\n      clearInterval(this.pollingInterval);\n      this.pollingInterval = null;\n    }\n  }\n\n  // Get attendance summary for dashboard\n  async getAttendanceSummary(employeeId: string): Promise<{\n    todayStatus: 'PRESENT' | 'ABSENT' | 'LATE' | 'EARLY_OUT';\n    checkInTime?: string;\n    checkOutTime?: string;\n    hoursWorked: number;\n    breakTime: number;\n    overtime: number;\n    weeklyHours: number;\n    monthlyAttendance: number;\n  }> {\n    try {\n      // Get today's attendance\n      const todayAttendance = await this.getTodayAttendance(employeeId);\n      \n      // Get this week's attendance\n      const weekStart = format(new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd');\n      const today = format(new Date(), 'yyyy-MM-dd');\n      const weeklyAttendance = await this.getAttendanceRange(employeeId, weekStart, today);\n      \n      // Get this month's attendance\n      const monthStart = format(new Date(new Date().getFullYear(), new Date().getMonth(), 1), 'yyyy-MM-dd');\n      const monthlyAttendance = await this.getAttendanceRange(employeeId, monthStart, today);\n\n      const weeklyHours = weeklyAttendance.reduce((total, record) => total + (record.total_hours || 0), 0);\n      const monthlyDays = monthlyAttendance.filter(record => record.status === 'PRESENT').length;\n\n      return {\n        todayStatus: todayAttendance?.status || 'ABSENT',\n        checkInTime: todayAttendance?.first_in,\n        checkOutTime: todayAttendance?.last_out,\n        hoursWorked: todayAttendance?.total_hours || 0,\n        breakTime: todayAttendance?.break_time || 0,\n        overtime: todayAttendance?.overtime || 0,\n        weeklyHours,\n        monthlyAttendance: monthlyDays,\n      };\n    } catch (error) {\n      console.error('Error fetching attendance summary:', error);\n      return {\n        todayStatus: 'ABSENT',\n        hoursWorked: 0,\n        breakTime: 0,\n        overtime: 0,\n        weeklyHours: 0,\n        monthlyAttendance: 0,\n      };\n    }\n  }\n\n  // Get available devices\n  async getDevices(): Promise<BiometricDevice[]> {\n    try {\n      const response = await biostarApi.getDevices();\n      return response.results || [];\n    } catch (error) {\n      console.error('Error fetching devices:', error);\n      return [];\n    }\n  }\n\n  // Sync employee with BioStar\n  async syncEmployee(employee: any): Promise<BiometricUser | null> {\n    try {\n      const biostarUser: Partial<BiometricUser> = {\n        user_id: employee.employee_id,\n        name: `${employee.first_name} ${employee.last_name}`,\n        email: employee.email,\n        phone: employee.phone_number,\n        department: employee.department_name,\n        position: employee.job_title,\n        employee_id: employee.employee_id,\n        disabled: !employee.is_active,\n      };\n\n      // Try to find existing user first\n      try {\n        const existingUser = await biostarApi.getUserById(employee.employee_id);\n        // Update existing user\n        return await biostarApi.updateUser(existingUser.id, biostarUser);\n      } catch (error) {\n        // User doesn't exist, create new one\n        return await biostarApi.createUser(biostarUser);\n      }\n    } catch (error) {\n      console.error('Error syncing employee with BioStar:', error);\n      return null;\n    }\n  }\n\n  // Private helper methods\n  private processAttendanceEvents(\n    employeeId: string, \n    date: string, \n    events: BiometricEvent[]\n  ): AttendanceRecord | null {\n    if (!events || events.length === 0) {\n      return null;\n    }\n\n    // Sort events by time\n    const sortedEvents = events.sort((a, b) => \n      new Date(a.datetime).getTime() - new Date(b.datetime).getTime()\n    );\n\n    // Find first entry and last exit\n    const entryEvents = sortedEvents.filter(e => e.event_type === 'ENTRY');\n    const exitEvents = sortedEvents.filter(e => e.event_type === 'EXIT');\n\n    const firstIn = entryEvents.length > 0 ? entryEvents[0].datetime : undefined;\n    const lastOut = exitEvents.length > 0 ? exitEvents[exitEvents.length - 1].datetime : undefined;\n\n    // Calculate total hours\n    let totalHours = 0;\n    if (firstIn && lastOut) {\n      totalHours = differenceInHours(parseISO(lastOut), parseISO(firstIn));\n    }\n\n    // Determine status\n    let status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EARLY_OUT' = 'ABSENT';\n    if (firstIn) {\n      const checkInTime = parseISO(firstIn);\n      const workStartTime = new Date(checkInTime);\n      workStartTime.setHours(9, 0, 0, 0); // Assuming 9 AM start time\n\n      if (checkInTime <= workStartTime) {\n        status = 'PRESENT';\n      } else {\n        status = 'LATE';\n      }\n\n      // Check for early out\n      if (lastOut) {\n        const checkOutTime = parseISO(lastOut);\n        const workEndTime = new Date(checkOutTime);\n        workEndTime.setHours(17, 0, 0, 0); // Assuming 5 PM end time\n\n        if (checkOutTime < workEndTime && totalHours < 8) {\n          status = 'EARLY_OUT';\n        }\n      }\n    }\n\n    return {\n      id: `${employeeId}-${date}`,\n      employee_id: employeeId,\n      employee_name: events[0]?.user_name || 'Unknown',\n      date,\n      first_in: firstIn,\n      last_out: lastOut,\n      total_hours: totalHours,\n      break_time: 0, // Calculate based on gaps between entry/exit\n      overtime: Math.max(0, totalHours - 8),\n      status,\n      events: sortedEvents,\n      biostar_synced: true,\n    };\n  }\n\n  private groupEventsByDate(events: BiometricEvent[]): Record<string, BiometricEvent[]> {\n    return events.reduce((groups, event) => {\n      const date = format(parseISO(event.datetime), 'yyyy-MM-dd');\n      if (!groups[date]) {\n        groups[date] = [];\n      }\n      groups[date].push(event);\n      return groups;\n    }, {} as Record<string, BiometricEvent[]>);\n  }\n\n  private mapEventType(biostarEventType: string): 'CHECK_IN' | 'CHECK_OUT' | 'BREAK_START' | 'BREAK_END' {\n    switch (biostarEventType) {\n      case 'ENTRY':\n        return 'CHECK_IN';\n      case 'EXIT':\n        return 'CHECK_OUT';\n      default:\n        return 'CHECK_IN';\n    }\n  }\n}\n\n// Export singleton instance\nexport const attendanceService = AttendanceService.getInstance();\n"], "names": ["AttendanceService", "attendanceService", "getInstance", "instance", "getTodayAttendance", "employeeId", "today", "format", "Date", "startDate", "startOfDay", "endDate", "endOfDay", "eventsResponse", "biostarApi", "getEvents", "undefined", "results", "length", "processAttendanceEvents", "error", "console", "getAttendanceRange", "eventsByDate", "groupEventsByDate", "attendanceRecords", "date", "events", "Object", "entries", "record", "push", "sort", "a", "b", "localeCompare", "getRealTimeUpdates", "getRealtimeEvents", "map", "event", "employee_id", "user_id", "employee_name", "user_name", "event_type", "mapEventType", "timestamp", "datetime", "device_name", "location", "startRealTimeMonitoring", "callback", "realtimeListeners", "pollingInterval", "setInterval", "updates", "for<PERSON>ach", "update", "listener", "stopRealTimeMonitoring", "filter", "clearInterval", "getAttendanceSummary", "todayAttendance", "weekStart", "now", "weeklyAttendance", "monthStart", "getFullYear", "getMonth", "monthlyAttendance", "weeklyHours", "reduce", "total", "total_hours", "monthlyDays", "status", "todayStatus", "checkInTime", "first_in", "checkOutTime", "last_out", "hoursWorked", "breakTime", "break_time", "overtime", "getDevices", "response", "syncEmployee", "employee", "biostarUser", "name", "first_name", "last_name", "email", "phone", "phone_number", "department", "department_name", "position", "job_title", "disabled", "is_active", "existingUser", "getUserById", "updateUser", "id", "createUser", "sortedEvents", "getTime", "entryEvents", "e", "exitEvents", "firstIn", "lastOut", "totalHours", "differenceInHours", "parseISO", "workStartTime", "setHours", "workEndTime", "Math", "max", "biostar_synced", "groups", "biostarEventType"], "mappings": ";;;;;;;;;;;IAUaA,iBAAiB;eAAjBA;;IAyTAC,iBAAiB;eAAjBA;;;4BAnUc;yBAQ+C;AAEnE,MAAMD;IAKX,OAAOE,cAAiC;QACtC,IAAI,CAACF,kBAAkBG,QAAQ,EAAE;YAC/BH,kBAAkBG,QAAQ,GAAG,IAAIH;QACnC;QACA,OAAOA,kBAAkBG,QAAQ;IACnC;IAEA,0CAA0C;IAC1C,MAAMC,mBAAmBC,UAAkB,EAAoC;QAC7E,IAAI;YACF,MAAMC,QAAQC,IAAAA,eAAM,EAAC,IAAIC,QAAQ;YACjC,MAAMC,YAAYF,IAAAA,eAAM,EAACG,IAAAA,mBAAU,EAAC,IAAIF,SAAS;YACjD,MAAMG,UAAUJ,IAAAA,eAAM,EAACK,IAAAA,iBAAQ,EAAC,IAAIJ,SAAS;YAE7C,uBAAuB;YACvB,MAAMK,iBAAiB,MAAMC,sBAAU,CAACC,SAAS,CAC/CN,WACAE,SACAN,YACAW,WACA,KACA;YAGF,IAAI,CAACH,eAAeI,OAAO,IAAIJ,eAAeI,OAAO,CAACC,MAAM,KAAK,GAAG;gBAClE,OAAO;YACT;YAEA,OAAO,IAAI,CAACC,uBAAuB,CAACd,YAAYC,OAAOO,eAAeI,OAAO;QAC/E,EAAE,OAAOG,OAAO;YACdC,QAAQD,KAAK,CAAC,uCAAuCA;YACrD,OAAO;QACT;IACF;IAEA,kCAAkC;IAClC,MAAME,mBACJjB,UAAkB,EAClBI,SAAiB,EACjBE,OAAe,EACc;QAC7B,IAAI;YACF,MAAME,iBAAiB,MAAMC,sBAAU,CAACC,SAAS,CAC/C,GAAGN,UAAU,SAAS,CAAC,EACvB,GAAGE,QAAQ,SAAS,CAAC,EACrBN,YACAW,WACA,MACA;YAGF,IAAI,CAACH,eAAeI,OAAO,EAAE;gBAC3B,OAAO,EAAE;YACX;YAEA,uBAAuB;YACvB,MAAMM,eAAe,IAAI,CAACC,iBAAiB,CAACX,eAAeI,OAAO;YAElE,oBAAoB;YACpB,MAAMQ,oBAAwC,EAAE;YAChD,KAAK,MAAM,CAACC,MAAMC,OAAO,IAAIC,OAAOC,OAAO,CAACN,cAAe;gBACzD,MAAMO,SAAS,IAAI,CAACX,uBAAuB,CAACd,YAAYqB,MAAMC;gBAC9D,IAAIG,QAAQ;oBACVL,kBAAkBM,IAAI,CAACD;gBACzB;YACF;YAEA,OAAOL,kBAAkBO,IAAI,CAAC,CAACC,GAAGC,IAAMA,EAAER,IAAI,CAACS,aAAa,CAACF,EAAEP,IAAI;QACrE,EAAE,OAAON,OAAO;YACdC,QAAQD,KAAK,CAAC,oCAAoCA;YAClD,OAAO,EAAE;QACX;IACF;IAEA,mCAAmC;IACnC,MAAMgB,qBAAoD;QACxD,IAAI;YACF,MAAMT,SAAS,MAAMb,sBAAU,CAACuB,iBAAiB,CAAC;YAElD,OAAOV,OAAOW,GAAG,CAACC,CAAAA,QAAU,CAAA;oBAC1BC,aAAaD,MAAME,OAAO;oBAC1BC,eAAeH,MAAMI,SAAS,IAAI;oBAClCC,YAAY,IAAI,CAACC,YAAY,CAACN,MAAMK,UAAU;oBAC9CE,WAAWP,MAAMQ,QAAQ;oBACzBC,aAAaT,MAAMS,WAAW,IAAI;oBAClCC,UAAU,cAAc,mBAAmB;gBAC7C,CAAA;QACF,EAAE,OAAO7B,OAAO;YACdC,QAAQD,KAAK,CAAC,qCAAqCA;YACnD,OAAO,EAAE;QACX;IACF;IAEA,6BAA6B;IAC7B8B,wBAAwBC,QAA4C,EAAQ;QAC1E,IAAI,CAACC,iBAAiB,CAACrB,IAAI,CAACoB;QAE5B,IAAI,CAAC,IAAI,CAACE,eAAe,EAAE;YACzB,IAAI,CAACA,eAAe,GAAGC,YAAY;gBACjC,IAAI;oBACF,MAAMC,UAAU,MAAM,IAAI,CAACnB,kBAAkB;oBAC7CmB,QAAQC,OAAO,CAACC,CAAAA;wBACd,IAAI,CAACL,iBAAiB,CAACI,OAAO,CAACE,CAAAA,WAAYA,SAASD;oBACtD;gBACF,EAAE,OAAOrC,OAAO;oBACdC,QAAQD,KAAK,CAAC,+BAA+BA;gBAC/C;YACF,GAAG,QAAQ,wBAAwB;QACrC;IACF;IAEA,4BAA4B;IAC5BuC,uBAAuBR,QAA6C,EAAQ;QAC1E,IAAIA,UAAU;YACZ,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACQ,MAAM,CAACF,CAAAA,WAAYA,aAAaP;QAClF,OAAO;YACL,IAAI,CAACC,iBAAiB,GAAG,EAAE;QAC7B;QAEA,IAAI,IAAI,CAACA,iBAAiB,CAAClC,MAAM,KAAK,KAAK,IAAI,CAACmC,eAAe,EAAE;YAC/DQ,cAAc,IAAI,CAACR,eAAe;YAClC,IAAI,CAACA,eAAe,GAAG;QACzB;IACF;IAEA,uCAAuC;IACvC,MAAMS,qBAAqBzD,UAAkB,EAS1C;QACD,IAAI;YACF,yBAAyB;YACzB,MAAM0D,kBAAkB,MAAM,IAAI,CAAC3D,kBAAkB,CAACC;YAEtD,6BAA6B;YAC7B,MAAM2D,YAAYzD,IAAAA,eAAM,EAAC,IAAIC,KAAKA,KAAKyD,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,OAAO;YACzE,MAAM3D,QAAQC,IAAAA,eAAM,EAAC,IAAIC,QAAQ;YACjC,MAAM0D,mBAAmB,MAAM,IAAI,CAAC5C,kBAAkB,CAACjB,YAAY2D,WAAW1D;YAE9E,8BAA8B;YAC9B,MAAM6D,aAAa5D,IAAAA,eAAM,EAAC,IAAIC,KAAK,IAAIA,OAAO4D,WAAW,IAAI,IAAI5D,OAAO6D,QAAQ,IAAI,IAAI;YACxF,MAAMC,oBAAoB,MAAM,IAAI,CAAChD,kBAAkB,CAACjB,YAAY8D,YAAY7D;YAEhF,MAAMiE,cAAcL,iBAAiBM,MAAM,CAAC,CAACC,OAAO3C,SAAW2C,QAAS3C,CAAAA,OAAO4C,WAAW,IAAI,CAAA,GAAI;YAClG,MAAMC,cAAcL,kBAAkBV,MAAM,CAAC9B,CAAAA,SAAUA,OAAO8C,MAAM,KAAK,WAAW1D,MAAM;YAE1F,OAAO;gBACL2D,aAAad,iBAAiBa,UAAU;gBACxCE,aAAaf,iBAAiBgB;gBAC9BC,cAAcjB,iBAAiBkB;gBAC/BC,aAAanB,iBAAiBW,eAAe;gBAC7CS,WAAWpB,iBAAiBqB,cAAc;gBAC1CC,UAAUtB,iBAAiBsB,YAAY;gBACvCd;gBACAD,mBAAmBK;YACrB;QACF,EAAE,OAAOvD,OAAO;YACdC,QAAQD,KAAK,CAAC,sCAAsCA;YACpD,OAAO;gBACLyD,aAAa;gBACbK,aAAa;gBACbC,WAAW;gBACXE,UAAU;gBACVd,aAAa;gBACbD,mBAAmB;YACrB;QACF;IACF;IAEA,wBAAwB;IACxB,MAAMgB,aAAyC;QAC7C,IAAI;YACF,MAAMC,WAAW,MAAMzE,sBAAU,CAACwE,UAAU;YAC5C,OAAOC,SAAStE,OAAO,IAAI,EAAE;QAC/B,EAAE,OAAOG,OAAO;YACdC,QAAQD,KAAK,CAAC,2BAA2BA;YACzC,OAAO,EAAE;QACX;IACF;IAEA,6BAA6B;IAC7B,MAAMoE,aAAaC,QAAa,EAAiC;QAC/D,IAAI;YACF,MAAMC,cAAsC;gBAC1CjD,SAASgD,SAASjD,WAAW;gBAC7BmD,MAAM,GAAGF,SAASG,UAAU,CAAC,CAAC,EAAEH,SAASI,SAAS,EAAE;gBACpDC,OAAOL,SAASK,KAAK;gBACrBC,OAAON,SAASO,YAAY;gBAC5BC,YAAYR,SAASS,eAAe;gBACpCC,UAAUV,SAASW,SAAS;gBAC5B5D,aAAaiD,SAASjD,WAAW;gBACjC6D,UAAU,CAACZ,SAASa,SAAS;YAC/B;YAEA,kCAAkC;YAClC,IAAI;gBACF,MAAMC,eAAe,MAAMzF,sBAAU,CAAC0F,WAAW,CAACf,SAASjD,WAAW;gBACtE,uBAAuB;gBACvB,OAAO,MAAM1B,sBAAU,CAAC2F,UAAU,CAACF,aAAaG,EAAE,EAAEhB;YACtD,EAAE,OAAOtE,OAAO;gBACd,qCAAqC;gBACrC,OAAO,MAAMN,sBAAU,CAAC6F,UAAU,CAACjB;YACrC;QACF,EAAE,OAAOtE,OAAO;YACdC,QAAQD,KAAK,CAAC,wCAAwCA;YACtD,OAAO;QACT;IACF;IAEA,yBAAyB;IACjBD,wBACNd,UAAkB,EAClBqB,IAAY,EACZC,MAAwB,EACC;QACzB,IAAI,CAACA,UAAUA,OAAOT,MAAM,KAAK,GAAG;YAClC,OAAO;QACT;QAEA,sBAAsB;QACtB,MAAM0F,eAAejF,OAAOK,IAAI,CAAC,CAACC,GAAGC,IACnC,IAAI1B,KAAKyB,EAAEc,QAAQ,EAAE8D,OAAO,KAAK,IAAIrG,KAAK0B,EAAEa,QAAQ,EAAE8D,OAAO;QAG/D,iCAAiC;QACjC,MAAMC,cAAcF,aAAahD,MAAM,CAACmD,CAAAA,IAAKA,EAAEnE,UAAU,KAAK;QAC9D,MAAMoE,aAAaJ,aAAahD,MAAM,CAACmD,CAAAA,IAAKA,EAAEnE,UAAU,KAAK;QAE7D,MAAMqE,UAAUH,YAAY5F,MAAM,GAAG,IAAI4F,WAAW,CAAC,EAAE,CAAC/D,QAAQ,GAAG/B;QACnE,MAAMkG,UAAUF,WAAW9F,MAAM,GAAG,IAAI8F,UAAU,CAACA,WAAW9F,MAAM,GAAG,EAAE,CAAC6B,QAAQ,GAAG/B;QAErF,wBAAwB;QACxB,IAAImG,aAAa;QACjB,IAAIF,WAAWC,SAAS;YACtBC,aAAaC,IAAAA,0BAAiB,EAACC,IAAAA,iBAAQ,EAACH,UAAUG,IAAAA,iBAAQ,EAACJ;QAC7D;QAEA,mBAAmB;QACnB,IAAIrC,SAAsD;QAC1D,IAAIqC,SAAS;YACX,MAAMnC,cAAcuC,IAAAA,iBAAQ,EAACJ;YAC7B,MAAMK,gBAAgB,IAAI9G,KAAKsE;YAC/BwC,cAAcC,QAAQ,CAAC,GAAG,GAAG,GAAG,IAAI,2BAA2B;YAE/D,IAAIzC,eAAewC,eAAe;gBAChC1C,SAAS;YACX,OAAO;gBACLA,SAAS;YACX;YAEA,sBAAsB;YACtB,IAAIsC,SAAS;gBACX,MAAMlC,eAAeqC,IAAAA,iBAAQ,EAACH;gBAC9B,MAAMM,cAAc,IAAIhH,KAAKwE;gBAC7BwC,YAAYD,QAAQ,CAAC,IAAI,GAAG,GAAG,IAAI,yBAAyB;gBAE5D,IAAIvC,eAAewC,eAAeL,aAAa,GAAG;oBAChDvC,SAAS;gBACX;YACF;QACF;QAEA,OAAO;YACL8B,IAAI,GAAGrG,WAAW,CAAC,EAAEqB,MAAM;YAC3Bc,aAAanC;YACbqC,eAAef,MAAM,CAAC,EAAE,EAAEgB,aAAa;YACvCjB;YACAqD,UAAUkC;YACVhC,UAAUiC;YACVxC,aAAayC;YACb/B,YAAY;YACZC,UAAUoC,KAAKC,GAAG,CAAC,GAAGP,aAAa;YACnCvC;YACAjD,QAAQiF;YACRe,gBAAgB;QAClB;IACF;IAEQnG,kBAAkBG,MAAwB,EAAoC;QACpF,OAAOA,OAAO6C,MAAM,CAAC,CAACoD,QAAQrF;YAC5B,MAAMb,OAAOnB,IAAAA,eAAM,EAAC8G,IAAAA,iBAAQ,EAAC9E,MAAMQ,QAAQ,GAAG;YAC9C,IAAI,CAAC6E,MAAM,CAAClG,KAAK,EAAE;gBACjBkG,MAAM,CAAClG,KAAK,GAAG,EAAE;YACnB;YACAkG,MAAM,CAAClG,KAAK,CAACK,IAAI,CAACQ;YAClB,OAAOqF;QACT,GAAG,CAAC;IACN;IAEQ/E,aAAagF,gBAAwB,EAA0D;QACrG,OAAQA;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;;aAnTQzE,oBAA4D,EAAE;aAC9DC,kBAAyC;;AAmTnD;AAGO,MAAMpD,oBAAoBD,kBAAkBE,WAAW"}