// Re-export the useToast hook from ToastProvider for convenience
export { useToast } from '@/providers/ToastProvider';

// Additional toast utilities and helpers
import { useToast as useToastContext } from '@/providers/ToastProvider';

/**
 * Enhanced toast hook with additional utilities
 */
export const useToastWithUtils = () => {
  const toast = useToastContext();

  // Promise-based toast for async operations
  const promiseToast = async <T>(
    promise: Promise<T>,
    {
      loading = 'Loading...',
      success = 'Success!',
      error = 'Something went wrong',
    }: {
      loading?: string;
      success?: string | ((data: T) => string);
      error?: string | ((error: any) => string);
    }
  ): Promise<T> => {
    const loadingId = toast.info(loading, { persistent: true });

    try {
      const result = await promise;
      toast.hideToast(loadingId);
      
      const successMessage = typeof success === 'function' ? success(result) : success;
      toast.success(successMessage);
      
      return result;
    } catch (err) {
      toast.hideToast(loadingId);
      
      const errorMessage = typeof error === 'function' ? error(err) : error;
      toast.error(errorMessage);
      
      throw err;
    }
  };

  // API error handler
  const handleApiError = (error: any, fallbackMessage = 'An error occurred') => {
    let message = fallbackMessage;
    
    if (error?.response?.data?.message) {
      message = error.response.data.message;
    } else if (error?.message) {
      message = error.message;
    }
    
    return toast.error(message);
  };

  // Form validation error handler
  const handleValidationErrors = (errors: Record<string, string[]>) => {
    const errorMessages = Object.entries(errors)
      .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
      .join('\n');
    
    return toast.error(errorMessages, {
      title: 'Validation Errors',
      persistent: true,
    });
  };

  // Network error handler
  const handleNetworkError = () => {
    return toast.error('Network error. Please check your connection and try again.', {
      title: 'Connection Error',
      persistent: true,
      action: {
        label: 'Retry',
        onClick: () => window.location.reload(),
      },
    });
  };

  return {
    ...toast,
    promiseToast,
    handleApiError,
    handleValidationErrors,
    handleNetworkError,
  };
};

export default useToastWithUtils;
