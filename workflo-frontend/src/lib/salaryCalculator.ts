interface SalaryConfig {
  isDynamic: boolean;
  hourlyRate: number;
  overtimeMultiplier: number;
  workingHoursPerDay: number;
  workingDaysPerMonth: number;
  leaveDeductions: {
    unpaidLeavePerDay: number;
    lateArrivalPerHour: number;
    earlyDeparturePerHour: number;
    absenteeismPerDay: number;
  };
  bonuses: {
    perfectAttendanceBonus: number;
    overtimeBonus: number;
    performanceBonus: number;
  };
  penalties: {
    excessiveAbsenceThreshold: number;
    excessiveAbsencePenalty: number;
    lateArrivalThreshold: number;
    lateArrivalPenalty: number;
  };
}

interface AttendanceRecord {
  id: number;
  employee_id: number;
  date: string;
  check_in_time?: string;
  check_out_time?: string;
  total_hours: number;
  regular_hours: number;
  overtime_hours: number;
  status: 'present' | 'absent' | 'late' | 'half_day' | 'holiday';
  late_minutes?: number;
  early_departure_minutes?: number;
}

interface LeaveRecord {
  id: number;
  employee_id: number;
  start_date: string;
  end_date: string;
  business_days: number;
  leave_type: 'paid' | 'unpaid' | 'sick' | 'annual';
  status: 'approved' | 'pending' | 'rejected';
}

interface PerformanceData {
  overall_rating: number;
  goals_completed: number;
  total_goals: number;
}

interface DynamicSalaryCalculation {
  baseSalary: number;
  regularHours: number;
  overtimeHours: number;
  regularPay: number;
  overtimePay: number;
  bonuses: {
    perfectAttendance: number;
    overtime: number;
    performance: number;
    total: number;
  };
  deductions: {
    unpaidLeave: number;
    lateArrival: number;
    earlyDeparture: number;
    absenteeism: number;
    excessiveAbsence: number;
    lateArrivalPenalty: number;
    total: number;
  };
  grossPay: number;
  netPay: number;
  attendanceRate: number;
  punctualityRate: number;
  breakdown: {
    workingDays: number;
    presentDays: number;
    absentDays: number;
    lateDays: number;
    halfDays: number;
    unpaidLeaveDays: number;
    totalRegularHours: number;
    totalOvertimeHours: number;
  };
}

export class SalaryCalculator {
  private config: SalaryConfig;

  constructor(config: SalaryConfig) {
    this.config = config;
  }

  calculateDynamicSalary(
    employeeId: number,
    baseSalary: number,
    attendanceRecords: AttendanceRecord[],
    leaveRecords: LeaveRecord[],
    performanceData?: PerformanceData,
    month?: string
  ): DynamicSalaryCalculation {
    if (!this.config.isDynamic) {
      // Return static calculation
      return this.getStaticSalaryCalculation(baseSalary);
    }

    // Filter records for the specified month
    const monthRecords = month 
      ? attendanceRecords.filter(record => record.date.startsWith(month))
      : attendanceRecords;

    const monthLeaveRecords = month
      ? leaveRecords.filter(record => 
          record.start_date.startsWith(month) || record.end_date.startsWith(month)
        )
      : leaveRecords;

    // Calculate attendance metrics
    const attendanceMetrics = this.calculateAttendanceMetrics(monthRecords);
    
    // Calculate leave deductions
    const leaveDeductions = this.calculateLeaveDeductions(monthLeaveRecords, attendanceMetrics);
    
    // Calculate bonuses
    const bonuses = this.calculateBonuses(attendanceMetrics, performanceData);
    
    // Calculate penalties
    const penalties = this.calculatePenalties(attendanceMetrics, baseSalary);
    
    // Calculate pay based on hours worked
    const payCalculation = this.calculateHourlyPay(attendanceMetrics);

    const totalBonuses = bonuses.perfectAttendance + bonuses.overtime + bonuses.performance;
    const totalDeductions = leaveDeductions.unpaidLeave + leaveDeductions.lateArrival + 
                           leaveDeductions.earlyDeparture + leaveDeductions.absenteeism + 
                           penalties.excessiveAbsence + penalties.lateArrivalPenalty;

    const grossPay = payCalculation.regularPay + payCalculation.overtimePay + totalBonuses;
    const netPay = grossPay - totalDeductions;

    return {
      baseSalary,
      regularHours: attendanceMetrics.totalRegularHours,
      overtimeHours: attendanceMetrics.totalOvertimeHours,
      regularPay: payCalculation.regularPay,
      overtimePay: payCalculation.overtimePay,
      bonuses: {
        perfectAttendance: bonuses.perfectAttendance,
        overtime: bonuses.overtime,
        performance: bonuses.performance,
        total: totalBonuses
      },
      deductions: {
        unpaidLeave: leaveDeductions.unpaidLeave,
        lateArrival: leaveDeductions.lateArrival,
        earlyDeparture: leaveDeductions.earlyDeparture,
        absenteeism: leaveDeductions.absenteeism,
        excessiveAbsence: penalties.excessiveAbsence,
        lateArrivalPenalty: penalties.lateArrivalPenalty,
        total: totalDeductions
      },
      grossPay,
      netPay,
      attendanceRate: attendanceMetrics.attendanceRate,
      punctualityRate: attendanceMetrics.punctualityRate,
      breakdown: {
        workingDays: this.config.workingDaysPerMonth,
        presentDays: attendanceMetrics.presentDays,
        absentDays: attendanceMetrics.absentDays,
        lateDays: attendanceMetrics.lateDays,
        halfDays: attendanceMetrics.halfDays,
        unpaidLeaveDays: attendanceMetrics.unpaidLeaveDays,
        totalRegularHours: attendanceMetrics.totalRegularHours,
        totalOvertimeHours: attendanceMetrics.totalOvertimeHours
      }
    };
  }

  private calculateAttendanceMetrics(records: AttendanceRecord[]) {
    const presentDays = records.filter(r => r.status === 'present').length;
    const absentDays = records.filter(r => r.status === 'absent').length;
    const lateDays = records.filter(r => r.status === 'late').length;
    const halfDays = records.filter(r => r.status === 'half_day').length;
    const unpaidLeaveDays = records.filter(r => r.status === 'absent' && !r.check_in_time).length;

    const totalRegularHours = records.reduce((sum, r) => sum + r.regular_hours, 0);
    const totalOvertimeHours = records.reduce((sum, r) => sum + r.overtime_hours, 0);
    const totalLateMinutes = records.reduce((sum, r) => sum + (r.late_minutes || 0), 0);
    const totalEarlyDepartureMinutes = records.reduce((sum, r) => sum + (r.early_departure_minutes || 0), 0);

    const attendanceRate = records.length > 0 ? ((presentDays + lateDays) / records.length) * 100 : 0;
    const punctualityRate = records.length > 0 ? (presentDays / records.length) * 100 : 0;

    return {
      presentDays,
      absentDays,
      lateDays,
      halfDays,
      unpaidLeaveDays,
      totalRegularHours,
      totalOvertimeHours,
      totalLateMinutes,
      totalEarlyDepartureMinutes,
      attendanceRate,
      punctualityRate
    };
  }

  private calculateLeaveDeductions(leaveRecords: LeaveRecord[], attendanceMetrics: any) {
    const unpaidLeaveRecords = leaveRecords.filter(r => 
      r.leave_type === 'unpaid' && r.status === 'approved'
    );
    
    const unpaidLeaveDays = unpaidLeaveRecords.reduce((sum, r) => sum + r.business_days, 0);
    const unpaidLeave = unpaidLeaveDays * this.config.leaveDeductions.unpaidLeavePerDay;

    const lateArrivalHours = attendanceMetrics.totalLateMinutes / 60;
    const lateArrival = lateArrivalHours * this.config.leaveDeductions.lateArrivalPerHour;

    const earlyDepartureHours = attendanceMetrics.totalEarlyDepartureMinutes / 60;
    const earlyDeparture = earlyDepartureHours * this.config.leaveDeductions.earlyDeparturePerHour;

    const absenteeism = attendanceMetrics.absentDays * this.config.leaveDeductions.absenteeismPerDay;

    return {
      unpaidLeave,
      lateArrival,
      earlyDeparture,
      absenteeism
    };
  }

  private calculateBonuses(attendanceMetrics: any, performanceData?: PerformanceData) {
    // Perfect attendance bonus
    const perfectAttendance = attendanceMetrics.absentDays === 0 && attendanceMetrics.lateDays === 0
      ? this.config.bonuses.perfectAttendanceBonus
      : 0;

    // Overtime bonus
    const overtime = attendanceMetrics.totalOvertimeHours > 0
      ? this.config.bonuses.overtimeBonus
      : 0;

    // Performance bonus
    let performance = 0;
    if (performanceData) {
      const performanceScore = performanceData.overall_rating;
      const goalCompletionRate = performanceData.goals_completed / performanceData.total_goals;
      
      if (performanceScore >= 4.5 && goalCompletionRate >= 0.9) {
        performance = this.config.bonuses.performanceBonus;
      } else if (performanceScore >= 4.0 && goalCompletionRate >= 0.8) {
        performance = this.config.bonuses.performanceBonus * 0.7;
      } else if (performanceScore >= 3.5 && goalCompletionRate >= 0.7) {
        performance = this.config.bonuses.performanceBonus * 0.5;
      }
    }

    return {
      perfectAttendance,
      overtime,
      performance
    };
  }

  private calculatePenalties(attendanceMetrics: any, baseSalary: number) {
    // Excessive absence penalty
    let excessiveAbsence = 0;
    if (attendanceMetrics.absentDays > this.config.penalties.excessiveAbsenceThreshold) {
      excessiveAbsence = (baseSalary * this.config.penalties.excessiveAbsencePenalty) / 100;
    }

    // Late arrival penalty
    let lateArrivalPenalty = 0;
    if (attendanceMetrics.lateDays > this.config.penalties.lateArrivalThreshold) {
      lateArrivalPenalty = this.config.penalties.lateArrivalPenalty;
    }

    return {
      excessiveAbsence,
      lateArrivalPenalty
    };
  }

  private calculateHourlyPay(attendanceMetrics: any) {
    const regularPay = attendanceMetrics.totalRegularHours * this.config.hourlyRate;
    const overtimePay = attendanceMetrics.totalOvertimeHours * this.config.hourlyRate * this.config.overtimeMultiplier;

    return {
      regularPay,
      overtimePay
    };
  }

  private getStaticSalaryCalculation(baseSalary: number): DynamicSalaryCalculation {
    return {
      baseSalary,
      regularHours: this.config.workingHoursPerDay * this.config.workingDaysPerMonth,
      overtimeHours: 0,
      regularPay: baseSalary,
      overtimePay: 0,
      bonuses: {
        perfectAttendance: 0,
        overtime: 0,
        performance: 0,
        total: 0
      },
      deductions: {
        unpaidLeave: 0,
        lateArrival: 0,
        earlyDeparture: 0,
        absenteeism: 0,
        excessiveAbsence: 0,
        lateArrivalPenalty: 0,
        total: 0
      },
      grossPay: baseSalary,
      netPay: baseSalary,
      attendanceRate: 100,
      punctualityRate: 100,
      breakdown: {
        workingDays: this.config.workingDaysPerMonth,
        presentDays: this.config.workingDaysPerMonth,
        absentDays: 0,
        lateDays: 0,
        halfDays: 0,
        unpaidLeaveDays: 0,
        totalRegularHours: this.config.workingHoursPerDay * this.config.workingDaysPerMonth,
        totalOvertimeHours: 0
      }
    };
  }

  updateConfig(newConfig: SalaryConfig) {
    this.config = newConfig;
  }

  getConfig(): SalaryConfig {
    return this.config;
  }
}

export type { SalaryConfig, DynamicSalaryCalculation, AttendanceRecord, LeaveRecord, PerformanceData };
