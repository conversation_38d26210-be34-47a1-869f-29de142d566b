f35234037adcbf339fa16842afeece4b
"use strict";
// Mock the BioStar attendance hook
jest.mock('@/hooks/useBiostarAttendance');
// Mock the BioStar API
jest.mock('@/lib/biostarApi', ()=>({
        biostarApi: {
            getUserById: jest.fn(),
            getEvents: jest.fn()
        }
    }));
// Mock the AuthProvider
jest.mock('@/providers/AuthProvider', ()=>({
        AuthProvider: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: children
            }),
        useAuth: ()=>({
                user: {
                    id: 1,
                    employee_id: 'EMP001',
                    first_name: '<PERSON>',
                    last_name: '<PERSON><PERSON>',
                    email: '<EMAIL>',
                    role: 'employee'
                }
            })
    }));
// Mock Next.js router
jest.mock('next/navigation', ()=>({
        useRouter: ()=>({
                push: jest.fn(),
                back: jest.fn()
            }),
        useParams: ()=>({})
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _react1 = require("@testing-library/react");
const _AuthProvider = require("../providers/AuthProvider");
const _page = /*#__PURE__*/ _interop_require_default(require("../app/(auth)/(staff)/staff/info/biostar/page"));
const _EmployeeBiostarWidget = /*#__PURE__*/ _interop_require_default(require("../components/biostar/EmployeeBiostarWidget"));
const _useBiostarAttendance = require("../hooks/useBiostarAttendance");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockUseBiostarAttendance = _useBiostarAttendance.useBiostarAttendance;
const mockUser = {
    id: 1,
    employee_id: 'EMP001',
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
    role: 'employee'
};
const mockBiostarData = {
    todayAttendance: {
        id: 'att-001',
        employee_id: 'EMP001',
        employee_name: 'John Doe',
        date: '2024-12-21',
        first_in: '2024-12-21T08:30:00Z',
        last_out: null,
        total_hours: 0,
        break_time: 0,
        overtime: 0,
        status: 'PRESENT',
        events: [],
        biostar_synced: true
    },
    devices: [
        {
            id: 'dev-001',
            name: 'Main Entrance',
            ip: '*************',
            port: 8080,
            status: 'ONLINE',
            type: 'Fingerprint Scanner',
            location: 'Main Building'
        },
        {
            id: 'dev-002',
            name: 'Back Entrance',
            ip: '*************',
            port: 8080,
            status: 'OFFLINE',
            type: 'Face Recognition',
            location: 'Back Building'
        }
    ],
    connected: true,
    loading: false,
    error: null,
    summary: {
        todayStatus: 'PRESENT',
        checkInTime: '2024-12-21T08:30:00Z',
        hoursWorked: 0,
        breakTime: 0,
        overtime: 0,
        weeklyHours: 32,
        monthlyAttendance: 20
    }
};
const TestWrapper = ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_AuthProvider.AuthProvider, {
        children: children
    });
// Import the mocked API
const { biostarApi } = require('@/lib/biostarApi');
describe('Employee BioStar View', ()=>{
    beforeEach(()=>{
        // Mock API responses
        biostarApi.getUserById.mockResolvedValue({
            id: 'user-001',
            user_id: 'EMP001',
            name: 'John Doe',
            email: '<EMAIL>',
            department: 'Engineering',
            position: 'Software Engineer',
            disabled: false,
            created: '2024-01-01T00:00:00Z',
            updated: '2024-12-21T00:00:00Z'
        });
        biostarApi.getEvents.mockResolvedValue({
            results: [],
            count: 0
        });
        mockUseBiostarAttendance.mockReturnValue({
            ...mockBiostarData,
            attendanceRecords: [],
            realtimeUpdates: [],
            refresh: jest.fn(),
            getAttendanceRange: jest.fn()
        });
    });
    afterEach(()=>{
        jest.clearAllMocks();
    });
    describe('StaffBiostarPage', ()=>{
        it('should render the BioStar profile page', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('BioStar Profile')).toBeInTheDocument();
                expect(_react1.screen.getByText('Manage your biometric data and view access history')).toBeInTheDocument();
            });
        });
        it('should show connection status', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('BioStar Connected')).toBeInTheDocument();
            });
        });
        it('should display profile status cards', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Profile Status')).toBeInTheDocument();
                expect(_react1.screen.getByText('Fingerprints')).toBeInTheDocument();
                expect(_react1.screen.getByText('Face Templates')).toBeInTheDocument();
                expect(_react1.screen.getByText('Devices')).toBeInTheDocument();
            });
        });
        it('should show tab navigation', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Biometric Profile')).toBeInTheDocument();
                expect(_react1.screen.getByText('Devices')).toBeInTheDocument();
                expect(_react1.screen.getByText('Access History')).toBeInTheDocument();
                expect(_react1.screen.getByText('Security')).toBeInTheDocument();
            });
        });
        it('should switch between tabs', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                const devicesTab = _react1.screen.getByText('Devices');
                _react1.fireEvent.click(devicesTab);
                expect(_react1.screen.getByText('System Status')).toBeInTheDocument();
            });
        });
        it('should show device information in devices tab', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                const devicesTab = _react1.screen.getByText('Devices');
                _react1.fireEvent.click(devicesTab);
                expect(_react1.screen.getByText('Available Devices')).toBeInTheDocument();
                expect(_react1.screen.getByText('Main Entrance')).toBeInTheDocument();
                expect(_react1.screen.getByText('Back Entrance')).toBeInTheDocument();
            });
        });
        it('should handle connection errors', async ()=>{
            mockUseBiostarAttendance.mockReturnValue({
                ...mockBiostarData,
                connected: false,
                error: 'Connection failed',
                attendanceRecords: [],
                realtimeUpdates: [],
                refresh: jest.fn(),
                getAttendanceRange: jest.fn()
            });
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Connection Error')).toBeInTheDocument();
                expect(_react1.screen.getByText('Connection failed')).toBeInTheDocument();
            });
        });
        it('should show refresh button and handle refresh', async ()=>{
            const mockRefresh = jest.fn();
            mockUseBiostarAttendance.mockReturnValue({
                ...mockBiostarData,
                attendanceRecords: [],
                realtimeUpdates: [],
                refresh: mockRefresh,
                getAttendanceRange: jest.fn()
            });
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                const refreshButton = _react1.screen.getByText('Refresh');
                _react1.fireEvent.click(refreshButton);
                expect(mockRefresh).toHaveBeenCalled();
            });
        });
    });
    describe('EmployeeBiostarWidget', ()=>{
        it('should render the BioStar widget', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('BioStar Profile')).toBeInTheDocument();
            });
        });
        it('should show connection status in widget', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Connected')).toBeInTheDocument();
            });
        });
        it('should display biometric stats', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Fingerprints')).toBeInTheDocument();
                expect(_react1.screen.getByText('Face Template')).toBeInTheDocument();
            });
        });
        it('should show today\'s attendance status', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Today\'s Status')).toBeInTheDocument();
                expect(_react1.screen.getByText('PRESENT')).toBeInTheDocument();
            });
        });
        it('should display recent access events', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001",
                    showFullDetails: true
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Recent Access')).toBeInTheDocument();
            });
        });
        it('should show device status summary', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Devices Online')).toBeInTheDocument();
                expect(_react1.screen.getByText('1/2')).toBeInTheDocument(); // 1 online out of 2 total
            });
        });
        it('should have link to full profile', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('View Full BioStar Profile →')).toBeInTheDocument();
            });
        });
        it('should handle disconnected state', async ()=>{
            mockUseBiostarAttendance.mockReturnValue({
                ...mockBiostarData,
                connected: false,
                attendanceRecords: [],
                realtimeUpdates: [],
                refresh: jest.fn(),
                getAttendanceRange: jest.fn()
            });
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Disconnected')).toBeInTheDocument();
            });
        });
        it('should show loading state', ()=>{
            mockUseBiostarAttendance.mockReturnValue({
                ...mockBiostarData,
                loading: true,
                attendanceRecords: [],
                realtimeUpdates: [],
                refresh: jest.fn(),
                getAttendanceRange: jest.fn()
            });
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            expect(_react1.screen.getByText('Loading BioStar data...')).toBeInTheDocument();
        });
    });
    describe('Integration Tests', ()=>{
        it('should integrate with useBiostarAttendance hook correctly', async ()=>{
            const mockGetAttendanceRange = jest.fn();
            mockUseBiostarAttendance.mockReturnValue({
                ...mockBiostarData,
                attendanceRecords: [],
                realtimeUpdates: [],
                refresh: jest.fn(),
                getAttendanceRange: mockGetAttendanceRange
            });
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                expect(mockUseBiostarAttendance).toHaveBeenCalledWith({
                    employeeId: undefined,
                    autoRefresh: true,
                    enableRealTime: false
                });
            });
        });
        it('should handle empty device list', async ()=>{
            mockUseBiostarAttendance.mockReturnValue({
                ...mockBiostarData,
                devices: [],
                attendanceRecords: [],
                realtimeUpdates: [],
                refresh: jest.fn(),
                getAttendanceRange: jest.fn()
            });
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                const devicesTab = _react1.screen.getByText('Devices');
                _react1.fireEvent.click(devicesTab);
                expect(_react1.screen.getByText('No Devices Found')).toBeInTheDocument();
            });
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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