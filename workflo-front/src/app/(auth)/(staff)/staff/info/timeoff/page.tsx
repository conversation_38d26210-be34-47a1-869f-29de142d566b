'use client';

import React, { useState, useEffect } from 'react';
import {
  Clock,
  Calendar,
  Play,
  Pause,
  Square,
  BarChart3,
  Download,
  Filter,
  MapPin,
  Activity,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Wifi,
  WifiOff,
  RefreshCw,
  FileText,
  Users,
  Fingerprint
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { cn, formatDate } from '@/lib/utils';
import { useBiostarAttendance } from '@/hooks/useBiostarAttendance';
import { AttendanceRecord } from '@/types';

const StaffTimeOffPage: React.FC = () => {
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [workingTime, setWorkingTime] = useState(0);
  const [selectedPeriod, setSelectedPeriod] = useState('this_month');
  const [refreshing, setRefreshing] = useState(false);

  // Use BioStar attendance hook
  const {
    todayAttendance,
    attendanceRecords,
    devices,
    loading,
    connected: biostarConnected,
    refresh,
    getAttendanceRange
  } = useBiostarAttendance({
    employeeId: user?.employee_id,
    autoRefresh: true,
    enableRealTime: false // Disable real-time for this page to reduce noise
  });

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
      if (isCheckedIn) {
        setWorkingTime(prev => prev + 1);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [isCheckedIn]);

  // Set check-in status based on today's data
  useEffect(() => {
    if (todayAttendance) {
      setIsCheckedIn(Boolean(todayAttendance.first_in && !todayAttendance.last_out));
    }
  }, [todayAttendance]);

  // Load attendance data for selected period
  useEffect(() => {
    const loadPeriodData = async () => {
      if (!user?.employee_id) return;

      const endDate = new Date().toISOString().split('T')[0];
      let startDate: string;

      switch (selectedPeriod) {
        case 'this_week':
          startDate = new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
          break;
        case 'this_month':
          startDate = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
          break;
        case 'last_month':
          const lastMonth = new Date();
          lastMonth.setMonth(lastMonth.getMonth() - 1);
          startDate = new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1).toISOString().split('T')[0];
          break;
        case 'this_year':
          startDate = new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0];
          break;
        default:
          startDate = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
      }

      // This will be handled by the hook's getAttendanceRange function
      // For now, we'll use the default monthly data from the hook
    };

    loadPeriodData();
  }, [selectedPeriod, user?.employee_id, getAttendanceRange]);

  const refreshData = async () => {
    setRefreshing(true);
    await refresh();
    setRefreshing(false);
  };

  // Calculate attendance statistics from BioStar data
  const attendanceStats = React.useMemo(() => {
    if (!attendanceRecords.length) {
      return {
        total_days: 0,
        present_days: 0,
        absent_days: 0,
        late_days: 0,
        total_hours: 0,
        overtime_hours: 0,
        average_check_in: '00:00',
        average_check_out: '00:00'
      };
    }

    const presentRecords = attendanceRecords.filter(r => r.status === 'PRESENT');
    const lateRecords = attendanceRecords.filter(r => r.status === 'LATE');
    const absentRecords = attendanceRecords.filter(r => r.status === 'ABSENT');

    const totalHours = attendanceRecords.reduce((sum, r) => sum + (r.total_hours || 0), 0);
    const overtimeHours = attendanceRecords.reduce((sum, r) => sum + (r.overtime || 0), 0);

    // Calculate average check-in and check-out times
    const checkInTimes = attendanceRecords
      .filter(r => r.first_in)
      .map(r => new Date(`1970-01-01T${r.first_in}`).getTime());

    const checkOutTimes = attendanceRecords
      .filter(r => r.last_out)
      .map(r => new Date(`1970-01-01T${r.last_out}`).getTime());

    const avgCheckIn = checkInTimes.length > 0
      ? new Date(checkInTimes.reduce((sum, time) => sum + time, 0) / checkInTimes.length)
        .toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      : '00:00';

    const avgCheckOut = checkOutTimes.length > 0
      ? new Date(checkOutTimes.reduce((sum, time) => sum + time, 0) / checkOutTimes.length)
        .toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      : '00:00';

    return {
      total_days: attendanceRecords.length,
      present_days: presentRecords.length,
      absent_days: absentRecords.length,
      late_days: lateRecords.length,
      total_hours: Math.round(totalHours * 10) / 10,
      overtime_hours: Math.round(overtimeHours * 10) / 10,
      average_check_in: avgCheckIn,
      average_check_out: avgCheckOut
    };
  }, [attendanceRecords]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present': return 'bg-green-100 text-green-800';
      case 'late': return 'bg-yellow-100 text-yellow-800';
      case 'absent': return 'bg-red-100 text-red-800';
      case 'overtime': return 'bg-blue-100 text-blue-800';
      case 'checked_in': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'late': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'absent': return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'overtime': return <TrendingUp className="h-4 w-4 text-blue-500" />;
      case 'checked_in': return <Activity className="h-4 w-4 text-green-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleCheckIn = () => {
    setIsCheckedIn(true);
    // TODO: Implement actual check-in API call
    console.log('Checking in at:', currentTime.toLocaleTimeString());
  };

  const handleCheckOut = () => {
    setIsCheckedIn(false);
    setWorkingTime(0);
    // TODO: Implement actual check-out API call
    console.log('Checking out at:', currentTime.toLocaleTimeString());
  };

  const handleBreakStart = () => {
    // TODO: Implement break start
    console.log('Starting break at:', currentTime.toLocaleTimeString());
  };

  const handleBreakEnd = () => {
    // TODO: Implement break end
    console.log('Ending break at:', currentTime.toLocaleTimeString());
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Time & Attendance</h1>
          <p className="text-gray-600 mt-1">
            Track your working hours and attendance records via BioStar 2
          </p>
          <div className="flex items-center mt-2 space-x-4">
            <div className="flex items-center space-x-1">
              {biostarConnected ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
              <span className="text-sm text-gray-600">
                BioStar {biostarConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            {devices.length > 0 && (
              <div className="flex items-center space-x-1">
                <MapPin className="h-4 w-4 text-blue-500" />
                <span className="text-sm text-gray-600">
                  {devices.filter(d => d.status === 'ONLINE').length}/{devices.length} devices online
                </span>
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="secondary"
            onClick={refreshData}
            disabled={refreshing}
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", refreshing && "animate-spin")} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
          >
            <option value="this_week">This Week</option>
            <option value="this_month">This Month</option>
            <option value="last_month">Last Month</option>
            <option value="this_year">This Year</option>
          </select>
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Current Status & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Time Clock */}
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Time Clock</h3>
            <div className="text-center">
              <div className="text-4xl font-bold text-gray-900 mb-2">
                {currentTime.toLocaleTimeString()}
              </div>
              <div className="text-sm text-gray-600 mb-6">
                {formatDate(new Date().toISOString(), 'EEEE, MMMM dd, yyyy')}
              </div>

              {isCheckedIn ? (
                <div className="space-y-4">
                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <Activity className="h-5 w-5 text-green-600" />
                      <span className="text-green-800 font-medium">Currently Working</span>
                    </div>
                    <div className="text-2xl font-bold text-green-800">
                      {formatTime(workingTime)}
                    </div>
                    <div className="text-sm text-green-600">
                      Checked in at {todayAttendance?.first_in ?
                        new Date(todayAttendance.first_in).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) :
                        'Unknown'
                      }
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <Button variant="secondary" onClick={handleBreakStart}>
                      <Pause className="h-4 w-4 mr-2" />
                      Start Break
                    </Button>
                    <Button variant="primary" onClick={handleCheckOut}>
                      <Square className="h-4 w-4 mr-2" />
                      Check Out
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <Clock className="h-5 w-5 text-gray-600" />
                      <span className="text-gray-800 font-medium">Not Checked In</span>
                    </div>
                    <div className="text-sm text-gray-600">
                      Ready to start your workday?
                    </div>
                  </div>

                  <Button variant="primary" onClick={handleCheckIn} className="w-full">
                    <Play className="h-4 w-4 mr-2" />
                    Check In
                  </Button>
                </div>
              )}
            </div>
          </div>
        </Card>

        {/* Today's Summary */}
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Today's Summary</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Check-in Time</span>
                <span className="font-medium text-gray-900">
                  {todayAttendance?.first_in ?
                    new Date(todayAttendance.first_in).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) :
                    'Not checked in'
                  }
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Check-out Time</span>
                <span className="font-medium text-gray-900">
                  {todayAttendance?.last_out ?
                    new Date(todayAttendance.last_out).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) :
                    'Not checked out'
                  }
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Break Duration</span>
                <span className="font-medium text-gray-900">
                  {todayAttendance?.break_time ?
                    `${Math.floor(todayAttendance.break_time / 60)}:${(todayAttendance.break_time % 60).toString().padStart(2, '0')}` :
                    'No breaks'
                  }
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Total Hours</span>
                <span className="font-medium text-gray-900">
                  {todayAttendance?.total_hours ?
                    `${todayAttendance.total_hours.toFixed(1)}h` :
                    '0.0h'
                  }
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Status</span>
                <div className="flex items-center">
                  {getStatusIcon(todayAttendance?.status || 'absent')}
                  <span className={cn(
                    'ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                    getStatusColor(todayAttendance?.status || 'absent')
                  )}>
                    {(todayAttendance?.status || 'ABSENT').replace('_', ' ')}
                  </span>
                </div>
              </div>

              {todayAttendance?.biostar_synced && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">BioStar Events</span>
                  <span className="font-medium text-gray-900">
                    {todayAttendance.events.length} recorded
                  </span>
                </div>
              )}
            </div>
          </div>
        </Card>
      </div>

      {/* Attendance Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Present Days</p>
                <p className="text-2xl font-bold text-gray-900">
                  {attendanceStats.present_days}/{attendanceStats.total_days}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Clock className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Hours</p>
                <p className="text-2xl font-bold text-gray-900">{attendanceStats.total_hours}h</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Overtime Hours</p>
                <p className="text-2xl font-bold text-gray-900">{attendanceStats.overtime_hours}h</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Late Days</p>
                <p className="text-2xl font-bold text-gray-900">{attendanceStats.late_days}</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Attendance */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Recent Attendance</h3>
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-400" />
              <select className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500">
                <option value="all">All Status</option>
                <option value="present">Present</option>
                <option value="late">Late</option>
                <option value="absent">Absent</option>
                <option value="overtime">Overtime</option>
              </select>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Check In
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Check Out
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Break Duration
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Hours
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                      Loading attendance data...
                    </td>
                  </tr>
                ) : attendanceRecords.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                      No attendance records found for the selected period
                    </td>
                  </tr>
                ) : (
                  attendanceRecords.slice(0, 10).map((record) => (
                    <tr key={record.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {formatDate(record.date)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {record.first_in ?
                          new Date(record.first_in).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) :
                          '-'
                        }
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {record.last_out ?
                          new Date(record.last_out).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) :
                          '-'
                        }
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {record.break_time ?
                          `${Math.floor(record.break_time / 60)}:${(record.break_time % 60).toString().padStart(2, '0')}` :
                          '-'
                        }
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {record.total_hours && record.total_hours > 0 ? `${record.total_hours.toFixed(1)}h` : '-'}
                        {record.overtime && record.overtime > 0 && (
                          <span className="ml-1 text-blue-600">(+{record.overtime.toFixed(1)}h OT)</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(record.status.toLowerCase())}
                          <span className={cn(
                            'ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                            getStatusColor(record.status.toLowerCase())
                          )}>
                            {record.status}
                          </span>
                          {record.biostar_synced && (
                            <div title="Synced with BioStar">
                              <Wifi className="ml-2 h-3 w-3 text-green-500" />
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default StaffTimeOffPage;
