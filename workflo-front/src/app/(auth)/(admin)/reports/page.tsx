'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { BarChart3, Download, Calendar, Users, TrendingUp, FileText, Filter, Eye, ArrowLeft } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input, { Select } from '@/components/ui/Input';
import { useAuth } from '@/providers/AuthProvider';
import { formatDate } from '@/lib/utils';

const ReportsPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState('last_30_days');

  // Mock reports data
  const reportCategories = [
    {
      id: 1,
      name: 'Employee Reports',
      description: 'Employee demographics, performance, and attendance',
      icon: Users,
      color: 'bg-blue-100 text-blue-600',
      reports: [
        { name: 'Employee Directory', description: 'Complete list of all employees', lastGenerated: '2024-01-15' },
        { name: 'Attendance Report', description: 'Employee attendance and time tracking', lastGenerated: '2024-01-14' },
        { name: 'Performance Summary', description: 'Employee performance metrics', lastGenerated: '2024-01-13' },
      ]
    },
    {
      id: 2,
      name: 'Leave Reports',
      description: 'Leave applications, balances, and trends',
      icon: Calendar,
      color: 'bg-green-100 text-green-600',
      reports: [
        { name: 'Leave Balance Report', description: 'Current leave balances for all employees', lastGenerated: '2024-01-15' },
        { name: 'Leave Applications', description: 'All leave applications and their status', lastGenerated: '2024-01-14' },
        { name: 'Leave Trends', description: 'Leave usage patterns and trends', lastGenerated: '2024-01-12' },
      ]
    },
    {
      id: 3,
      name: 'Financial Reports',
      description: 'Payroll, expenses, and budget reports',
      icon: TrendingUp,
      color: 'bg-purple-100 text-purple-600',
      reports: [
        { name: 'Payroll Summary', description: 'Monthly payroll breakdown', lastGenerated: '2024-01-15' },
        { name: 'Department Costs', description: 'Cost analysis by department', lastGenerated: '2024-01-14' },
        { name: 'Budget vs Actual', description: 'Budget comparison report', lastGenerated: '2024-01-13' },
      ]
    },
    {
      id: 4,
      name: 'Compliance Reports',
      description: 'Regulatory and compliance documentation',
      icon: FileText,
      color: 'bg-orange-100 text-orange-600',
      reports: [
        { name: 'Audit Trail', description: 'System access and changes log', lastGenerated: '2024-01-15' },
        { name: 'Training Compliance', description: 'Employee training completion status', lastGenerated: '2024-01-14' },
        { name: 'Policy Acknowledgments', description: 'Policy acceptance tracking', lastGenerated: '2024-01-13' },
      ]
    }
  ];

  const quickStats = [
    { label: 'Total Reports Generated', value: '1,247', change: '+12%', color: 'text-green-600' },
    { label: 'Active Report Schedules', value: '23', change: '+3', color: 'text-blue-600' },
    { label: 'Data Sources Connected', value: '8', change: '0', color: 'text-gray-600' },
    { label: 'Average Generation Time', value: '2.3s', change: '-0.5s', color: 'text-green-600' },
  ];

  const recentReports = [
    { name: 'Monthly Payroll Report', type: 'Financial', generatedBy: 'System', date: '2024-01-15', size: '2.4 MB' },
    { name: 'Employee Attendance', type: 'Employee', generatedBy: 'John Smith', date: '2024-01-15', size: '1.8 MB' },
    { name: 'Leave Balance Summary', type: 'Leave', generatedBy: 'Sarah Johnson', date: '2024-01-14', size: '856 KB' },
    { name: 'Training Compliance', type: 'Compliance', generatedBy: 'System', date: '2024-01-14', size: '1.2 MB' },
    { name: 'Department Performance', type: 'Employee', generatedBy: 'Mike Wilson', date: '2024-01-13', size: '3.1 MB' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <div className="flex items-center space-x-4 mb-2">
              <Link href="/manage">
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Management
                </Button>
              </Link>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
            <p className="text-gray-600 mt-1">
              Generate and manage organizational reports and insights
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex space-x-3">
            <Button variant="secondary">
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Report
            </Button>
            <Button variant="primary">
              <BarChart3 className="h-4 w-4 mr-2" />
              Custom Report
            </Button>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {quickStats.map((stat, index) => (
          <Card key={index} className="p-6">
            <div>
              <h3 className="text-sm font-medium text-gray-600">{stat.label}</h3>
              <div className="flex items-center mt-2">
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <span className={`ml-2 text-sm font-medium ${stat.color}`}>
                  {stat.change}
                </span>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { key: 'overview', label: 'Overview' },
              { key: 'categories', label: 'Report Categories' },
              { key: 'recent', label: 'Recent Reports' },
              { key: 'scheduled', label: 'Scheduled Reports' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.key
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Popular Reports */}
                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Popular Reports</h3>
                    <div className="space-y-3">
                      {recentReports.slice(0, 5).map((report, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">{report.name}</p>
                            <p className="text-sm text-gray-600">{report.type}</p>
                          </div>
                          <div className="flex space-x-2">
                            <Button variant="secondary" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="secondary" size="sm">
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </Card>

                {/* Quick Actions */}
                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                    <div className="space-y-3">
                      <Button variant="primary" className="w-full justify-start">
                        <Users className="h-4 w-4 mr-2" />
                        Generate Employee Report
                      </Button>
                      <Button variant="secondary" className="w-full justify-start">
                        <Calendar className="h-4 w-4 mr-2" />
                        Export Leave Data
                      </Button>
                      <Button variant="secondary" className="w-full justify-start">
                        <TrendingUp className="h-4 w-4 mr-2" />
                        Financial Summary
                      </Button>
                      <Button variant="secondary" className="w-full justify-start">
                        <FileText className="h-4 w-4 mr-2" />
                        Compliance Report
                      </Button>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}

          {activeTab === 'categories' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {reportCategories.map((category) => {
                  const IconComponent = category.icon;
                  return (
                    <Card key={category.id}>
                      <div className="p-6">
                        <div className="flex items-center mb-4">
                          <div className={`p-3 rounded-lg ${category.color}`}>
                            <IconComponent className="h-6 w-6" />
                          </div>
                          <div className="ml-4">
                            <h3 className="text-lg font-medium text-gray-900">{category.name}</h3>
                            <p className="text-sm text-gray-600">{category.description}</p>
                          </div>
                        </div>

                        <div className="space-y-3">
                          {category.reports.map((report, index) => (
                            <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                              <div>
                                <p className="font-medium text-gray-900">{report.name}</p>
                                <p className="text-sm text-gray-600">{report.description}</p>
                                <p className="text-xs text-gray-500">Last: {formatDate(report.lastGenerated)}</p>
                              </div>
                              <Button variant="secondary" size="sm">
                                Generate
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </div>
          )}

          {activeTab === 'recent' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Recent Reports</h3>
                <div className="flex space-x-3">
                  <Select
                    value={dateRange}
                    onChange={(e) => setDateRange(e.target.value)}
                    options={[
                      { value: 'last_7_days', label: 'Last 7 days' },
                      { value: 'last_30_days', label: 'Last 30 days' },
                      { value: 'last_90_days', label: 'Last 90 days' },
                    ]}
                  />
                  <Button variant="secondary">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                {recentReports.map((report, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 rounded-lg bg-blue-100">
                          <FileText className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{report.name}</h4>
                          <p className="text-sm text-gray-600">{report.type} • Generated by {report.generatedBy}</p>
                          <p className="text-sm text-gray-500">{formatDate(report.date)} • {report.size}</p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="secondary" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="secondary" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'scheduled' && (
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Scheduled Reports
              </h3>
              <p className="text-gray-600 mb-4">
                Set up automated report generation schedules.
              </p>
              <Button variant="primary">
                <Calendar className="h-4 w-4 mr-2" />
                Create Schedule
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReportsPage;
