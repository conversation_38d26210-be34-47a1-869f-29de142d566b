{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/components/ui/NotificationDropdown.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport {\n  <PERSON>,\n  <PERSON><PERSON>ing,\n  <PERSON>,\n  <PERSON><PERSON>he<PERSON>,\n  Trash2,\n  Settings,\n  X,\n  Wifi,\n  WifiOff,\n  AlertCircle,\n  Info,\n  Calendar,\n  DollarSign,\n  Star,\n  Building,\n  Wrench\n} from 'lucide-react';\nimport { useNotifications } from '@/providers/NotificationProvider';\nimport { NotificationData } from '@/lib/websocket';\nimport { cn, formatDate } from '@/lib/utils';\n\ninterface NotificationDropdownProps {\n  className?: string;\n}\n\nconst NotificationDropdown: React.FC<NotificationDropdownProps> = ({ className }) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [showSettings, setShowSettings] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  \n  const {\n    notifications,\n    unreadCount,\n    isConnected,\n    connectionState,\n    markAsRead,\n    markAllAsRead,\n    clearNotifications,\n    requestPermission\n  } = useNotifications();\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n        setShowSettings(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const getNotificationIcon = (type: NotificationData['type']) => {\n    switch (type) {\n      case 'leave_update':\n        return <Calendar className=\"h-4 w-4 text-blue-500\" />;\n      case 'payroll_update':\n        return <DollarSign className=\"h-4 w-4 text-green-500\" />;\n      case 'performance_update':\n        return <Star className=\"h-4 w-4 text-yellow-500\" />;\n      case 'company_announcement':\n        return <Building className=\"h-4 w-4 text-purple-500\" />;\n      case 'system_maintenance':\n        return <Wrench className=\"h-4 w-4 text-orange-500\" />;\n      case 'ticket_update':\n        return <Info className=\"h-4 w-4 text-indigo-500\" />;\n      default:\n        return <Bell className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getPriorityColor = (priority: NotificationData['priority']) => {\n    switch (priority) {\n      case 'high':\n        return 'border-l-red-500 bg-red-50';\n      case 'medium':\n        return 'border-l-yellow-500 bg-yellow-50';\n      case 'low':\n        return 'border-l-green-500 bg-green-50';\n      default:\n        return 'border-l-gray-500 bg-gray-50';\n    }\n  };\n\n  const handleNotificationClick = (notification: NotificationData) => {\n    if (!notification.read) {\n      markAsRead(notification.id);\n    }\n    // Handle navigation based on notification type\n    // You can implement routing logic here\n  };\n\n  const handleRequestPermission = async () => {\n    const permission = await requestPermission();\n    if (permission === 'granted') {\n      console.log('Notification permission granted');\n    }\n  };\n\n  return (\n    <div className={cn('relative', className)} ref={dropdownRef}>\n      {/* Notification Bell Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n      >\n        {unreadCount > 0 ? (\n          <BellRing className=\"h-5 w-5\" />\n        ) : (\n          <Bell className=\"h-5 w-5\" />\n        )}\n        \n        {/* Unread Count Badge */}\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n\n        {/* Connection Status Indicator */}\n        <div className={cn(\n          'absolute -bottom-1 -right-1 h-3 w-3 rounded-full border-2 border-white',\n          isConnected ? 'bg-green-500' : 'bg-red-500'\n        )} />\n      </button>\n\n      {/* Dropdown */}\n      {isOpen && (\n        <div className=\"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden\">\n          {/* Header */}\n          <div className=\"p-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Notifications</h3>\n                <div className=\"flex items-center space-x-1\">\n                  {isConnected ? (\n                    <Wifi className=\"h-4 w-4 text-green-500\" />\n                  ) : (\n                    <WifiOff className=\"h-4 w-4 text-red-500\" />\n                  )}\n                  <span className=\"text-xs text-gray-500 capitalize\">\n                    {connectionState}\n                  </span>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                {unreadCount > 0 && (\n                  <button\n                    onClick={markAllAsRead}\n                    className=\"text-xs text-orange-600 hover:text-orange-700\"\n                    title=\"Mark all as read\"\n                  >\n                    <CheckCheck className=\"h-4 w-4\" />\n                  </button>\n                )}\n                <button\n                  onClick={() => setShowSettings(!showSettings)}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                  title=\"Settings\"\n                >\n                  <Settings className=\"h-4 w-4\" />\n                </button>\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </div>\n            \n            {unreadCount > 0 && (\n              <p className=\"text-sm text-gray-600 mt-1\">\n                {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}\n              </p>\n            )}\n          </div>\n\n          {/* Settings Panel */}\n          {showSettings && (\n            <div className=\"p-4 border-b border-gray-200 bg-gray-50\">\n              <h4 className=\"text-sm font-medium text-gray-900 mb-3\">Notification Settings</h4>\n              <div className=\"space-y-2\">\n                <button\n                  onClick={handleRequestPermission}\n                  className=\"w-full text-left text-sm text-gray-700 hover:text-gray-900 py-1\"\n                >\n                  Enable Browser Notifications\n                </button>\n                <button\n                  onClick={clearNotifications}\n                  className=\"w-full text-left text-sm text-red-600 hover:text-red-700 py-1\"\n                >\n                  Clear All Notifications\n                </button>\n              </div>\n            </div>\n          )}\n\n          {/* Notifications List */}\n          <div className=\"max-h-64 overflow-y-auto\">\n            {notifications.length === 0 ? (\n              <div className=\"p-8 text-center\">\n                <Bell className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n                <p className=\"text-gray-500 text-sm\">No notifications yet</p>\n              </div>\n            ) : (\n              <div className=\"divide-y divide-gray-100\">\n                {notifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    onClick={() => handleNotificationClick(notification)}\n                    className={cn(\n                      'p-4 cursor-pointer hover:bg-gray-50 transition-colors border-l-4',\n                      getPriorityColor(notification.priority),\n                      !notification.read && 'bg-blue-50'\n                    )}\n                  >\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"flex-shrink-0 mt-1\">\n                        {getNotificationIcon(notification.type)}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-center justify-between\">\n                          <p className={cn(\n                            'text-sm font-medium truncate',\n                            notification.read ? 'text-gray-700' : 'text-gray-900'\n                          )}>\n                            {notification.title}\n                          </p>\n                          {!notification.read && (\n                            <div className=\"h-2 w-2 bg-blue-500 rounded-full flex-shrink-0 ml-2\" />\n                          )}\n                        </div>\n                        <p className={cn(\n                          'text-sm mt-1',\n                          notification.read ? 'text-gray-500' : 'text-gray-700'\n                        )}>\n                          {notification.message}\n                        </p>\n                        <div className=\"flex items-center justify-between mt-2\">\n                          <p className=\"text-xs text-gray-400\">\n                            {formatDate(notification.timestamp, 'MMM dd, HH:mm')}\n                          </p>\n                          <div className=\"flex items-center space-x-1\">\n                            {notification.priority === 'high' && (\n                              <AlertCircle className=\"h-3 w-3 text-red-500\" />\n                            )}\n                            {!notification.read && (\n                              <button\n                                onClick={(e) => {\n                                  e.stopPropagation();\n                                  markAsRead(notification.id);\n                                }}\n                                className=\"text-xs text-orange-600 hover:text-orange-700\"\n                                title=\"Mark as read\"\n                              >\n                                <Check className=\"h-3 w-3\" />\n                              </button>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Footer */}\n          {notifications.length > 0 && (\n            <div className=\"p-3 border-t border-gray-200 bg-gray-50\">\n              <button\n                onClick={() => {\n                  // Navigate to notifications page\n                  setIsOpen(false);\n                }}\n                className=\"w-full text-center text-sm text-orange-600 hover:text-orange-700 font-medium\"\n              >\n                View All Notifications\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default NotificationDropdown;\n"], "names": ["NotificationDropdown", "className", "isOpen", "setIsOpen", "useState", "showSettings", "setShowSettings", "dropdownRef", "useRef", "notifications", "unreadCount", "isConnected", "connectionState", "mark<PERSON><PERSON><PERSON>", "markAllAsRead", "clearNotifications", "requestPermission", "useNotifications", "useEffect", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "getNotificationIcon", "type", "Calendar", "DollarSign", "Star", "Building", "<PERSON><PERSON>", "Info", "Bell", "getPriorityColor", "priority", "handleNotificationClick", "notification", "read", "id", "handleRequestPermission", "permission", "console", "log", "div", "cn", "ref", "button", "onClick", "BellRing", "span", "h3", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title", "Check<PERSON>heck", "Settings", "X", "p", "h4", "length", "map", "message", "formatDate", "timestamp", "AlertCircle", "e", "stopPropagation", "Check"], "mappings": "AAAA;;;;;+BAwSA;;;eAAA;;;;+DAtSmD;6BAkB5C;sCAC0B;uBAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM/B,MAAMA,uBAA4D,CAAC,EAAEC,SAAS,EAAE;IAC9E,MAAM,CAACC,QAAQC,UAAU,GAAGC,IAAAA,eAAQ,EAAC;IACrC,MAAM,CAACC,cAAcC,gBAAgB,GAAGF,IAAAA,eAAQ,EAAC;IACjD,MAAMG,cAAcC,IAAAA,aAAM,EAAiB;IAE3C,MAAM,EACJC,aAAa,EACbC,WAAW,EACXC,WAAW,EACXC,eAAe,EACfC,UAAU,EACVC,aAAa,EACbC,kBAAkB,EAClBC,iBAAiB,EAClB,GAAGC,IAAAA,sCAAgB;IAEpB,uCAAuC;IACvCC,IAAAA,gBAAS,EAAC;QACR,MAAMC,qBAAqB,CAACC;YAC1B,IAAIb,YAAYc,OAAO,IAAI,CAACd,YAAYc,OAAO,CAACC,QAAQ,CAACF,MAAMG,MAAM,GAAW;gBAC9EpB,UAAU;gBACVG,gBAAgB;YAClB;QACF;QAEAkB,SAASC,gBAAgB,CAAC,aAAaN;QACvC,OAAO,IAAMK,SAASE,mBAAmB,CAAC,aAAaP;IACzD,GAAG,EAAE;IAEL,MAAMQ,sBAAsB,CAACC;QAC3B,OAAQA;YACN,KAAK;gBACH,qBAAO,qBAACC,qBAAQ;oBAAC5B,WAAU;;YAC7B,KAAK;gBACH,qBAAO,qBAAC6B,uBAAU;oBAAC7B,WAAU;;YAC/B,KAAK;gBACH,qBAAO,qBAAC8B,iBAAI;oBAAC9B,WAAU;;YACzB,KAAK;gBACH,qBAAO,qBAAC+B,qBAAQ;oBAAC/B,WAAU;;YAC7B,KAAK;gBACH,qBAAO,qBAACgC,mBAAM;oBAAChC,WAAU;;YAC3B,KAAK;gBACH,qBAAO,qBAACiC,iBAAI;oBAACjC,WAAU;;YACzB;gBACE,qBAAO,qBAACkC,iBAAI;oBAAClC,WAAU;;QAC3B;IACF;IAEA,MAAMmC,mBAAmB,CAACC;QACxB,OAAQA;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAMC,0BAA0B,CAACC;QAC/B,IAAI,CAACA,aAAaC,IAAI,EAAE;YACtB3B,WAAW0B,aAAaE,EAAE;QAC5B;IACA,+CAA+C;IAC/C,uCAAuC;IACzC;IAEA,MAAMC,0BAA0B;QAC9B,MAAMC,aAAa,MAAM3B;QACzB,IAAI2B,eAAe,WAAW;YAC5BC,QAAQC,GAAG,CAAC;QACd;IACF;IAEA,qBACE,sBAACC;QAAI7C,WAAW8C,IAAAA,SAAE,EAAC,YAAY9C;QAAY+C,KAAKzC;;0BAE9C,sBAAC0C;gBACCC,SAAS,IAAM/C,UAAU,CAACD;gBAC1BD,WAAU;;oBAETS,cAAc,kBACb,qBAACyC,qBAAQ;wBAAClD,WAAU;uCAEpB,qBAACkC,iBAAI;wBAAClC,WAAU;;oBAIjBS,cAAc,mBACb,qBAAC0C;wBAAKnD,WAAU;kCACbS,cAAc,KAAK,QAAQA;;kCAKhC,qBAACoC;wBAAI7C,WAAW8C,IAAAA,SAAE,EAChB,0EACApC,cAAc,iBAAiB;;;;YAKlCT,wBACC,sBAAC4C;gBAAI7C,WAAU;;kCAEb,sBAAC6C;wBAAI7C,WAAU;;0CACb,sBAAC6C;gCAAI7C,WAAU;;kDACb,sBAAC6C;wCAAI7C,WAAU;;0DACb,qBAACoD;gDAAGpD,WAAU;0DAAoC;;0DAClD,sBAAC6C;gDAAI7C,WAAU;;oDACZU,4BACC,qBAAC2C,iBAAI;wDAACrD,WAAU;uEAEhB,qBAACsD,oBAAO;wDAACtD,WAAU;;kEAErB,qBAACmD;wDAAKnD,WAAU;kEACbW;;;;;;kDAIP,sBAACkC;wCAAI7C,WAAU;;4CACZS,cAAc,mBACb,qBAACuC;gDACCC,SAASpC;gDACTb,WAAU;gDACVuD,OAAM;0DAEN,cAAA,qBAACC,uBAAU;oDAACxD,WAAU;;;0DAG1B,qBAACgD;gDACCC,SAAS,IAAM5C,gBAAgB,CAACD;gDAChCJ,WAAU;gDACVuD,OAAM;0DAEN,cAAA,qBAACE,qBAAQ;oDAACzD,WAAU;;;0DAEtB,qBAACgD;gDACCC,SAAS,IAAM/C,UAAU;gDACzBF,WAAU;0DAEV,cAAA,qBAAC0D,cAAC;oDAAC1D,WAAU;;;;;;;4BAKlBS,cAAc,mBACb,sBAACkD;gCAAE3D,WAAU;;oCACVS;oCAAY;oCAAqBA,gBAAgB,IAAI,MAAM;;;;;oBAMjEL,8BACC,sBAACyC;wBAAI7C,WAAU;;0CACb,qBAAC4D;gCAAG5D,WAAU;0CAAyC;;0CACvD,sBAAC6C;gCAAI7C,WAAU;;kDACb,qBAACgD;wCACCC,SAASR;wCACTzC,WAAU;kDACX;;kDAGD,qBAACgD;wCACCC,SAASnC;wCACTd,WAAU;kDACX;;;;;;kCAQP,qBAAC6C;wBAAI7C,WAAU;kCACZQ,cAAcqD,MAAM,KAAK,kBACxB,sBAAChB;4BAAI7C,WAAU;;8CACb,qBAACkC,iBAAI;oCAAClC,WAAU;;8CAChB,qBAAC2D;oCAAE3D,WAAU;8CAAwB;;;2CAGvC,qBAAC6C;4BAAI7C,WAAU;sCACZQ,cAAcsD,GAAG,CAAC,CAACxB,6BAClB,qBAACO;oCAECI,SAAS,IAAMZ,wBAAwBC;oCACvCtC,WAAW8C,IAAAA,SAAE,EACX,oEACAX,iBAAiBG,aAAaF,QAAQ,GACtC,CAACE,aAAaC,IAAI,IAAI;8CAGxB,cAAA,sBAACM;wCAAI7C,WAAU;;0DACb,qBAAC6C;gDAAI7C,WAAU;0DACZ0B,oBAAoBY,aAAaX,IAAI;;0DAExC,sBAACkB;gDAAI7C,WAAU;;kEACb,sBAAC6C;wDAAI7C,WAAU;;0EACb,qBAAC2D;gEAAE3D,WAAW8C,IAAAA,SAAE,EACd,gCACAR,aAAaC,IAAI,GAAG,kBAAkB;0EAErCD,aAAaiB,KAAK;;4DAEpB,CAACjB,aAAaC,IAAI,kBACjB,qBAACM;gEAAI7C,WAAU;;;;kEAGnB,qBAAC2D;wDAAE3D,WAAW8C,IAAAA,SAAE,EACd,gBACAR,aAAaC,IAAI,GAAG,kBAAkB;kEAErCD,aAAayB,OAAO;;kEAEvB,sBAAClB;wDAAI7C,WAAU;;0EACb,qBAAC2D;gEAAE3D,WAAU;0EACVgE,IAAAA,iBAAU,EAAC1B,aAAa2B,SAAS,EAAE;;0EAEtC,sBAACpB;gEAAI7C,WAAU;;oEACZsC,aAAaF,QAAQ,KAAK,wBACzB,qBAAC8B,wBAAW;wEAAClE,WAAU;;oEAExB,CAACsC,aAAaC,IAAI,kBACjB,qBAACS;wEACCC,SAAS,CAACkB;4EACRA,EAAEC,eAAe;4EACjBxD,WAAW0B,aAAaE,EAAE;wEAC5B;wEACAxC,WAAU;wEACVuD,OAAM;kFAEN,cAAA,qBAACc,kBAAK;4EAACrE,WAAU;;;;;;;;;;;mCA/CxBsC,aAAaE,EAAE;;;oBA6D7BhC,cAAcqD,MAAM,GAAG,mBACtB,qBAAChB;wBAAI7C,WAAU;kCACb,cAAA,qBAACgD;4BACCC,SAAS;gCACP,iCAAiC;gCACjC/C,UAAU;4BACZ;4BACAF,WAAU;sCACX;;;;;;;AASf;MAEA,WAAeD"}