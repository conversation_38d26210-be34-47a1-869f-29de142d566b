'use client';

import React, { useState } from 'react';
import {
  DollarSign,
  FileText,
  Calculator,
  TrendingUp,
  Users,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  BarChart3,
  PieChart,
  CreditCard,
  Receipt,
  Banknote,
  Shield
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useToast } from '@/hooks/useToast';
import Link from 'next/link';

interface ManagementStats {
  pendingPayroll: number;
  completedPayslips: number;
  pendingApprovals: number;
  monthlyTotal: number;
}

const AccountantManagePage: React.FC = () => {
  const { success, info } = useToast();
  const [stats] = useState<ManagementStats>({
    pendingPayroll: 3,
    completedPayslips: 142,
    pendingApprovals: 8,
    monthlyTotal: 2450000
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const managementSections = [
    {
      title: 'Payroll Management',
      description: 'Process payroll, manage salaries, and handle deductions',
      icon: DollarSign,
      href: '/accountant/manage/payroll',
      color: 'bg-green-100 text-green-600',
      stats: [
        { label: 'Pending', value: stats.pendingPayroll },
        { label: 'This Month', value: formatCurrency(stats.monthlyTotal) }
      ],
      actions: [
        { label: 'Process Payroll', variant: 'primary' as const },
        { label: 'View History', variant: 'outline' as const }
      ]
    },
    {
      title: 'Payslip Management',
      description: 'Generate, distribute, and manage employee payslips',
      icon: FileText,
      href: '/accountant/manage/payslips',
      color: 'bg-blue-100 text-blue-600',
      stats: [
        { label: 'Generated', value: stats.completedPayslips },
        { label: 'Pending', value: 12 }
      ],
      actions: [
        { label: 'Generate Payslips', variant: 'primary' as const },
        { label: 'Bulk Actions', variant: 'outline' as const }
      ]
    },
    {
      title: 'Tax Management',
      description: 'Handle tax calculations, deductions, and compliance',
      icon: Calculator,
      href: '/accountant/manage/taxes',
      color: 'bg-red-100 text-red-600',
      stats: [
        { label: 'Tax Rate', value: '30%' },
        { label: 'Deductions', value: formatCurrency(735000) }
      ],
      actions: [
        { label: 'Calculate Taxes', variant: 'primary' as const },
        { label: 'Tax Reports', variant: 'outline' as const }
      ]
    },
    {
      title: 'Financial Reports',
      description: 'Generate comprehensive financial and payroll reports',
      icon: TrendingUp,
      href: '/accountant/manage/reports',
      color: 'bg-purple-100 text-purple-600',
      stats: [
        { label: 'Reports', value: 24 },
        { label: 'Scheduled', value: 6 }
      ],
      actions: [
        { label: 'Generate Report', variant: 'primary' as const },
        { label: 'View All', variant: 'outline' as const }
      ]
    }
  ];

  const quickActions = [
    {
      title: 'Process Monthly Payroll',
      description: 'Run the complete payroll process for all employees',
      icon: DollarSign,
      color: 'bg-green-500',
      action: () => info('Payroll processing initiated')
    },
    {
      title: 'Generate All Payslips',
      description: 'Create payslips for all employees for current period',
      icon: FileText,
      color: 'bg-blue-500',
      action: () => info('Payslip generation started')
    },
    {
      title: 'Tax Compliance Check',
      description: 'Verify all tax calculations and compliance requirements',
      icon: Shield,
      color: 'bg-red-500',
      action: () => info('Tax compliance check initiated')
    },
    {
      title: 'Financial Summary',
      description: 'Generate comprehensive financial summary report',
      icon: BarChart3,
      color: 'bg-purple-500',
      action: () => info('Financial summary being generated')
    }
  ];

  const recentActivities = [
    {
      id: '1',
      type: 'payroll',
      description: 'November 2024 payroll processed successfully',
      timestamp: '2 hours ago',
      status: 'completed'
    },
    {
      id: '2',
      type: 'payslip',
      description: '142 payslips generated and distributed',
      timestamp: '4 hours ago',
      status: 'completed'
    },
    {
      id: '3',
      type: 'approval',
      description: '8 overtime payments pending approval',
      timestamp: '6 hours ago',
      status: 'pending'
    },
    {
      id: '4',
      type: 'report',
      description: 'Monthly financial report generated',
      timestamp: '1 day ago',
      status: 'completed'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Financial Management</h1>
          <p className="text-gray-600">Manage payroll, payslips, and financial operations</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <BarChart3 className="h-4 w-4 mr-2" />
            View Analytics
          </Button>
          <Button variant="primary">
            <DollarSign className="h-4 w-4 mr-2" />
            Process Payroll
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Payroll</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pendingPayroll}</p>
            </div>
            <Clock className="h-8 w-8 text-yellow-500" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed Payslips</p>
              <p className="text-2xl font-bold text-gray-900">{stats.completedPayslips}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Approvals</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pendingApprovals}</p>
            </div>
            <AlertCircle className="h-8 w-8 text-orange-500" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Monthly Total</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.monthlyTotal)}</p>
            </div>
            <TrendingUp className="h-8 w-8 text-blue-500" />
          </div>
        </Card>
      </div>

      {/* Management Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {managementSections.map((section) => (
          <Card key={section.title} className="p-6">
            <div className="flex items-start space-x-4 mb-4">
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${section.color}`}>
                <section.icon className="h-6 w-6" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                <p className="text-gray-600 text-sm">{section.description}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              {section.stats.map((stat, index) => (
                <div key={index} className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-lg font-bold text-gray-900">{stat.value}</div>
                  <div className="text-xs text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>

            <div className="flex space-x-2">
              {section.actions.map((action, index) => (
                <Link key={index} href={section.href}>
                  <Button variant={action.variant} size="sm" className="flex-1">
                    {action.label}
                  </Button>
                </Link>
              ))}
            </div>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className="p-4 text-left rounded-lg border border-gray-200 hover:border-orange-300 hover:shadow-md transition-all group"
            >
              <div className={`w-10 h-10 ${action.color} rounded-lg flex items-center justify-center mb-3`}>
                <action.icon className="h-5 w-5 text-white" />
              </div>
              <h4 className="font-medium text-gray-900 group-hover:text-orange-600 transition-colors">
                {action.title}
              </h4>
              <p className="text-sm text-gray-600 mt-1">{action.description}</p>
            </button>
          ))}
        </div>
      </Card>

      {/* Recent Activities */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
        <div className="space-y-3">
          {recentActivities.map((activity) => (
            <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className={`w-2 h-2 rounded-full ${
                  activity.status === 'completed' ? 'bg-green-500' : 'bg-yellow-500'
                }`} />
                <span className="text-sm text-gray-900">{activity.description}</span>
              </div>
              <span className="text-xs text-gray-500">{activity.timestamp}</span>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default AccountantManagePage;
