'use client';

import React from 'react';
import {
  Briefcase,
  Building,
  Calendar,
  User,
  Clock,
  MapPin,
  Award,
  TrendingUp,
  Users,
  Target,
  CheckCircle
} from 'lucide-react';
import Card from '@/components/ui/Card';
import { useAuth } from '@/providers/AuthProvider';
import { cn, formatDate } from '@/lib/utils';

const StaffEmploymentPage: React.FC = () => {
  const { user } = useAuth();

  // Mock employment data - in real app, this would come from API
  const employmentData = {
    employee_id: user?.employee_id || 'EMP001',
    position: 'Senior Software Engineer',
    department: 'Engineering',
    hire_date: '2022-03-15',
    employment_type: 'Full-time',
    work_location: 'Hybrid',
    manager: '<PERSON>',
    status: 'Active',
    probation_end_date: '2022-09-15',
    contract_end_date: null,
    work_schedule: 'Monday - Friday, 9:00 AM - 5:00 PM',
    office_location: 'Nairobi, Kenya'
  };

  const employmentHistory = [
    {
      id: 1,
      position: 'Senior Software Engineer',
      department: 'Engineering',
      start_date: '2022-03-15',
      end_date: null,
      status: 'Current',
      type: 'Promotion'
    },
    {
      id: 2,
      position: 'Software Engineer',
      department: 'Engineering',
      start_date: '2021-01-10',
      end_date: '2022-03-14',
      status: 'Completed',
      type: 'Initial Hire'
    }
  ];

  const achievements = [
    {
      id: 1,
      title: 'Employee of the Month',
      date: '2024-10-01',
      description: 'Outstanding performance in Q3 project delivery',
      icon: Award,
      color: 'text-yellow-500'
    },
    {
      id: 2,
      title: 'Team Leadership Recognition',
      date: '2024-08-15',
      description: 'Successfully led the mobile app development team',
      icon: Users,
      color: 'text-blue-500'
    },
    {
      id: 3,
      title: 'Innovation Award',
      date: '2024-06-01',
      description: 'Implemented new CI/CD pipeline reducing deployment time by 50%',
      icon: Target,
      color: 'text-green-500'
    }
  ];

  const calculateTenure = (hireDate: string) => {
    const hire = new Date(hireDate);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - hire.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);

    if (years > 0) {
      return `${years} year${years > 1 ? 's' : ''}, ${months} month${months > 1 ? 's' : ''}`;
    }
    return `${months} month${months > 1 ? 's' : ''}`;
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'probation': return 'bg-yellow-100 text-yellow-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Employment Information</h1>
        <p className="text-gray-600 mt-1">
          View your job details, employment history, and achievements
        </p>
      </div>

      {/* Current Employment Overview */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900">Current Position</h3>
            <span className={cn(
              'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
              getStatusColor(employmentData.status)
            )}>
              {employmentData.status}
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Briefcase className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Position</p>
                <p className="font-medium text-gray-900">{employmentData.position}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Building className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Department</p>
                <p className="font-medium text-gray-900">{employmentData.department}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <User className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Manager</p>
                <p className="font-medium text-gray-900">{employmentData.manager}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Calendar className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Hire Date</p>
                <p className="font-medium text-gray-900">{formatDate(employmentData.hire_date)}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="p-2 bg-indigo-100 rounded-lg">
                <Clock className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Tenure</p>
                <p className="font-medium text-gray-900">{calculateTenure(employmentData.hire_date)}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="p-2 bg-pink-100 rounded-lg">
                <MapPin className="h-5 w-5 text-pink-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Work Location</p>
                <p className="font-medium text-gray-900">{employmentData.work_location}</p>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Employment Details */}
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Employment Details</h3>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Employee ID</span>
                <span className="text-sm font-medium text-gray-900">{employmentData.employee_id}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Employment Type</span>
                <span className="text-sm font-medium text-gray-900">{employmentData.employment_type}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Work Schedule</span>
                <span className="text-sm font-medium text-gray-900">{employmentData.work_schedule}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Office Location</span>
                <span className="text-sm font-medium text-gray-900">{employmentData.office_location}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Probation End Date</span>
                <span className="text-sm font-medium text-gray-900">
                  {employmentData.probation_end_date ? formatDate(employmentData.probation_end_date) : 'Completed'}
                </span>
              </div>
              
              {employmentData.contract_end_date && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Contract End Date</span>
                  <span className="text-sm font-medium text-gray-900">
                    {formatDate(employmentData.contract_end_date)}
                  </span>
                </div>
              )}
            </div>
          </div>
        </Card>

        {/* Recent Achievements */}
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Achievements</h3>
            <div className="space-y-4">
              {achievements.map((achievement) => {
                const Icon = achievement.icon;
                return (
                  <div key={achievement.id} className="flex items-start space-x-3">
                    <div className={cn('p-2 rounded-lg bg-gray-100', achievement.color)}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">{achievement.title}</h4>
                      <p className="text-xs text-gray-500 mt-1">{achievement.description}</p>
                      <p className="text-xs text-gray-400 mt-1">{formatDate(achievement.date)}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </Card>
      </div>

      {/* Employment History */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Employment History</h3>
          <div className="space-y-4">
            {employmentHistory.map((position, index) => (
              <div key={position.id} className="border-l-4 border-orange-500 pl-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">{position.position}</h4>
                    <p className="text-sm text-gray-600">{position.department}</p>
                  </div>
                  <div className="text-right">
                    <span className={cn(
                      'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                      position.status === 'Current' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    )}>
                      {position.status}
                    </span>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  <span>{formatDate(position.start_date)}</span>
                  {position.end_date && (
                    <>
                      <span className="mx-2">-</span>
                      <span>{formatDate(position.end_date)}</span>
                    </>
                  )}
                  {!position.end_date && (
                    <>
                      <span className="mx-2">-</span>
                      <span>Present</span>
                    </>
                  )}
                  <span className="ml-4 text-gray-400">({position.type})</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default StaffEmploymentPage;
