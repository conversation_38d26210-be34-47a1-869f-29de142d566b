14fb56b4a66fb1b9396a42702c06ee82
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _lucidereact = require("lucide-react");
const _Card = /*#__PURE__*/ _interop_require_default(require("../../../../../../components/ui/Card"));
const _Button = /*#__PURE__*/ _interop_require_default(require("../../../../../../components/ui/Button"));
const _AuthProvider = require("../../../../../../providers/AuthProvider");
const _utils = require("../../../../../../lib/utils");
const _useBiostarAttendance = require("../../../../../../hooks/useBiostarAttendance");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const StaffBiostarPage = ()=>{
    const { user } = (0, _AuthProvider.useAuth)();
    const [activeTab, setActiveTab] = (0, _react.useState)('profile');
    const [biometricProfile, setBiometricProfile] = (0, _react.useState)(null);
    const [recentEvents, setRecentEvents] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(true);
    const [refreshing, setRefreshing] = (0, _react.useState)(false);
    // Use BioStar attendance hook for device and connectivity data
    const { devices, connected: biostarConnected, error: connectionError, refresh: refreshAttendance } = (0, _useBiostarAttendance.useBiostarAttendance)({
        employeeId: user?.employee_id,
        autoRefresh: true,
        enableRealTime: false
    });
    (0, _react.useEffect)(()=>{
        loadBiometricData();
    }, [
        user?.employee_id
    ]);
    const loadBiometricData = async ()=>{
        if (!user?.employee_id) {
            setLoading(false);
            return;
        }
        try {
            setLoading(true);
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, 500));
            // Use mock data instead of API calls
            const mockBiostarUser = {
                id: 'user-001',
                user_id: user.employee_id,
                name: `${user.first_name} ${user.last_name}`,
                email: user.email,
                department: 'Engineering',
                position: 'Software Engineer',
                disabled: false,
                created: '2024-01-01T00:00:00Z',
                updated: new Date().toISOString()
            };
            // Mock recent access events
            const mockEvents = Array.from({
                length: 8
            }, (_, i)=>({
                    id: `event-${i}`,
                    user_id: user.employee_id,
                    device_id: `dev-${i % 3 + 1}`,
                    event_type: i % 2 === 0 ? 'ENTRY' : 'EXIT',
                    datetime: new Date(Date.now() - i * 2 * 60 * 60 * 1000).toISOString(),
                    user_name: `${user.first_name} ${user.last_name}`,
                    device_name: [
                        'Main Entrance',
                        'Office Floor',
                        'Back Entrance'
                    ][i % 3]
                }));
            setBiometricProfile({
                user: mockBiostarUser,
                enrolledFingerprints: 2,
                enrolledFaces: 1,
                lastSync: new Date().toISOString(),
                accessLevel: 'Standard Employee',
                isActive: true
            });
            setRecentEvents(mockEvents);
        } catch (error) {
            console.error('Failed to load biometric data:', error);
            // Set default profile even on error
            setBiometricProfile({
                user: null,
                enrolledFingerprints: 2,
                enrolledFaces: 1,
                lastSync: new Date().toISOString(),
                accessLevel: 'Standard Employee',
                isActive: true
            });
            setRecentEvents([]);
        } finally{
            setLoading(false);
        }
    };
    const handleRefresh = async ()=>{
        setRefreshing(true);
        await Promise.all([
            loadBiometricData(),
            refreshAttendance()
        ]);
        setRefreshing(false);
    };
    const tabs = [
        {
            id: 'profile',
            label: 'Biometric Profile',
            icon: _lucidereact.User
        },
        {
            id: 'devices',
            label: 'Devices',
            icon: _lucidereact.Monitor
        },
        {
            id: 'history',
            label: 'Access History',
            icon: _lucidereact.History
        },
        {
            id: 'security',
            label: 'Security',
            icon: _lucidereact.Shield
        }
    ];
    if (loading) {
        return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            className: "flex items-center justify-center p-8",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.RefreshCw, {
                    className: "h-8 w-8 animate-spin text-gray-400"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                    className: "ml-2 text-gray-600",
                    children: "Loading biometric data..."
                })
            ]
        });
    }
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                                className: "text-2xl font-bold text-gray-900",
                                children: "BioStar Profile"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                className: "text-gray-600 mt-1",
                                children: "Manage your biometric data and view access history"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "flex items-center space-x-3",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center space-x-2",
                                children: [
                                    biostarConnected ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Wifi, {
                                        className: "h-4 w-4 text-green-500"
                                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.WifiOff, {
                                        className: "h-4 w-4 text-red-500"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                                        className: "text-sm text-gray-600",
                                        children: [
                                            "BioStar ",
                                            biostarConnected ? 'Connected' : 'Disconnected'
                                        ]
                                    })
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Button.default, {
                                variant: "secondary",
                                onClick: handleRefresh,
                                disabled: refreshing,
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.RefreshCw, {
                                        className: (0, _utils.cn)("h-4 w-4 mr-2", refreshing && "animate-spin")
                                    }),
                                    refreshing ? 'Refreshing...' : 'Refresh'
                                ]
                            })
                        ]
                    })
                ]
            }),
            connectionError && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "bg-red-50 border border-red-200 rounded-lg p-4",
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "flex items-center",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertCircle, {
                            className: "h-5 w-5 text-red-500 mr-2"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                    className: "text-sm font-medium text-red-800",
                                    children: "Connection Error"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                    className: "text-sm text-red-700 mt-1",
                                    children: connectionError
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "grid grid-cols-1 md:grid-cols-4 gap-6",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-blue-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.User, {
                                            className: "h-6 w-6 text-blue-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Profile Status"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: biometricProfile?.isActive ? 'Active' : 'Inactive'
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-green-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Fingerprint, {
                                            className: "h-6 w-6 text-green-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Fingerprints"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: biometricProfile?.enrolledFingerprints || 0
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-purple-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Eye, {
                                            className: "h-6 w-6 text-purple-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Face Templates"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: biometricProfile?.enrolledFaces || 0
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-orange-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Monitor, {
                                            className: "h-6 w-6 text-orange-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Devices"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: [
                                                    devices.filter((d)=>d.status === 'ONLINE').length,
                                                    "/",
                                                    devices.length
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "border-b border-gray-200",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("nav", {
                    className: "-mb-px flex space-x-8",
                    children: tabs.map((tab)=>{
                        const Icon = tab.icon;
                        return /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                            onClick: ()=>setActiveTab(tab.id),
                            className: (0, _utils.cn)('flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm', activeTab === tab.id ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(Icon, {
                                    className: "h-4 w-4"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                    children: tab.label
                                })
                            ]
                        }, tab.id);
                    })
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "mt-6",
                children: [
                    activeTab === 'profile' && /*#__PURE__*/ (0, _jsxruntime.jsx)(BiometricProfileTab, {
                        profile: biometricProfile,
                        user: user
                    }),
                    activeTab === 'devices' && /*#__PURE__*/ (0, _jsxruntime.jsx)(DevicesTab, {
                        devices: devices,
                        connected: biostarConnected
                    }),
                    activeTab === 'history' && /*#__PURE__*/ (0, _jsxruntime.jsx)(AccessHistoryTab, {
                        events: recentEvents,
                        employeeId: user?.employee_id
                    }),
                    activeTab === 'security' && /*#__PURE__*/ (0, _jsxruntime.jsx)(SecurityTab, {
                        profile: biometricProfile,
                        user: user
                    })
                ]
            })
        ]
    });
};
const BiometricProfileTab = ({ profile, user })=>{
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "grid grid-cols-1 lg:grid-cols-2 gap-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Profile Information"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "space-y-4",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Employee ID"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: user?.employee_id || 'N/A'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Full Name"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.user?.name || `${user?.first_name} ${user?.last_name}`
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Email"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.user?.email || user?.email
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Department"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.user?.department || 'N/A'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Position"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.user?.position || 'N/A'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Access Level"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.accessLevel || 'Standard'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Status"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: (0, _utils.cn)('inline-flex items-center px-2 py-1 rounded-full text-xs font-medium', profile?.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),
                                            children: profile?.isActive ? 'Active' : 'Inactive'
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Biometric Enrollment"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "space-y-6",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-3",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: "p-2 bg-blue-100 rounded-lg",
                                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Fingerprint, {
                                                        className: "h-5 w-5 text-blue-600"
                                                    })
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                            className: "font-medium text-gray-900",
                                                            children: "Fingerprints"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                            className: "text-sm text-gray-600",
                                                            children: [
                                                                profile?.enrolledFingerprints || 0,
                                                                " enrolled"
                                                            ]
                                                        })
                                                    ]
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "text-right",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "text-2xl font-bold text-gray-900",
                                                    children: [
                                                        profile?.enrolledFingerprints || 0,
                                                        "/10"
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: "text-xs text-gray-500",
                                                    children: "Max: 10"
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-3",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: "p-2 bg-purple-100 rounded-lg",
                                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Eye, {
                                                        className: "h-5 w-5 text-purple-600"
                                                    })
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                            className: "font-medium text-gray-900",
                                                            children: "Face Templates"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                            className: "text-sm text-gray-600",
                                                            children: [
                                                                profile?.enrolledFaces || 0,
                                                                " enrolled"
                                                            ]
                                                        })
                                                    ]
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "text-right",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "text-2xl font-bold text-gray-900",
                                                    children: [
                                                        profile?.enrolledFaces || 0,
                                                        "/5"
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: "text-xs text-gray-500",
                                                    children: "Max: 5"
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "pt-4 border-t border-gray-200",
                                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "flex items-center justify-between",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                className: "text-sm text-gray-600",
                                                children: "Last Sync"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                className: "font-medium",
                                                children: profile?.lastSync ? (0, _utils.formatDate)(profile.lastSync, 'MMM dd, yyyy HH:mm') : 'Never'
                                            })
                                        ]
                                    })
                                })
                            ]
                        })
                    ]
                })
            })
        ]
    });
};
const DevicesTab = ({ devices, connected })=>{
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "p-6",
                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center space-x-3",
                                children: [
                                    connected ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Wifi, {
                                        className: "h-6 w-6 text-green-500"
                                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.WifiOff, {
                                        className: "h-6 w-6 text-red-500"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                                className: "text-lg font-medium text-gray-900",
                                                children: "System Status"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                className: "text-sm text-gray-600",
                                                children: [
                                                    "BioStar system is ",
                                                    connected ? 'online and accessible' : 'offline or unreachable'
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                className: (0, _utils.cn)('inline-flex items-center px-3 py-1 rounded-full text-sm font-medium', connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),
                                children: connected ? 'Connected' : 'Disconnected'
                            })
                        ]
                    })
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Available Devices"
                        }),
                        devices.length === 0 ? /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "text-center py-8",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Monitor, {
                                    className: "h-12 w-12 text-gray-400 mx-auto mb-4"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                    className: "text-lg font-medium text-gray-900 mb-2",
                                    children: "No Devices Found"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                    className: "text-gray-600",
                                    children: "No biometric devices are currently available."
                                })
                            ]
                        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                            children: devices.map((device)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "border border-gray-200 rounded-lg p-4",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center justify-between mb-3",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex items-center space-x-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Monitor, {
                                                            className: "h-5 w-5 text-gray-600"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                            className: "font-medium text-gray-900",
                                                            children: device.name
                                                        })
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                    className: (0, _utils.cn)('inline-flex items-center px-2 py-1 rounded-full text-xs font-medium', device.status === 'ONLINE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),
                                                    children: device.status
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "space-y-2 text-sm",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex justify-between",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "text-gray-600",
                                                            children: "Location"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "font-medium",
                                                            children: device.location || 'Unknown'
                                                        })
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex justify-between",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "text-gray-600",
                                                            children: "IP Address"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "font-medium",
                                                            children: device.ip
                                                        })
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex justify-between",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "text-gray-600",
                                                            children: "Type"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "font-medium",
                                                            children: device.type
                                                        })
                                                    ]
                                                })
                                            ]
                                        })
                                    ]
                                }, device.id))
                        })
                    ]
                })
            })
        ]
    });
};
const AccessHistoryTab = ({ events })=>{
    const [selectedPeriod, setSelectedPeriod] = (0, _react.useState)('7_days');
    const [filteredEvents, setFilteredEvents] = (0, _react.useState)(events);
    (0, _react.useEffect)(()=>{
        // Filter events based on selected period
        const now = new Date();
        let startDate;
        switch(selectedPeriod){
            case '1_day':
                startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case '7_days':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30_days':
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            default:
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        }
        const filtered = events.filter((event)=>new Date(event.datetime) >= startDate);
        setFilteredEvents(filtered);
    }, [
        events,
        selectedPeriod
    ]);
    const getEventTypeIcon = (eventType)=>{
        switch(eventType){
            case 'ENTRY':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                    className: "h-4 w-4 text-green-500"
                });
            case 'EXIT':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Activity, {
                    className: "h-4 w-4 text-blue-500"
                });
            case 'DENIED':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertCircle, {
                    className: "h-4 w-4 text-red-500"
                });
            default:
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                    className: "h-4 w-4 text-gray-500"
                });
        }
    };
    const getEventTypeColor = (eventType)=>{
        switch(eventType){
            case 'ENTRY':
                return 'bg-green-100 text-green-800';
            case 'EXIT':
                return 'bg-blue-100 text-blue-800';
            case 'DENIED':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "p-6",
                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                className: "text-lg font-medium text-gray-900",
                                children: "Access History"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("select", {
                                value: selectedPeriod,
                                onChange: (e)=>setSelectedPeriod(e.target.value),
                                className: "px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                        value: "1_day",
                                        children: "Last 24 Hours"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                        value: "7_days",
                                        children: "Last 7 Days"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                        value: "30_days",
                                        children: "Last 30 Days"
                                    })
                                ]
                            })
                        ]
                    })
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "p-6",
                    children: filteredEvents.length === 0 ? /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "text-center py-8",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.History, {
                                className: "h-12 w-12 text-gray-400 mx-auto mb-4"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                className: "text-lg font-medium text-gray-900 mb-2",
                                children: "No Access Events"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                className: "text-gray-600",
                                children: "No access events found for the selected period."
                            })
                        ]
                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "space-y-4",
                        children: filteredEvents.map((event, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                className: "flex items-center justify-between p-4 border border-gray-200 rounded-lg",
                                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center space-x-4",
                                    children: [
                                        getEventTypeIcon(event.event_type),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex items-center space-x-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "font-medium text-gray-900",
                                                            children: event.event_type === 'ENTRY' ? 'Check In' : event.event_type === 'EXIT' ? 'Check Out' : 'Access Denied'
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: (0, _utils.cn)('inline-flex items-center px-2 py-1 rounded-full text-xs font-medium', getEventTypeColor(event.event_type)),
                                                            children: event.event_type
                                                        })
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex items-center space-x-4 text-sm text-gray-600 mt-1",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                            className: "flex items-center space-x-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                                                                    className: "h-3 w-3"
                                                                }),
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                                    children: (0, _utils.formatDate)(event.datetime, 'MMM dd, yyyy HH:mm:ss')
                                                                })
                                                            ]
                                                        }),
                                                        event.device_name && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                            className: "flex items-center space-x-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Monitor, {
                                                                    className: "h-3 w-3"
                                                                }),
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                                    children: event.device_name
                                                                })
                                                            ]
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                            className: "flex items-center space-x-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.MapPin, {
                                                                    className: "h-3 w-3"
                                                                }),
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                                    children: "Main Office"
                                                                })
                                                            ]
                                                        })
                                                    ]
                                                })
                                            ]
                                        })
                                    ]
                                })
                            }, `${event.id}-${index}`))
                    })
                })
            })
        ]
    });
};
const SecurityTab = ({ profile })=>{
    const [showSecuritySettings, setShowSecuritySettings] = (0, _react.useState)(false);
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Security Overview"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "grid grid-cols-1 md:grid-cols-3 gap-6",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                            className: "p-3 bg-green-100 rounded-full w-12 h-12 mx-auto mb-2",
                                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Shield, {
                                                className: "h-6 w-6 text-green-600"
                                            })
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                            className: "font-medium text-gray-900",
                                            children: "Account Status"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            className: "text-sm text-gray-600 mt-1",
                                            children: profile?.isActive ? 'Active & Secure' : 'Inactive'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                            className: "p-3 bg-blue-100 rounded-full w-12 h-12 mx-auto mb-2",
                                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Database, {
                                                className: "h-6 w-6 text-blue-600"
                                            })
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                            className: "font-medium text-gray-900",
                                            children: "Data Sync"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            className: "text-sm text-gray-600 mt-1",
                                            children: profile?.lastSync ? 'Recently Synced' : 'Not Synced'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                            className: "p-3 bg-purple-100 rounded-full w-12 h-12 mx-auto mb-2",
                                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Fingerprint, {
                                                className: "h-6 w-6 text-purple-600"
                                            })
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                            className: "font-medium text-gray-900",
                                            children: "Biometric Security"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            className: "text-sm text-gray-600 mt-1",
                                            children: (profile?.enrolledFingerprints || 0) + (profile?.enrolledFaces || 0) > 0 ? 'Enrolled' : 'Not Enrolled'
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "flex items-center justify-between mb-4",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                    className: "text-lg font-medium text-gray-900",
                                    children: "Security Settings"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Button.default, {
                                    variant: "secondary",
                                    onClick: ()=>setShowSecuritySettings(!showSecuritySettings),
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Settings, {
                                            className: "h-4 w-4 mr-2"
                                        }),
                                        showSecuritySettings ? 'Hide Settings' : 'Show Settings'
                                    ]
                                })
                            ]
                        }),
                        showSecuritySettings && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "space-y-4 border-t border-gray-200 pt-4",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                    className: "font-medium text-gray-900",
                                                    children: "Two-Factor Authentication"
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                    className: "text-sm text-gray-600",
                                                    children: "Add an extra layer of security to your account"
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_Button.default, {
                                            variant: "secondary",
                                            size: "sm",
                                            children: "Enable"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                    className: "font-medium text-gray-900",
                                                    children: "Access Notifications"
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                    className: "text-sm text-gray-600",
                                                    children: "Get notified when your biometric data is used"
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_Button.default, {
                                            variant: "secondary",
                                            size: "sm",
                                            children: "Configure"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                    className: "font-medium text-gray-900",
                                                    children: "Data Privacy"
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                    className: "text-sm text-gray-600",
                                                    children: "Manage how your biometric data is stored and used"
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_Button.default, {
                                            variant: "secondary",
                                            size: "sm",
                                            children: "Review"
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Privacy & Data Protection"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "space-y-4 text-sm text-gray-600",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-start space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                                            className: "h-4 w-4 text-green-500 mt-0.5"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            children: "Your biometric data is encrypted and stored securely"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-start space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                                            className: "h-4 w-4 text-green-500 mt-0.5"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            children: "Access logs are maintained for security and compliance purposes"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-start space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                                            className: "h-4 w-4 text-green-500 mt-0.5"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            children: "Your data is only used for authentication and attendance tracking"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-start space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                                            className: "h-4 w-4 text-green-500 mt-0.5"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            children: "You can request data deletion upon employment termination"
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            })
        ]
    });
};
const _default = StaffBiostarPage;

//# sourceMappingURL=data:application/json;base64,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