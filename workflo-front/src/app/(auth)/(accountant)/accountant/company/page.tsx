'use client';

import React, { useState } from 'react';
import {
  Building,
  Users,
  DollarSign,
  TrendingUp,
  MapPin,
  Phone,
  Mail,
  Globe,
  CreditCard,
  Settings,
  FileText,
  BarChart3
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useToast } from '@/hooks/useToast';

interface CompanyInfo {
  name: string;
  address: string;
  phone: string;
  email: string;
  website: string;
  taxId: string;
  registrationNumber: string;
  employeeCount: number;
  departments: number;
  monthlyPayroll: number;
}

const AccountantCompanyPage: React.FC = () => {
  const { success } = useToast();
  const [companyInfo] = useState<CompanyInfo>({
    name: 'WorkFlow Technologies Ltd',
    address: '123 Business District, Nairobi, Kenya',
    phone: '+*********** 456',
    email: '<EMAIL>',
    website: 'www.workflow.co.ke',
    taxId: 'A123456789P',
    registrationNumber: 'C.123456',
    employeeCount: 156,
    departments: 8,
    monthlyPayroll: 2450000
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const companyStats = [
    {
      label: 'Total Employees',
      value: companyInfo.employeeCount.toString(),
      icon: Users,
      color: 'bg-blue-100 text-blue-600'
    },
    {
      label: 'Departments',
      value: companyInfo.departments.toString(),
      icon: Building,
      color: 'bg-green-100 text-green-600'
    },
    {
      label: 'Monthly Payroll',
      value: formatCurrency(companyInfo.monthlyPayroll),
      icon: DollarSign,
      color: 'bg-orange-100 text-orange-600'
    },
    {
      label: 'Growth Rate',
      value: '+12.5%',
      icon: TrendingUp,
      color: 'bg-purple-100 text-purple-600'
    }
  ];

  const managementSections = [
    {
      title: 'Financial Settings',
      description: 'Configure accounting periods, tax rates, and financial policies',
      icon: Settings,
      href: '/accountant/company/financial-settings',
      color: 'bg-blue-100 text-blue-600'
    },
    {
      title: 'Bank Accounts',
      description: 'Manage company bank accounts and payment methods',
      icon: CreditCard,
      href: '/accountant/company/bank-accounts',
      color: 'bg-green-100 text-green-600'
    },
    {
      title: 'Tax Configuration',
      description: 'Set up tax brackets, deductions, and compliance settings',
      icon: FileText,
      href: '/accountant/company/tax-configuration',
      color: 'bg-red-100 text-red-600'
    },
    {
      title: 'Financial Reports',
      description: 'Generate and view company financial reports',
      icon: BarChart3,
      href: '/accountant/company/financial-reports',
      color: 'bg-purple-100 text-purple-600'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Company Information</h1>
          <p className="text-gray-600">Overview and financial management of company details</p>
        </div>
        <Button variant="primary">
          <Settings className="h-4 w-4 mr-2" />
          Company Settings
        </Button>
      </div>

      {/* Company Overview Card */}
      <Card className="p-6">
        <div className="flex items-start space-x-6">
          <div className="w-20 h-20 bg-orange-500 rounded-lg flex items-center justify-center">
            <Building className="h-10 w-10 text-white" />
          </div>

          <div className="flex-1">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">{companyInfo.name}</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4" />
                <span>{companyInfo.address}</span>
              </div>

              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4" />
                <span>{companyInfo.phone}</span>
              </div>

              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4" />
                <span>{companyInfo.email}</span>
              </div>

              <div className="flex items-center space-x-2">
                <Globe className="h-4 w-4" />
                <span>{companyInfo.website}</span>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-900">Tax ID:</span>
                  <span className="ml-2 text-gray-600">{companyInfo.taxId}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-900">Registration No:</span>
                  <span className="ml-2 text-gray-600">{companyInfo.registrationNumber}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Company Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {companyStats.map((stat) => (
          <Card key={stat.label} className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
              </div>
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${stat.color}`}>
                <stat.icon className="h-6 w-6" />
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Management Sections */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Management</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {managementSections.map((section) => (
            <Card key={section.title} className="p-6 hover:shadow-lg transition-shadow cursor-pointer group">
              <div className="flex items-start space-x-4">
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${section.color}`}>
                  <section.icon className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <h4 className="text-lg font-semibold text-gray-900 group-hover:text-orange-600 transition-colors">
                    {section.title}
                  </h4>
                  <p className="text-gray-600 text-sm mt-1">
                    {section.description}
                  </p>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="mt-3 p-0 h-auto text-orange-600 hover:text-orange-700"
                  >
                    Configure →
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="flex flex-wrap gap-3">
          <Button variant="outline" size="sm">
            <FileText className="h-4 w-4 mr-2" />
            Generate Financial Report
          </Button>
          <Button variant="outline" size="sm">
            <CreditCard className="h-4 w-4 mr-2" />
            Update Bank Details
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Configure Tax Settings
          </Button>
          <Button variant="outline" size="sm">
            <BarChart3 className="h-4 w-4 mr-2" />
            View Analytics
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default AccountantCompanyPage;
