// BioStar 2 Production Configuration
export interface BiostarConfig {
  apiUrl: string;
  username: string;
  password: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  pollingInterval: number;
  maxConcurrentRequests: number;
  enableLogging: boolean;
  enableMetrics: boolean;
}

// Environment-based configuration
const getEnvironmentConfig = (): BiostarConfig => {
  const env = process.env.NODE_ENV || 'development';

  const baseConfig: BiostarConfig = {
    apiUrl: process.env.NEXT_PUBLIC_BIOSTAR_API_URL || 'https://bs2api.biostar2.com',
    username: process.env.NEXT_PUBLIC_BIOSTAR_USERNAME || '',
    password: process.env.NEXT_PUBLIC_BIOSTAR_PASSWORD || '',
    timeout: 15000,
    retryAttempts: 3,
    retryDelay: 1000,
    pollingInterval: 30000,
    maxConcurrentRequests: 10,
    enableLogging: true,
    enableMetrics: true,
  };

  // Environment-specific overrides
  switch (env) {
    case 'production':
      return {
        ...baseConfig,
        timeout: 20000,
        retryAttempts: 5,
        retryDelay: 2000,
        pollingInterval: 60000, // Slower polling in production
        enableLogging: false, // Disable verbose logging in production
      };

    case 'test':
      return {
        ...baseConfig,
        timeout: 5000,
        retryAttempts: 1,
        pollingInterval: 5000,
        enableLogging: false,
      };

    case 'development':
    default:
      return {
        ...baseConfig,
        pollingInterval: 10000, // Faster polling for development
        enableLogging: true,
      };
  }
};

export const biostarConfig = getEnvironmentConfig();

// Validation function
export const validateBiostarConfig = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!biostarConfig.apiUrl) {
    errors.push('NEXT_PUBLIC_BIOSTAR_API_URL is required');
  }

  if (!biostarConfig.username) {
    errors.push('NEXT_PUBLIC_BIOSTAR_USERNAME is required');
  }

  if (!biostarConfig.password) {
    errors.push('NEXT_PUBLIC_BIOSTAR_PASSWORD is required');
  }

  // Validate URL format
  try {
    new URL(biostarConfig.apiUrl);
  } catch {
    errors.push('NEXT_PUBLIC_BIOSTAR_API_URL must be a valid URL');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Network connectivity test
export const testBiostarConnectivity = async (): Promise<{
  success: boolean;
  responseTime: number;
  error?: string;
}> => {
  const startTime = Date.now();

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), biostarConfig.timeout);

    const response = await fetch(`${biostarConfig.apiUrl}/health`, {
      method: 'GET',
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;

    return {
      success: response.ok,
      responseTime,
      error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
    };
  } catch (error) {
    return {
      success: false,
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

// Configuration health check
export const performHealthCheck = async (): Promise<{
  config: { isValid: boolean; errors: string[] };
  connectivity: { success: boolean; responseTime: number; error?: string };
  timestamp: string;
}> => {
  const configValidation = validateBiostarConfig();
  const connectivityTest = await testBiostarConnectivity();

  return {
    config: configValidation,
    connectivity: connectivityTest,
    timestamp: new Date().toISOString()
  };
};
