{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/providers/AuthProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';\nimport { User, LoginCredentials } from '@/types';\nimport { authApi, TokenManager } from '@/lib/api';\nimport { AuthPersistence } from '@/lib/authPersistence';\n\ninterface AuthState {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n  isInitialized: boolean;\n  isHydrated: boolean;\n}\n\ninterface AuthActions {\n  login: (credentials: LoginCredentials) => Promise<void>;\n  logout: () => void;\n  getCurrentUser: () => Promise<void>;\n  clearError: () => void;\n  initialize: () => Promise<void>;\n}\n\ntype AuthContextType = AuthState & AuthActions;\n\nconst AuthContext = createContext<AuthContextType | null>(null);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [state, setState] = useState<AuthState>({\n    user: null,\n    isAuthenticated: false,\n    isLoading: false,\n    error: null,\n    isInitialized: false,\n    isHydrated: false,\n  });\n\n  const updateState = (updates: Partial<AuthState>) => {\n    setState(prev => ({ ...prev, ...updates }));\n  };\n\n  const login = useCallback(async (credentials: LoginCredentials) => {\n    try {\n      updateState({ isLoading: true, error: null });\n\n      const tokens = await authApi.login(credentials);\n      TokenManager.setTokens(tokens);\n      const user = await authApi.getCurrentUser() as User;\n\n      updateState({\n        user,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      });\n\n      AuthPersistence.save(user, true);\n\n      // Log successful login with role information\n      console.log(`Login successful for user: ${user.email}, role: ${user.role}`);\n\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.message ||\n                          error.response?.data?.detail ||\n                          error.message ||\n                          'Login failed. Please check your credentials.';\n\n      updateState({\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: errorMessage,\n      });\n\n      AuthPersistence.clear();\n      throw error;\n    }\n  }, []);\n\n  const logout = useCallback(() => {\n    TokenManager.clearTokens();\n    AuthPersistence.clear();\n\n    updateState({\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n      isInitialized: false,\n    });\n\n    if (typeof window !== 'undefined') {\n      window.location.href = '/login';\n    }\n  }, []);\n\n  const getCurrentUser = useCallback(async () => {\n    try {\n      // Only show loading if we don't have a user already\n      if (!state.user) {\n        updateState({ isLoading: true, error: null });\n      }\n\n      const token = TokenManager.getAccessToken();\n      if (!token) {\n        updateState({\n          user: null,\n          isAuthenticated: false,\n          isLoading: false,\n          error: null,\n        });\n        AuthPersistence.clear();\n        return;\n      }\n\n      const user = await authApi.getCurrentUser() as User;\n      updateState({\n        user,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      });\n\n      AuthPersistence.save(user, true);\n    } catch (error: any) {\n      TokenManager.clearTokens();\n      AuthPersistence.clear();\n\n      updateState({\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      });\n    }\n  }, [state.user]);\n\n  const clearError = useCallback(() => {\n    updateState({ error: null });\n  }, []);\n\n  const initialize = useCallback(async () => {\n    if (state.isInitialized) return;\n\n    try {\n      // First, try to load from persistence without showing loading\n      const persisted = AuthPersistence.load();\n      if (persisted && persisted.isAuthenticated && persisted.user) {\n        updateState({\n          user: persisted.user,\n          isAuthenticated: persisted.isAuthenticated,\n          isInitialized: true, // Mark as initialized immediately\n        });\n\n        // Validate in background without loading state\n        try {\n          await getCurrentUser();\n        } catch (error) {\n          // If validation fails, clear everything\n          AuthPersistence.clear();\n          updateState({\n            user: null,\n            isAuthenticated: false,\n            error: null,\n          });\n        }\n      } else {\n        // No persisted data, validate with server\n        await getCurrentUser();\n        updateState({ isInitialized: true });\n      }\n    } catch (error) {\n      // If validation fails, clear everything\n      AuthPersistence.clear();\n      updateState({\n        user: null,\n        isAuthenticated: false,\n        error: null,\n        isInitialized: true,\n      });\n    }\n  }, [state.isInitialized, getCurrentUser]);\n\n  // Initialize on mount\n  useEffect(() => {\n    updateState({ isHydrated: true });\n    initialize();\n  }, []);\n\n  const contextValue: AuthContextType = useMemo(() => ({\n    ...state,\n    login,\n    logout,\n    getCurrentUser,\n    clearError,\n    initialize,\n  }), [state, login, logout, getCurrentUser, clearError, initialize]);\n\n  return (\n    <AuthContext.Provider value={contextValue}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "names": ["<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "AuthContext", "createContext", "context", "useContext", "Error", "children", "state", "setState", "useState", "user", "isAuthenticated", "isLoading", "error", "isInitialized", "isHydrated", "updateState", "updates", "prev", "login", "useCallback", "credentials", "tokens", "authApi", "TokenManager", "setTokens", "getCurrentUser", "AuthPersistence", "save", "console", "log", "email", "role", "errorMessage", "response", "data", "message", "detail", "clear", "logout", "clearTokens", "window", "location", "href", "token", "getAccessToken", "clearError", "initialize", "persisted", "load", "useEffect", "contextValue", "useMemo", "Provider", "value"], "mappings": "AAAA;;;;;;;;;;;;IAwCaA,YAAY;eAAZA;;IAZAC,OAAO;eAAPA;;;;+DA1B+E;qBAEtD;iCACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBhC,MAAMC,4BAAcC,IAAAA,oBAAa,EAAyB;AAEnD,MAAMF,UAAU;IACrB,MAAMG,UAAUC,IAAAA,iBAAU,EAACH;IAC3B,IAAI,CAACE,SAAS;QACZ,MAAM,IAAIE,MAAM;IAClB;IACA,OAAOF;AACT;AAMO,MAAMJ,eAA4C,CAAC,EAAEO,QAAQ,EAAE;IACpE,MAAM,CAACC,OAAOC,SAAS,GAAGC,IAAAA,eAAQ,EAAY;QAC5CC,MAAM;QACNC,iBAAiB;QACjBC,WAAW;QACXC,OAAO;QACPC,eAAe;QACfC,YAAY;IACd;IAEA,MAAMC,cAAc,CAACC;QACnBT,SAASU,CAAAA,OAAS,CAAA;gBAAE,GAAGA,IAAI;gBAAE,GAAGD,OAAO;YAAC,CAAA;IAC1C;IAEA,MAAME,QAAQC,IAAAA,kBAAW,EAAC,OAAOC;QAC/B,IAAI;YACFL,YAAY;gBAAEJ,WAAW;gBAAMC,OAAO;YAAK;YAE3C,MAAMS,SAAS,MAAMC,YAAO,CAACJ,KAAK,CAACE;YACnCG,iBAAY,CAACC,SAAS,CAACH;YACvB,MAAMZ,OAAO,MAAMa,YAAO,CAACG,cAAc;YAEzCV,YAAY;gBACVN;gBACAC,iBAAiB;gBACjBC,WAAW;gBACXC,OAAO;YACT;YAEAc,gCAAe,CAACC,IAAI,CAAClB,MAAM;YAE3B,6CAA6C;YAC7CmB,QAAQC,GAAG,CAAC,CAAC,2BAA2B,EAAEpB,KAAKqB,KAAK,CAAC,QAAQ,EAAErB,KAAKsB,IAAI,EAAE;QAE5E,EAAE,OAAOnB,OAAY;YACnB,MAAMoB,eAAepB,MAAMqB,QAAQ,EAAEC,MAAMC,WACvBvB,MAAMqB,QAAQ,EAAEC,MAAME,UACtBxB,MAAMuB,OAAO,IACb;YAEpBpB,YAAY;gBACVN,MAAM;gBACNC,iBAAiB;gBACjBC,WAAW;gBACXC,OAAOoB;YACT;YAEAN,gCAAe,CAACW,KAAK;YACrB,MAAMzB;QACR;IACF,GAAG,EAAE;IAEL,MAAM0B,SAASnB,IAAAA,kBAAW,EAAC;QACzBI,iBAAY,CAACgB,WAAW;QACxBb,gCAAe,CAACW,KAAK;QAErBtB,YAAY;YACVN,MAAM;YACNC,iBAAiB;YACjBC,WAAW;YACXC,OAAO;YACPC,eAAe;QACjB;QAEA,IAAI,OAAO2B,WAAW,aAAa;YACjCA,OAAOC,QAAQ,CAACC,IAAI,GAAG;QACzB;IACF,GAAG,EAAE;IAEL,MAAMjB,iBAAiBN,IAAAA,kBAAW,EAAC;QACjC,IAAI;YACF,oDAAoD;YACpD,IAAI,CAACb,MAAMG,IAAI,EAAE;gBACfM,YAAY;oBAAEJ,WAAW;oBAAMC,OAAO;gBAAK;YAC7C;YAEA,MAAM+B,QAAQpB,iBAAY,CAACqB,cAAc;YACzC,IAAI,CAACD,OAAO;gBACV5B,YAAY;oBACVN,MAAM;oBACNC,iBAAiB;oBACjBC,WAAW;oBACXC,OAAO;gBACT;gBACAc,gCAAe,CAACW,KAAK;gBACrB;YACF;YAEA,MAAM5B,OAAO,MAAMa,YAAO,CAACG,cAAc;YACzCV,YAAY;gBACVN;gBACAC,iBAAiB;gBACjBC,WAAW;gBACXC,OAAO;YACT;YAEAc,gCAAe,CAACC,IAAI,CAAClB,MAAM;QAC7B,EAAE,OAAOG,OAAY;YACnBW,iBAAY,CAACgB,WAAW;YACxBb,gCAAe,CAACW,KAAK;YAErBtB,YAAY;gBACVN,MAAM;gBACNC,iBAAiB;gBACjBC,WAAW;gBACXC,OAAO;YACT;QACF;IACF,GAAG;QAACN,MAAMG,IAAI;KAAC;IAEf,MAAMoC,aAAa1B,IAAAA,kBAAW,EAAC;QAC7BJ,YAAY;YAAEH,OAAO;QAAK;IAC5B,GAAG,EAAE;IAEL,MAAMkC,aAAa3B,IAAAA,kBAAW,EAAC;QAC7B,IAAIb,MAAMO,aAAa,EAAE;QAEzB,IAAI;YACF,8DAA8D;YAC9D,MAAMkC,YAAYrB,gCAAe,CAACsB,IAAI;YACtC,IAAID,aAAaA,UAAUrC,eAAe,IAAIqC,UAAUtC,IAAI,EAAE;gBAC5DM,YAAY;oBACVN,MAAMsC,UAAUtC,IAAI;oBACpBC,iBAAiBqC,UAAUrC,eAAe;oBAC1CG,eAAe;gBACjB;gBAEA,+CAA+C;gBAC/C,IAAI;oBACF,MAAMY;gBACR,EAAE,OAAOb,OAAO;oBACd,wCAAwC;oBACxCc,gCAAe,CAACW,KAAK;oBACrBtB,YAAY;wBACVN,MAAM;wBACNC,iBAAiB;wBACjBE,OAAO;oBACT;gBACF;YACF,OAAO;gBACL,0CAA0C;gBAC1C,MAAMa;gBACNV,YAAY;oBAAEF,eAAe;gBAAK;YACpC;QACF,EAAE,OAAOD,OAAO;YACd,wCAAwC;YACxCc,gCAAe,CAACW,KAAK;YACrBtB,YAAY;gBACVN,MAAM;gBACNC,iBAAiB;gBACjBE,OAAO;gBACPC,eAAe;YACjB;QACF;IACF,GAAG;QAACP,MAAMO,aAAa;QAAEY;KAAe;IAExC,sBAAsB;IACtBwB,IAAAA,gBAAS,EAAC;QACRlC,YAAY;YAAED,YAAY;QAAK;QAC/BgC;IACF,GAAG,EAAE;IAEL,MAAMI,eAAgCC,IAAAA,cAAO,EAAC,IAAO,CAAA;YACnD,GAAG7C,KAAK;YACRY;YACAoB;YACAb;YACAoB;YACAC;QACF,CAAA,GAAI;QAACxC;QAAOY;QAAOoB;QAAQb;QAAgBoB;QAAYC;KAAW;IAElE,qBACE,qBAAC9C,YAAYoD,QAAQ;QAACC,OAAOH;kBAC1B7C;;AAGP"}