d2f9e082bd200a834eac46f357721506
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    NotificationProvider: function() {
        return NotificationProvider;
    },
    default: function() {
        return _default;
    },
    useNotifications: function() {
        return useNotifications;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _AuthProvider = require("./AuthProvider");
const _websocket = /*#__PURE__*/ _interop_require_default(require("../lib/websocket"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const NotificationContext = /*#__PURE__*/ (0, _react.createContext)(null);
const useNotifications = ()=>{
    const context = (0, _react.useContext)(NotificationContext);
    if (!context) {
        throw new Error('useNotifications must be used within a NotificationProvider');
    }
    return context;
};
const NotificationProvider = ({ children })=>{
    const { isAuthenticated, user } = (0, _AuthProvider.useAuth)();
    const [notifications, setNotifications] = (0, _react.useState)([]);
    const [isConnected, setIsConnected] = (0, _react.useState)(false);
    const [connectionState, setConnectionState] = (0, _react.useState)('disconnected');
    // Update notifications from WebSocket service
    const updateNotifications = (0, _react.useCallback)(()=>{
        setNotifications(_websocket.default.getNotifications());
    }, []);
    // Update connection state
    const updateConnectionState = (0, _react.useCallback)(()=>{
        setIsConnected(_websocket.default.isConnected());
        setConnectionState(_websocket.default.getConnectionState());
    }, []);
    // WebSocket event handlers
    (0, _react.useEffect)(()=>{
        if (!isAuthenticated) return;
        const handleNotification = (notification)=>{
            updateNotifications();
            // Show toast notification (you can integrate with a toast library here)
            console.log('New notification:', notification);
        };
        const handleConnected = ()=>{
            updateConnectionState();
            console.log('WebSocket connected');
        };
        const handleDisconnected = ()=>{
            updateConnectionState();
            console.log('WebSocket disconnected');
        };
        const handleError = (error)=>{
            updateConnectionState();
            console.error('WebSocket error:', error);
        };
        const handleMaxReconnectAttempts = ()=>{
            console.error('WebSocket max reconnection attempts reached');
        // You could show a user notification here
        };
        // Register event handlers
        _websocket.default.on('notification', handleNotification);
        _websocket.default.on('connected', handleConnected);
        _websocket.default.on('disconnected', handleDisconnected);
        _websocket.default.on('error', handleError);
        _websocket.default.on('maxReconnectAttemptsReached', handleMaxReconnectAttempts);
        // Connect WebSocket
        _websocket.default.connect();
        // Initial state update
        updateNotifications();
        updateConnectionState();
        // Cleanup
        return ()=>{
            _websocket.default.off('notification', handleNotification);
            _websocket.default.off('connected', handleConnected);
            _websocket.default.off('disconnected', handleDisconnected);
            _websocket.default.off('error', handleError);
            _websocket.default.off('maxReconnectAttemptsReached', handleMaxReconnectAttempts);
        };
    }, [
        isAuthenticated,
        updateNotifications,
        updateConnectionState
    ]);
    // Disconnect when user logs out
    (0, _react.useEffect)(()=>{
        if (!isAuthenticated) {
            _websocket.default.disconnect();
            setNotifications([]);
            setIsConnected(false);
            setConnectionState('disconnected');
        }
    }, [
        isAuthenticated
    ]);
    // Mark notification as read
    const markAsRead = (0, _react.useCallback)((notificationId)=>{
        _websocket.default.markNotificationAsRead(notificationId);
        updateNotifications();
    }, [
        updateNotifications
    ]);
    // Mark all notifications as read
    const markAllAsRead = (0, _react.useCallback)(()=>{
        _websocket.default.markAllNotificationsAsRead();
        updateNotifications();
    }, [
        updateNotifications
    ]);
    // Clear all notifications
    const clearNotifications = (0, _react.useCallback)(()=>{
        _websocket.default.clearNotifications();
        updateNotifications();
    }, [
        updateNotifications
    ]);
    // Request browser notification permission
    const requestPermission = (0, _react.useCallback)(async ()=>{
        return await _websocket.default.constructor.requestNotificationPermission();
    }, []);
    // Calculate unread count
    const unreadCount = notifications.filter((n)=>!n.read).length;
    const value = {
        notifications,
        unreadCount,
        isConnected,
        connectionState,
        markAsRead,
        markAllAsRead,
        clearNotifications,
        requestPermission
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(NotificationContext.Provider, {
        value: value,
        children: children
    });
};
const _default = NotificationProvider;

//# sourceMappingURL=data:application/json;base64,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