'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import {
  Search,
  Bell,
  User,
  Settings,
  LogOut,
  Menu,
  ChevronDown,
  Home,
  Info,
  Building,
  Ticket,
  Cog
} from 'lucide-react';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials } from '@/lib/utils';
import NotificationDropdown from '@/components/ui/NotificationDropdown';

interface StaffHeaderProps {
  onMobileMenuToggle: () => void;
}

const StaffHeader: React.FC<StaffHeaderProps> = ({ onMobileMenuToggle }) => {
  const { user, logout } = useAuth();
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleLogout = () => {
    logout();
  };

  const userInitials = user ? getInitials(user.first_name, user.last_name) : 'U';

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
      {/* Top Header Section */}
      <div className="bg-gray-900">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
          <div className="flex items-center justify-between h-14 sm:h-16">
            {/* Logo and Mobile Menu */}
            <div className="flex items-center space-x-3">
              {/* Mobile Menu Button - Moved to left for better UX */}
              <button
                onClick={onMobileMenuToggle}
                className="md:hidden text-gray-300 hover:text-white p-2 rounded-md transition-colors touch-manipulation"
                aria-label="Toggle menu"
              >
                <Menu className="h-5 w-5" />
              </button>

              <Link href="/staff" className="flex items-center">
                <div className="w-7 h-7 sm:w-8 sm:h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-base sm:text-lg">W</span>
                </div>
                <span className="ml-2 text-white font-semibold text-base sm:text-lg hidden sm:block">
                  WorkFlow Staff
                </span>
              </Link>
            </div>

            {/* Search Bar */}
            <div className="flex-1 max-w-lg mx-8 hidden md:block">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search here..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>
            </div>

            {/* Right Side */}
            <div className="flex items-center space-x-2 sm:space-x-4">
              {/* Notifications - Hidden on mobile */}
              <div className="hidden sm:block">
                <NotificationDropdown />
              </div>

              {/* User Menu */}
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-1 sm:space-x-2 text-gray-300 hover:text-white p-1 sm:p-2 rounded-md transition-colors touch-manipulation"
                  aria-label="User menu"
                >
                  {user?.profile_picture ? (
                    <Image
                      src={user.profile_picture}
                      alt="Profile"
                      width={28}
                      height={28}
                      className="sm:w-8 sm:h-8 rounded-full"
                    />
                  ) : (
                    <div className="w-7 h-7 sm:w-8 sm:h-8 bg-orange-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs sm:text-sm font-medium">
                        {userInitials}
                      </span>
                    </div>
                  )}
                  <ChevronDown className="h-3 w-3 sm:h-4 sm:w-4" />
                </button>

                {/* User Dropdown */}
                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                    {/* Mobile-only notifications link */}
                    <div className="sm:hidden border-b border-gray-100">
                      <Link
                        href="/staff/notifications"
                        className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <Bell className="h-4 w-4 mr-2" />
                        Notifications
                      </Link>
                    </div>

                    <Link
                      href="/staff/info/profile"
                      className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 touch-manipulation"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <User className="h-4 w-4 mr-2" />
                      Profile
                    </Link>
                    <Link
                      href="/staff/settings"
                      className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 touch-manipulation"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      Settings
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 touch-manipulation"
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      Logout
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Bar */}
      <div className="bg-white hidden md:block">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
          <nav className="flex space-x-4 lg:space-x-8 h-12 overflow-x-auto">
            <StaffNavLink href="/staff" icon={Home}>Dashboard</StaffNavLink>
            <StaffNavLink href="/staff/info" icon={Info}>Info</StaffNavLink>
            <StaffNavLink href="/staff/company" icon={Building}>Company</StaffNavLink>
            <StaffNavLink href="/staff/ticket" icon={Ticket}>Ticket</StaffNavLink>
            <StaffNavLink href="/staff/settings" icon={Cog}>Settings</StaffNavLink>
          </nav>
        </div>
      </div>

      {/* Mobile Search */}
      <div className="md:hidden bg-white border-t border-gray-200 px-3 py-3">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search here..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-base"
          />
        </div>
      </div>
    </header>
  );
};

// Staff Navigation Link Component
const StaffNavLink: React.FC<{
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  children: React.ReactNode;
}> = ({ href, icon: Icon, children }) => {
  const pathname = usePathname();
  const isActive = pathname === href || (href !== '/staff' && pathname.startsWith(href));

  return (
    <Link
      href={href}
      className={cn(
        'flex items-center space-x-1 md:space-x-2 px-2 md:px-3 py-3 text-sm font-medium rounded-md transition-colors whitespace-nowrap',
        isActive
          ? 'text-orange-600 bg-orange-50 border-b-2 border-orange-500'
          : 'text-gray-600 hover:text-orange-600 hover:bg-orange-50 border-b-2 border-transparent hover:border-orange-500'
      )}
    >
      <Icon className="h-4 w-4 flex-shrink-0" />
      <span className="hidden md:block">{children}</span>
    </Link>
  );
};

export default StaffHeader;
