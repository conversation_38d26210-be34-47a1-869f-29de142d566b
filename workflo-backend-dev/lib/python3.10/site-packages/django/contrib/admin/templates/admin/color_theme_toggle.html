{% load i18n %}
<button class="theme-toggle">
  <span class="visually-hidden theme-label-when-auto">{% translate 'Toggle theme (current theme: auto)' %}</span>
  <span class="visually-hidden theme-label-when-light">{% translate 'Toggle theme (current theme: light)' %}</span>
  <span class="visually-hidden theme-label-when-dark">{% translate 'Toggle theme (current theme: dark)' %}</span>
  <svg aria-hidden="true" class="theme-icon-when-auto">
    <use xlink:href="#icon-auto" />
  </svg>
  <svg aria-hidden="true" class="theme-icon-when-dark">
    <use xlink:href="#icon-moon" />
  </svg>
  <svg aria-hidden="true" class="theme-icon-when-light">
    <use xlink:href="#icon-sun" />
  </svg>
</button>
