# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON>, 2019-2020
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-01-21 20:49+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Serbian (Latin) (http://www.transifex.com/django/django/"
"language/sr@latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

msgid "Content Types"
msgstr "Tipovi sadržaja"

msgid "python model class name"
msgstr "ime python klase modela"

msgid "content type"
msgstr "tip sadržaja"

msgid "content types"
msgstr "tipovi sadržaja"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Tip sadržaja %(ct_id)s nema asocirani model"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Tip sadržaja %(ct_id)s objekta %(obj_id)s ne postoji"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Objekti %(ct_name)s nemaju get_absolute_url() metod"
