6e9109c52f45facbecc388f651dd2591
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _lucidereact = require("lucide-react");
const _Card = /*#__PURE__*/ _interop_require_default(require("../../../../../../components/ui/Card"));
const _Button = /*#__PURE__*/ _interop_require_default(require("../../../../../../components/ui/Button"));
const _AuthProvider = require("../../../../../../providers/AuthProvider");
const _utils = require("../../../../../../lib/utils");
const _useBiostarAttendance = require("../../../../../../hooks/useBiostarAttendance");
const _biostarApi = require("../../../../../../lib/biostarApi");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const StaffBiostarPage = ()=>{
    const { user } = (0, _AuthProvider.useAuth)();
    const [activeTab, setActiveTab] = (0, _react.useState)('profile');
    const [biometricProfile, setBiometricProfile] = (0, _react.useState)(null);
    const [recentEvents, setRecentEvents] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(true);
    const [refreshing, setRefreshing] = (0, _react.useState)(false);
    // Use BioStar attendance hook for device and connectivity data
    const { devices, connected: biostarConnected, error: connectionError, refresh: refreshAttendance } = (0, _useBiostarAttendance.useBiostarAttendance)({
        employeeId: user?.employee_id,
        autoRefresh: true,
        enableRealTime: false
    });
    (0, _react.useEffect)(()=>{
        loadBiometricData();
    }, [
        user?.employee_id
    ]);
    const loadBiometricData = async ()=>{
        if (!user?.employee_id) {
            setLoading(false);
            return;
        }
        try {
            setLoading(true);
            // Get user's biometric profile from BioStar
            const biostarUser = await _biostarApi.biostarApi.getUserById(user.employee_id);
            // Get recent access events for this user
            const endDate = new Date().toISOString();
            const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(); // Last 7 days
            const eventsResponse = await _biostarApi.biostarApi.getEvents(startDate, endDate, user.employee_id);
            setBiometricProfile({
                user: biostarUser,
                enrolledFingerprints: 2,
                enrolledFaces: 1,
                lastSync: new Date().toISOString(),
                accessLevel: 'Standard Employee',
                isActive: !biostarUser?.disabled
            });
            setRecentEvents(eventsResponse?.results || []);
        } catch (error) {
            console.error('Failed to load biometric data:', error);
            // Set default profile even on error
            setBiometricProfile({
                user: null,
                enrolledFingerprints: 2,
                enrolledFaces: 1,
                lastSync: new Date().toISOString(),
                accessLevel: 'Standard Employee',
                isActive: true
            });
            setRecentEvents([]);
        } finally{
            setLoading(false);
        }
    };
    const handleRefresh = async ()=>{
        setRefreshing(true);
        await Promise.all([
            loadBiometricData(),
            refreshAttendance()
        ]);
        setRefreshing(false);
    };
    const getEventTypeIcon = (eventType)=>{
        switch(eventType){
            case 'ENTRY':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                    className: "h-4 w-4 text-green-500"
                });
            case 'EXIT':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Activity, {
                    className: "h-4 w-4 text-blue-500"
                });
            case 'DENIED':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertCircle, {
                    className: "h-4 w-4 text-red-500"
                });
            default:
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                    className: "h-4 w-4 text-gray-500"
                });
        }
    };
    const getEventTypeColor = (eventType)=>{
        switch(eventType){
            case 'ENTRY':
                return 'bg-green-100 text-green-800';
            case 'EXIT':
                return 'bg-blue-100 text-blue-800';
            case 'DENIED':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    const tabs = [
        {
            id: 'profile',
            label: 'Biometric Profile',
            icon: _lucidereact.User
        },
        {
            id: 'devices',
            label: 'Devices',
            icon: _lucidereact.Monitor
        },
        {
            id: 'history',
            label: 'Access History',
            icon: _lucidereact.History
        },
        {
            id: 'security',
            label: 'Security',
            icon: _lucidereact.Shield
        }
    ];
    if (loading) {
        return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            className: "flex items-center justify-center p-8",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.RefreshCw, {
                    className: "h-8 w-8 animate-spin text-gray-400"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                    className: "ml-2 text-gray-600",
                    children: "Loading biometric data..."
                })
            ]
        });
    }
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                                className: "text-2xl font-bold text-gray-900",
                                children: "BioStar Profile"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                className: "text-gray-600 mt-1",
                                children: "Manage your biometric data and view access history"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "flex items-center space-x-3",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center space-x-2",
                                children: [
                                    biostarConnected ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Wifi, {
                                        className: "h-4 w-4 text-green-500"
                                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.WifiOff, {
                                        className: "h-4 w-4 text-red-500"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                                        className: "text-sm text-gray-600",
                                        children: [
                                            "BioStar ",
                                            biostarConnected ? 'Connected' : 'Disconnected'
                                        ]
                                    })
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Button.default, {
                                variant: "secondary",
                                onClick: handleRefresh,
                                disabled: refreshing,
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.RefreshCw, {
                                        className: (0, _utils.cn)("h-4 w-4 mr-2", refreshing && "animate-spin")
                                    }),
                                    refreshing ? 'Refreshing...' : 'Refresh'
                                ]
                            })
                        ]
                    })
                ]
            }),
            connectionError && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "bg-red-50 border border-red-200 rounded-lg p-4",
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "flex items-center",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertCircle, {
                            className: "h-5 w-5 text-red-500 mr-2"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                    className: "text-sm font-medium text-red-800",
                                    children: "Connection Error"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                    className: "text-sm text-red-700 mt-1",
                                    children: connectionError
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "grid grid-cols-1 md:grid-cols-4 gap-6",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-blue-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.User, {
                                            className: "h-6 w-6 text-blue-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Profile Status"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: biometricProfile?.isActive ? 'Active' : 'Inactive'
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-green-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Fingerprint, {
                                            className: "h-6 w-6 text-green-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Fingerprints"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: biometricProfile?.enrolledFingerprints || 0
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-purple-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Eye, {
                                            className: "h-6 w-6 text-purple-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Face Templates"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: biometricProfile?.enrolledFaces || 0
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-orange-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Monitor, {
                                            className: "h-6 w-6 text-orange-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Devices"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: [
                                                    devices.filter((d)=>d.status === 'ONLINE').length,
                                                    "/",
                                                    devices.length
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "border-b border-gray-200",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("nav", {
                    className: "-mb-px flex space-x-8",
                    children: tabs.map((tab)=>{
                        const Icon = tab.icon;
                        return /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                            onClick: ()=>setActiveTab(tab.id),
                            className: (0, _utils.cn)('flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm', activeTab === tab.id ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(Icon, {
                                    className: "h-4 w-4"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                    children: tab.label
                                })
                            ]
                        }, tab.id);
                    })
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "mt-6",
                children: [
                    activeTab === 'profile' && /*#__PURE__*/ (0, _jsxruntime.jsx)(BiometricProfileTab, {
                        profile: biometricProfile,
                        user: user
                    }),
                    activeTab === 'devices' && /*#__PURE__*/ (0, _jsxruntime.jsx)(DevicesTab, {
                        devices: devices,
                        connected: biostarConnected
                    }),
                    activeTab === 'history' && /*#__PURE__*/ (0, _jsxruntime.jsx)(AccessHistoryTab, {
                        events: recentEvents,
                        employeeId: user?.employee_id
                    }),
                    activeTab === 'security' && /*#__PURE__*/ (0, _jsxruntime.jsx)(SecurityTab, {
                        profile: biometricProfile,
                        user: user
                    })
                ]
            })
        ]
    });
};
const BiometricProfileTab = ({ profile, user })=>{
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "grid grid-cols-1 lg:grid-cols-2 gap-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Profile Information"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "space-y-4",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Employee ID"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: user?.employee_id || 'N/A'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Full Name"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.user?.name || `${user?.first_name} ${user?.last_name}`
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Email"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.user?.email || user?.email
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Department"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.user?.department || 'N/A'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Position"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.user?.position || 'N/A'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Access Level"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.accessLevel || 'Standard'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Status"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: (0, _utils.cn)('inline-flex items-center px-2 py-1 rounded-full text-xs font-medium', profile?.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),
                                            children: profile?.isActive ? 'Active' : 'Inactive'
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Biometric Enrollment"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "space-y-6",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-3",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: "p-2 bg-blue-100 rounded-lg",
                                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Fingerprint, {
                                                        className: "h-5 w-5 text-blue-600"
                                                    })
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                            className: "font-medium text-gray-900",
                                                            children: "Fingerprints"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                            className: "text-sm text-gray-600",
                                                            children: [
                                                                profile?.enrolledFingerprints || 0,
                                                                " enrolled"
                                                            ]
                                                        })
                                                    ]
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "text-right",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "text-2xl font-bold text-gray-900",
                                                    children: [
                                                        profile?.enrolledFingerprints || 0,
                                                        "/10"
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: "text-xs text-gray-500",
                                                    children: "Max: 10"
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-3",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: "p-2 bg-purple-100 rounded-lg",
                                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Eye, {
                                                        className: "h-5 w-5 text-purple-600"
                                                    })
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                            className: "font-medium text-gray-900",
                                                            children: "Face Templates"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                            className: "text-sm text-gray-600",
                                                            children: [
                                                                profile?.enrolledFaces || 0,
                                                                " enrolled"
                                                            ]
                                                        })
                                                    ]
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "text-right",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "text-2xl font-bold text-gray-900",
                                                    children: [
                                                        profile?.enrolledFaces || 0,
                                                        "/5"
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: "text-xs text-gray-500",
                                                    children: "Max: 5"
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "pt-4 border-t border-gray-200",
                                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "flex items-center justify-between",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                className: "text-sm text-gray-600",
                                                children: "Last Sync"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                className: "font-medium",
                                                children: profile?.lastSync ? (0, _utils.formatDate)(profile.lastSync, 'MMM dd, yyyy HH:mm') : 'Never'
                                            })
                                        ]
                                    })
                                })
                            ]
                        })
                    ]
                })
            })
        ]
    });
};
const DevicesTab = ({ devices, connected })=>{
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "p-6",
                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center space-x-3",
                                children: [
                                    connected ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Wifi, {
                                        className: "h-6 w-6 text-green-500"
                                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.WifiOff, {
                                        className: "h-6 w-6 text-red-500"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                                className: "text-lg font-medium text-gray-900",
                                                children: "System Status"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                className: "text-sm text-gray-600",
                                                children: [
                                                    "BioStar system is ",
                                                    connected ? 'online and accessible' : 'offline or unreachable'
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                className: (0, _utils.cn)('inline-flex items-center px-3 py-1 rounded-full text-sm font-medium', connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),
                                children: connected ? 'Connected' : 'Disconnected'
                            })
                        ]
                    })
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Available Devices"
                        }),
                        devices.length === 0 ? /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "text-center py-8",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Monitor, {
                                    className: "h-12 w-12 text-gray-400 mx-auto mb-4"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                    className: "text-lg font-medium text-gray-900 mb-2",
                                    children: "No Devices Found"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                    className: "text-gray-600",
                                    children: "No biometric devices are currently available."
                                })
                            ]
                        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                            children: devices.map((device)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "border border-gray-200 rounded-lg p-4",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center justify-between mb-3",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex items-center space-x-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Monitor, {
                                                            className: "h-5 w-5 text-gray-600"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                            className: "font-medium text-gray-900",
                                                            children: device.name
                                                        })
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                    className: (0, _utils.cn)('inline-flex items-center px-2 py-1 rounded-full text-xs font-medium', device.status === 'ONLINE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),
                                                    children: device.status
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "space-y-2 text-sm",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex justify-between",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "text-gray-600",
                                                            children: "Location"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "font-medium",
                                                            children: device.location || 'Unknown'
                                                        })
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex justify-between",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "text-gray-600",
                                                            children: "IP Address"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "font-medium",
                                                            children: device.ip
                                                        })
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex justify-between",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "text-gray-600",
                                                            children: "Type"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "font-medium",
                                                            children: device.type
                                                        })
                                                    ]
                                                })
                                            ]
                                        })
                                    ]
                                }, device.id))
                        })
                    ]
                })
            })
        ]
    });
};
const AccessHistoryTab = ({ events, employeeId })=>{
    const [selectedPeriod, setSelectedPeriod] = (0, _react.useState)('7_days');
    const [filteredEvents, setFilteredEvents] = (0, _react.useState)(events);
    (0, _react.useEffect)(()=>{
        // Filter events based on selected period
        const now = new Date();
        let startDate;
        switch(selectedPeriod){
            case '1_day':
                startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case '7_days':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30_days':
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            default:
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        }
        const filtered = events.filter((event)=>new Date(event.datetime) >= startDate);
        setFilteredEvents(filtered);
    }, [
        events,
        selectedPeriod
    ]);
    const getEventTypeIcon = (eventType)=>{
        switch(eventType){
            case 'ENTRY':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                    className: "h-4 w-4 text-green-500"
                });
            case 'EXIT':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Activity, {
                    className: "h-4 w-4 text-blue-500"
                });
            case 'DENIED':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertCircle, {
                    className: "h-4 w-4 text-red-500"
                });
            default:
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                    className: "h-4 w-4 text-gray-500"
                });
        }
    };
    const getEventTypeColor = (eventType)=>{
        switch(eventType){
            case 'ENTRY':
                return 'bg-green-100 text-green-800';
            case 'EXIT':
                return 'bg-blue-100 text-blue-800';
            case 'DENIED':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "p-6",
                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                className: "text-lg font-medium text-gray-900",
                                children: "Access History"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("select", {
                                value: selectedPeriod,
                                onChange: (e)=>setSelectedPeriod(e.target.value),
                                className: "px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                        value: "1_day",
                                        children: "Last 24 Hours"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                        value: "7_days",
                                        children: "Last 7 Days"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                        value: "30_days",
                                        children: "Last 30 Days"
                                    })
                                ]
                            })
                        ]
                    })
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "p-6",
                    children: filteredEvents.length === 0 ? /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "text-center py-8",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.History, {
                                className: "h-12 w-12 text-gray-400 mx-auto mb-4"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                className: "text-lg font-medium text-gray-900 mb-2",
                                children: "No Access Events"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                className: "text-gray-600",
                                children: "No access events found for the selected period."
                            })
                        ]
                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "space-y-4",
                        children: filteredEvents.map((event, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                className: "flex items-center justify-between p-4 border border-gray-200 rounded-lg",
                                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center space-x-4",
                                    children: [
                                        getEventTypeIcon(event.event_type),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex items-center space-x-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "font-medium text-gray-900",
                                                            children: event.event_type === 'ENTRY' ? 'Check In' : event.event_type === 'EXIT' ? 'Check Out' : 'Access Denied'
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: (0, _utils.cn)('inline-flex items-center px-2 py-1 rounded-full text-xs font-medium', getEventTypeColor(event.event_type)),
                                                            children: event.event_type
                                                        })
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex items-center space-x-4 text-sm text-gray-600 mt-1",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                            className: "flex items-center space-x-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                                                                    className: "h-3 w-3"
                                                                }),
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                                    children: (0, _utils.formatDate)(event.datetime, 'MMM dd, yyyy HH:mm:ss')
                                                                })
                                                            ]
                                                        }),
                                                        event.device_name && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                            className: "flex items-center space-x-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Monitor, {
                                                                    className: "h-3 w-3"
                                                                }),
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                                    children: event.device_name
                                                                })
                                                            ]
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                            className: "flex items-center space-x-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.MapPin, {
                                                                    className: "h-3 w-3"
                                                                }),
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                                    children: "Main Office"
                                                                })
                                                            ]
                                                        })
                                                    ]
                                                })
                                            ]
                                        })
                                    ]
                                })
                            }, `${event.id}-${index}`))
                    })
                })
            })
        ]
    });
};
const SecurityTab = ({ profile, user })=>{
    const [showSecuritySettings, setShowSecuritySettings] = (0, _react.useState)(false);
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Security Overview"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "grid grid-cols-1 md:grid-cols-3 gap-6",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                            className: "p-3 bg-green-100 rounded-full w-12 h-12 mx-auto mb-2",
                                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Shield, {
                                                className: "h-6 w-6 text-green-600"
                                            })
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                            className: "font-medium text-gray-900",
                                            children: "Account Status"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            className: "text-sm text-gray-600 mt-1",
                                            children: profile?.isActive ? 'Active & Secure' : 'Inactive'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                            className: "p-3 bg-blue-100 rounded-full w-12 h-12 mx-auto mb-2",
                                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Database, {
                                                className: "h-6 w-6 text-blue-600"
                                            })
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                            className: "font-medium text-gray-900",
                                            children: "Data Sync"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            className: "text-sm text-gray-600 mt-1",
                                            children: profile?.lastSync ? 'Recently Synced' : 'Not Synced'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                            className: "p-3 bg-purple-100 rounded-full w-12 h-12 mx-auto mb-2",
                                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Fingerprint, {
                                                className: "h-6 w-6 text-purple-600"
                                            })
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                            className: "font-medium text-gray-900",
                                            children: "Biometric Security"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            className: "text-sm text-gray-600 mt-1",
                                            children: (profile?.enrolledFingerprints || 0) + (profile?.enrolledFaces || 0) > 0 ? 'Enrolled' : 'Not Enrolled'
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "flex items-center justify-between mb-4",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                    className: "text-lg font-medium text-gray-900",
                                    children: "Security Settings"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Button.default, {
                                    variant: "secondary",
                                    onClick: ()=>setShowSecuritySettings(!showSecuritySettings),
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Settings, {
                                            className: "h-4 w-4 mr-2"
                                        }),
                                        showSecuritySettings ? 'Hide Settings' : 'Show Settings'
                                    ]
                                })
                            ]
                        }),
                        showSecuritySettings && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "space-y-4 border-t border-gray-200 pt-4",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                    className: "font-medium text-gray-900",
                                                    children: "Two-Factor Authentication"
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                    className: "text-sm text-gray-600",
                                                    children: "Add an extra layer of security to your account"
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_Button.default, {
                                            variant: "secondary",
                                            size: "sm",
                                            children: "Enable"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                    className: "font-medium text-gray-900",
                                                    children: "Access Notifications"
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                    className: "text-sm text-gray-600",
                                                    children: "Get notified when your biometric data is used"
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_Button.default, {
                                            variant: "secondary",
                                            size: "sm",
                                            children: "Configure"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                    className: "font-medium text-gray-900",
                                                    children: "Data Privacy"
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                    className: "text-sm text-gray-600",
                                                    children: "Manage how your biometric data is stored and used"
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_Button.default, {
                                            variant: "secondary",
                                            size: "sm",
                                            children: "Review"
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Privacy & Data Protection"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "space-y-4 text-sm text-gray-600",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-start space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                                            className: "h-4 w-4 text-green-500 mt-0.5"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            children: "Your biometric data is encrypted and stored securely"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-start space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                                            className: "h-4 w-4 text-green-500 mt-0.5"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            children: "Access logs are maintained for security and compliance purposes"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-start space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                                            className: "h-4 w-4 text-green-500 mt-0.5"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            children: "Your data is only used for authentication and attendance tracking"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-start space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                                            className: "h-4 w-4 text-green-500 mt-0.5"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            children: "You can request data deletion upon employment termination"
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            })
        ]
    });
};
const _default = StaffBiostarPage;

//# sourceMappingURL=data:application/json;base64,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