'use client';

import React, { useMemo, useCallback } from 'react';
import Link from 'next/link';
import {
  X,
  Calendar,
  Clock,
  DollarSign,
  User,
  FileText,
  Star,
  Activity,
  Target,
  Coffee,
  Plus,
  ChevronRight,
  Bell,
  TrendingUp,
  CheckCircle,
  BarChart3,
  LogOut
} from 'lucide-react';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials, formatDate } from '@/lib/utils';

interface StaffHomeSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const StaffHomeSidebar: React.FC<StaffHomeSidebarProps> = ({ isOpen, onClose }) => {
  const { user, logout } = useAuth();

  // Memoize user-related data
  const userInitials = useMemo(() =>
    user ? getInitials(user.first_name, user.last_name) : 'U',
    [user?.first_name, user?.last_name]
  );

  const currentDate = useMemo(() =>
    formatDate(new Date().toISOString(), 'EEE, MMM dd, yyyy'),
    []
  );

  // Memoize close handler
  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  // Memoize logout handler
  const handleLogout = useCallback(() => {
    logout();
    onClose();
  }, [logout, onClose]);

  // Dashboard quick actions with enhanced details
  const dashboardActions = useMemo(() => [
    {
      name: 'Apply Leave',
      href: '/staff/info/leave-management?action=apply',
      icon: Calendar,
      description: 'Submit leave request',
      badge: '15 days',
      color: 'text-green-500'
    },
    {
      name: 'Check In/Out',
      href: '/staff/info/attendance?action=checkin',
      icon: Clock,
      description: 'Mark attendance',
      color: 'text-blue-500'
    },
    {
      name: 'View Payslip',
      href: '/staff/info/salary?view=payslip',
      icon: DollarSign,
      description: 'Download latest payslip',
      color: 'text-purple-500'
    },
    {
      name: 'Update Profile',
      href: '/staff/info/profile?action=edit',
      icon: User,
      description: 'Edit personal information',
      color: 'text-orange-500'
    },
    {
      name: 'Submit Timesheet',
      href: '/staff/info/attendance?action=timesheet',
      icon: FileText,
      description: 'Log work hours',
      color: 'text-indigo-500'
    },
    {
      name: 'View Performance',
      href: '/staff/info/performance',
      icon: Star,
      description: 'Check performance metrics',
      color: 'text-yellow-500'
    }
  ], []);

  // Today's stats
  const todayStats = useMemo(() => [
    {
      name: 'Check-in Status',
      value: '08:30 AM',
      icon: Clock,
      color: 'text-green-500',
      bgColor: 'bg-green-50'
    },
    {
      name: 'Hours Today',
      value: '6.5h',
      icon: Activity,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50'
    },
    {
      name: 'Leave Balance',
      value: '15 days',
      icon: Coffee,
      color: 'text-orange-500',
      bgColor: 'bg-orange-50'
    },
    {
      name: 'Tasks Completed',
      value: '4/6',
      icon: CheckCircle,
      color: 'text-purple-500',
      bgColor: 'bg-purple-50'
    }
  ], []);

  // Dashboard features
  const dashboardFeatures = useMemo(() => [
    {
      name: 'Today\'s Overview',
      icon: Activity,
      description: 'Current day summary and stats'
    },
    {
      name: 'Recent Activities',
      icon: Clock,
      description: 'Latest actions and updates'
    },
    {
      name: 'Upcoming Events',
      icon: Calendar,
      description: 'Scheduled meetings and deadlines'
    },
    {
      name: 'Performance Metrics',
      icon: BarChart3,
      description: 'Track your progress and goals'
    }
  ], []);

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={handleClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-50 bg-white shadow-lg transform transition-transform duration-300 ease-in-out md:relative md:translate-x-0 md:z-10 md:top-0 md:h-full',
          isOpen ? 'translate-x-0' : '-translate-x-full'
        )}
        style={{ width: '368px' }}
      >
        {/* Mobile close button */}
        <div className="md:hidden absolute top-3 right-3 z-20">
          <button
            onClick={handleClose}
            className="p-2 rounded-md text-gray-400 hover:text-gray-600 bg-white shadow-sm border border-gray-200 touch-manipulation"
            aria-label="Close sidebar"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Sidebar content */}
        <div className="flex flex-col h-full overflow-y-auto md:h-[calc(100vh-112px)] md:mt-[112px]">
          {/* Header */}
          <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-4 sm:p-6 pt-12 md:pt-6">
            <div className="text-center">
              <h1 className="text-lg sm:text-xl font-bold text-white">Staff Dashboard</h1>
              <p className="text-orange-100 text-sm mt-1">Your personal workspace</p>
            </div>
          </div>

          {/* User Card */}
          <div className="p-4 sm:p-6 border-b border-gray-200">
            <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg border border-orange-200 p-3 sm:p-4 text-center">
              <div className="mb-3 sm:mb-4">
                {user?.profile_picture ? (
                  <img
                    src={user.profile_picture}
                    alt="Profile"
                    className="w-14 h-14 sm:w-16 sm:h-16 rounded-full mx-auto object-cover border-2 border-orange-300"
                  />
                ) : (
                  <div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full mx-auto flex items-center justify-center border-2 border-orange-300 shadow-md">
                    <span className="text-white text-lg sm:text-xl font-medium">
                      {userInitials}
                    </span>
                  </div>
                )}
              </div>
              <div>
                <h3 className="font-semibold text-orange-900 text-sm sm:text-base">
                  Welcome {user?.first_name || 'Staff'}
                </h3>
                <p className="text-xs sm:text-sm text-orange-700 mt-1">{currentDate}</p>
                {user?.employee_id && (
                  <p className="text-xs text-orange-600 mt-1">ID: {user.employee_id}</p>
                )}
                <div className="flex items-center justify-center mt-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-xs text-green-700 font-medium">Active</span>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="p-4 sm:p-6 border-b border-gray-200">
            <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg border border-orange-200">
              <div className="p-3 sm:p-4">
                <div className="flex items-center mb-3">
                  <Plus className="h-4 w-4 text-orange-600 mr-2" />
                  <h3 className="font-medium text-orange-900 text-sm sm:text-base">Quick Actions</h3>
                </div>
                <div className="space-y-2">
                  {dashboardActions.map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <Link
                        key={index}
                        href={action.href}
                        onClick={handleClose}
                        className="flex items-center justify-between w-full text-left py-2.5 sm:py-2 px-3 rounded-md text-sm font-medium text-gray-700 hover:bg-white hover:shadow-sm transition-all duration-150 ease-in-out touch-manipulation group"
                      >
                        <div className="flex items-center min-w-0 flex-1">
                          <Icon className={cn("h-4 w-4 mr-2 flex-shrink-0 group-hover:scale-110 transition-transform", action.color)} />
                          <div className="min-w-0 flex-1">
                            <span className="truncate block">{action.name}</span>
                            <span className="text-xs text-gray-500 truncate block">{action.description}</span>
                          </div>
                        </div>
                        {action.badge && (
                          <span className="ml-2 px-2 py-1 text-xs bg-orange-200 text-orange-800 rounded-full flex-shrink-0">
                            {action.badge}
                          </span>
                        )}
                        <ChevronRight className="h-3 w-3 text-gray-400 ml-2 flex-shrink-0 group-hover:text-gray-600" />
                      </Link>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* Today's Summary */}
          <div className="p-4 sm:p-6 border-b border-gray-200">
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
              <div className="p-3 sm:p-4">
                <div className="flex items-center mb-3">
                  <Target className="h-4 w-4 text-blue-600 mr-2" />
                  <h3 className="font-medium text-blue-900 text-sm sm:text-base">Today's Summary</h3>
                </div>
                <div className="grid grid-cols-1 gap-2">
                  {todayStats.map((stat, index) => {
                    const Icon = stat.icon;
                    return (
                      <div key={index} className={cn("flex items-center justify-between py-2 px-3 rounded-md", stat.bgColor)}>
                        <div className="flex items-center">
                          <Icon className={cn("h-4 w-4 mr-2", stat.color)} />
                          <span className="text-sm text-gray-700">{stat.name}</span>
                        </div>
                        <span className={cn("text-sm font-medium", stat.color)}>{stat.value}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* Dashboard Features */}
          <div className="p-4 sm:p-6 border-b border-gray-200">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="p-3 sm:p-4">
                <div className="flex items-center mb-3">
                  <Activity className="h-4 w-4 text-blue-600 mr-2" />
                  <h3 className="font-medium text-gray-900 text-sm sm:text-base">Dashboard Features</h3>
                </div>
                <div className="space-y-2">
                  {dashboardFeatures.map((feature, index) => {
                    const Icon = feature.icon;
                    return (
                      <div
                        key={index}
                        className="flex items-start py-2 px-3 rounded-md text-sm transition-all duration-150 ease-in-out hover:bg-blue-50 group"
                      >
                        <Icon className="h-4 w-4 mr-3 text-blue-500 flex-shrink-0 mt-0.5 group-hover:text-blue-600" />
                        <div className="min-w-0 flex-1">
                          <span className="text-gray-900 font-medium block">{feature.name}</span>
                          <span className="text-xs text-gray-500 block mt-0.5">{feature.description}</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* Spacer */}
          <div className="flex-1"></div>

          {/* User Actions */}
          <div className="p-4 sm:p-6 border-t border-gray-200 space-y-1 mt-auto">
            <Link
              href="/staff/info/profile"
              onClick={handleClose}
              className="flex items-center px-3 py-2.5 sm:py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors touch-manipulation"
            >
              <User className="h-5 w-5 mr-3 flex-shrink-0" />
              <span>Profile</span>
            </Link>
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-3 py-2.5 sm:py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors touch-manipulation"
            >
              <LogOut className="h-5 w-5 mr-3 flex-shrink-0" />
              <span>Logout</span>
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default StaffHomeSidebar;
