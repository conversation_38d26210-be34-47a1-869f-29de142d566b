# This file is distributed under the same license as the PACKAGE package.
# <PERSON> <<EMAIL>>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-24 11:40-0500\n"
"PO-Revision-Date: 2022-10-17 17:42+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.1.1\n"

#: admin.py templates/admin/import_export/change_list_import_item.html
#: templates/admin/import_export/import.html
msgid "Import"
msgstr "Importieren"

#: admin.py
#, python-format
msgid "%s through import_export"
msgstr "%s durch import_export"

#: admin.py
msgid "Import finished: {} new, {} updated, {} deleted and {} skipped {}."
msgstr ""
"Import fertiggestellt: {} neue, {} aktualisierte, {} gelöschte und {} "
"übersprungene {}."

#: admin.py
#, python-format
msgid ""
"%(exc_name)s encountered while trying to read file. Ensure you have chosen "
"the correct format for the file."
msgstr ""
"%(exc_name)s trat auf, beim Versuch, die Datei zu lesen. Stelle sicher, dass "
"du das richtige Format für die Datei gewählt hast."

#: admin.py
msgid ""
"No valid data to import. Ensure your file has the correct headers or data "
"for import."
msgstr ""
"Keine gültigen Daten für den Import. Stelle sicher, dass deine Datei die "
"korrektenKopfzeilen und Daten für den Import hat."

#: admin.py templates/admin/import_export/change_form.html
#: templates/admin/import_export/change_list_export_item.html
#: templates/admin/import_export/export.html
msgid "Export"
msgstr "Exportieren"

#: admin.py
#, python-format
msgid "Export selected %(verbose_name_plural)s"
msgstr "Ausgewählte %(verbose_name_plural)s exportieren"

#: formats/base_formats.py
msgid "export failed due to IllegalCharacterError"
msgstr "Export schlug fehl wegen IllegalCharacterError"

#: forms.py
msgid "Resource"
msgstr "Ressource"

#: forms.py
msgid "Format"
msgstr "Dateiformat"

#: forms.py
msgid "File to import"
msgstr "Zu importierende Datei"

#: forms.py
msgid "Form is not validated, call `is_valid` first"
msgstr "Formular ist nicht validiert, führe zuerst `is_valid` aus"

#: forms.py
#, python-format
msgid "Select at least 1 field for \"%(resource_name)s\" to export"
msgstr "Wähle mindestens 1 Feld für \"%(resource_name)s\" zum Exportieren aus"

#: resources.py
#, python-format
msgid ""
"The following fields are declared in 'import_id_fields' but are not present "
"in the resource fields: %s"
msgstr ""
"Die folgenden Felder sind in 'import_id_fields' deklariert, sind aber keine "
"Felder der Ressource: %s"

#: resources.py
#, python-format
msgid ""
"The following fields are declared in 'import_id_fields' but are not present "
"in the file headers: %s"
msgstr ""
"Die folgenden Felder sind in 'import_id_fields' deklariert, aber nicht in "
"der Kopfzeile der Datei vorhanden: %s"

#: results.py
#, python-format
msgid "call to force_str() on instance failed: %s"
msgstr "Aufruf von force_str() in der Instanz schlug fehl: %s"

#: templates/admin/import_export/base.html
msgid "Home"
msgstr "Start"

#: templates/admin/import_export/export.html
#, python-format
msgid "Export %(len)s selected item."
msgid_plural "Export %(len)s selected items."
msgstr[0] "Exportiere %(len)s ausgewähltes Element."
msgstr[1] "Exportiere %(len)s ausgewählte Elemente."

#: templates/admin/import_export/export.html
#: templates/admin/import_export/resource_fields_list.html
msgid "This exporter will export the following fields: "
msgstr "Es werden die folgenden Felder exportiert: "

#: templates/admin/import_export/export.html
#: templates/admin/import_export/import.html
msgid "Submit"
msgstr "Absenden"

#: templates/admin/import_export/import.html
msgid ""
"Below is a preview of data to be imported. If you are satisfied with the "
"results, click 'Confirm import'"
msgstr ""
"Unten befindet sich eine Vorschau der zu importierenden Daten. Wenn die "
"Ergebnisse zufriedenstellend sind, klicke auf \"Import bestätigen\"."

#: templates/admin/import_export/import.html
msgid "Confirm import"
msgstr "Import bestätigen"

#: templates/admin/import_export/import.html
msgid "Errors"
msgstr "Fehler"

#: templates/admin/import_export/import.html
msgid "Line number"
msgstr "Zeilennummer"

#: templates/admin/import_export/import.html
msgid "Some rows failed to validate"
msgstr "Die Validierung einiger Zeilen schlug fehl"

#: templates/admin/import_export/import.html
msgid ""
"Please correct these errors in your data where possible, then reupload it "
"using the form above."
msgstr ""
"Bitte korrigiere falls möglich diese Fehler in deiner Datei und lade sie "
"anschließend erneut mit dem obigen Formular hoch."

#: templates/admin/import_export/import.html
msgid "Row"
msgstr "Zeile"

#: templates/admin/import_export/import.html
msgid "Non field specific"
msgstr "Nicht feldspezifisch"

#: templates/admin/import_export/import.html
msgid "Preview"
msgstr "Vorschau"

#: templates/admin/import_export/import.html
msgid "New"
msgstr "Neu"

#: templates/admin/import_export/import.html
msgid "Skipped"
msgstr "Übersprungen"

#: templates/admin/import_export/import.html
msgid "Delete"
msgstr "Löschen"

#: templates/admin/import_export/import.html
msgid "Update"
msgstr "Ändern"

#: templates/admin/import_export/resource_fields_list.html
msgid "This importer will import the following fields: "
msgstr "Es werden die folgenden Felder importiert: "

#: widgets.py
msgid "Value could not be parsed."
msgstr "Wert konnte nicht eingelesen werden."

#: widgets.py
msgid "use_natural_foreign_keys and key_is_id cannot both be True"
msgstr "use_natural_foreign_keys und key_is_id können nicht beide True sein"
