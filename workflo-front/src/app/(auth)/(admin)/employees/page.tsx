'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Users, Plus, Search, Filter, Download, Upload, Grid, List, Trash2,
  UserCheck, UserX, Building, RefreshCw, CheckSquare, Square, ChevronDown, Loader2, Calendar
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useAuth } from '@/providers/AuthProvider';
import { Employee, EmployeeFilters, Department, EmployeeBulkAction } from '@/types';
import { cn } from '@/lib/utils';
import { EmployeeApi } from '@/lib/api/employeeApi';
import CreateEmployeeModal from '@/components/employees/CreateEmployeeModal';
import EditEmployeeModal from '@/components/employees/EditEmployeeModal';
import DeleteEmployeeModal from '@/components/employees/DeleteEmployeeModal';

// Enhanced Employee Management Component
const EmployeesPage: React.FC = () => {
  const { user } = useAuth();

  // State Management
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [selectedEmployees, setSelectedEmployees] = useState<Set<number>>(new Set());
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalCount, setTotalCount] = useState(0);

  // Filters
  const [filters, setFilters] = useState<EmployeeFilters>({
    search: '',
    department: undefined,
    status: '',
    employment_type: '',
    role: '',
  });

  // Sorting
  const [sortField, setSortField] = useState<keyof Employee>('first_name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Modals
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);

  // Load employees data
  const loadEmployees = useCallback(async () => {
    try {
      setLoading(true);
      const response = await EmployeeApi.getEmployees(filters, currentPage, pageSize);
      setEmployees(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Failed to load employees:', error);
    } finally {
      setLoading(false);
    }
  }, [filters, currentPage, pageSize]);

  // Load departments
  const loadDepartments = useCallback(async () => {
    try {
      const depts = await EmployeeApi.getDepartments();
      setDepartments(depts);
    } catch (error) {
      console.error('Failed to load departments:', error);
    }
  }, []);

  // Initial data load
  useEffect(() => {
    loadEmployees();
    loadDepartments();
  }, [loadEmployees, loadDepartments]);

  // Handle filter changes
  const handleFilterChange = useCallback((key: keyof EmployeeFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page when filtering
  }, []);

  // Handle sorting
  const handleSort = useCallback((field: keyof Employee) => {
    if (sortField === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  }, [sortField]);

  // Handle employee selection
  const handleEmployeeSelect = useCallback((employeeId: number, selected: boolean) => {
    setSelectedEmployees(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(employeeId);
      } else {
        newSet.delete(employeeId);
      }
      return newSet;
    });
  }, []);

  // Handle select all
  const handleSelectAll = useCallback((selected: boolean) => {
    if (selected) {
      setSelectedEmployees(new Set(employees.map(emp => emp.id)));
    } else {
      setSelectedEmployees(new Set());
    }
  }, [employees]);

  // Handle bulk actions
  const handleBulkAction = useCallback(async (action: EmployeeBulkAction) => {
    try {
      setActionLoading(true);
      const result = await EmployeeApi.bulkAction(action);
      console.log('Bulk action result:', result);

      // Reload employees after bulk action
      await loadEmployees();
      setSelectedEmployees(new Set());
      setShowBulkActions(false);
    } catch (error) {
      console.error('Bulk action failed:', error);
    } finally {
      setActionLoading(false);
    }
  }, [loadEmployees]);

  // Handle employee actions
  const handleCreateEmployee = useCallback(() => {
    setSelectedEmployee(null);
    setShowCreateModal(true);
  }, []);

  const handleEditEmployee = useCallback((employee: Employee) => {
    setSelectedEmployee(employee);
    setShowEditModal(true);
  }, []);

  const handleDeleteEmployee = useCallback((employee: Employee) => {
    setSelectedEmployee(employee);
    setShowDeleteModal(true);
  }, []);

  const handleViewEmployee = useCallback((employee: Employee) => {
    // Navigate to employee detail page
    window.location.href = `/employees/${employee.id}`;
  }, []);

  // CRUD Modal Handlers
  const handleCreateSubmit = useCallback(async (data: any) => {
    try {
      setActionLoading(true);
      await EmployeeApi.createEmployee(data);
      await loadEmployees();
      setShowCreateModal(false);
    } catch (error) {
      console.error('Failed to create employee:', error);
      throw error;
    } finally {
      setActionLoading(false);
    }
  }, [loadEmployees]);

  const handleEditSubmit = useCallback(async (id: number, data: any) => {
    try {
      setActionLoading(true);
      await EmployeeApi.updateEmployee(id, data);
      await loadEmployees();
      setShowEditModal(false);
    } catch (error) {
      console.error('Failed to update employee:', error);
      throw error;
    } finally {
      setActionLoading(false);
    }
  }, [loadEmployees]);

  const handleDeleteConfirm = useCallback(async (id: number) => {
    try {
      setActionLoading(true);
      await EmployeeApi.deleteEmployee(id);
      await loadEmployees();
      setShowDeleteModal(false);
    } catch (error) {
      console.error('Failed to delete employee:', error);
      throw error;
    } finally {
      setActionLoading(false);
    }
  }, [loadEmployees]);

  // Export employees
  const handleExport = useCallback(async (format: 'csv' | 'excel' | 'pdf') => {
    try {
      setActionLoading(true);
      const result = await EmployeeApi.exportEmployees(format, filters);
      // Download the file
      window.open(result.download_url, '_blank');
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setActionLoading(false);
    }
  }, [filters]);

  // Import employees
  const handleImport = useCallback(() => {
    setShowImportModal(true);
  }, []);

  // Computed values
  const isAllSelected = useMemo(() => {
    return employees.length > 0 && selectedEmployees.size === employees.length;
  }, [employees.length, selectedEmployees.size]);

  const isPartiallySelected = useMemo(() => {
    return selectedEmployees.size > 0 && selectedEmployees.size < employees.length;
  }, [selectedEmployees.size, employees.length]);

  const totalPages = useMemo(() => {
    return Math.ceil(totalCount / pageSize);
  }, [totalCount, pageSize]);

  // Filter options
  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'on_leave', label: 'On Leave' },
    { value: 'terminated', label: 'Terminated' },
  ];

  const employmentTypeOptions = [
    { value: '', label: 'All Types' },
    { value: 'full_time', label: 'Full Time' },
    { value: 'part_time', label: 'Part Time' },
    { value: 'contract', label: 'Contract' },
    { value: 'intern', label: 'Intern' },
  ];

  const roleOptions = [
    { value: '', label: 'All Roles' },
    { value: 'employee', label: 'Employee' },
    { value: 'supervisor', label: 'Supervisor' },
    { value: 'hr', label: 'HR' },
    { value: 'accountant', label: 'Accountant' },
    { value: 'admin', label: 'Admin' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Employee Management</h1>
            <p className="text-gray-600 mt-1">
              Manage your organization's employees with comprehensive CRUD operations
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-3">
            <Button
              variant="secondary"
              onClick={handleImport}
              disabled={actionLoading}
            >
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <div className="relative">
              <Button
                variant="secondary"
                onClick={() => setShowBulkActions(!showBulkActions)}
                disabled={actionLoading}
              >
                <Download className="h-4 w-4 mr-2" />
                Export
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
              {showBulkActions && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                  <div className="py-1">
                    <button
                      onClick={() => handleExport('csv')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Export as CSV
                    </button>
                    <button
                      onClick={() => handleExport('excel')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Export as Excel
                    </button>
                    <button
                      onClick={() => handleExport('pdf')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Export as PDF
                    </button>
                  </div>
                </div>
              )}
            </div>
            <Button
              variant="primary"
              onClick={handleCreateEmployee}
              disabled={actionLoading}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Employee
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Employees</p>
                <p className="text-2xl font-semibold text-gray-900">{totalCount}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserCheck className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {employees.filter(emp => emp.status === 'active').length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Building className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Departments</p>
                <p className="text-2xl font-semibold text-gray-900">{departments.length}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">New This Month</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {employees.filter(emp => {
                    const hireDate = new Date(emp.hire_date);
                    const now = new Date();
                    return hireDate.getMonth() === now.getMonth() &&
                           hireDate.getFullYear() === now.getFullYear();
                  }).length}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          {/* Left side - Search and filters toggle */}
          <div className="flex items-center space-x-4">
            <div className="w-80">
              <Input
                placeholder="Search employees by name, email, or ID..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                leftIcon={<Search className="h-4 w-4" />}
              />
            </div>
            <Button
              variant="secondary"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {Object.values(filters).some(v => v && v !== '') && (
                <span className="ml-2 bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                  Active
                </span>
              )}
            </Button>
          </div>

          {/* Right side - View mode and actions */}
          <div className="flex items-center space-x-4">
            {/* Selected count and bulk actions */}
            {selectedEmployees.size > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">
                  {selectedEmployees.size} selected
                </span>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => handleBulkAction({
                    action: 'activate',
                    employee_ids: Array.from(selectedEmployees)
                  })}
                  disabled={actionLoading}
                >
                  <UserCheck className="h-4 w-4 mr-1" />
                  Activate
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => handleBulkAction({
                    action: 'deactivate',
                    employee_ids: Array.from(selectedEmployees)
                  })}
                  disabled={actionLoading}
                >
                  <UserX className="h-4 w-4 mr-1" />
                  Deactivate
                </Button>
                <Button
                  variant="danger"
                  size="sm"
                  onClick={() => handleBulkAction({
                    action: 'delete',
                    employee_ids: Array.from(selectedEmployees)
                  })}
                  disabled={actionLoading}
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </Button>
              </div>
            )}

            {/* View Mode Toggle */}
            <div className="flex items-center space-x-1 bg-gray-100 rounded-md p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={cn(
                  'p-2 rounded-md transition-colors',
                  viewMode === 'grid'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                )}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={cn(
                  'p-2 rounded-md transition-colors',
                  viewMode === 'list'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                )}
              >
                <List className="h-4 w-4" />
              </button>
            </div>

            <Button
              variant="secondary"
              onClick={loadEmployees}
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Department
                </label>
                <select
                  value={filters.department || ''}
                  onChange={(e) => handleFilterChange('department', e.target.value ? Number(e.target.value) : undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  <option value="">All Departments</option>
                  {departments.map(dept => (
                    <option key={dept.id} value={dept.id}>
                      {dept.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={filters.status || ''}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  {statusOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Employment Type
                </label>
                <select
                  value={filters.employment_type || ''}
                  onChange={(e) => handleFilterChange('employment_type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  {employmentTypeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Role
                </label>
                <select
                  value={filters.role || ''}
                  onChange={(e) => handleFilterChange('role', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  {roleOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="mt-4 flex items-center space-x-3">
              <Button
                variant="secondary"
                onClick={() => {
                  setFilters({
                    search: '',
                    department: undefined,
                    status: '',
                    employment_type: '',
                    role: '',
                  });
                }}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Employee List/Grid */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="p-6">
            <div className="animate-pulse space-y-4">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : employees.length === 0 ? (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No employees found
            </h3>
            <p className="text-gray-600 mb-4">
              {Object.values(filters).some(v => v && v !== '')
                ? 'Try adjusting your search criteria or filters.'
                : 'Get started by adding your first employee.'
              }
            </p>
            <Button variant="primary" onClick={handleCreateEmployee}>
              <Plus className="h-4 w-4 mr-2" />
              Add Employee
            </Button>
          </div>
        ) : (
          <div className="p-6">
            {/* Select All Checkbox */}
            <div className="flex items-center mb-6">
              <button
                onClick={() => handleSelectAll(!isAllSelected)}
                className="flex items-center text-sm text-gray-600 hover:text-gray-900"
              >
                {isAllSelected ? (
                  <CheckSquare className="h-4 w-4 mr-2 text-orange-600" />
                ) : isPartiallySelected ? (
                  <Square className="h-4 w-4 mr-2 text-orange-600 bg-orange-100" />
                ) : (
                  <Square className="h-4 w-4 mr-2" />
                )}
                Select All ({employees.length})
              </button>
            </div>

            {/* Employee Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Employee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Position
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Department
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Employment Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Start Date
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {employees.map((employee) => (
                    <tr key={employee.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={selectedEmployees.has(employee.id)}
                            onChange={(e) => handleEmployeeSelect(employee.id, e.target.checked)}
                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded mr-4"
                          />
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              {employee.profile_picture ? (
                                <img
                                  className="h-10 w-10 rounded-full object-cover"
                                  src={employee.profile_picture}
                                  alt={`${employee.first_name} ${employee.last_name}`}
                                />
                              ) : (
                                <div className="h-10 w-10 rounded-full bg-orange-500 flex items-center justify-center">
                                  <span className="text-sm font-medium text-white">
                                    {employee.first_name.charAt(0)}{employee.last_name.charAt(0)}
                                  </span>
                                </div>
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {employee.first_name} {employee.last_name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {employee.email}
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{employee.position}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{employee.department_name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize">
                          {employee.employment_type.replace('_', ' ')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={cn(
                          "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                          employee.status === 'active'
                            ? "bg-green-100 text-green-800"
                            : employee.status === 'inactive'
                            ? "bg-red-100 text-red-800"
                            : "bg-yellow-100 text-yellow-800"
                        )}>
                          {employee.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(employee.hire_date).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => handleViewEmployee(employee)}
                          >
                            View
                          </Button>
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => handleEditEmployee(employee)}
                          >
                            Edit
                          </Button>
                          <Button
                            variant="danger"
                            size="sm"
                            onClick={() => handleDeleteEmployee(employee)}
                          >
                            Delete
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-6 flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} employees
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm text-gray-600">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* CRUD Modals */}
      <CreateEmployeeModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateSubmit}
        departments={departments}
        loading={actionLoading}
      />

      <EditEmployeeModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSubmit={handleEditSubmit}
        employee={selectedEmployee}
        departments={departments}
        loading={actionLoading}
      />

      <DeleteEmployeeModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteConfirm}
        employee={selectedEmployee}
        loading={actionLoading}
      />
    </div>
  );
};

export default EmployeesPage;
