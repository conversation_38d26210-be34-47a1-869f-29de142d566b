'use client';

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Building,
  DollarSign,
  User,
  Crown,
  Shield,
  Clock,
  FileText,
  Award,
  TrendingUp,
  Users,
  MoreHorizontal,
  Download,
  Eye,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials, formatDate, formatCurrency } from '@/lib/utils';
import DocumentsTab from '@/components/employees/tabs/DocumentsTab';
import LeaveManagementTab from '@/components/employees/tabs/LeaveManagementTab';
import AttendanceTab from '@/components/employees/tabs/AttendanceTab';
import SalaryTab from '@/components/employees/tabs/SalaryTab';
import PerformanceTab from '@/components/employees/tabs/PerformanceTab';
import EmployeeForm from '@/components/employees/EmployeeForm';
import { Employee as ServiceEmployee, employeeService } from '@/lib/employeeService';

interface Employee {
  id: number;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  profile_picture?: string;
  department_id: number;
  department_name: string;
  position: string;
  hire_date: string;
  salary: number;
  employment_type: 'full_time' | 'part_time' | 'contract' | 'intern';
  status: 'active' | 'inactive' | 'on_leave' | 'terminated';
  role: 'employee' | 'supervisor' | 'hr' | 'accountant' | 'admin';
  address?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  date_of_birth?: string;
  gender?: 'male' | 'female' | 'other';
  marital_status?: 'single' | 'married' | 'divorced' | 'widowed';
  nationality?: string;
  created_at: string;
  updated_at: string;
}

// Mock employee data
const mockEmployees: Employee[] = [
  {
    id: 1,
    employee_id: 'EMP001',
    first_name: 'Maria',
    last_name: 'Cotton',
    email: '<EMAIL>',
    phone_number: '******-0101',
    profile_picture: '/api/placeholder/150/150',
    department_id: 1,
    department_name: 'Engineering',
    position: 'Senior Software Engineer',
    hire_date: '2022-03-15',
    salary: 95000,
    employment_type: 'full_time',
    status: 'active',
    role: 'employee',
    address: '123 Tech Street, San Francisco, CA 94105',
    emergency_contact_name: 'John Cotton',
    emergency_contact_phone: '******-0102',
    date_of_birth: '1990-05-15',
    gender: 'female',
    marital_status: 'married',
    nationality: 'American',
    created_at: '2022-03-15T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z'
  },
  {
    id: 2,
    employee_id: 'EMP002',
    first_name: 'John',
    last_name: 'Smith',
    email: '<EMAIL>',
    phone_number: '******-0201',
    profile_picture: '/api/placeholder/150/150',
    department_id: 1,
    department_name: 'Engineering',
    position: 'Engineering Team Lead',
    hire_date: '2021-06-01',
    salary: 120000,
    employment_type: 'full_time',
    status: 'active',
    role: 'supervisor',
    address: '456 Innovation Ave, San Francisco, CA 94107',
    emergency_contact_name: 'Jane Smith',
    emergency_contact_phone: '******-0202',
    date_of_birth: '1985-08-22',
    gender: 'male',
    marital_status: 'married',
    nationality: 'American',
    created_at: '2021-06-01T00:00:00Z',
    updated_at: '2024-01-10T00:00:00Z'
  },
  {
    id: 3,
    employee_id: 'EMP003',
    first_name: 'Sarah',
    last_name: 'Johnson',
    email: '<EMAIL>',
    phone_number: '******-0301',
    profile_picture: '/api/placeholder/150/150',
    department_id: 2,
    department_name: 'Human Resources',
    position: 'HR Manager',
    hire_date: '2020-09-01',
    salary: 85000,
    employment_type: 'full_time',
    status: 'active',
    role: 'hr',
    address: '789 Business Blvd, San Francisco, CA 94108',
    emergency_contact_name: 'Michael Johnson',
    emergency_contact_phone: '******-0302',
    date_of_birth: '1988-12-03',
    gender: 'female',
    marital_status: 'single',
    nationality: 'American',
    created_at: '2020-09-01T00:00:00Z',
    updated_at: '2024-01-12T00:00:00Z'
  }
];

const EmployeeProfilePage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [employee, setEmployee] = useState<ServiceEmployee | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'personal' | 'employment' | 'documents' | 'leave' | 'attendance' | 'salary' | 'performance'>('overview');
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const employeeId = params?.id ? parseInt(params.id as string) : null;

  useEffect(() => {
    const loadEmployee = async () => {
      if (!employeeId) {
        setError('Invalid employee ID');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const foundEmployee = await employeeService.getEmployeeById(employeeId);
        if (foundEmployee) {
          setEmployee(foundEmployee);
        } else {
          setError('Employee not found');
        }
      } catch (err) {
        setError('Failed to load employee data');
        console.error('Error loading employee:', err);
      } finally {
        setLoading(false);
      }
    };

    loadEmployee();
  }, [employeeId]);

  const handleEditEmployee = () => {
    setShowEditForm(true);
  };

  const handleDeleteEmployee = async () => {
    if (!employee) return;

    if (confirm(`Are you sure you want to delete ${employee.first_name} ${employee.last_name}?`)) {
      try {
        await employeeService.deleteEmployee(employee.id);
        router.push('/employees');
      } catch (error) {
        console.error('Error deleting employee:', error);
        alert('Failed to delete employee');
      }
    }
  };

  const handleFormSave = async (data: any) => {
    try {
      if (employee) {
        const updatedEmployee = await employeeService.updateEmployee({ id: employee.id, ...data });
        if (updatedEmployee) {
          setEmployee(updatedEmployee);
        }
      }
      setShowEditForm(false);
    } catch (error) {
      console.error('Error updating employee:', error);
      throw error;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'on_leave': return 'bg-yellow-100 text-yellow-800';
      case 'terminated': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return <Shield className="h-4 w-4" />;
      case 'supervisor': return <Crown className="h-4 w-4" />;
      case 'hr': return <Users className="h-4 w-4" />;
      default: return <User className="h-4 w-4" />;
    }
  };

  const calculateTenure = (hireDate: string) => {
    const hire = new Date(hireDate);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - hire.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);

    if (years > 0) {
      return `${years} year${years > 1 ? 's' : ''}, ${months} month${months > 1 ? 's' : ''}`;
    }
    return `${months} month${months > 1 ? 's' : ''}`;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-4">
              <div className="w-24 h-24 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !employee) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link href="/employees">
            <Button variant="secondary">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Employees
            </Button>
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {error || 'Employee Not Found'}
          </h2>
          <p className="text-gray-600 mb-6">
            The employee you're looking for doesn't exist or has been removed.
          </p>
          <Link href="/employees">
            <Button variant="primary">
              Return to Employee List
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const userInitials = getInitials(employee.first_name, employee.last_name);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/employees">
            <Button variant="secondary">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Employees
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Employee Profile</h1>
            <p className="text-gray-600">View and manage employee information</p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Export Profile
          </Button>
          <Button variant="secondary" onClick={handleEditEmployee}>
            <Edit className="h-4 w-4 mr-2" />
            Edit Employee
          </Button>
          <Button variant="secondary" onClick={handleDeleteEmployee}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Employee
          </Button>
        </div>
      </div>

      {/* Employee Header Card */}
      <Card>
        <div className="p-6">
          <div className="flex items-start space-x-6">
            {/* Profile Picture */}
            <div className="flex-shrink-0">
              {employee.profile_picture ? (
                <img
                  src={employee.profile_picture}
                  alt={`${employee.first_name} ${employee.last_name}`}
                  className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"
                />
              ) : (
                <div className="w-24 h-24 bg-orange-500 rounded-full flex items-center justify-center border-4 border-white shadow-lg">
                  <span className="text-white text-2xl font-bold">{userInitials}</span>
                </div>
              )}
            </div>

            {/* Employee Info */}
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h2 className="text-2xl font-bold text-gray-900">
                  {employee.first_name} {employee.last_name}
                </h2>
                <span className={cn(
                  'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
                  getStatusColor(employee.status)
                )}>
                  {employee.status.replace('_', ' ').toUpperCase()}
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center text-gray-600">
                  <User className="h-4 w-4 mr-2" />
                  <span>{employee.employee_id}</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Building className="h-4 w-4 mr-2" />
                  <span>{employee.position.department}</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <User className="h-4 w-4 mr-2" />
                  <span className="ml-2 capitalize">Employee</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  <span>{calculateTenure(employee.position.start_date)}</span>
                </div>
              </div>

              <div className="mt-4">
                <h3 className="text-lg font-medium text-gray-900">{employee.position.title}</h3>
                <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 mr-1" />
                    <a href={`mailto:${employee.email}`} className="hover:text-orange-600">
                      {employee.email}
                    </a>
                  </div>
                  {employee.phone && (
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-1" />
                      <a href={`tel:${employee.phone}`} className="hover:text-orange-600">
                        {employee.phone}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="flex-shrink-0 text-right">
              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                <div>
                  <div className="text-2xl font-bold text-gray-900">
                    {formatCurrency(employee.position.salary, employee.position.currency)}
                  </div>
                  <div className="text-sm text-gray-600">Annual Salary</div>
                </div>
                <div>
                  <div className="text-lg font-semibold text-gray-900">
                    {employee.position.employment_type.replace('_', ' ').toUpperCase()}
                  </div>
                  <div className="text-sm text-gray-600">Employment Type</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { key: 'overview', label: 'Overview', icon: Eye },
              { key: 'personal', label: 'Personal Info', icon: User },
              { key: 'employment', label: 'Employment', icon: Building },
              { key: 'documents', label: 'Documents', icon: FileText },
              { key: 'leave', label: 'Leave Management', icon: Calendar },
              { key: 'attendance', label: 'Attendance', icon: Clock },
              { key: 'salary', label: 'Salary', icon: DollarSign },
              { key: 'performance', label: 'Performance', icon: TrendingUp },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={cn(
                    'py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2',
                    activeTab === tab.key
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Quick Info */}
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Information</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Hire Date</label>
                        <p className="text-sm text-gray-900">{formatDate(employee.position.start_date)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Department</label>
                        <p className="text-sm text-gray-900">{employee.position.department}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Position</label>
                        <p className="text-sm text-gray-900">{employee.position.title}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Employment Type</label>
                        <p className="text-sm text-gray-900 capitalize">
                          {employee.position.employment_type.replace('_', ' ')}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Role</label>
                        <p className="text-sm text-gray-900 capitalize">Employee</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Status</label>
                        <span className={cn(
                          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                          getStatusColor(employee.status)
                        )}>
                          {employee.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>

                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 text-gray-400 mr-3" />
                        <span className="text-sm text-gray-900">{employee.email}</span>
                      </div>
                      {employee.phone && (
                        <div className="flex items-center">
                          <Phone className="h-4 w-4 text-gray-400 mr-3" />
                          <span className="text-sm text-gray-900">{employee.phone}</span>
                        </div>
                      )}
                      {employee.address && (
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 text-gray-400 mr-3" />
                          <span className="text-sm text-gray-900">{employee.address.street}, {employee.address.city}, {employee.address.state}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </Card>
              </div>

              {/* Stats */}
              <div className="space-y-6">
                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Performance</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Overall Rating</span>
                        <div className="flex items-center">
                          <Award className="h-4 w-4 text-yellow-400 mr-1" />
                          <span className="text-sm font-medium">4.8/5.0</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Projects Completed</span>
                        <span className="text-sm font-medium">12</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Training Completed</span>
                        <span className="text-sm font-medium">8</span>
                      </div>
                    </div>
                  </div>
                </Card>

                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
                    <div className="space-y-3 text-sm">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-600">Completed training module</span>
                      </div>
                      <div className="flex items-center">
                        <TrendingUp className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-600">Performance review submitted</span>
                      </div>
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-600">Updated profile information</span>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}

          {activeTab === 'personal' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <div className="p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Personal Details</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Full Name</label>
                      <p className="text-sm text-gray-900">{employee.first_name} {employee.last_name}</p>
                    </div>
                    {employee.date_of_birth && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Date of Birth</label>
                        <p className="text-sm text-gray-900">{formatDate(employee.date_of_birth)}</p>
                      </div>
                    )}
                    {employee.gender && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Gender</label>
                        <p className="text-sm text-gray-900 capitalize">{employee.gender}</p>
                      </div>
                    )}
                    {employee.marital_status && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Marital Status</label>
                        <p className="text-sm text-gray-900 capitalize">{employee.marital_status}</p>
                      </div>
                    )}
                    {employee.nationality && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Nationality</label>
                        <p className="text-sm text-gray-900">{employee.nationality}</p>
                      </div>
                    )}
                  </div>
                </div>
              </Card>

              <Card>
                <div className="p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Emergency Contact</h3>
                  <div className="space-y-4">
                    {employee.emergency_contact.name && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Contact Name</label>
                        <p className="text-sm text-gray-900">{employee.emergency_contact.name}</p>
                      </div>
                    )}
                    {employee.emergency_contact.phone && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Contact Phone</label>
                        <p className="text-sm text-gray-900">{employee.emergency_contact.phone}</p>
                      </div>
                    )}
                    {employee.emergency_contact.relationship && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Relationship</label>
                        <p className="text-sm text-gray-900">{employee.emergency_contact.relationship}</p>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            </div>
          )}

          {activeTab === 'employment' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <div className="p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Employment Information</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Employee ID</label>
                      <p className="text-sm text-gray-900">{employee.employee_id}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Department</label>
                      <p className="text-sm text-gray-900">{employee.position.department}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Position</label>
                      <p className="text-sm text-gray-900">{employee.position.title}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Hire Date</label>
                      <p className="text-sm text-gray-900">{formatDate(employee.position.start_date)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Employment Type</label>
                      <p className="text-sm text-gray-900 capitalize">
                        {employee.position.employment_type.replace('_', ' ')}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Manager</label>
                      <p className="text-sm text-gray-900">{employee.position.manager}</p>
                    </div>
                  </div>
                </div>
              </Card>

              <Card>
                <div className="p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Compensation</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Annual Salary</label>
                      <p className="text-lg font-semibold text-gray-900">{formatCurrency(employee.position.salary, employee.position.currency)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Tenure</label>
                      <p className="text-sm text-gray-900">{calculateTenure(employee.position.start_date)}</p>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          )}

          {activeTab === 'documents' && (
            <DocumentsTab
              employeeId={employee.id}
              employeeName={`${employee.first_name} ${employee.last_name}`}
            />
          )}

          {activeTab === 'leave' && (
            <LeaveManagementTab
              employeeId={employee.id}
              employeeName={`${employee.first_name} ${employee.last_name}`}
              isCurrentUser={user?.id === employee.id}
            />
          )}

          {activeTab === 'attendance' && (
            <AttendanceTab
              employeeId={employee.id}
              employeeName={`${employee.first_name} ${employee.last_name}`}
              isCurrentUser={user?.id === employee.id}
            />
          )}

          {activeTab === 'salary' && (
            <SalaryTab
              employeeId={employee.id}
              employeeName={`${employee.first_name} ${employee.last_name}`}
              isCurrentUser={user?.id === employee.id}
            />
          )}

          {activeTab === 'performance' && (
            <PerformanceTab
              employeeId={employee.id}
              employeeName={`${employee.first_name} ${employee.last_name}`}
              isCurrentUser={user?.id === employee.id}
            />
          )}
        </div>
      </div>

      {/* Employee Edit Form Modal */}
      <EmployeeForm
        isOpen={showEditForm}
        onClose={() => setShowEditForm(false)}
        onSave={handleFormSave}
        employee={employee}
        mode="edit"
      />
    </div>
  );
};

export default EmployeeProfilePage;
