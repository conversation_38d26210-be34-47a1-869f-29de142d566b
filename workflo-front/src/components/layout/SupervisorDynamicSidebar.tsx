'use client';

import React, { useMemo, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Home,
  User,
  Building,
  Calendar,
  FileText,
  Star,
  Settings,
  X,
  LogOut,
  Plus,
  Clock,
  DollarSign,
  Award,
  Activity,
  Bell,
  Shield,
  Edit,
  Eye,
  Download,
  Briefcase,
  Coffee,
  Target,
  ChevronRight,
  Info,
  BellRing,
  Users,
  CheckCircle,
  AlertCircle,
  UserCheck,
  CalendarCheck,
  TrendingUp,
  BarChart3,
  UserPlus,
  ClipboardList,
  Zap,
  Filter,
  Megaphone,
  AlertTriangle,
  History,
  Workflow,
  CheckSquare
} from 'lucide-react';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials, formatDate } from '@/lib/utils';

interface SupervisorSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

// Define page-specific features and actions for supervisor
const getSupervisorPageFeatures = (pathname: string) => {
  switch (pathname) {
    case '/supervisor':
      return {
        title: 'Supervisor Dashboard',
        breadcrumb: 'Supervisor Home',
        quickActions: [
          { name: 'Approve Leave', icon: CalendarCheck, href: '/supervisor/manage/leave-management?action=approve' },
          { name: 'Manage Overtime', icon: Clock, href: '/supervisor/manage/overtime?action=apply' },
          { name: 'View Team', icon: Users, href: '/supervisor/manage?view=team' },
          { name: 'Check Notifications', icon: Bell, href: '/supervisor/notifications' },
          { name: 'Team Performance', icon: BarChart3, href: '/supervisor/manage?view=performance' },
          { name: 'Schedule Meeting', icon: Calendar, href: '/supervisor/manage?action=meeting' },
        ],
        features: [
          { name: 'Team Overview', icon: Users },
          { name: 'Pending Approvals', icon: CheckCircle },
          { name: 'Team Performance', icon: TrendingUp },
          { name: 'Quick Actions', icon: Zap },
          { name: 'Recent Activities', icon: Activity },
          { name: 'Team Statistics', icon: BarChart3 },
        ]
      };

    case '/supervisor/info':
      return {
        title: 'Personal Info',
        breadcrumb: 'Supervisor Information Hub',
        quickActions: [
          { name: 'Edit Profile', icon: Edit, href: '/supervisor/info/profile?action=edit' },
          { name: 'View Documents', icon: FileText, href: '/supervisor/info/documents' },
          { name: 'Check Leave Balance', icon: Calendar, href: '/supervisor/info/leave-management' },
          { name: 'View Performance', icon: Star, href: '/supervisor/info/performance' },
        ],
        features: [
          { name: 'Profile Management', icon: User },
          { name: 'Employment Details', icon: Briefcase },
          { name: 'Salary Information', icon: DollarSign },
          { name: 'Leave Management', icon: Calendar },
          { name: 'Document Center', icon: FileText },
          { name: 'Performance Reviews', icon: Star },
          { name: 'Time & Attendance', icon: Clock },
        ]
      };

    case '/supervisor/company':
      return {
        title: 'Company',
        breadcrumb: 'Company Information',
        quickActions: [
          { name: 'View Org Chart', icon: Users, href: '/supervisor/company?view=org-chart' },
          { name: 'Company Policies', icon: FileText, href: '/supervisor/company?view=policies' },
          { name: 'Department Info', icon: Building, href: '/supervisor/company?view=departments' },
          { name: 'Company News', icon: Bell, href: '/supervisor/company?view=news' },
          { name: 'Employee Directory', icon: Users, href: '/supervisor/company?view=directory' },
          { name: 'Company Calendar', icon: Calendar, href: '/supervisor/company?view=calendar' },
        ],
        features: [
          { name: 'Organization Chart', icon: Users },
          { name: 'Company Policies', icon: FileText },
          { name: 'Department Structure', icon: Building },
          { name: 'Company Updates', icon: Bell },
          { name: 'Employee Directory', icon: Users },
          { name: 'Company Events', icon: Calendar },
          { name: 'Contact Information', icon: Info },
          { name: 'Company Culture', icon: Award },
        ]
      };

    case '/supervisor/manage':
      return {
        title: 'Team Management',
        breadcrumb: 'Manage Team & Operations',
        quickActions: [
          { name: 'Approve Leave', icon: CalendarCheck, href: '/supervisor/manage/leave-management' },
          { name: 'Manage Overtime', icon: Clock, href: '/supervisor/manage/overtime' },
          { name: 'View Team Performance', icon: BarChart3, href: '/supervisor/manage?view=performance' },
          { name: 'Team Directory', icon: Users, href: '/supervisor/manage?view=team' },
          { name: 'Schedule Review', icon: Star, href: '/supervisor/manage?action=review' },
          { name: 'Generate Report', icon: FileText, href: '/supervisor/manage?action=report' },
        ],
        features: [
          { name: 'Leave Management', icon: Calendar },
          { name: 'Overtime Management', icon: Clock },
          { name: 'Team Performance', icon: TrendingUp },
          { name: 'Employee Oversight', icon: Users },
          { name: 'Performance Reviews', icon: Star },
          { name: 'Team Analytics', icon: BarChart3 },
          { name: 'Workflow Management', icon: Workflow },
          { name: 'Task Assignment', icon: CheckSquare },
        ]
      };

    case '/supervisor/notifications':
      return {
        title: 'Notifications',
        breadcrumb: 'Notification Center',
        quickActions: [
          { name: 'Mark All Read', icon: CheckCircle, href: '/supervisor/notifications?action=mark-read' },
          { name: 'Filter Notifications', icon: Filter, href: '/supervisor/notifications?action=filter' },
          { name: 'Send Announcement', icon: Megaphone, href: '/supervisor/notifications?action=announce' },
          { name: 'Notification Settings', icon: Settings, href: '/supervisor/notifications?view=settings' },
          { name: 'Team Alerts', icon: AlertTriangle, href: '/supervisor/notifications?view=alerts' },
          { name: 'Export Notifications', icon: Download, href: '/supervisor/notifications?action=export' },
        ],
        features: [
          { name: 'Pending Approvals', icon: Clock },
          { name: 'Team Updates', icon: Users },
          { name: 'System Alerts', icon: AlertTriangle },
          { name: 'Reminders', icon: Bell },
          { name: 'Announcements', icon: Megaphone },
          { name: 'Priority Messages', icon: AlertCircle },
          { name: 'Notification History', icon: History },
          { name: 'Custom Alerts', icon: Settings },
        ]
      };



    default:
      return {
        title: 'Supervisor Dashboard',
        breadcrumb: 'Supervisor Portal',
        quickActions: [],
        features: []
      };
  }
};

const SupervisorDynamicSidebar: React.FC<SupervisorSidebarProps> = ({ isOpen, onClose }) => {
  const { user, logout } = useAuth();
  const pathname = usePathname();

  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  const handleLogout = useCallback(() => {
    logout();
    handleClose();
  }, [logout, handleClose]);

  // Get current page features
  const pageFeatures = useMemo(() => getSupervisorPageFeatures(pathname), [pathname]);

  // Info section areas - Always available on info pages
  const infoSectionAreas = useMemo(() => [
    {
      name: 'Profile',
      href: '/supervisor/info/profile',
      icon: User,
      description: 'Personal information and contact details'
    },
    {
      name: 'Employment',
      href: '/supervisor/info/employment',
      icon: Briefcase,
      description: 'Job details and employment history'
    },
    {
      name: 'Salary',
      href: '/supervisor/info/salary',
      icon: DollarSign,
      description: 'Salary information and payslips'
    },
    {
      name: 'Leave Management',
      href: '/supervisor/info/leave-management',
      icon: Calendar,
      description: 'Leave applications and balance'
    },
    {
      name: 'Documents',
      href: '/supervisor/info/documents',
      icon: FileText,
      description: 'Personal and employment documents'
    },
    {
      name: 'Performance',
      href: '/supervisor/info/performance',
      icon: Star,
      description: 'Performance reviews and goals'
    },
    {
      name: 'Time Off',
      href: '/supervisor/info/timeoff',
      icon: Clock,
      description: 'Time off requests and history'
    },
  ], []);

  // Management areas for supervisor
  const managementAreas = useMemo(() => [
    {
      name: 'Leave Management',
      href: '/supervisor/manage/leave-management',
      icon: Calendar,
      description: 'Approve leave requests and manage holidays'
    },
    {
      name: 'Overtime Management',
      href: '/supervisor/manage/overtime',
      icon: Clock,
      description: 'Manage overtime applications and approvals'
    },
  ], []);

  // Company areas for supervisor
  const companyAreas = useMemo(() => [
    {
      name: 'Organization Chart',
      href: '/supervisor/company?view=org-chart',
      icon: Users,
      description: 'View company structure and hierarchy'
    },
    {
      name: 'Company Policies',
      href: '/supervisor/company?view=policies',
      icon: FileText,
      description: 'Access company policies and procedures'
    },
    {
      name: 'Department Directory',
      href: '/supervisor/company?view=departments',
      icon: Building,
      description: 'Browse departments and team information'
    },
    {
      name: 'Employee Directory',
      href: '/supervisor/company?view=directory',
      icon: Users,
      description: 'Search and contact employees'
    },
    {
      name: 'Company Calendar',
      href: '/supervisor/company?view=calendar',
      icon: Calendar,
      description: 'View company events and holidays'
    },
    {
      name: 'Company News',
      href: '/supervisor/company?view=news',
      icon: Bell,
      description: 'Latest company announcements'
    },
  ], []);

  // Notification areas for supervisor
  const notificationAreas = useMemo(() => [
    {
      name: 'Pending Approvals',
      href: '/supervisor/notifications?filter=approvals',
      icon: Clock,
      description: 'Leave and overtime requests awaiting approval'
    },
    {
      name: 'Team Alerts',
      href: '/supervisor/notifications?filter=team',
      icon: Users,
      description: 'Important team-related notifications'
    },
    {
      name: 'System Notifications',
      href: '/supervisor/notifications?filter=system',
      icon: AlertTriangle,
      description: 'System alerts and maintenance notices'
    },
    {
      name: 'Announcements',
      href: '/supervisor/notifications?filter=announcements',
      icon: Megaphone,
      description: 'Company-wide announcements'
    },
    {
      name: 'Reminders',
      href: '/supervisor/notifications?filter=reminders',
      icon: Bell,
      description: 'Personal and team reminders'
    },
    {
      name: 'Priority Messages',
      href: '/supervisor/notifications?filter=priority',
      icon: AlertCircle,
      description: 'High-priority notifications requiring attention'
    },
  ], []);

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
        onClick={handleClose}
      />

      {/* Sidebar */}
      <div
        className={cn(
          "fixed left-0 top-0 h-full bg-white shadow-xl z-30 transform transition-transform duration-300 ease-in-out overflow-y-auto",
          "md:relative md:translate-x-0 md:shadow-lg md:top-0 md:h-full",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
        style={{ width: '368px' }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-orange-500 to-orange-600">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
              <span className="text-orange-500 font-bold">W</span>
            </div>
            <div>
              <h2 className="text-white font-semibold">WorkFlo</h2>
              <p className="text-orange-100 text-sm">Supervisor Portal</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 rounded-lg text-white hover:bg-white hover:bg-opacity-20 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* User Info */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
              <span className="text-white font-medium">
                {getInitials(user?.first_name || '', user?.last_name || '')}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="font-medium text-gray-900 truncate">
                {user?.first_name} {user?.last_name}
              </p>
              <p className="text-sm text-gray-500 capitalize">{user?.role}</p>
              <p className="text-xs text-gray-400">{user?.email}</p>
            </div>
          </div>
        </div>

        {/* Page Context */}
        <div className="p-6 border-b border-gray-200">
          <div className="mb-4">
            <h3 className="font-semibold text-gray-900">{pageFeatures.title}</h3>
            <p className="text-sm text-gray-600">{pageFeatures.breadcrumb}</p>
          </div>

          {/* Quick Actions */}
          {pageFeatures.quickActions.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Quick Actions</h4>
              <div className="space-y-1">
                {pageFeatures.quickActions.map((action, index) => {
                  const Icon = action.icon;
                  return (
                    <Link
                      key={index}
                      href={action.href}
                      onClick={handleClose}
                      className="flex items-center p-2 text-sm text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-md transition-colors"
                    >
                      <Icon className="h-4 w-4 mr-2" />
                      {action.name}
                    </Link>
                  );
                })}
              </div>
            </div>
          )}

          {/* Page Features */}
          {pageFeatures.features.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Features</h4>
              <div className="space-y-1">
                {pageFeatures.features.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <div key={index} className="flex items-center p-2 text-sm text-gray-600">
                      <Icon className="h-4 w-4 mr-2 text-gray-400" />
                      {feature.name}
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Info section areas - Always available on info pages */}
        {pathname.startsWith('/supervisor/info') && (
          <div className="p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="p-4">
                <h3 className="font-medium text-gray-900 mb-3">Personal Information</h3>
                <div className="space-y-2">
                  {infoSectionAreas.map((area, index) => {
                    const Icon = area.icon;
                    const isCurrentPage = pathname === area.href;
                    return (
                      <Link
                        key={index}
                        href={area.href}
                        onClick={handleClose}
                        className={cn(
                          "flex items-center justify-between p-3 rounded-lg transition-colors",
                          isCurrentPage
                            ? "bg-orange-50 text-orange-700 border border-orange-200"
                            : "hover:bg-gray-50 text-gray-700"
                        )}
                      >
                        <div className="flex items-center">
                          <Icon className={cn(
                            "h-4 w-4 mr-3",
                            isCurrentPage ? "text-orange-600" : "text-gray-400"
                          )} />
                          <div>
                            <div className="font-medium text-sm">{area.name}</div>
                            <div className="text-xs text-gray-500">{area.description}</div>
                          </div>
                        </div>
                        <ChevronRight className="h-3 w-3 text-gray-400" />
                      </Link>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Management areas - Show on manage pages */}
        {pathname.startsWith('/supervisor/manage') && (
          <div className="p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="p-4">
                <h3 className="font-medium text-gray-900 mb-3">Management Areas</h3>
                <div className="space-y-2">
                  {managementAreas.map((area, index) => {
                    const Icon = area.icon;
                    const isCurrentPage = pathname === area.href;
                    return (
                      <Link
                        key={index}
                        href={area.href}
                        onClick={handleClose}
                        className={cn(
                          "flex items-center justify-between p-3 rounded-lg transition-colors",
                          isCurrentPage
                            ? "bg-orange-50 text-orange-700 border border-orange-200"
                            : "hover:bg-gray-50 text-gray-700"
                        )}
                      >
                        <div className="flex items-center">
                          <Icon className={cn(
                            "h-4 w-4 mr-3",
                            isCurrentPage ? "text-orange-600" : "text-gray-400"
                          )} />
                          <div>
                            <div className="font-medium text-sm">{area.name}</div>
                            <div className="text-xs text-gray-500">{area.description}</div>
                          </div>
                        </div>
                        <ChevronRight className="h-3 w-3 text-gray-400" />
                      </Link>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Company areas - Show on company pages */}
        {pathname.startsWith('/supervisor/company') && (
          <div className="p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="p-4">
                <h3 className="font-medium text-gray-900 mb-3">Company Information</h3>
                <div className="space-y-2">
                  {companyAreas.map((area, index) => {
                    const Icon = area.icon;
                    const isCurrentPage = pathname.includes(area.href.split('?')[0]) &&
                                         (area.href.includes('view=') ?
                                          pathname.includes(area.href.split('view=')[1]) : true);
                    return (
                      <Link
                        key={index}
                        href={area.href}
                        onClick={handleClose}
                        className={cn(
                          "flex items-center justify-between p-3 rounded-lg transition-colors",
                          isCurrentPage
                            ? "bg-orange-50 text-orange-700 border border-orange-200"
                            : "hover:bg-gray-50 text-gray-700"
                        )}
                      >
                        <div className="flex items-center">
                          <Icon className={cn(
                            "h-4 w-4 mr-3",
                            isCurrentPage ? "text-orange-600" : "text-gray-400"
                          )} />
                          <div>
                            <div className="font-medium text-sm">{area.name}</div>
                            <div className="text-xs text-gray-500">{area.description}</div>
                          </div>
                        </div>
                        <ChevronRight className="h-3 w-3 text-gray-400" />
                      </Link>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Notification areas - Show on notification pages */}
        {pathname.startsWith('/supervisor/notifications') && (
          <div className="p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="p-4">
                <h3 className="font-medium text-gray-900 mb-3">Notification Categories</h3>
                <div className="space-y-2">
                  {notificationAreas.map((area, index) => {
                    const Icon = area.icon;
                    const isCurrentPage = pathname.includes(area.href.split('?')[0]) &&
                                         (area.href.includes('filter=') ?
                                          pathname.includes(area.href.split('filter=')[1]) : true);
                    return (
                      <Link
                        key={index}
                        href={area.href}
                        onClick={handleClose}
                        className={cn(
                          "flex items-center justify-between p-3 rounded-lg transition-colors",
                          isCurrentPage
                            ? "bg-orange-50 text-orange-700 border border-orange-200"
                            : "hover:bg-gray-50 text-gray-700"
                        )}
                      >
                        <div className="flex items-center">
                          <Icon className={cn(
                            "h-4 w-4 mr-3",
                            isCurrentPage ? "text-orange-600" : "text-gray-400"
                          )} />
                          <div>
                            <div className="font-medium text-sm">{area.name}</div>
                            <div className="text-xs text-gray-500">{area.description}</div>
                          </div>
                        </div>
                        <ChevronRight className="h-3 w-3 text-gray-400" />
                      </Link>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 mt-auto">
          <div className="space-y-2">
            <Link
              href="/supervisor/settings"
              onClick={handleClose}
              className="flex items-center p-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Link>
            <button
              onClick={handleLogout}
              className="flex items-center w-full p-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </button>
          </div>
          <div className="mt-4 pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-500 text-center">
              © 2024 WorkFlo. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default SupervisorDynamicSidebar;
