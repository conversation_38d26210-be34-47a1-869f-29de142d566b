# This file is distributed under the same license as the Django package.
#
# Translators:
# 534b44a19bf18d20b71ecc4eb77c572f_db336e9 <f8268c65f822ec11a3a2e5d482cd7ead_175>, 2011
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012,2014-2015,2019-2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-08-02 13:26+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Hebrew (http://www.transifex.com/django/django/language/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % "
"1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

msgid "Advanced options"
msgstr "אפשרויות מתקדמות"

msgid "Flat Pages"
msgstr "דפים פשוטים"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr "דוגמה: “/about/contact/”. יש לוודא שמכיל קווים נטויים בהתחלה ובסוף."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"הערך הזאת חייב להכיל רק אותיות, מספרים, נקודות, מקפים, קווים תחתונים, חתכים "
"או סימני טילדה בלבד."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "דוגמה: “‎/about/contact”. יש לוודא שמכיל קו נטוי בהתחלה."

msgid "URL is missing a leading slash."
msgstr "חסר קו נטוי בתחילת URL."

msgid "URL is missing a trailing slash."
msgstr "חסר קו נטוי בסוף URL."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "קיים כבר דף פשוט עם  url %(url)s עבור אתר %(site)s"

msgid "title"
msgstr "כותרת"

msgid "content"
msgstr "תוכן"

msgid "enable comments"
msgstr "אפשר תגובות"

msgid "template name"
msgstr "שם תבנית"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"דוגמה: “flatpages/contact_page.html”. אם לא צוין, המערכת תשתמש ב־“flatpages/"
"default.html”."

msgid "registration required"
msgstr "הרשמה נדרשת"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "אם מסומן, רק משתמשים מחוברים יוכלו לצפות בדף."

msgid "sites"
msgstr "אתרים"

msgid "flat page"
msgstr "דף פשוט"

msgid "flat pages"
msgstr "דפים פשוטים"
