'use client';

import React, { useMemo, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Bell,
  AlertCircle,
  Clock,
  Users,
  AlertTriangle,
  Megaphone,
  CheckCircle,
  Filter,
  Settings,
  X,
  LogOut,
  ChevronRight,
  Download,
  Eye,
  History,
  Star,
  Calendar,
  FileText,
  Activity
} from 'lucide-react';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials, formatDate } from '@/lib/utils';

interface SupervisorNotificationsSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const SupervisorNotificationsSidebar: React.FC<SupervisorNotificationsSidebarProps> = ({ isOpen, onClose }) => {
  const { user, logout } = useAuth();
  const pathname = usePathname();

  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  const handleLogout = useCallback(() => {
    logout();
    handleClose();
  }, [logout, handleClose]);

  // Notification categories with counts and priorities
  const notificationCategories = useMemo(() => [
    {
      name: 'Pending Approvals',
      href: '/supervisor/notifications?filter=approvals',
      icon: Clock,
      description: 'Leave and overtime requests awaiting approval',
      count: 5,
      priority: 'high',
      color: 'text-red-600 bg-red-50'
    },
    {
      name: 'Team Alerts',
      href: '/supervisor/notifications?filter=team',
      icon: Users,
      description: 'Important team-related notifications',
      count: 3,
      priority: 'medium',
      color: 'text-orange-600 bg-orange-50'
    },
    {
      name: 'System Notifications',
      href: '/supervisor/notifications?filter=system',
      icon: AlertTriangle,
      description: 'System alerts and maintenance notices',
      count: 2,
      priority: 'low',
      color: 'text-blue-600 bg-blue-50'
    },
    {
      name: 'Announcements',
      href: '/supervisor/notifications?filter=announcements',
      icon: Megaphone,
      description: 'Company-wide announcements',
      count: 4,
      priority: 'medium',
      color: 'text-purple-600 bg-purple-50'
    },
    {
      name: 'Reminders',
      href: '/supervisor/notifications?filter=reminders',
      icon: Bell,
      description: 'Personal and team reminders',
      count: 7,
      priority: 'low',
      color: 'text-green-600 bg-green-50'
    },
    {
      name: 'Priority Messages',
      href: '/supervisor/notifications?filter=priority',
      icon: AlertCircle,
      description: 'High-priority notifications requiring attention',
      count: 1,
      priority: 'high',
      color: 'text-red-600 bg-red-50'
    }
  ], []);

  // Notification quick actions
  const notificationActions = useMemo(() => [
    {
      name: 'Mark All as Read',
      href: '/supervisor/notifications?action=mark-read',
      icon: CheckCircle,
      description: 'Clear all unread notifications'
    },
    {
      name: 'Filter Notifications',
      href: '/supervisor/notifications?action=filter',
      icon: Filter,
      description: 'Filter by type, date, or priority'
    },
    {
      name: 'Send Announcement',
      href: '/supervisor/notifications?action=announce',
      icon: Megaphone,
      description: 'Send message to team members'
    },
    {
      name: 'Notification Settings',
      href: '/supervisor/notifications?view=settings',
      icon: Settings,
      description: 'Configure notification preferences'
    },
    {
      name: 'Export Notifications',
      href: '/supervisor/notifications?action=export',
      icon: Download,
      description: 'Download notification history'
    },
    {
      name: 'View History',
      href: '/supervisor/notifications?view=history',
      icon: History,
      description: 'Browse past notifications'
    }
  ], []);

  // Recent notifications preview
  const recentNotifications = useMemo(() => [
    {
      id: 1,
      type: 'approval',
      title: 'Leave Request - John Doe',
      message: 'Annual leave request for Dec 25-27',
      time: '2 hours ago',
      priority: 'high',
      icon: Calendar,
      unread: true
    },
    {
      id: 2,
      type: 'team',
      title: 'Team Performance Update',
      message: 'Monthly performance metrics available',
      time: '4 hours ago',
      priority: 'medium',
      icon: Activity,
      unread: true
    },
    {
      id: 3,
      type: 'system',
      title: 'System Maintenance',
      message: 'Scheduled maintenance this weekend',
      time: '1 day ago',
      priority: 'low',
      icon: AlertTriangle,
      unread: false
    },
    {
      id: 4,
      type: 'announcement',
      title: 'Company Holiday',
      message: 'New Year holiday announcement',
      time: '2 days ago',
      priority: 'medium',
      icon: Megaphone,
      unread: false
    }
  ], []);

  // Notification statistics
  const notificationStats = useMemo(() => [
    { label: 'Total Unread', value: '22', icon: Bell, color: 'text-red-600' },
    { label: 'High Priority', value: '6', icon: AlertCircle, color: 'text-orange-600' },
    { label: 'Team Alerts', value: '3', icon: Users, color: 'text-blue-600' },
    { label: 'This Week', value: '45', icon: Calendar, color: 'text-green-600' }
  ], []);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-4 border-red-500';
      case 'medium':
        return 'border-l-4 border-yellow-500';
      case 'low':
        return 'border-l-4 border-green-500';
      default:
        return 'border-l-4 border-gray-500';
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
        onClick={handleClose}
      />

      {/* Sidebar */}
      <div
        className={cn(
          "fixed left-0 top-0 h-full bg-white shadow-xl z-30 transform transition-transform duration-300 ease-in-out overflow-y-auto",
          "md:relative md:translate-x-0 md:shadow-lg md:top-0 md:h-full",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
        style={{ width: '368px' }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-500 to-purple-600">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
              <Bell className="h-6 w-6 text-purple-500" />
            </div>
            <div>
              <h2 className="text-white font-semibold">Notification Center</h2>
              <p className="text-purple-100 text-sm">Supervisor Alerts Hub</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 rounded-lg text-white hover:bg-white hover:bg-opacity-20 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* User Info */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
              <span className="text-white font-medium">
                {getInitials(user?.first_name || '', user?.last_name || '')}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="font-medium text-gray-900 truncate">
                {user?.first_name} {user?.last_name}
              </p>
              <p className="text-sm text-gray-500">Notification Manager</p>
              <p className="text-xs text-gray-400">22 unread notifications</p>
            </div>
          </div>
        </div>

        {/* Notification Stats */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-4">Notification Overview</h3>
          <div className="grid grid-cols-2 gap-3">
            {notificationStats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className="bg-gray-50 rounded-lg p-3 text-center">
                  <Icon className={cn("h-5 w-5 mx-auto mb-2", stat.color)} />
                  <div className="text-lg font-semibold text-gray-900">{stat.value}</div>
                  <div className="text-xs text-gray-500">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-2">
            {notificationActions.map((action, index) => {
              const Icon = action.icon;
              return (
                <Link
                  key={index}
                  href={action.href}
                  onClick={handleClose}
                  className="flex items-center justify-between p-3 rounded-lg hover:bg-purple-50 transition-colors group"
                >
                  <div className="flex items-center">
                    <Icon className="h-4 w-4 mr-3 text-purple-600" />
                    <div>
                      <div className="font-medium text-sm text-gray-900">{action.name}</div>
                      <div className="text-xs text-gray-500">{action.description}</div>
                    </div>
                  </div>
                  <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-purple-600" />
                </Link>
              );
            })}
          </div>
        </div>

        {/* Notification Categories */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-4">Notification Categories</h3>
          <div className="space-y-2">
            {notificationCategories.map((category, index) => {
              const Icon = category.icon;
              const isCurrentFilter = pathname.includes(`filter=${category.href.split('filter=')[1]}`);
              return (
                <Link
                  key={index}
                  href={category.href}
                  onClick={handleClose}
                  className={cn(
                    "flex items-center justify-between p-3 rounded-lg transition-colors",
                    getPriorityColor(category.priority),
                    isCurrentFilter
                      ? "bg-purple-50 text-purple-700 border border-purple-200"
                      : "hover:bg-gray-50 text-gray-700"
                  )}
                >
                  <div className="flex items-center">
                    <Icon className={cn(
                      "h-4 w-4 mr-3",
                      isCurrentFilter ? "text-purple-600" : "text-gray-400"
                    )} />
                    <div>
                      <div className="font-medium text-sm">{category.name}</div>
                      <div className="text-xs text-gray-500">{category.description}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={cn(
                      "text-xs font-medium px-2 py-1 rounded-full",
                      category.color
                    )}>
                      {category.count}
                    </span>
                    <ChevronRight className="h-3 w-3 text-gray-400" />
                  </div>
                </Link>
              );
            })}
          </div>
        </div>

        {/* Recent Notifications */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-4">Recent Notifications</h3>
          <div className="space-y-3">
            {recentNotifications.map((notification) => {
              const Icon = notification.icon;
              return (
                <div
                  key={notification.id}
                  className={cn(
                    "flex items-start space-x-3 p-3 rounded-lg",
                    notification.unread ? "bg-purple-50 border border-purple-200" : "bg-gray-50"
                  )}
                >
                  <Icon className={cn(
                    "h-4 w-4 mt-1",
                    notification.unread ? "text-purple-600" : "text-gray-400"
                  )} />
                  <div className="flex-1 min-w-0">
                    <p className={cn(
                      "text-sm font-medium",
                      notification.unread ? "text-gray-900" : "text-gray-600"
                    )}>
                      {notification.title}
                    </p>
                    <p className="text-xs text-gray-500">{notification.message}</p>
                    <p className="text-xs text-gray-400 mt-1">{notification.time}</p>
                  </div>
                  {notification.unread && (
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2" />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 mt-auto">
          <div className="space-y-2">
            <Link
              href="/supervisor/settings"
              onClick={handleClose}
              className="flex items-center p-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Link>
            <button
              onClick={handleLogout}
              className="flex items-center w-full p-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default SupervisorNotificationsSidebar;
