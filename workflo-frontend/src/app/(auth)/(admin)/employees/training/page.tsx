'use client';

import React, { useState, useEffect } from 'react';
import { 
  GraduationCap, 
  Plus, 
  Search, 
  Filter,
  BookOpen, 
  Users,
  Clock,
  CheckCircle,
  XCircle,
  Play,
  Pause,
  Target,
  Award,
  MapPin,
  Calendar,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  TrendingUp,
  BarChart3
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useAuth } from '@/providers/AuthProvider';
import { cn, formatDate } from '@/lib/utils';

interface TrainingModule {
  id: number;
  title: string;
  description: string;
  category: string;
  duration_hours: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  instructor: string;
  venue_type: 'online' | 'in_person' | 'hybrid';
  venue_location?: string;
  max_participants: number;
  enrolled_count: number;
  completion_rate: number;
  created_at: string;
  status: 'active' | 'inactive' | 'draft';
}

interface EmployeeTraining {
  id: number;
  employee_id: string;
  employee_name: string;
  training_id: number;
  training_title: string;
  assigned_date: string;
  start_date?: string;
  completion_date?: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'overdue';
  progress_percentage: number;
  score?: number;
  certificate_url?: string;
}

interface TrainingVenue {
  id: number;
  name: string;
  location: string;
  capacity: number;
  equipment: string[];
  status: 'available' | 'occupied' | 'maintenance';
}

const TrainingManagementPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'modules' | 'assignments' | 'venues' | 'analytics'>('modules');
  const [trainingModules, setTrainingModules] = useState<TrainingModule[]>([]);
  const [employeeTrainings, setEmployeeTrainings] = useState<EmployeeTraining[]>([]);
  const [venues, setVenues] = useState<TrainingVenue[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Mock data
  const mockTrainingModules: TrainingModule[] = [
    {
      id: 1,
      title: 'Cybersecurity Fundamentals',
      description: 'Essential cybersecurity practices for all employees',
      category: 'Security',
      duration_hours: 4,
      difficulty: 'beginner',
      instructor: 'John Security',
      venue_type: 'online',
      max_participants: 50,
      enrolled_count: 35,
      completion_rate: 85,
      created_at: '2024-01-01T00:00:00Z',
      status: 'active'
    },
    {
      id: 2,
      title: 'Leadership Development',
      description: 'Advanced leadership skills for managers and supervisors',
      category: 'Leadership',
      duration_hours: 16,
      difficulty: 'advanced',
      instructor: 'Sarah Leader',
      venue_type: 'in_person',
      venue_location: 'Conference Room A',
      max_participants: 20,
      enrolled_count: 18,
      completion_rate: 92,
      created_at: '2024-01-05T00:00:00Z',
      status: 'active'
    },
    {
      id: 3,
      title: 'Project Management Basics',
      description: 'Introduction to project management methodologies',
      category: 'Management',
      duration_hours: 8,
      difficulty: 'intermediate',
      instructor: 'Mike Manager',
      venue_type: 'hybrid',
      venue_location: 'Training Room B',
      max_participants: 30,
      enrolled_count: 25,
      completion_rate: 78,
      created_at: '2024-01-10T00:00:00Z',
      status: 'active'
    }
  ];

  const mockEmployeeTrainings: EmployeeTraining[] = [
    {
      id: 1,
      employee_id: 'EMP001',
      employee_name: 'Maria Cotton',
      training_id: 1,
      training_title: 'Cybersecurity Fundamentals',
      assigned_date: '2024-01-15T00:00:00Z',
      start_date: '2024-01-16T00:00:00Z',
      completion_date: '2024-01-20T00:00:00Z',
      status: 'completed',
      progress_percentage: 100,
      score: 95,
      certificate_url: '/certificates/maria_cybersecurity.pdf'
    },
    {
      id: 2,
      employee_id: 'EMP002',
      employee_name: 'John Smith',
      training_id: 2,
      training_title: 'Leadership Development',
      assigned_date: '2024-01-10T00:00:00Z',
      start_date: '2024-01-12T00:00:00Z',
      status: 'in_progress',
      progress_percentage: 65,
    },
    {
      id: 3,
      employee_id: 'EMP003',
      employee_name: 'Sarah Johnson',
      training_id: 3,
      training_title: 'Project Management Basics',
      assigned_date: '2024-01-08T00:00:00Z',
      status: 'not_started',
      progress_percentage: 0,
    }
  ];

  const mockVenues: TrainingVenue[] = [
    {
      id: 1,
      name: 'Conference Room A',
      location: 'Building A, Floor 2',
      capacity: 25,
      equipment: ['Projector', 'Whiteboard', 'Video Conference'],
      status: 'available'
    },
    {
      id: 2,
      name: 'Training Room B',
      location: 'Building B, Floor 1',
      capacity: 40,
      equipment: ['Smart Board', 'Audio System', 'Computers'],
      status: 'occupied'
    },
    {
      id: 3,
      name: 'Workshop Space',
      location: 'Building A, Floor 1',
      capacity: 15,
      equipment: ['Flipcharts', 'Breakout Areas'],
      status: 'maintenance'
    }
  ];

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setTimeout(() => {
        setTrainingModules(mockTrainingModules);
        setEmployeeTrainings(mockEmployeeTrainings);
        setVenues(mockVenues);
        setLoading(false);
      }, 1000);
    };

    loadData();
  }, []);

  const filteredModules = trainingModules.filter(module =>
    module.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    module.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
    module.instructor.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredTrainings = employeeTrainings.filter(training =>
    training.employee_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    training.training_title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalModules = trainingModules.length;
  const activeModules = trainingModules.filter(m => m.status === 'active').length;
  const totalEnrollments = employeeTrainings.length;
  const completedTrainings = employeeTrainings.filter(t => t.status === 'completed').length;
  const avgCompletionRate = trainingModules.reduce((sum, m) => sum + m.completion_rate, 0) / trainingModules.length;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'not_started': return 'bg-gray-100 text-gray-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      case 'available': return 'bg-green-100 text-green-800';
      case 'occupied': return 'bg-yellow-100 text-yellow-800';
      case 'maintenance': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Training Management</h1>
            <p className="text-gray-600 mt-1">
              Manage training modules, employee assignments, and track learning progress
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <Button variant="primary">
              <Plus className="h-4 w-4 mr-2" />
              Create Training Module
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BookOpen className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Training Modules</p>
                <p className="text-2xl font-semibold text-gray-900">{totalModules}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Play className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Modules</p>
                <p className="text-2xl font-semibold text-gray-900">{activeModules}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Enrollments</p>
                <p className="text-2xl font-semibold text-gray-900">{totalEnrollments}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Completed</p>
                <p className="text-2xl font-semibold text-gray-900">{completedTrainings}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Avg Completion</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {avgCompletionRate ? avgCompletionRate.toFixed(0) : 0}%
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { key: 'modules', label: 'Training Modules', icon: BookOpen },
              { key: 'assignments', label: 'Employee Training', icon: Users },
              { key: 'venues', label: 'Training Venues', icon: MapPin },
              { key: 'analytics', label: 'Analytics', icon: BarChart3 },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={cn(
                    'py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2',
                    activeTab === tab.key
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Search */}
        <div className="p-6 border-b border-gray-200">
          <div className="w-full max-w-md">
            <Input
              placeholder={`Search ${activeTab}...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
            />
          </div>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'modules' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {loading ? (
                Array.from({ length: 6 }).map((_, index) => (
                  <div key={index} className="animate-pulse">
                    <div className="border border-gray-200 rounded-lg p-4">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                      <div className="h-3 bg-gray-200 rounded w-full"></div>
                    </div>
                  </div>
                ))
              ) : (
                filteredModules.map((module) => (
                  <TrainingModuleCard key={module.id} module={module} />
                ))
              )}
            </div>
          )}

          {activeTab === 'assignments' && (
            <div className="space-y-4">
              {loading ? (
                Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="animate-pulse border border-gray-200 rounded-lg p-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))
              ) : (
                filteredTrainings.map((training) => (
                  <EmployeeTrainingCard key={training.id} training={training} />
                ))
              )}
            </div>
          )}

          {activeTab === 'venues' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {loading ? (
                Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="animate-pulse border border-gray-200 rounded-lg p-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))
              ) : (
                venues.map((venue) => (
                  <VenueCard key={venue.id} venue={venue} />
                ))
              )}
            </div>
          )}

          {activeTab === 'analytics' && (
            <div className="space-y-6">
              <div className="text-center py-12">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Training Analytics
                </h3>
                <p className="text-gray-600">
                  Detailed analytics and reporting features coming soon.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Training Module Card Component
interface TrainingModuleCardProps {
  module: TrainingModule;
}

const TrainingModuleCard: React.FC<TrainingModuleCardProps> = ({ module }) => {
  const [showActions, setShowActions] = useState(false);

  return (
    <Card className="relative hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        <div className="absolute top-4 right-4">
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-1 rounded-full hover:bg-gray-100"
            >
              <MoreHorizontal className="h-4 w-4 text-gray-500" />
            </button>

            {showActions && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </button>
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Module
                  </button>
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <Users className="h-4 w-4 mr-2" />
                    Assign to Employees
                  </button>
                  <button className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Module
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{module.title}</h3>
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">{module.description}</p>
          
          <div className="flex items-center space-x-2 mb-3">
            <span className={cn(
              'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
              module.status === 'active' ? 'bg-green-100 text-green-800' :
              module.status === 'inactive' ? 'bg-gray-100 text-gray-800' :
              'bg-yellow-100 text-yellow-800'
            )}>
              {module.status}
            </span>
            <span className={cn(
              'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
              module.difficulty === 'beginner' ? 'bg-green-100 text-green-800' :
              module.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            )}>
              {module.difficulty}
            </span>
          </div>
        </div>

        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center justify-between">
            <span>Category:</span>
            <span className="font-medium">{module.category}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Duration:</span>
            <span className="font-medium">{module.duration_hours}h</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Instructor:</span>
            <span className="font-medium">{module.instructor}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Enrolled:</span>
            <span className="font-medium">{module.enrolled_count}/{module.max_participants}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Completion Rate:</span>
            <span className="font-medium">{module.completion_rate}%</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Venue:</span>
            <span className="font-medium capitalize">{module.venue_type.replace('_', ' ')}</span>
          </div>
        </div>
      </div>
    </Card>
  );
};

// Employee Training Card Component
interface EmployeeTrainingCardProps {
  training: EmployeeTraining;
}

const EmployeeTrainingCard: React.FC<EmployeeTrainingCardProps> = ({ training }) => {
  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-semibold text-gray-900">{training.employee_name}</h3>
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                training.status === 'not_started' ? 'bg-gray-100 text-gray-800' :
                training.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                training.status === 'completed' ? 'bg-green-100 text-green-800' :
                'bg-red-100 text-red-800'
              )}>
                {training.status.replace('_', ' ')}
              </span>
            </div>
            
            <p className="text-sm text-gray-600 mb-4">{training.training_title}</p>
            
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center justify-between">
                <span>Assigned:</span>
                <span>{formatDate(training.assigned_date)}</span>
              </div>
              {training.start_date && (
                <div className="flex items-center justify-between">
                  <span>Started:</span>
                  <span>{formatDate(training.start_date)}</span>
                </div>
              )}
              {training.completion_date && (
                <div className="flex items-center justify-between">
                  <span>Completed:</span>
                  <span>{formatDate(training.completion_date)}</span>
                </div>
              )}
              <div className="flex items-center justify-between">
                <span>Progress:</span>
                <span className="font-medium">{training.progress_percentage}%</span>
              </div>
              {training.score && (
                <div className="flex items-center justify-between">
                  <span>Score:</span>
                  <span className="font-medium">{training.score}%</span>
                </div>
              )}
            </div>

            {/* Progress Bar */}
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-orange-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${training.progress_percentage}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

// Venue Card Component
interface VenueCardProps {
  venue: TrainingVenue;
}

const VenueCard: React.FC<VenueCardProps> = ({ venue }) => {
  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">{venue.name}</h3>
          <span className={cn(
            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
            venue.status === 'available' ? 'bg-green-100 text-green-800' :
            venue.status === 'occupied' ? 'bg-yellow-100 text-yellow-800' :
            'bg-red-100 text-red-800'
          )}>
            {venue.status}
          </span>
        </div>
        
        <div className="space-y-2 text-sm text-gray-600 mb-4">
          <div className="flex items-center">
            <MapPin className="h-4 w-4 mr-2" />
            {venue.location}
          </div>
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-2" />
            Capacity: {venue.capacity} people
          </div>
        </div>
        
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">Equipment:</h4>
          <div className="flex flex-wrap gap-1">
            {venue.equipment.map((item, index) => (
              <span 
                key={index}
                className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800"
              >
                {item}
              </span>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default TrainingManagementPage;
