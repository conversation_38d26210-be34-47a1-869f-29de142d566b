f79065e3b0df3fc8e8bf43a69a250ec2
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    CardBody: function() {
        return CardBody;
    },
    CardFooter: function() {
        return CardFooter;
    },
    CardHeader: function() {
        return CardHeader;
    },
    EmployeeCard: function() {
        return EmployeeCard;
    },
    StatsCard: function() {
        return StatsCard;
    },
    default: function() {
        return _default;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _utils = require("../../lib/utils");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const Card = ({ title, children, className, actions, ...props })=>{
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: (0, _utils.cn)('bg-white rounded-lg shadow-sm border border-gray-200 min-w-0 overflow-hidden touch-manipulation', className),
        ...props,
        children: [
            (title || actions) && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "px-3 sm:px-4 md:px-6 py-3 sm:py-4 border-b border-gray-200 flex items-center justify-between min-w-0",
                children: [
                    title && /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                        className: "text-sm sm:text-base md:text-lg font-medium text-gray-900 truncate",
                        children: title
                    }),
                    actions && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "flex items-center space-x-1 sm:space-x-2 flex-shrink-0",
                        children: actions
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "p-3 sm:p-4 md:p-6 min-w-0",
                children: children
            })
        ]
    });
};
const CardHeader = ({ children, className })=>{
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
        className: (0, _utils.cn)('px-3 sm:px-4 md:px-6 py-3 sm:py-4 border-b border-gray-200', className),
        children: children
    });
};
const CardBody = ({ children, className })=>{
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
        className: (0, _utils.cn)('p-3 sm:p-4 md:p-6', className),
        children: children
    });
};
const CardFooter = ({ children, className })=>{
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
        className: (0, _utils.cn)('px-3 sm:px-4 md:px-6 py-3 sm:py-4 border-t border-gray-200 bg-gray-50', className),
        children: children
    });
};
const StatsCard = ({ title, value, icon, color = 'primary', trend, className })=>{
    const colorClasses = {
        primary: 'bg-orange-500',
        success: 'bg-green-500',
        warning: 'bg-yellow-500',
        danger: 'bg-red-500'
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(Card, {
        className: (0, _utils.cn)('touch-manipulation', className),
        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            className: "flex items-center min-w-0",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: (0, _utils.cn)('p-2 sm:p-3 rounded-lg flex-shrink-0', colorClasses[color]),
                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "text-white text-lg sm:text-xl",
                        children: icon
                    })
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "ml-3 sm:ml-4 flex-1 min-w-0",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                            className: "text-xs sm:text-sm font-medium text-gray-600 truncate",
                            children: title
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                            className: "text-lg sm:text-xl md:text-2xl font-bold text-gray-900 truncate",
                            children: value
                        }),
                        trend && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "flex items-center mt-1",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                                    className: (0, _utils.cn)('text-xs sm:text-sm font-medium', trend.isPositive ? 'text-green-600' : 'text-red-600'),
                                    children: [
                                        trend.isPositive ? '+' : '-',
                                        Math.abs(trend.value),
                                        "%"
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                    className: "text-xs sm:text-sm text-gray-500 ml-1 truncate",
                                    children: "vs last month"
                                })
                            ]
                        })
                    ]
                })
            ]
        })
    });
};
const EmployeeCard = ({ employee, onClick, className })=>{
    const initials = `${employee.first_name.charAt(0)}${employee.last_name.charAt(0)}`;
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(Card, {
        className: (0, _utils.cn)('cursor-pointer hover:shadow-md transition-shadow touch-manipulation', className),
        onClick: onClick,
        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            className: "text-center min-w-0",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "mb-3 sm:mb-4",
                    children: employee.profile_picture ? /*#__PURE__*/ (0, _jsxruntime.jsx)("img", {
                        src: employee.profile_picture,
                        alt: `${employee.first_name} ${employee.last_name}`,
                        className: "w-14 h-14 sm:w-16 sm:h-16 rounded-full mx-auto object-cover"
                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "w-14 h-14 sm:w-16 sm:h-16 rounded-full mx-auto bg-orange-500 flex items-center justify-center text-white font-medium text-base sm:text-lg",
                        children: initials
                    })
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsxs)("h4", {
                    className: "text-sm sm:text-base md:text-lg font-medium text-gray-900 truncate",
                    children: [
                        employee.first_name,
                        " ",
                        employee.last_name
                    ]
                }),
                employee.job_title && /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                    className: "text-xs sm:text-sm font-medium text-gray-600 mt-1 truncate",
                    title: employee.job_title,
                    children: employee.job_title
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                    className: "text-xs sm:text-sm text-gray-500 mt-1 truncate",
                    title: employee.email,
                    children: employee.email
                }),
                employee.department && /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                    className: "text-xs text-gray-400 mt-1 truncate",
                    title: employee.department.name,
                    children: employee.department.name
                })
            ]
        })
    });
};
const _default = Card;

//# sourceMappingURL=data:application/json;base64,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