f27684d8087b6f7af3122fa8f8dc352a
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    TokenManager: function() {
        return TokenManager;
    },
    api: function() {
        return api;
    },
    apiClient: function() {
        return apiClient;
    },
    attendanceApi: function() {
        return attendanceApi;
    },
    authApi: function() {
        return authApi;
    },
    benefitApi: function() {
        return benefitApi;
    },
    departmentApi: function() {
        return departmentApi;
    },
    documentApi: function() {
        return documentApi;
    },
    employeeApi: function() {
        return employeeApi;
    },
    jobApi: function() {
        return jobApi;
    },
    leaveApi: function() {
        return leaveApi;
    },
    payrollApi: function() {
        return payrollApi;
    },
    performanceApi: function() {
        return performanceApi;
    },
    trainingApi: function() {
        return trainingApi;
    }
});
const _axios = /*#__PURE__*/ _interop_require_default(require("axios"));
const _mockApi = require("./mockApi");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
const USE_MOCK_API = process.env.NEXT_PUBLIC_USE_MOCK_API === 'true' || true; // Default to true for development
// Create axios instance
const apiClient = _axios.default.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json'
    }
});
// Token management
class TokenManager {
    static{
        this.ACCESS_TOKEN_KEY = 'access_token';
    }
    static{
        this.REFRESH_TOKEN_KEY = 'refresh_token';
    }
    static getAccessToken() {
        if (typeof window === 'undefined') return null;
        return localStorage.getItem(this.ACCESS_TOKEN_KEY);
    }
    static getRefreshToken() {
        if (typeof window === 'undefined') return null;
        return localStorage.getItem(this.REFRESH_TOKEN_KEY);
    }
    static setTokens(tokens) {
        if (typeof window === 'undefined') return;
        localStorage.setItem(this.ACCESS_TOKEN_KEY, tokens.access);
        localStorage.setItem(this.REFRESH_TOKEN_KEY, tokens.refresh);
    }
    static clearTokens() {
        if (typeof window === 'undefined') return;
        localStorage.removeItem(this.ACCESS_TOKEN_KEY);
        localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    }
    static isTokenExpired(token) {
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Date.now() / 1000;
            return payload.exp < currentTime;
        } catch  {
            return true;
        }
    }
}
// Request interceptor to add auth token
apiClient.interceptors.request.use((config)=>{
    const token = TokenManager.getAccessToken();
    if (token && !TokenManager.isTokenExpired(token)) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Response interceptor to handle token refresh
apiClient.interceptors.response.use((response)=>response, async (error)=>{
    const originalRequest = error.config;
    if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        const refreshToken = TokenManager.getRefreshToken();
        if (refreshToken && !TokenManager.isTokenExpired(refreshToken)) {
            try {
                const response = await _axios.default.post(`${API_BASE_URL}/token/refresh/`, {
                    refresh: refreshToken
                });
                const newTokens = response.data;
                TokenManager.setTokens(newTokens);
                // Retry original request with new token
                originalRequest.headers.Authorization = `Bearer ${newTokens.access}`;
                return apiClient(originalRequest);
            } catch (refreshError) {
                // Refresh failed, redirect to login
                TokenManager.clearTokens();
                if (typeof window !== 'undefined') {
                    window.location.href = '/login';
                }
                return Promise.reject(refreshError);
            }
        } else {
            // No valid refresh token, redirect to login
            TokenManager.clearTokens();
            if (typeof window !== 'undefined') {
                window.location.href = '/login';
            }
        }
    }
    return Promise.reject(error);
});
// API wrapper class
class ApiService {
    // Generic request method
    async request(config) {
        try {
            const response = await apiClient(config);
            return response.data;
        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }
    // GET request
    async get(url, config) {
        return this.request({
            ...config,
            method: 'GET',
            url
        });
    }
    // POST request
    async post(url, data, config) {
        return this.request({
            ...config,
            method: 'POST',
            url,
            data
        });
    }
    // PUT request
    async put(url, data, config) {
        return this.request({
            ...config,
            method: 'PUT',
            url,
            data
        });
    }
    // PATCH request
    async patch(url, data, config) {
        return this.request({
            ...config,
            method: 'PATCH',
            url,
            data
        });
    }
    // DELETE request
    async delete(url, config) {
        return this.request({
            ...config,
            method: 'DELETE',
            url
        });
    }
    // File upload
    async uploadFile(url, file, onProgress) {
        const formData = new FormData();
        formData.append('file', file);
        return this.request({
            method: 'POST',
            url,
            data: formData,
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent)=>{
                if (onProgress && progressEvent.total) {
                    const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);
                    onProgress(progress);
                }
            }
        });
    }
}
const api = new ApiService();
const authApi = {
    login: async (credentials)=>{
        if (USE_MOCK_API) {
            const tokens = await _mockApi.mockApi.login(credentials);
            (0, _mockApi.setMockUserEmail)(credentials.email);
            return tokens;
        }
        return api.post('/token/', credentials);
    },
    refreshToken: (refreshToken)=>api.post('/token/refresh/', {
            refresh: refreshToken
        }),
    logout: ()=>{
        TokenManager.clearTokens();
        if (USE_MOCK_API) {
            (0, _mockApi.clearMockUserEmail)();
        }
        if (typeof window !== 'undefined') {
            window.location.href = '/login';
        }
    },
    getCurrentUser: ()=>{
        if (USE_MOCK_API) {
            return _mockApi.mockApi.getCurrentUser();
        }
        return api.get('/employees/me/');
    }
};
const employeeApi = {
    getAll: ()=>{
        if (USE_MOCK_API) {
            return _mockApi.mockApi.getEmployees();
        }
        return api.get('/get_employees/');
    },
    getById: (id)=>{
        if (USE_MOCK_API) {
            return _mockApi.mockApi.getEmployeeById(id);
        }
        return api.get(`/employees/${id}/`);
    },
    create: (data)=>api.post('/new_employee/', data),
    update: (id, data)=>api.put(`/new_employee/${id}/`, data),
    delete: (id)=>api.delete(`/employees/${id}/`),
    getProfile: ()=>{
        if (USE_MOCK_API) {
            return _mockApi.mockApi.getCurrentUser();
        }
        return api.get('/employees/me/');
    }
};
const departmentApi = {
    getAll: ()=>api.get('/get_departments/'),
    create: (data)=>api.post('/departments/', data),
    update: (id, data)=>api.put(`/departments/${id}/`, data),
    delete: (id)=>api.delete(`/departments/${id}/`)
};
const attendanceApi = {
    getMyAttendance: ()=>api.get('/attendance/'),
    clockIn: ()=>api.post('/attendance/', {
            action: 'clock_in'
        }),
    clockOut: ()=>api.post('/attendance/', {
            action: 'clock_out'
        }),
    getByEmployee: (employeeId)=>api.get(`/attendance/?employee=${employeeId}`)
};
const leaveApi = {
    getMyLeaves: ()=>api.get('/leave_applications/'),
    getPendingLeaves: ()=>api.get('/pending_leave/'),
    getRejectedLeaves: ()=>api.get('/my_rejected_leaves/'),
    apply: (data)=>api.post('/leave_applications/', data),
    approve: (id, data)=>api.put(`/pending_leave/${id}/`, data),
    reject: (id, data)=>api.put(`/pending_leave/${id}/`, data)
};
const payrollApi = {
    getMyPayroll: ()=>api.get('/payroll/'),
    getPayCycles: ()=>api.get('/paycycle/'),
    createPayCycle: (data)=>api.post('/paycycle/', data),
    markPaid: (id)=>api.patch(`/paycycle/${id}/mark-paid/`),
    bulkCreatePayroll: (data)=>api.post('/payroll_create/bulk-create/', data),
    getEmployeePayroll: (employeeId, payCycleId)=>api.get(`/payroll_create/employee-detail/?employee=${employeeId}&pay_cycle=${payCycleId}`)
};
const performanceApi = {
    getMyReviews: ()=>api.get('/performance_reviews/'),
    getTeamReviews: ()=>api.get('/reviews/'),
    create: (data)=>api.post('/performance_reviews/', data),
    update: (id, data)=>api.put(`/performance_reviews/${id}/`, data)
};
const trainingApi = {
    getModules: ()=>api.get('/training_modules/'),
    getMyTrainings: ()=>api.get('/employee_trainings/'),
    assignTraining: (data)=>api.post('/employee_trainings/', data),
    updateProgress: (id, data)=>api.put(`/employee_trainings/${id}/`, data)
};
const jobApi = {
    getPostings: ()=>api.get('/get_job_postings/'),
    getCandidates: ()=>api.get('/candidates/'),
    createPosting: (data)=>api.post('/job_postings/', data),
    apply: (data)=>api.post('/candidates/', data)
};
const documentApi = {
    getMyDocuments: ()=>api.get('/user_documents/'),
    getAllDocuments: ()=>api.get('/admin_documents/'),
    getCompanyDocuments: ()=>api.get('/company_documents/'),
    upload: (file, onProgress)=>api.uploadFile('/employee_documents/', file, onProgress)
};
const benefitApi = {
    getAll: ()=>api.get('/benefits/'),
    create: (data)=>api.post('/benefits/', data),
    update: (id, data)=>api.put(`/benefits/${id}/`, data),
    delete: (id)=>api.delete(`/benefits/${id}/`)
};

//# sourceMappingURL=data:application/json;base64,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