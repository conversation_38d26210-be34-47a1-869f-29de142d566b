/**
 * Simple auth persistence without Zustand middleware
 * This prevents hydration issues while maintaining auth state
 */

import { User } from '@/types';

const AUTH_STORAGE_KEY = 'workflo-auth';

interface PersistedAuthState {
  user: User | null;
  isAuthenticated: boolean;
}

export class AuthPersistence {
  static save(user: User | null, isAuthenticated: boolean): void {
    if (typeof window === 'undefined') return;
    
    try {
      const state: PersistedAuthState = {
        user,
        isAuthenticated,
      };
      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(state));
    } catch (error) {
      console.warn('Failed to save auth state:', error);
    }
  }

  static load(): PersistedAuthState | null {
    if (typeof window === 'undefined') return null;
    
    try {
      const stored = localStorage.getItem(AUTH_STORAGE_KEY);
      if (!stored) return null;
      
      return JSON.parse(stored);
    } catch (error) {
      console.warn('Failed to load auth state:', error);
      return null;
    }
  }

  static clear(): void {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.removeItem(AUTH_STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear auth state:', error);
    }
  }
}
