'use client';

import React, { useState } from 'react';
import { Settings, Users, Shield, Database, Globe, Bell, Plus, Edit, Trash2, DollarSign, Calendar, FileText, Star, BarChart3 } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import Link from 'next/link';

const ManagePage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  const managementAreas = [
    {
      id: 1,
      title: 'Payroll Management',
      description: 'Manage employee payroll, salaries, and benefits',
      icon: DollarSign,
      color: 'bg-green-100 text-green-600',
      href: '/payroll',
      actions: ['Process Payroll', 'Manage Benefits', 'Tax Settings'],
      stats: { employees: 150, processed: 142, pending: 8 }
    },
    {
      id: 2,
      title: 'Calendar Management',
      description: 'Schedule events, meetings, and company activities',
      icon: Calendar,
      color: 'bg-blue-100 text-blue-600',
      href: '/calendar',
      actions: ['Add Event', 'Schedule Meeting', 'Manage Holidays'],
      stats: { events: 25, meetings: 12, holidays: 15 }
    },
    {
      id: 3,
      title: 'Leave Management',
      description: 'Approve leave requests and manage holidays',
      icon: FileText,
      color: 'bg-orange-100 text-orange-600',
      href: '/leave',
      actions: ['Approve Requests', 'Set Policies', 'View Calendar'],
      stats: { pending: 8, approved: 45, rejected: 2 }
    },
    {
      id: 4,
      title: 'Performance Reviews',
      description: 'Conduct and manage employee performance reviews',
      icon: Star,
      color: 'bg-purple-100 text-purple-600',
      href: '/reviews',
      actions: ['Create Review', 'Set Goals', 'Generate Reports'],
      stats: { completed: 30, pending: 15, overdue: 3 }
    },
    {
      id: 5,
      title: 'Reports & Analytics',
      description: 'Generate reports and view analytics',
      icon: BarChart3,
      color: 'bg-red-100 text-red-600',
      href: '/reports',
      actions: ['HR Reports', 'Financial Reports', 'Custom Analytics'],
      stats: { reports: 25, scheduled: 8, exports: 12 }
    }
  ];

  const recentActivities = [
    { id: 1, action: 'User created', details: 'New employee John Doe added', time: '2 hours ago', type: 'user' },
    { id: 2, action: 'System backup', details: 'Automated backup completed successfully', time: '4 hours ago', type: 'system' },
    { id: 3, action: 'Security alert', details: 'Failed login attempts detected', time: '6 hours ago', type: 'security' },
    { id: 4, action: 'Integration updated', details: 'Slack integration configuration changed', time: '1 day ago', type: 'integration' },
    { id: 5, action: 'Policy updated', details: 'Password policy requirements modified', time: '2 days ago', type: 'policy' },
  ];

  const quickActions = [
    { title: 'Add New User', description: 'Create a new user account', icon: Users, action: 'add-user' },
    { title: 'System Backup', description: 'Create a manual system backup', icon: Database, action: 'backup' },
    { title: 'Security Audit', description: 'Run a security audit report', icon: Shield, action: 'audit' },
    { title: 'Export Data', description: 'Export system data for analysis', icon: Database, action: 'export' },
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user': return <Users className="h-4 w-4" />;
      case 'system': return <Settings className="h-4 w-4" />;
      case 'security': return <Shield className="h-4 w-4" />;
      case 'integration': return <Globe className="h-4 w-4" />;
      case 'policy': return <Bell className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'user': return 'bg-blue-100 text-blue-600';
      case 'system': return 'bg-green-100 text-green-600';
      case 'security': return 'bg-red-100 text-red-600';
      case 'integration': return 'bg-orange-100 text-orange-600';
      case 'policy': return 'bg-yellow-100 text-yellow-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Management Center</h1>
            <p className="text-gray-600 mt-1">
              Manage payroll, calendar, leave, reviews, and reports from one central location
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <Button variant="primary">
              <Plus className="h-4 w-4 mr-2" />
              Quick Setup
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { key: 'overview', label: 'Overview' },
              { key: 'management', label: 'Management Areas' },
              { key: 'activities', label: 'Recent Activities' },
              { key: 'quick-actions', label: 'Quick Actions' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.key
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* System Status */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <Card className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-lg bg-green-100">
                      <Settings className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-600">System Status</h3>
                      <p className="text-2xl font-bold text-green-600">Healthy</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-lg bg-blue-100">
                      <Users className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-600">Active Users</h3>
                      <p className="text-2xl font-bold text-gray-900">142</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-lg bg-purple-100">
                      <Database className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-600">Data Usage</h3>
                      <p className="text-2xl font-bold text-gray-900">2.4GB</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-lg bg-orange-100">
                      <Globe className="h-6 w-6 text-orange-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-600">Integrations</h3>
                      <p className="text-2xl font-bold text-gray-900">5</p>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Quick Overview */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">System Health</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Database</span>
                        <span className="text-sm font-medium text-green-600">Operational</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">API Services</span>
                        <span className="text-sm font-medium text-green-600">Operational</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Email Service</span>
                        <span className="text-sm font-medium text-green-600">Operational</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Backup System</span>
                        <span className="text-sm font-medium text-green-600">Operational</span>
                      </div>
                    </div>
                  </div>
                </Card>

                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activities</h3>
                    <div className="space-y-3">
                      {recentActivities.slice(0, 4).map((activity) => (
                        <div key={activity.id} className="flex items-start space-x-3">
                          <div className={`p-1 rounded-full ${getActivityColor(activity.type)}`}>
                            {getActivityIcon(activity.type)}
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                            <p className="text-xs text-gray-500">{activity.time}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}

          {activeTab === 'management' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {managementAreas.map((area) => {
                  const IconComponent = area.icon;
                  return (
                    <Card key={area.id}>
                      <div className="p-6">
                        <div className="flex items-center mb-4">
                          <div className={`p-3 rounded-lg ${area.color}`}>
                            <IconComponent className="h-6 w-6" />
                          </div>
                          <div className="ml-4">
                            <h3 className="text-lg font-medium text-gray-900">{area.title}</h3>
                          </div>
                        </div>

                        <p className="text-sm text-gray-600 mb-4">{area.description}</p>

                        <div className="space-y-2 mb-4">
                          {area.actions.map((action, index) => (
                            <Button key={index} variant="secondary" size="sm" className="w-full justify-start">
                              {action}
                            </Button>
                          ))}
                        </div>

                        <div className="mb-4">
                          <Link href={area.href}>
                            <Button variant="primary" size="sm" className="w-full">
                              Go to {area.title}
                            </Button>
                          </Link>
                        </div>

                        <div className="pt-4 border-t border-gray-200">
                          <div className="grid grid-cols-3 gap-4 text-center">
                            {Object.entries(area.stats).map(([key, value]) => (
                              <div key={key}>
                                <p className="text-lg font-bold text-gray-900">{value}</p>
                                <p className="text-xs text-gray-500 capitalize">{key}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </div>
          )}

          {activeTab === 'activities' && (
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 rounded-lg ${getActivityColor(activity.type)}`}>
                        {getActivityIcon(activity.type)}
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{activity.action}</h4>
                        <p className="text-sm text-gray-600">{activity.details}</p>
                        <p className="text-sm text-gray-500">{activity.time}</p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="secondary" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="secondary" size="sm">
                        View Details
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'quick-actions' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {quickActions.map((action, index) => {
                const IconComponent = action.icon;
                return (
                  <Card key={index}>
                    <div className="p-6">
                      <div className="flex items-center mb-4">
                        <div className="p-3 rounded-lg bg-orange-100">
                          <IconComponent className="h-6 w-6 text-orange-600" />
                        </div>
                        <div className="ml-4">
                          <h3 className="text-lg font-medium text-gray-900">{action.title}</h3>
                          <p className="text-sm text-gray-600">{action.description}</p>
                        </div>
                      </div>
                      <Button variant="primary" className="w-full">
                        Execute Action
                      </Button>
                    </div>
                  </Card>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ManagePage;
