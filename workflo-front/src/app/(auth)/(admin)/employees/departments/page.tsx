'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Building, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Users, 
  Crown, 
  MapPin,
  Calendar,
  MoreHorizontal,
  Eye,
  UserPlus,
  Settings,
  TrendingUp,
  Loader2
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useAuth } from '@/providers/AuthProvider';
import { cn, formatDate } from '@/lib/utils';

interface Department {
  id: number;
  name: string;
  description: string;
  supervisor_id?: number;
  supervisor_name?: string;
  employee_count: number;
  budget?: number;
  location?: string;
  created_at: string;
  updated_at: string;
  status: 'active' | 'inactive';
}

const DepartmentManagementPage: React.FC = () => {
  const { user } = useAuth();
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);

  // Mock data
  const mockDepartments: Department[] = [
    {
      id: 1,
      name: 'Engineering',
      description: 'Software development and technology innovation',
      supervisor_id: 2,
      supervisor_name: 'John Smith',
      employee_count: 25,
      budget: 500000,
      location: 'Building A, Floor 3',
      created_at: '2023-01-15T00:00:00Z',
      updated_at: '2023-12-01T00:00:00Z',
      status: 'active'
    },
    {
      id: 2,
      name: 'Human Resources',
      description: 'People management and organizational development',
      supervisor_id: 3,
      supervisor_name: 'Sarah Johnson',
      employee_count: 8,
      budget: 200000,
      location: 'Building B, Floor 1',
      created_at: '2023-01-15T00:00:00Z',
      updated_at: '2023-11-15T00:00:00Z',
      status: 'active'
    },
    {
      id: 3,
      name: 'Finance',
      description: 'Financial planning and accounting operations',
      supervisor_id: 4,
      supervisor_name: 'David Wilson',
      employee_count: 12,
      budget: 300000,
      location: 'Building A, Floor 2',
      created_at: '2023-02-01T00:00:00Z',
      updated_at: '2023-12-10T00:00:00Z',
      status: 'active'
    },
    {
      id: 4,
      name: 'Marketing',
      description: 'Brand management and customer acquisition',
      supervisor_id: 5,
      supervisor_name: 'Emily Davis',
      employee_count: 15,
      budget: 400000,
      location: 'Building B, Floor 2',
      created_at: '2023-03-01T00:00:00Z',
      updated_at: '2023-11-30T00:00:00Z',
      status: 'active'
    },
    {
      id: 5,
      name: 'Operations',
      description: 'Business operations and process optimization',
      supervisor_id: 6,
      supervisor_name: 'Michael Brown',
      employee_count: 18,
      budget: 350000,
      location: 'Building A, Floor 1',
      created_at: '2023-04-01T00:00:00Z',
      updated_at: '2023-12-05T00:00:00Z',
      status: 'active'
    }
  ];

  useEffect(() => {
    const loadDepartments = async () => {
      setLoading(true);
      // Simulate API call
      setTimeout(() => {
        setDepartments(mockDepartments);
        setLoading(false);
      }, 1000);
    };

    loadDepartments();
  }, []);

  const filteredDepartments = departments.filter(dept =>
    dept.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    dept.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    dept.supervisor_name?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCreateDepartment = () => {
    setSelectedDepartment(null);
    setShowCreateModal(true);
  };

  const handleEditDepartment = (department: Department) => {
    setSelectedDepartment(department);
    setShowCreateModal(true);
  };

  const handleDeleteDepartment = (department: Department) => {
    if (confirm(`Are you sure you want to delete ${department.name}?`)) {
      setDepartments(prev => prev.filter(d => d.id !== department.id));
    }
  };

  const totalEmployees = departments.reduce((sum, dept) => sum + dept.employee_count, 0);
  const totalBudget = departments.reduce((sum, dept) => sum + (dept.budget || 0), 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Department Management</h1>
            <p className="text-gray-600 mt-1">
              Organize and manage your company's departments and organizational structure
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <Button variant="primary" onClick={handleCreateDepartment}>
              <Plus className="h-4 w-4 mr-2" />
              Add Department
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Building className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Departments</p>
                <p className="text-2xl font-semibold text-gray-900">{departments.length}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Employees</p>
                <p className="text-2xl font-semibold text-gray-900">{totalEmployees}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Crown className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Supervisors</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {departments.filter(d => d.supervisor_id).length}
                </p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Budget</p>
                <p className="text-2xl font-semibold text-gray-900">
                  ${(totalBudget / 1000000).toFixed(1)}M
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="w-full sm:w-80">
            <Input
              placeholder="Search departments..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
            />
          </div>
          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-600">
              {filteredDepartments.length} departments
            </span>
          </div>
        </div>
      </div>

      {/* Departments Grid */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="p-6">
            <div className="animate-pulse space-y-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredDepartments.map((department) => (
                <DepartmentCard
                  key={department.id}
                  department={department}
                  onEdit={() => handleEditDepartment(department)}
                  onDelete={() => handleDeleteDepartment(department)}
                />
              ))}
            </div>
          </div>
        )}

        {!loading && filteredDepartments.length === 0 && (
          <div className="text-center py-12">
            <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No departments found
            </h3>
            <p className="text-gray-600 mb-4">
              {searchQuery ? 'Try adjusting your search criteria.' : 'Get started by creating your first department.'}
            </p>
            <Button variant="primary" onClick={handleCreateDepartment}>
              <Plus className="h-4 w-4 mr-2" />
              Add Department
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

// Department Card Component
interface DepartmentCardProps {
  department: Department;
  onEdit: () => void;
  onDelete: () => void;
}

const DepartmentCard: React.FC<DepartmentCardProps> = ({ department, onEdit, onDelete }) => {
  const [showActions, setShowActions] = useState(false);

  return (
    <Card className="relative hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        {/* Actions Menu */}
        <div className="absolute top-4 right-4">
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-1 rounded-full hover:bg-gray-100"
            >
              <MoreHorizontal className="h-4 w-4 text-gray-500" />
            </button>

            {showActions && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button
                    onClick={() => {
                      onEdit();
                      setShowActions(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Department
                  </button>
                  <button
                    onClick={() => {
                      onDelete();
                      setShowActions(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Department
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Department Info */}
        <div className="mb-4">
          <div className="flex items-center mb-2">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
              <Building className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{department.name}</h3>
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                department.status === 'active' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
              )}>
                {department.status}
              </span>
            </div>
          </div>
          <p className="text-sm text-gray-600 mb-4">{department.description}</p>
        </div>

        {/* Department Stats */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm text-gray-600">
              <Users className="h-4 w-4 mr-2" />
              <span>Employees</span>
            </div>
            <span className="text-sm font-medium text-gray-900">{department.employee_count}</span>
          </div>

          {department.supervisor_name && (
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm text-gray-600">
                <Crown className="h-4 w-4 mr-2" />
                <span>Supervisor</span>
              </div>
              <span className="text-sm font-medium text-gray-900">{department.supervisor_name}</span>
            </div>
          )}

          {department.location && (
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="h-4 w-4 mr-2" />
                <span>Location</span>
              </div>
              <span className="text-sm font-medium text-gray-900">{department.location}</span>
            </div>
          )}

          {department.budget && (
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm text-gray-600">
                <TrendingUp className="h-4 w-4 mr-2" />
                <span>Budget</span>
              </div>
              <span className="text-sm font-medium text-gray-900">
                ${(department.budget / 1000).toFixed(0)}K
              </span>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm text-gray-600">
              <Calendar className="h-4 w-4 mr-2" />
              <span>Created</span>
            </div>
            <span className="text-sm font-medium text-gray-900">
              {formatDate(department.created_at)}
            </span>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default DepartmentManagementPage;
