'use client';

import React from 'react';
import SupervisorLayout from '@/components/layout/SupervisorLayout';
import RouteTransition from '@/components/RouteTransition';
import { SupervisorGuard } from '@/components/auth/RoleGuard';

interface SupervisorLayoutProps {
  children: React.ReactNode;
}

const SupervisorLayoutWrapper: React.FC<SupervisorLayoutProps> = ({ children }) => {
  return (
    <SupervisorGuard>
      <SupervisorLayout>
        <RouteTransition>
          {children}
        </RouteTransition>
      </SupervisorLayout>
    </SupervisorGuard>
  );
};

export default SupervisorLayoutWrapper;
