'use client';

import React from 'react';
import Link from 'next/link';
import { useAuth } from '@/providers/AuthProvider';
import Button from '@/components/ui/Button';
import {
  Users,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  BarChart3,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ClipboardList,
  Award,
  Target,
  ChevronRight,
  Plus,
  Eye,
  Settings
} from 'lucide-react';

interface ManagementCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  href: string;
  color: string;
  stats?: {
    label: string;
    value: string;
    trend?: 'up' | 'down' | 'neutral';
  };
  quickActions?: {
    label: string;
    href: string;
    icon: React.ComponentType<any>;
  }[];
}

const ManagementCard: React.FC<ManagementCardProps> = ({
  title,
  description,
  icon: Icon,
  href,
  color,
  stats,
  quickActions
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
      <Link href={href} className="block p-6">
        <div className="flex items-start space-x-4">
          <div className={`p-3 rounded-lg ${color}`}>
            <Icon className="h-6 w-6 text-white" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
            <p className="text-gray-600 text-sm mb-3">{description}</p>
            {stats && (
              <div className="flex items-center text-sm text-gray-500 mb-3">
                <TrendingUp className={`h-4 w-4 mr-1 ${
                  stats.trend === 'up' ? 'text-green-500' :
                  stats.trend === 'down' ? 'text-red-500 rotate-180' : 'text-gray-400'
                }`} />
                <span>{stats.label}: </span>
                <span className="font-medium text-gray-900 ml-1">{stats.value}</span>
              </div>
            )}
          </div>
          <ChevronRight className="h-5 w-5 text-gray-400 flex-shrink-0" />
        </div>
      </Link>

      {quickActions && quickActions.length > 0 && (
        <div className="border-t border-gray-200 px-6 py-3">
          <div className="flex flex-wrap gap-2">
            {quickActions.map((action, index) => {
              const ActionIcon = action.icon;
              return (
                <Link
                  key={index}
                  href={action.href}
                  className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  <ActionIcon className="h-3 w-3 mr-1" />
                  {action.label}
                </Link>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

const SupervisorManagePage: React.FC = () => {
  const { user } = useAuth();

  // Mock management data
  const managementStats = {
    team_size: 12,
    pending_approvals: 5,
    active_projects: 8,
    team_performance: 87,
    leave_requests: 3,
    overtime_requests: 2
  };

  const managementAreas = [
    {
      title: 'Leave Management',
      description: 'Approve leave requests, manage holidays, and track team availability.',
      icon: Calendar,
      href: '/supervisor/manage/leave-management',
      color: 'bg-blue-500',
      stats: {
        label: 'Pending requests',
        value: `${managementStats.leave_requests}`,
        trend: 'neutral' as const
      },
      quickActions: [
        { label: 'Approve Leave', href: '/supervisor/manage/leave-management?action=approve', icon: CheckCircle },
        { label: 'View Calendar', href: '/supervisor/manage/leave-management?view=calendar', icon: Calendar },
        { label: 'Add Holiday', href: '/supervisor/manage/leave-management?action=holiday', icon: Plus }
      ]
    },
    {
      title: 'Overtime Management',
      description: 'Manage overtime applications, approvals, and track overtime hours.',
      icon: Clock,
      href: '/supervisor/manage/overtime',
      color: 'bg-orange-500',
      stats: {
        label: 'Pending requests',
        value: `${managementStats.overtime_requests}`,
        trend: 'neutral' as const
      },
      quickActions: [
        { label: 'Approve Overtime', href: '/supervisor/manage/overtime?action=approve', icon: CheckCircle },
        { label: 'Apply for Team', href: '/supervisor/manage/overtime?action=apply', icon: Plus },
        { label: 'View Reports', href: '/supervisor/manage/overtime?view=reports', icon: BarChart3 }
      ]
    },
    {
      title: 'Team Performance',
      description: 'Monitor team performance, set goals, and track progress.',
      icon: TrendingUp,
      href: '/supervisor/manage/performance',
      color: 'bg-green-500',
      stats: {
        label: 'Team score',
        value: `${managementStats.team_performance}%`,
        trend: 'up' as const
      },
      quickActions: [
        { label: 'View Dashboard', href: '/supervisor/manage/performance', icon: BarChart3 },
        { label: 'Set Goals', href: '/supervisor/manage/performance?action=goals', icon: Target },
        { label: 'Generate Report', href: '/supervisor/manage/performance?action=report', icon: ClipboardList }
      ]
    },
    {
      title: 'Team Management',
      description: 'Manage team members, assignments, and organizational structure.',
      icon: Users,
      href: '/supervisor/manage/team',
      color: 'bg-purple-500',
      stats: {
        label: 'Team members',
        value: `${managementStats.team_size}`,
        trend: 'neutral' as const
      },
      quickActions: [
        { label: 'View Team', href: '/supervisor/manage/team', icon: Users },
        { label: 'Assign Tasks', href: '/supervisor/manage/team?action=assign', icon: ClipboardList },
        { label: 'Team Directory', href: '/supervisor/manage/team?view=directory', icon: Eye }
      ]
    }
  ];

  const quickStats = [
    {
      title: 'Team Members',
      value: managementStats.team_size,
      icon: Users,
      color: 'bg-blue-500',
      change: '+2 this month'
    },
    {
      title: 'Pending Approvals',
      value: managementStats.pending_approvals,
      icon: AlertCircle,
      color: 'bg-orange-500',
      change: '3 urgent'
    },
    {
      title: 'Active Projects',
      value: managementStats.active_projects,
      icon: ClipboardList,
      color: 'bg-green-500',
      change: '2 due this week'
    },
    {
      title: 'Team Performance',
      value: `${managementStats.team_performance}%`,
      icon: TrendingUp,
      color: 'bg-purple-500',
      change: '+5% this month'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Team Management Hub</h1>
            <p className="text-purple-100 mt-2">
              Manage your team operations, approvals, and performance from one central location
            </p>
          </div>
          <div className="hidden sm:block">
            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <Users className="h-8 w-8 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg ${stat.color}`}>
                  <Icon className="h-5 w-5 text-white" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-lg font-semibold text-gray-900">{stat.value}</p>
                  <p className="text-xs text-gray-500">{stat.change}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Management Areas */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Management Areas</h2>
          <Button variant="secondary">
            <Settings className="h-4 w-4 mr-2" />
            Customize Dashboard
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {managementAreas.map((area, index) => (
            <ManagementCard
              key={index}
              title={area.title}
              description={area.description}
              icon={area.icon}
              href={area.href}
              color={area.color}
              stats={area.stats}
              quickActions={area.quickActions}
            />
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <ClipboardList className="h-5 w-5 mr-2 text-blue-500" />
            Recent Management Activity
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between py-2">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-900">Approved leave request for John Doe</span>
              </div>
              <span className="text-xs text-gray-500">2 hours ago</span>
            </div>
            <div className="flex items-center justify-between py-2">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-gray-900">Overtime request submitted by Sarah Wilson</span>
              </div>
              <span className="text-xs text-gray-500">4 hours ago</span>
            </div>
            <div className="flex items-center justify-between py-2">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span className="text-sm text-gray-900">Team performance report generated</span>
              </div>
              <span className="text-xs text-gray-500">1 day ago</span>
            </div>
          </div>
          <div className="mt-4 pt-4 border-t">
            <Button variant="secondary" className="w-full">
              <Eye className="h-4 w-4 mr-2" />
              View All Activity
            </Button>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Target className="h-5 w-5 mr-2 text-green-500" />
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <CalendarCheck className="h-6 w-6 text-green-600 mr-3" />
              <span className="font-medium text-gray-900">Approve Leave</span>
            </button>
            <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Clock className="h-6 w-6 text-orange-600 mr-3" />
              <span className="font-medium text-gray-900">Manage Overtime</span>
            </button>
            <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <BarChart3 className="h-6 w-6 text-blue-600 mr-3" />
              <span className="font-medium text-gray-900">View Reports</span>
            </button>
            <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Users className="h-6 w-6 text-purple-600 mr-3" />
              <span className="font-medium text-gray-900">Team Overview</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupervisorManagePage;
