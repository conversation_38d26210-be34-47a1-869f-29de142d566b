e9b397713588bc276a1ebaa45e85c9c6
"use strict";
// Mock the file upload service
jest.mock('@/lib/fileUpload', ()=>({
        __esModule: true,
        default: {
            uploadFile: jest.fn(),
            formatFileSize: jest.fn((size)=>`${size} bytes`),
            isImage: jest.fn((file)=>file.type.startsWith('image/')),
            isPDF: jest.fn((file)=>file.type === 'application/pdf'),
            createPreviewUrl: jest.fn(()=>'mock-preview-url'),
            revokePreviewUrl: jest.fn()
        }
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _react1 = require("@testing-library/react");
const _userevent = /*#__PURE__*/ _interop_require_default(require("@testing-library/user-event"));
const _testutils = require("../../utils/test-utils");
const _FileUpload = /*#__PURE__*/ _interop_require_default(require("../../../components/ui/FileUpload"));
const _fileUpload = /*#__PURE__*/ _interop_require_default(require("../../../lib/fileUpload"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockFileUploadService = _fileUpload.default;
describe('FileUpload', ()=>{
    const defaultProps = {
        endpoint: '/api/upload',
        onUpload: jest.fn()
    };
    beforeEach(()=>{
        jest.clearAllMocks();
        mockFileUploadService.uploadFile.mockResolvedValue({
            success: true,
            url: 'https://example.com/file.pdf',
            filename: 'file.pdf',
            size: 1024
        });
    });
    describe('rendering', ()=>{
        it('renders upload area with default content', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps
            }));
            expect(_react1.screen.getByText('Click to upload or drag and drop')).toBeInTheDocument();
            expect(_react1.screen.getByText('All file types accepted')).toBeInTheDocument();
        });
        it('renders custom children when provided', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps,
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Custom Upload Area"
                })
            }));
            expect(_react1.screen.getByText('Custom Upload Area')).toBeInTheDocument();
            expect(_react1.screen.queryByText('Click to upload or drag and drop')).not.toBeInTheDocument();
        });
        it('shows accepted formats when accept prop is provided', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps,
                accept: ".pdf,.doc"
            }));
            expect(_react1.screen.getByText('Accepted formats: .pdf,.doc')).toBeInTheDocument();
        });
        it('shows max size when maxSize prop is provided', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps,
                maxSize: 5 * 1024 * 1024
            }));
            expect(_react1.screen.getByText('Max size: 5242880 bytes')).toBeInTheDocument();
        });
    });
    describe('file selection', ()=>{
        it('handles file selection via input', async ()=>{
            const user = _userevent.default.setup();
            const file = (0, _testutils.createMockFile)('test.pdf');
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps
            }));
            const input = _react1.screen.getByRole('button', {
                name: /click to upload/i
            });
            await user.click(input);
            // Simulate file selection
            const fileInput = document.querySelector('input[type="file"]');
            Object.defineProperty(fileInput, 'files', {
                value: [
                    file
                ],
                writable: false
            });
            _react1.fireEvent.change(fileInput);
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Selected Files (1)')).toBeInTheDocument();
                expect(_react1.screen.getByText('test.pdf')).toBeInTheDocument();
            });
        });
        it('handles multiple file selection when multiple is true', async ()=>{
            const user = _userevent.default.setup();
            const files = [
                (0, _testutils.createMockFile)('test1.pdf'),
                (0, _testutils.createMockFile)('test2.pdf')
            ];
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps,
                multiple: true
            }));
            const input = _react1.screen.getByRole('button', {
                name: /click to upload/i
            });
            await user.click(input);
            const fileInput = document.querySelector('input[type="file"]');
            Object.defineProperty(fileInput, 'files', {
                value: files,
                writable: false
            });
            _react1.fireEvent.change(fileInput);
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Selected Files (2)')).toBeInTheDocument();
                expect(_react1.screen.getByText('test1.pdf')).toBeInTheDocument();
                expect(_react1.screen.getByText('test2.pdf')).toBeInTheDocument();
            });
        });
        it('limits files to maxFiles when specified', async ()=>{
            const user = _userevent.default.setup();
            const files = [
                (0, _testutils.createMockFile)('test1.pdf'),
                (0, _testutils.createMockFile)('test2.pdf'),
                (0, _testutils.createMockFile)('test3.pdf')
            ];
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps,
                multiple: true,
                maxFiles: 2
            }));
            const input = _react1.screen.getByRole('button', {
                name: /click to upload/i
            });
            await user.click(input);
            const fileInput = document.querySelector('input[type="file"]');
            Object.defineProperty(fileInput, 'files', {
                value: files,
                writable: false
            });
            _react1.fireEvent.change(fileInput);
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Selected Files (2)')).toBeInTheDocument();
                expect(_react1.screen.getByText('test1.pdf')).toBeInTheDocument();
                expect(_react1.screen.getByText('test2.pdf')).toBeInTheDocument();
                expect(_react1.screen.queryByText('test3.pdf')).not.toBeInTheDocument();
            });
        });
    });
    describe('drag and drop', ()=>{
        it('handles drag over events', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps
            }));
            const dropZone = _react1.screen.getByRole('button', {
                name: /click to upload/i
            });
            _react1.fireEvent.dragOver(dropZone);
            // Should add drag over styling (tested via class changes)
            expect(dropZone).toHaveClass('border-orange-500', 'bg-orange-50');
        });
        it('handles drag leave events', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps
            }));
            const dropZone = _react1.screen.getByRole('button', {
                name: /click to upload/i
            });
            _react1.fireEvent.dragOver(dropZone);
            _react1.fireEvent.dragLeave(dropZone);
            // Should remove drag over styling
            expect(dropZone).not.toHaveClass('border-orange-500', 'bg-orange-50');
        });
        it('handles file drop', async ()=>{
            const file = (0, _testutils.createMockFile)('dropped.pdf');
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps
            }));
            const dropZone = _react1.screen.getByRole('button', {
                name: /click to upload/i
            });
            _react1.fireEvent.drop(dropZone, {
                dataTransfer: {
                    files: [
                        file
                    ]
                }
            });
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Selected Files (1)')).toBeInTheDocument();
                expect(_react1.screen.getByText('dropped.pdf')).toBeInTheDocument();
            });
        });
    });
    describe('file removal', ()=>{
        it('allows removing selected files', async ()=>{
            const user = _userevent.default.setup();
            const file = (0, _testutils.createMockFile)('test.pdf');
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps
            }));
            // Add file
            const input = _react1.screen.getByRole('button', {
                name: /click to upload/i
            });
            await user.click(input);
            const fileInput = document.querySelector('input[type="file"]');
            Object.defineProperty(fileInput, 'files', {
                value: [
                    file
                ],
                writable: false
            });
            _react1.fireEvent.change(fileInput);
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('test.pdf')).toBeInTheDocument();
            });
            // Remove file
            const removeButton = _react1.screen.getByRole('button', {
                name: ''
            }); // X button
            await user.click(removeButton);
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.queryByText('Selected Files (1)')).not.toBeInTheDocument();
                expect(_react1.screen.queryByText('test.pdf')).not.toBeInTheDocument();
            });
        });
    });
    describe('file upload', ()=>{
        it('uploads files when upload button is clicked', async ()=>{
            const user = _userevent.default.setup();
            const file = (0, _testutils.createMockFile)('test.pdf');
            const onUpload = jest.fn();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps,
                onUpload: onUpload
            }));
            // Add file
            const input = _react1.screen.getByRole('button', {
                name: /click to upload/i
            });
            await user.click(input);
            const fileInput = document.querySelector('input[type="file"]');
            Object.defineProperty(fileInput, 'files', {
                value: [
                    file
                ],
                writable: false
            });
            _react1.fireEvent.change(fileInput);
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Upload All')).toBeInTheDocument();
            });
            // Upload files
            const uploadButton = _react1.screen.getByText('Upload All');
            await user.click(uploadButton);
            await (0, _react1.waitFor)(()=>{
                expect(mockFileUploadService.uploadFile).toHaveBeenCalledWith(file, '/api/upload', expect.objectContaining({
                    folder: undefined,
                    maxSize: undefined,
                    onProgress: expect.any(Function)
                }));
                expect(onUpload).toHaveBeenCalledWith([
                    {
                        success: true,
                        url: 'https://example.com/file.pdf',
                        filename: 'file.pdf',
                        size: 1024
                    }
                ]);
            });
        });
        it('shows upload progress during upload', async ()=>{
            const user = _userevent.default.setup();
            const file = (0, _testutils.createMockFile)('test.pdf');
            let progressCallback;
            mockFileUploadService.uploadFile.mockImplementation((file, endpoint, options)=>{
                progressCallback = options?.onProgress;
                return new Promise((resolve)=>{
                    setTimeout(()=>{
                        resolve({
                            success: true,
                            url: 'https://example.com/file.pdf',
                            filename: 'file.pdf',
                            size: 1024
                        });
                    }, 100);
                });
            });
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps
            }));
            // Add file
            const input = _react1.screen.getByRole('button', {
                name: /click to upload/i
            });
            await user.click(input);
            const fileInput = document.querySelector('input[type="file"]');
            Object.defineProperty(fileInput, 'files', {
                value: [
                    file
                ],
                writable: false
            });
            _react1.fireEvent.change(fileInput);
            // Upload files
            const uploadButton = _react1.screen.getByText('Upload All');
            await user.click(uploadButton);
            // Simulate progress
            if (progressCallback) {
                progressCallback({
                    loaded: 50,
                    total: 100,
                    percentage: 50
                });
            }
            expect(_react1.screen.getByText('50%')).toBeInTheDocument();
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.queryByText('50%')).not.toBeInTheDocument();
            });
        });
        it('handles upload errors', async ()=>{
            const user = _userevent.default.setup();
            const file = (0, _testutils.createMockFile)('test.pdf');
            mockFileUploadService.uploadFile.mockResolvedValue({
                success: false,
                error: 'Upload failed'
            });
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps
            }));
            // Add file
            const input = _react1.screen.getByRole('button', {
                name: /click to upload/i
            });
            await user.click(input);
            const fileInput = document.querySelector('input[type="file"]');
            Object.defineProperty(fileInput, 'files', {
                value: [
                    file
                ],
                writable: false
            });
            _react1.fireEvent.change(fileInput);
            // Upload files
            const uploadButton = _react1.screen.getByText('Upload All');
            await user.click(uploadButton);
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Upload failed')).toBeInTheDocument();
            });
        });
    });
    describe('disabled state', ()=>{
        it('prevents interaction when disabled', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_FileUpload.default, {
                ...defaultProps,
                disabled: true
            }));
            const dropZone = _react1.screen.getByRole('button', {
                name: /click to upload/i
            });
            expect(dropZone).toHaveClass('opacity-50', 'cursor-not-allowed');
            // Should not respond to drag events
            _react1.fireEvent.dragOver(dropZone);
            expect(dropZone).not.toHaveClass('border-orange-500');
            // Should not open file dialog
            await user.click(dropZone);
            // File input should be disabled
            const fileInput = document.querySelector('input[type="file"]');
            expect(fileInput).toBeDisabled();
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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