{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/components/biostar/EmployeeBiostarWidget.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport {\n  <PERSON>gerprint,\n  Eye,\n  Wifi,\n  WifiOff,\n  Clock,\n  CheckCircle,\n  AlertCircle,\n  Activity,\n  Monitor,\n  ArrowRight,\n  RefreshCw\n} from 'lucide-react';\nimport Card from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport { cn, formatDate } from '@/lib/utils';\nimport { useBiostarAttendance } from '@/hooks/useBiostarAttendance';\nimport { BiometricEvent } from '@/types';\n\ninterface EmployeeBiostarWidgetProps {\n  employeeId?: string;\n  showFullDetails?: boolean;\n  className?: string;\n}\n\nconst EmployeeBiostarWidget: React.FC<EmployeeBiostarWidgetProps> = ({\n  employeeId,\n  showFullDetails = false,\n  className\n}) => {\n  const [recentEvents, setRecentEvents] = useState<BiometricEvent[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const {\n    todayAttendance,\n    devices,\n    connected: biostarConnected,\n    error: connectionError,\n    summary\n  } = useBiostarAttendance({\n    employeeId,\n    autoRefresh: true,\n    enableRealTime: true\n  });\n\n  useEffect(() => {\n    // Simulate loading recent events\n    setLoading(false);\n    \n    // Mock recent events - in real app, this would come from the hook\n    setRecentEvents([\n      {\n        id: '1',\n        user_id: employeeId || '',\n        device_id: 'dev-001',\n        event_type: 'ENTRY',\n        datetime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago\n        user_name: 'Current User',\n        device_name: 'Main Entrance'\n      },\n      {\n        id: '2',\n        user_id: employeeId || '',\n        device_id: 'dev-002',\n        event_type: 'EXIT',\n        datetime: new Date(Date.now() - 10 * 60 * 60 * 1000).toISOString(), // 10 hours ago\n        user_name: 'Current User',\n        device_name: 'Main Entrance'\n      }\n    ]);\n  }, [employeeId]);\n\n  const getEventTypeIcon = (eventType: string) => {\n    switch (eventType) {\n      case 'ENTRY': return <CheckCircle className=\"h-4 w-4 text-green-500\" />;\n      case 'EXIT': return <Activity className=\"h-4 w-4 text-blue-500\" />;\n      case 'DENIED': return <AlertCircle className=\"h-4 w-4 text-red-500\" />;\n      default: return <Clock className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getEventTypeColor = (eventType: string) => {\n    switch (eventType) {\n      case 'ENTRY': return 'bg-green-100 text-green-800';\n      case 'EXIT': return 'bg-blue-100 text-blue-800';\n      case 'DENIED': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  if (loading) {\n    return (\n      <Card className={className}>\n        <div className=\"p-6\">\n          <div className=\"flex items-center justify-center\">\n            <RefreshCw className=\"h-6 w-6 animate-spin text-gray-400\" />\n            <span className=\"ml-2 text-gray-600\">Loading BioStar data...</span>\n          </div>\n        </div>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className={className}>\n      <div className=\"p-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"p-2 bg-purple-100 rounded-lg\">\n              <Fingerprint className=\"h-5 w-5 text-purple-600\" />\n            </div>\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900\">BioStar Profile</h3>\n              <div className=\"flex items-center space-x-2\">\n                {biostarConnected ? (\n                  <Wifi className=\"h-3 w-3 text-green-500\" />\n                ) : (\n                  <WifiOff className=\"h-3 w-3 text-red-500\" />\n                )}\n                <span className=\"text-xs text-gray-600\">\n                  {biostarConnected ? 'Connected' : 'Disconnected'}\n                </span>\n              </div>\n            </div>\n          </div>\n          <Link href=\"/staff/info/biostar\">\n            <Button variant=\"secondary\" size=\"sm\">\n              <ArrowRight className=\"h-4 w-4\" />\n            </Button>\n          </Link>\n        </div>\n\n        {/* Connection Error */}\n        {connectionError && (\n          <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\n            <div className=\"flex items-center\">\n              <AlertCircle className=\"h-4 w-4 text-red-500 mr-2\" />\n              <span className=\"text-sm text-red-700\">Connection Error</span>\n            </div>\n          </div>\n        )}\n\n        {/* Quick Stats */}\n        <div className=\"grid grid-cols-2 gap-4 mb-6\">\n          <div className=\"text-center p-3 bg-gray-50 rounded-lg\">\n            <div className=\"flex items-center justify-center mb-1\">\n              <Fingerprint className=\"h-4 w-4 text-blue-600\" />\n            </div>\n            <div className=\"text-lg font-bold text-gray-900\">2</div>\n            <div className=\"text-xs text-gray-600\">Fingerprints</div>\n          </div>\n          <div className=\"text-center p-3 bg-gray-50 rounded-lg\">\n            <div className=\"flex items-center justify-center mb-1\">\n              <Eye className=\"h-4 w-4 text-purple-600\" />\n            </div>\n            <div className=\"text-lg font-bold text-gray-900\">1</div>\n            <div className=\"text-xs text-gray-600\">Face Template</div>\n          </div>\n        </div>\n\n        {/* Today's Status */}\n        {todayAttendance && (\n          <div className=\"mb-6 p-3 border border-gray-200 rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h4 className=\"font-medium text-gray-900\">Today's Status</h4>\n                <p className=\"text-sm text-gray-600\">\n                  {todayAttendance.first_in ? \n                    `Checked in at ${new Date(todayAttendance.first_in).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}` :\n                    'Not checked in'\n                  }\n                </p>\n              </div>\n              <span className={cn(\n                'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',\n                todayAttendance.status === 'PRESENT' ? 'bg-green-100 text-green-800' :\n                todayAttendance.status === 'LATE' ? 'bg-yellow-100 text-yellow-800' :\n                'bg-red-100 text-red-800'\n              )}>\n                {todayAttendance.status}\n              </span>\n            </div>\n          </div>\n        )}\n\n        {/* Recent Access Events */}\n        <div className=\"space-y-3\">\n          <h4 className=\"font-medium text-gray-900\">Recent Access</h4>\n          {recentEvents.length === 0 ? (\n            <p className=\"text-sm text-gray-600 italic\">No recent access events</p>\n          ) : (\n            <div className=\"space-y-2\">\n              {recentEvents.slice(0, showFullDetails ? 5 : 2).map((event, index) => (\n                <div key={`${event.id}-${index}`} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                  <div className=\"flex items-center space-x-2\">\n                    {getEventTypeIcon(event.event_type)}\n                    <div>\n                      <span className=\"text-sm font-medium text-gray-900\">\n                        {event.event_type === 'ENTRY' ? 'Check In' : \n                         event.event_type === 'EXIT' ? 'Check Out' : 'Access Denied'}\n                      </span>\n                      <div className=\"text-xs text-gray-600\">\n                        {formatDate(event.datetime, 'MMM dd, HH:mm')}\n                      </div>\n                    </div>\n                  </div>\n                  <span className={cn(\n                    'inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium',\n                    getEventTypeColor(event.event_type)\n                  )}>\n                    {event.event_type}\n                  </span>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Device Status */}\n        {devices.length > 0 && (\n          <div className=\"mt-4 pt-4 border-t border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">Devices Online</span>\n              <div className=\"flex items-center space-x-1\">\n                <Monitor className=\"h-3 w-3 text-gray-500\" />\n                <span className=\"text-sm font-medium\">\n                  {devices.filter(d => d.status === 'ONLINE').length}/{devices.length}\n                </span>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* View Full Profile Link */}\n        <div className=\"mt-4 pt-4 border-t border-gray-200\">\n          <Link \n            href=\"/staff/info/biostar\"\n            className=\"block w-full text-center py-2 text-sm text-purple-600 hover:text-purple-700 font-medium\"\n          >\n            View Full BioStar Profile →\n          </Link>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default EmployeeBiostarWidget;\n"], "names": ["EmployeeBiostarWidget", "employeeId", "showFullDetails", "className", "recentEvents", "setRecentEvents", "useState", "loading", "setLoading", "todayAttendance", "devices", "connected", "biostarConnected", "error", "connectionError", "summary", "useBiostarAttendance", "autoRefresh", "enableRealTime", "useEffect", "id", "user_id", "device_id", "event_type", "datetime", "Date", "now", "toISOString", "user_name", "device_name", "getEventTypeIcon", "eventType", "CheckCircle", "Activity", "AlertCircle", "Clock", "getEventTypeColor", "Card", "div", "RefreshCw", "span", "Fingerprint", "h3", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Link", "href", "<PERSON><PERSON>", "variant", "size", "ArrowRight", "Eye", "h4", "p", "first_in", "toLocaleTimeString", "hour", "minute", "cn", "status", "length", "slice", "map", "event", "index", "formatDate", "Monitor", "filter", "d"], "mappings": "AAAA;;;;;+BA4PA;;;eAAA;;;;+DA1P2C;6DAC1B;6BAaV;6DACU;+DACE;uBACY;sCACM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrC,MAAMA,wBAA8D,CAAC,EACnEC,UAAU,EACVC,kBAAkB,KAAK,EACvBC,SAAS,EACV;IACC,MAAM,CAACC,cAAcC,gBAAgB,GAAGC,IAAAA,eAAQ,EAAmB,EAAE;IACrE,MAAM,CAACC,SAASC,WAAW,GAAGF,IAAAA,eAAQ,EAAC;IAEvC,MAAM,EACJG,eAAe,EACfC,OAAO,EACPC,WAAWC,gBAAgB,EAC3BC,OAAOC,eAAe,EACtBC,OAAO,EACR,GAAGC,IAAAA,0CAAoB,EAAC;QACvBf;QACAgB,aAAa;QACbC,gBAAgB;IAClB;IAEAC,IAAAA,gBAAS,EAAC;QACR,iCAAiC;QACjCX,WAAW;QAEX,kEAAkE;QAClEH,gBAAgB;YACd;gBACEe,IAAI;gBACJC,SAASpB,cAAc;gBACvBqB,WAAW;gBACXC,YAAY;gBACZC,UAAU,IAAIC,KAAKA,KAAKC,GAAG,KAAK,IAAI,KAAK,KAAK,MAAMC,WAAW;gBAC/DC,WAAW;gBACXC,aAAa;YACf;YACA;gBACET,IAAI;gBACJC,SAASpB,cAAc;gBACvBqB,WAAW;gBACXC,YAAY;gBACZC,UAAU,IAAIC,KAAKA,KAAKC,GAAG,KAAK,KAAK,KAAK,KAAK,MAAMC,WAAW;gBAChEC,WAAW;gBACXC,aAAa;YACf;SACD;IACH,GAAG;QAAC5B;KAAW;IAEf,MAAM6B,mBAAmB,CAACC;QACxB,OAAQA;YACN,KAAK;gBAAS,qBAAO,qBAACC,wBAAW;oBAAC7B,WAAU;;YAC5C,KAAK;gBAAQ,qBAAO,qBAAC8B,qBAAQ;oBAAC9B,WAAU;;YACxC,KAAK;gBAAU,qBAAO,qBAAC+B,wBAAW;oBAAC/B,WAAU;;YAC7C;gBAAS,qBAAO,qBAACgC,kBAAK;oBAAChC,WAAU;;QACnC;IACF;IAEA,MAAMiC,oBAAoB,CAACL;QACzB,OAAQA;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAIxB,SAAS;QACX,qBACE,qBAAC8B,aAAI;YAAClC,WAAWA;sBACf,cAAA,qBAACmC;gBAAInC,WAAU;0BACb,cAAA,sBAACmC;oBAAInC,WAAU;;sCACb,qBAACoC,sBAAS;4BAACpC,WAAU;;sCACrB,qBAACqC;4BAAKrC,WAAU;sCAAqB;;;;;;IAK/C;IAEA,qBACE,qBAACkC,aAAI;QAAClC,WAAWA;kBACf,cAAA,sBAACmC;YAAInC,WAAU;;8BAEb,sBAACmC;oBAAInC,WAAU;;sCACb,sBAACmC;4BAAInC,WAAU;;8CACb,qBAACmC;oCAAInC,WAAU;8CACb,cAAA,qBAACsC,wBAAW;wCAACtC,WAAU;;;8CAEzB,sBAACmC;;sDACC,qBAACI;4CAAGvC,WAAU;sDAAoC;;sDAClD,sBAACmC;4CAAInC,WAAU;;gDACZS,iCACC,qBAAC+B,iBAAI;oDAACxC,WAAU;mEAEhB,qBAACyC,oBAAO;oDAACzC,WAAU;;8DAErB,qBAACqC;oDAAKrC,WAAU;8DACbS,mBAAmB,cAAc;;;;;;;;sCAK1C,qBAACiC,aAAI;4BAACC,MAAK;sCACT,cAAA,qBAACC,eAAM;gCAACC,SAAQ;gCAAYC,MAAK;0CAC/B,cAAA,qBAACC,uBAAU;oCAAC/C,WAAU;;;;;;gBAM3BW,iCACC,qBAACwB;oBAAInC,WAAU;8BACb,cAAA,sBAACmC;wBAAInC,WAAU;;0CACb,qBAAC+B,wBAAW;gCAAC/B,WAAU;;0CACvB,qBAACqC;gCAAKrC,WAAU;0CAAuB;;;;;8BAM7C,sBAACmC;oBAAInC,WAAU;;sCACb,sBAACmC;4BAAInC,WAAU;;8CACb,qBAACmC;oCAAInC,WAAU;8CACb,cAAA,qBAACsC,wBAAW;wCAACtC,WAAU;;;8CAEzB,qBAACmC;oCAAInC,WAAU;8CAAkC;;8CACjD,qBAACmC;oCAAInC,WAAU;8CAAwB;;;;sCAEzC,sBAACmC;4BAAInC,WAAU;;8CACb,qBAACmC;oCAAInC,WAAU;8CACb,cAAA,qBAACgD,gBAAG;wCAAChD,WAAU;;;8CAEjB,qBAACmC;oCAAInC,WAAU;8CAAkC;;8CACjD,qBAACmC;oCAAInC,WAAU;8CAAwB;;;;;;gBAK1CM,iCACC,qBAAC6B;oBAAInC,WAAU;8BACb,cAAA,sBAACmC;wBAAInC,WAAU;;0CACb,sBAACmC;;kDACC,qBAACc;wCAAGjD,WAAU;kDAA4B;;kDAC1C,qBAACkD;wCAAElD,WAAU;kDACVM,gBAAgB6C,QAAQ,GACvB,CAAC,cAAc,EAAE,IAAI7B,KAAKhB,gBAAgB6C,QAAQ,EAAEC,kBAAkB,CAAC,EAAE,EAAE;4CAAEC,MAAM;4CAAWC,QAAQ;wCAAU,IAAI,GACpH;;;;0CAIN,qBAACjB;gCAAKrC,WAAWuD,IAAAA,SAAE,EACjB,uEACAjD,gBAAgBkD,MAAM,KAAK,YAAY,gCACvClD,gBAAgBkD,MAAM,KAAK,SAAS,kCACpC;0CAEClD,gBAAgBkD,MAAM;;;;;8BAO/B,sBAACrB;oBAAInC,WAAU;;sCACb,qBAACiD;4BAAGjD,WAAU;sCAA4B;;wBACzCC,aAAawD,MAAM,KAAK,kBACvB,qBAACP;4BAAElD,WAAU;sCAA+B;2CAE5C,qBAACmC;4BAAInC,WAAU;sCACZC,aAAayD,KAAK,CAAC,GAAG3D,kBAAkB,IAAI,GAAG4D,GAAG,CAAC,CAACC,OAAOC,sBAC1D,sBAAC1B;oCAAiCnC,WAAU;;sDAC1C,sBAACmC;4CAAInC,WAAU;;gDACZ2B,iBAAiBiC,MAAMxC,UAAU;8DAClC,sBAACe;;sEACC,qBAACE;4DAAKrC,WAAU;sEACb4D,MAAMxC,UAAU,KAAK,UAAU,aAC/BwC,MAAMxC,UAAU,KAAK,SAAS,cAAc;;sEAE/C,qBAACe;4DAAInC,WAAU;sEACZ8D,IAAAA,iBAAU,EAACF,MAAMvC,QAAQ,EAAE;;;;;;sDAIlC,qBAACgB;4CAAKrC,WAAWuD,IAAAA,SAAE,EACjB,sEACAtB,kBAAkB2B,MAAMxC,UAAU;sDAEjCwC,MAAMxC,UAAU;;;mCAjBX,GAAGwC,MAAM3C,EAAE,CAAC,CAAC,EAAE4C,OAAO;;;;gBA0BvCtD,QAAQkD,MAAM,GAAG,mBAChB,qBAACtB;oBAAInC,WAAU;8BACb,cAAA,sBAACmC;wBAAInC,WAAU;;0CACb,qBAACqC;gCAAKrC,WAAU;0CAAwB;;0CACxC,sBAACmC;gCAAInC,WAAU;;kDACb,qBAAC+D,oBAAO;wCAAC/D,WAAU;;kDACnB,sBAACqC;wCAAKrC,WAAU;;4CACbO,QAAQyD,MAAM,CAACC,CAAAA,IAAKA,EAAET,MAAM,KAAK,UAAUC,MAAM;4CAAC;4CAAElD,QAAQkD,MAAM;;;;;;;;8BAQ7E,qBAACtB;oBAAInC,WAAU;8BACb,cAAA,qBAAC0C,aAAI;wBACHC,MAAK;wBACL3C,WAAU;kCACX;;;;;;AAOX;MAEA,WAAeH"}