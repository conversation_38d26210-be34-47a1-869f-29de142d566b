'use client';

import React, { useState, useEffect } from 'react';
import { User, ChevronDown, ChevronRight, Building, Crown, Shield, Users } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Employee {
  id: number;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  position: string;
  department_name: string;
  role: 'employee' | 'supervisor' | 'hr' | 'accountant' | 'admin';
  supervisor_id?: number;
  profile_picture?: string;
}

interface OrgNode {
  employee: Employee;
  children: OrgNode[];
  level: number;
}

interface OrganizationalChartProps {
  employees: Employee[];
  className?: string;
}

const getRoleIcon = (role: string) => {
  switch (role) {
    case 'admin':
      return <Crown className="h-4 w-4 text-yellow-600" />;
    case 'supervisor':
      return <Shield className="h-4 w-4 text-blue-600" />;
    case 'hr':
      return <Users className="h-4 w-4 text-green-600" />;
    default:
      return <User className="h-4 w-4 text-gray-600" />;
  }
};

const getRoleColor = (role: string) => {
  switch (role) {
    case 'admin':
      return 'border-yellow-200 bg-yellow-50';
    case 'supervisor':
      return 'border-blue-200 bg-blue-50';
    case 'hr':
      return 'border-green-200 bg-green-50';
    default:
      return 'border-gray-200 bg-white';
  }
};

const EmployeeNode: React.FC<{
  node: OrgNode;
  isExpanded: boolean;
  onToggle: () => void;
  hasChildren: boolean;
}> = ({ node, isExpanded, onToggle, hasChildren }) => {
  const { employee } = node;
  const initials = `${employee.first_name.charAt(0)}${employee.last_name.charAt(0)}`.toUpperCase();

  return (
    <div className="flex flex-col items-center">
      {/* Employee Card */}
      <div
        className={cn(
          'relative bg-white border-2 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer min-w-[200px] max-w-[250px]',
          getRoleColor(employee.role)
        )}
        onClick={hasChildren ? onToggle : undefined}
      >
        {/* Expand/Collapse Button */}
        {hasChildren && (
          <button
            className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 bg-white border border-gray-300 rounded-full p-1 hover:bg-gray-50"
            onClick={(e) => {
              e.stopPropagation();
              onToggle();
            }}
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 text-gray-600" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-600" />
            )}
          </button>
        )}

        <div className="flex items-center space-x-3">
          {/* Profile Picture */}
          <div className="flex-shrink-0">
            {employee.profile_picture ? (
              <img
                src={employee.profile_picture}
                alt={`${employee.first_name} ${employee.last_name}`}
                className="w-12 h-12 rounded-full object-cover"
              />
            ) : (
              <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center text-white font-medium">
                {initials}
              </div>
            )}
          </div>

          {/* Employee Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <h4 className="font-semibold text-gray-900 truncate">
                {employee.first_name} {employee.last_name}
              </h4>
              {getRoleIcon(employee.role)}
            </div>
            <p className="text-sm text-gray-600 truncate" title={employee.position}>
              {employee.position}
            </p>
            <p className="text-xs text-gray-500 truncate" title={employee.department_name}>
              {employee.department_name}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

const OrganizationalChart: React.FC<OrganizationalChartProps> = ({
  employees,
  className
}) => {
  const [expandedNodes, setExpandedNodes] = useState<Set<number>>(new Set());
  const [orgTree, setOrgTree] = useState<OrgNode[]>([]);

  // Build organizational tree
  useEffect(() => {
    const buildTree = (employees: Employee[]): OrgNode[] => {
      const employeeMap = new Map<number, Employee>();
      const childrenMap = new Map<number, Employee[]>();

      // Create maps for quick lookup
      employees.forEach(emp => {
        employeeMap.set(emp.id, emp);
        if (!childrenMap.has(emp.id)) {
          childrenMap.set(emp.id, []);
        }
      });

      // Group employees by supervisor
      employees.forEach(emp => {
        if (emp.supervisor_id && employeeMap.has(emp.supervisor_id)) {
          const children = childrenMap.get(emp.supervisor_id) || [];
          children.push(emp);
          childrenMap.set(emp.supervisor_id, children);
        }
      });

      // Find root employees (no supervisor or supervisor not in list)
      const roots = employees.filter(emp =>
        !emp.supervisor_id || !employeeMap.has(emp.supervisor_id)
      );

      // Build tree recursively
      const buildNode = (employee: Employee, level: number = 0): OrgNode => {
        const children = childrenMap.get(employee.id) || [];
        return {
          employee,
          children: children.map(child => buildNode(child, level + 1)),
          level
        };
      };

      return roots.map(root => buildNode(root));
    };

    setOrgTree(buildTree(employees));
  }, [employees]);

  const toggleNode = (nodeId: number) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };

  const renderTree = (nodes: OrgNode[], level: number = 0): React.ReactNode => {
    if (nodes.length === 0) return null;

    return (
      <div className="flex flex-col items-center space-y-8">
        {nodes.map((node, index) => {
          const isExpanded = expandedNodes.has(node.employee.id);
          const hasChildren = node.children.length > 0;

          return (
            <div key={node.employee.id} className="flex flex-col items-center">
              {/* Employee Node */}
              <EmployeeNode
                node={node}
                isExpanded={isExpanded}
                onToggle={() => toggleNode(node.employee.id)}
                hasChildren={hasChildren}
              />

              {/* Children */}
              {hasChildren && isExpanded && (
                <div className="mt-8 relative">
                  {/* Vertical line from parent */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-px h-6 bg-gray-300"></div>

                  {/* Children container */}
                  <div className="flex space-x-8 pt-6">
                    {node.children.map((child, childIndex) => (
                      <div key={child.employee.id} className="relative">
                        {/* Horizontal line to child */}
                        <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 w-px h-6 bg-gray-300"></div>
                        {childIndex > 0 && (
                          <div className="absolute -top-6 left-0 right-1/2 h-px bg-gray-300"></div>
                        )}
                        {childIndex < node.children.length - 1 && (
                          <div className="absolute -top-6 left-1/2 right-0 h-px bg-gray-300"></div>
                        )}

                        {renderTree([child], level + 1)}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  if (orgTree.length === 0) {
    return (
      <div className={cn('flex flex-col items-center justify-center py-12', className)}>
        <Building className="h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Organizational Data</h3>
        <p className="text-gray-600 text-center max-w-md">
          No employee hierarchy data available. Add employees with supervisor relationships to see the organizational chart.
        </p>
      </div>
    );
  }

  return (
    <div className={cn('w-full overflow-x-auto', className)}>
      <div className="min-w-max p-8">
        {renderTree(orgTree)}
      </div>
    </div>
  );
};

export default OrganizationalChart;
