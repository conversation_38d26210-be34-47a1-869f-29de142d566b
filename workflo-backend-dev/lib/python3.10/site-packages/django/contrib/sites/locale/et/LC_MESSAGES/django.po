# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON> <<EMAIL>>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Estonian (http://www.transifex.com/django/django/language/"
"et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Sites"
msgstr "Saidid"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "Domeeni nimes ei tohi olla ei tühikuid ega tabeldusmärke."

msgid "domain name"
msgstr "domeeninimi"

msgid "display name"
msgstr "hüüdnimi"

msgid "site"
msgstr "sait"

msgid "sites"
msgstr "saidid"
