# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-24 11:40-0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: hao wang <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: admin.py templates/admin/import_export/change_list_import_item.html
#: templates/admin/import_export/import.html
msgid "Import"
msgstr "导入"

#: admin.py
#, python-format
msgid "%s through import_export"
msgstr "%s 通过 django-import-export导入"

#: admin.py
msgid "Import finished: {} new, {} updated, {} deleted and {} skipped {}."
msgstr "导入成功，新增{}条记录，更新{}条记录，删除{}条记录，忽略{}条记录。"

#: admin.py
#, python-format
msgid ""
"%(exc_name)s encountered while trying to read file. Ensure you have chosen "
"the correct format for the file."
msgstr ""

#: admin.py
msgid ""
"No valid data to import. Ensure your file has the correct headers or data "
"for import."
msgstr ""

#: admin.py templates/admin/import_export/change_form.html
#: templates/admin/import_export/change_list_export_item.html
#: templates/admin/import_export/export.html
msgid "Export"
msgstr "导出"

#: admin.py
#, python-format
msgid "Export selected %(verbose_name_plural)s"
msgstr "导出选中的 %(verbose_name_plural)s"

#: formats/base_formats.py
msgid "export failed due to IllegalCharacterError"
msgstr ""

#: forms.py
msgid "Resource"
msgstr ""

#: forms.py
msgid "Format"
msgstr "格式"

#: forms.py
msgid "File to import"
msgstr "导入文件"

#: forms.py
msgid "Form is not validated, call `is_valid` first"
msgstr ""

#: forms.py
#, python-format
msgid "Select at least 1 field for \"%(resource_name)s\" to export"
msgstr ""

#: resources.py
#, python-format
msgid ""
"The following fields are declared in 'import_id_fields' but are not present "
"in the resource fields: %s"
msgstr ""

#: resources.py
#, python-format
msgid ""
"The following fields are declared in 'import_id_fields' but are not present "
"in the file headers: %s"
msgstr ""

#: results.py
#, python-format
msgid "call to force_str() on instance failed: %s"
msgstr ""

#: templates/admin/import_export/base.html
msgid "Home"
msgstr ""

#: templates/admin/import_export/export.html
#, python-format
msgid "Export %(len)s selected item."
msgid_plural "Export %(len)s selected items."
msgstr[0] ""

#: templates/admin/import_export/export.html
#: templates/admin/import_export/resource_fields_list.html
msgid "This exporter will export the following fields: "
msgstr "此次将导出以下字段："

#: templates/admin/import_export/export.html
#: templates/admin/import_export/import.html
msgid "Submit"
msgstr "提交"

#: templates/admin/import_export/import.html
msgid ""
"Below is a preview of data to be imported. If you are satisfied with the "
"results, click 'Confirm import'"
msgstr "以下是导入数据的预览。如果确认结果没有问题，可以点击 “确认导入”"

#: templates/admin/import_export/import.html
msgid "Confirm import"
msgstr "确认导入"

#: templates/admin/import_export/import.html
msgid "Errors"
msgstr "错误"

#: templates/admin/import_export/import.html
msgid "Line number"
msgstr "行号"

#: templates/admin/import_export/import.html
msgid "Some rows failed to validate"
msgstr "某些行验数据证失败"

#: templates/admin/import_export/import.html
msgid ""
"Please correct these errors in your data where possible, then reupload it "
"using the form above."
msgstr "请使用上面的表单，纠正这些提示有错误的数据，并重新上传"

#: templates/admin/import_export/import.html
msgid "Row"
msgstr "行"

#: templates/admin/import_export/import.html
msgid "Non field specific"
msgstr "没有指定的字段"

#: templates/admin/import_export/import.html
msgid "Preview"
msgstr "预览"

#: templates/admin/import_export/import.html
msgid "New"
msgstr "新增"

#: templates/admin/import_export/import.html
msgid "Skipped"
msgstr "忽略"

#: templates/admin/import_export/import.html
msgid "Delete"
msgstr "删除"

#: templates/admin/import_export/import.html
msgid "Update"
msgstr "更新"

#: templates/admin/import_export/resource_fields_list.html
msgid "This importer will import the following fields: "
msgstr "此次将导入以下字段："

#: widgets.py
msgid "Value could not be parsed."
msgstr ""

#: widgets.py
msgid "use_natural_foreign_keys and key_is_id cannot both be True"
msgstr ""

#~ msgid "You must select an export format."
#~ msgstr "您必须选择一个导出格式。"
