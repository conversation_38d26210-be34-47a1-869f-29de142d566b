-- =====================================================
-- WORKFLO-FRONT POSTGRESQL DATABASE SCHEMA
-- =====================================================
-- Comprehensive database schema for the workflo-front employee management system
--
-- SYSTEM FEATURES SCOPE:
-- 1. Employee Data Management & ATS (Applicant Tracking System)
-- 2. Time & Attendance Tracking with Suprema/BioStar Integration
-- 3. Payroll & Compensation Management (Kenyan Tax System)
-- 4. Learning & Development (Training, Certifications, Skills)
-- 5. Performance Management (Reviews, Goals, 360-degree Feedback)
-- 6. Compliance & Reporting (Labor Law, Tax Compliance, Analytics)
-- 7. Workflow Automation (Custom Workflows, Approvals, Notifications)
-- 8. Management (Company Settings, Billing, Module Configuration)
-- 9. Employee Engagement & Wellness (Surveys, Recognition, EAP, Events)
--
-- TECHNOLOGY STACK:
-- - Backend: Python Django
-- - Frontend: React
-- - Database: PostgreSQL
-- - Currency: KSH (Kenyan Shilling)
-- - Timezone: Africa/Nairobi

-- =====================================================
-- 1. AUTHENTICATION & USER MANAGEMENT
-- =====================================================

-- Users table (Core authentication)
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    employee_id VARCHAR(50) UNIQUE NOT NULL,
    phone_number VARCHAR(20),
    profile_picture TEXT,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'hr', 'supervisor', 'accountant', 'employee')),
    is_active BOOLEAN DEFAULT TRUE,
    is_deleted BOOLEAN DEFAULT FALSE,
    date_joined TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id),
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by INTEGER REFERENCES users(id)
);

-- User sessions for token management
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    access_token TEXT NOT NULL,
    refresh_token TEXT NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT
);

-- Password reset tokens
CREATE TABLE password_reset_tokens (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 2. ORGANIZATIONAL STRUCTURE
-- =====================================================

-- Departments
CREATE TABLE departments (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    supervisor_id INTEGER REFERENCES users(id),
    parent_department_id INTEGER REFERENCES departments(id),
    budget DECIMAL(15,2),
    location VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id)
);

-- =====================================================
-- 3. EMPLOYEE INFORMATION
-- =====================================================

-- Employee profiles (extends users table) - Core employment information only
CREATE TABLE employee_profiles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    department_id INTEGER REFERENCES departments(id),
    job_title VARCHAR(100) NOT NULL,
    hire_date DATE NOT NULL,
    employment_type VARCHAR(20) NOT NULL CHECK (employment_type IN ('full_time', 'part_time', 'contract', 'intern')),
    work_location VARCHAR(20) NOT NULL CHECK (work_location IN ('office', 'remote', 'hybrid')),
    supervisor_id INTEGER REFERENCES users(id),

    -- Government IDs (Kenya specific)
    nssf_number VARCHAR(20),
    nhif_number VARCHAR(20),
    kra_pin VARCHAR(20),
    national_id VARCHAR(20),

    -- Personal Information
    date_of_birth DATE,
    gender VARCHAR(30) CHECK (gender IN ('male', 'female', 'other', 'prefer_not_to_say')),
    marital_status VARCHAR(20) CHECK (marital_status IN ('single', 'married', 'divorced', 'widowed')),
    nationality VARCHAR(50),

    -- Contact Information
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Kenya',

    -- Employment Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'terminated', 'on_leave')),
    termination_date DATE,
    termination_reason TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id)
);

-- Salary profiles (Financial Information)
CREATE TABLE salary_profiles (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    basic_salary DECIMAL(15,2) NOT NULL,
    hourly_rate DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'KSH',
    pay_frequency VARCHAR(20) DEFAULT 'monthly' CHECK (pay_frequency IN ('monthly', 'bi_weekly', 'weekly')),

    -- Salary Structure
    allowances DECIMAL(15,2) DEFAULT 0,
    overtime_rate DECIMAL(10,2),
    commission_rate DECIMAL(5,2),

    -- Tax Information
    tax_exemption_amount DECIMAL(15,2) DEFAULT 0,
    tax_relief_amount DECIMAL(15,2) DEFAULT 0,

    -- Effective Dates
    effective_from DATE NOT NULL,
    effective_to DATE,

    -- Status
    is_active BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id)
);

-- Bank profiles (Banking Information)
CREATE TABLE bank_profiles (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    bank_name VARCHAR(100) NOT NULL,
    bank_code VARCHAR(20),
    branch_name VARCHAR(100),
    branch_code VARCHAR(20),
    account_number VARCHAR(50) NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    account_type VARCHAR(20) CHECK (account_type IN ('savings', 'current', 'fixed_deposit')),
    swift_code VARCHAR(20),

    -- Primary account flag
    is_primary BOOLEAN DEFAULT FALSE,

    -- Status
    is_active BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id)
);

-- Emergency contacts
CREATE TABLE emergency_contacts (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    contact_name VARCHAR(100) NOT NULL,
    relationship VARCHAR(50) NOT NULL,
    phone_number VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    address TEXT,

    -- Priority order (1 = primary contact)
    priority_order INTEGER DEFAULT 1,

    -- Status
    is_active BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id)
);

-- =====================================================
-- 4. ATTENDANCE & TIME TRACKING
-- =====================================================

-- Overtime types and policies
CREATE TABLE overtime_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    rate_multiplier DECIMAL(3,2) NOT NULL DEFAULT 1.5, -- e.g., 1.5 for time-and-a-half
    max_hours_per_day DECIMAL(4,2),
    max_hours_per_week DECIMAL(4,2),
    max_hours_per_month DECIMAL(5,2),
    requires_pre_approval BOOLEAN DEFAULT TRUE,
    auto_approve_threshold DECIMAL(4,2), -- Auto-approve if under this many hours
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id)
);

-- Overtime requests (pre-approval system)
CREATE TABLE overtime_requests (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL REFERENCES users(id),
    overtime_type_id INTEGER NOT NULL REFERENCES overtime_types(id),

    -- Request details
    request_date DATE NOT NULL,
    planned_start_time TIME NOT NULL,
    planned_end_time TIME NOT NULL,
    planned_hours DECIMAL(4,2) NOT NULL,
    reason TEXT NOT NULL,
    justification TEXT,
    project_code VARCHAR(50),
    department_approval_required BOOLEAN DEFAULT TRUE,

    -- Approval workflow
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled', 'completed')),
    requested_by INTEGER REFERENCES users(id),
    supervisor_approved_by INTEGER REFERENCES users(id),
    supervisor_approved_at TIMESTAMP WITH TIME ZONE,
    supervisor_comments TEXT,
    admin_approved_by INTEGER REFERENCES users(id),
    admin_approved_at TIMESTAMP WITH TIME ZONE,
    admin_comments TEXT,

    -- Final approval
    final_approved_by INTEGER REFERENCES users(id),
    final_approved_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,

    -- Completion tracking
    actual_start_time TIMESTAMP WITH TIME ZONE,
    actual_end_time TIMESTAMP WITH TIME ZONE,
    actual_hours DECIMAL(4,2),
    completion_notes TEXT,
    completed_by INTEGER REFERENCES users(id),
    completed_at TIMESTAMP WITH TIME ZONE,

    -- Audit trail
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id)
);

-- Overtime records (actual overtime worked)
CREATE TABLE overtime_records (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL REFERENCES users(id),
    overtime_request_id INTEGER REFERENCES overtime_requests(id),
    overtime_type_id INTEGER NOT NULL REFERENCES overtime_types(id),
    attendance_record_id INTEGER REFERENCES attendance_records(id),

    -- Overtime details
    overtime_date DATE NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    total_hours DECIMAL(4,2) NOT NULL,
    rate_multiplier DECIMAL(3,2) NOT NULL,

    -- Calculation details
    regular_hourly_rate DECIMAL(10,2),
    overtime_hourly_rate DECIMAL(10,2),
    total_amount DECIMAL(12,2),
    currency VARCHAR(3) DEFAULT 'KSH',

    -- Classification
    overtime_category VARCHAR(30) CHECK (overtime_category IN ('weekday', 'weekend', 'holiday', 'emergency', 'project_deadline')),
    is_emergency BOOLEAN DEFAULT FALSE,
    is_pre_approved BOOLEAN DEFAULT FALSE,

    -- Approval status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'paid')),
    approved_by INTEGER REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    approval_comments TEXT,

    -- Payroll integration
    pay_cycle_id INTEGER REFERENCES pay_cycles(id),
    included_in_payroll BOOLEAN DEFAULT FALSE,
    payroll_processed_at TIMESTAMP WITH TIME ZONE,

    -- Audit trail
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id),

    UNIQUE(employee_id, overtime_date, start_time)
);

-- Overtime approval workflows
CREATE TABLE overtime_approval_workflows (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    department_id INTEGER REFERENCES departments(id),

    -- Workflow steps
    requires_supervisor_approval BOOLEAN DEFAULT TRUE,
    requires_admin_approval BOOLEAN DEFAULT FALSE,
    requires_hr_approval BOOLEAN DEFAULT FALSE,

    -- Approval thresholds
    supervisor_approval_threshold DECIMAL(4,2), -- Hours threshold for supervisor approval
    admin_approval_threshold DECIMAL(4,2),      -- Hours threshold for admin approval
    auto_approval_threshold DECIMAL(4,2),       -- Auto-approve if under this threshold

    -- Time limits
    approval_deadline_hours INTEGER DEFAULT 24, -- Hours to approve/reject request
    advance_notice_hours INTEGER DEFAULT 4,     -- Minimum advance notice required

    -- Escalation rules
    escalation_enabled BOOLEAN DEFAULT TRUE,
    escalation_hours INTEGER DEFAULT 24,        -- Hours before escalation
    escalate_to_admin BOOLEAN DEFAULT TRUE,
    escalate_to_hr BOOLEAN DEFAULT FALSE,

    -- Notification settings
    notify_employee BOOLEAN DEFAULT TRUE,
    notify_supervisor BOOLEAN DEFAULT TRUE,
    notify_admin BOOLEAN DEFAULT FALSE,
    notify_hr BOOLEAN DEFAULT FALSE,

    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id)
);

-- Overtime budget and limits
CREATE TABLE overtime_budgets (
    id SERIAL PRIMARY KEY,
    department_id INTEGER REFERENCES departments(id),
    employee_id INTEGER REFERENCES users(id), -- NULL for department-wide budget

    -- Budget period
    budget_year INTEGER NOT NULL,
    budget_month INTEGER, -- NULL for annual budget

    -- Budget limits
    allocated_hours DECIMAL(6,2) NOT NULL,
    allocated_amount DECIMAL(15,2) NOT NULL,
    used_hours DECIMAL(6,2) DEFAULT 0,
    used_amount DECIMAL(15,2) DEFAULT 0,
    remaining_hours DECIMAL(6,2) GENERATED ALWAYS AS (allocated_hours - used_hours) STORED,
    remaining_amount DECIMAL(15,2) GENERATED ALWAYS AS (allocated_amount - used_amount) STORED,

    -- Alerts and thresholds
    warning_threshold_percentage INTEGER DEFAULT 80, -- Warn when 80% used
    block_threshold_percentage INTEGER DEFAULT 100,  -- Block when 100% used

    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    budget_exceeded BOOLEAN DEFAULT FALSE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id),

    UNIQUE(department_id, employee_id, budget_year, budget_month)
);

-- Attendance records
CREATE TABLE attendance_records (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL REFERENCES users(id),
    date DATE NOT NULL,
    check_in TIMESTAMP WITH TIME ZONE,
    check_out TIMESTAMP WITH TIME ZONE,
    break_start TIMESTAMP WITH TIME ZONE,
    break_end TIMESTAMP WITH TIME ZONE,
    total_hours DECIMAL(5,2),
    regular_hours DECIMAL(5,2),
    overtime_hours DECIMAL(5,2),
    break_time DECIMAL(5,2),
    status VARCHAR(20) DEFAULT 'present' CHECK (status IN ('present', 'absent', 'late', 'early_out', 'half_day')),
    notes TEXT,
    biostar_synced BOOLEAN DEFAULT FALSE,
    biostar_event_ids TEXT[], -- Array of BioStar event IDs
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(employee_id, date)
);

-- BioStar integration data
CREATE TABLE biostar_events (
    id SERIAL PRIMARY KEY,
    biostar_event_id VARCHAR(100) UNIQUE NOT NULL,
    employee_id INTEGER REFERENCES users(id),
    device_id VARCHAR(100),
    device_name VARCHAR(255),
    event_type VARCHAR(20) CHECK (event_type IN ('ENTRY', 'EXIT', 'DENIED')),
    event_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
    location VARCHAR(255),
    processed BOOLEAN DEFAULT FALSE,
    attendance_record_id INTEGER REFERENCES attendance_records(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- BioStar devices
CREATE TABLE biostar_devices (
    id SERIAL PRIMARY KEY,
    biostar_device_id VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    ip_address INET,
    port INTEGER,
    location VARCHAR(255),
    device_type VARCHAR(100),
    status VARCHAR(20) DEFAULT 'offline' CHECK (status IN ('online', 'offline', 'maintenance')),
    last_seen TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 5. LEAVE MANAGEMENT
-- =====================================================

-- Leave types
CREATE TABLE leave_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    max_days_per_year INTEGER,
    carry_forward_allowed BOOLEAN DEFAULT FALSE,
    max_carry_forward_days INTEGER,
    requires_approval BOOLEAN DEFAULT TRUE,
    is_paid BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Leave balances
CREATE TABLE leave_balances (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL REFERENCES users(id),
    leave_type_id INTEGER NOT NULL REFERENCES leave_types(id),
    year INTEGER NOT NULL,
    allocated_days DECIMAL(5,2) NOT NULL,
    used_days DECIMAL(5,2) DEFAULT 0,
    pending_days DECIMAL(5,2) DEFAULT 0,
    carried_forward_days DECIMAL(5,2) DEFAULT 0,
    remaining_days DECIMAL(5,2) GENERATED ALWAYS AS (allocated_days + carried_forward_days - used_days - pending_days) STORED,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(employee_id, leave_type_id, year)
);

-- Leave applications
CREATE TABLE leave_applications (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL REFERENCES users(id),
    leave_type_id INTEGER NOT NULL REFERENCES leave_types(id),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    days_requested DECIMAL(5,2) NOT NULL,
    reason TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')),
    approved_by INTEGER REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    reviewer_comments TEXT,
    attachment_url TEXT,
    applied_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Company holidays
CREATE TABLE company_holidays (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    date DATE NOT NULL,
    description TEXT,
    is_recurring BOOLEAN DEFAULT FALSE,
    applies_to_all BOOLEAN DEFAULT TRUE,
    department_ids INTEGER[], -- Array of department IDs if not applies_to_all
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- =====================================================
-- 6. PAYROLL SYSTEM
-- =====================================================

-- Pay cycles
CREATE TABLE pay_cycles (
    id SERIAL PRIMARY KEY,
    pay_period VARCHAR(50) NOT NULL, -- e.g., "January 2024", "Q1 2024"
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    pay_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'completed', 'cancelled')),
    total_employees INTEGER DEFAULT 0,
    total_gross_amount DECIMAL(15,2) DEFAULT 0,
    total_net_amount DECIMAL(15,2) DEFAULT 0,
    total_deductions DECIMAL(15,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    processed_by INTEGER REFERENCES users(id),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Payroll records
CREATE TABLE payroll_records (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL REFERENCES users(id),
    pay_cycle_id INTEGER NOT NULL REFERENCES pay_cycles(id),

    -- Salary Components
    basic_salary DECIMAL(15,2) NOT NULL,
    allowances DECIMAL(15,2) DEFAULT 0,
    overtime_amount DECIMAL(15,2) DEFAULT 0,
    bonuses DECIMAL(15,2) DEFAULT 0,
    gross_salary DECIMAL(15,2) NOT NULL,

    -- Deductions
    tax_deduction DECIMAL(15,2) DEFAULT 0,
    nssf_deduction DECIMAL(15,2) DEFAULT 0,
    nhif_deduction DECIMAL(15,2) DEFAULT 0,
    housing_levy DECIMAL(15,2) DEFAULT 0,
    loan_deductions DECIMAL(15,2) DEFAULT 0,
    other_deductions DECIMAL(15,2) DEFAULT 0,
    total_deductions DECIMAL(15,2) NOT NULL,

    -- Final Amount
    net_salary DECIMAL(15,2) NOT NULL,

    -- Additional Information
    currency VARCHAR(3) DEFAULT 'KSH',
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
    payment_method VARCHAR(20) DEFAULT 'bank_transfer',
    payment_reference VARCHAR(100),
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'cancelled')),
    payment_date TIMESTAMP WITH TIME ZONE,

    -- Calculation Details
    working_days INTEGER,
    days_worked INTEGER,
    overtime_hours DECIMAL(5,2) DEFAULT 0,
    leave_days_deducted DECIMAL(5,2) DEFAULT 0,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(employee_id, pay_cycle_id)
);

-- Payroll adjustments (bonuses, deductions, refunds for specific pay cycles)
CREATE TABLE payroll_adjustments (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL REFERENCES users(id),
    pay_cycle_id INTEGER REFERENCES pay_cycles(id),
    adjustment_type VARCHAR(20) NOT NULL CHECK (adjustment_type IN ('bonus', 'deduction', 'refund', 'allowance')),
    category VARCHAR(50), -- e.g., 'performance_bonus', 'transport_allowance', 'loan_repayment'
    amount DECIMAL(15,2) NOT NULL,
    description TEXT,
    is_recurring BOOLEAN DEFAULT FALSE,
    effective_date DATE,
    end_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- Salary adjustments (salary changes and historical records)
-- This table maintains a complete history of all salary changes for reporting and audit purposes
-- Reports can be generated to show salary progression, adjustment trends, and approval workflows
CREATE TABLE salary_adjustments (
    id SERIAL PRIMARY KEY,
    salary_profile_id INTEGER NOT NULL REFERENCES salary_profiles(id) ON DELETE CASCADE,
    adjustment_type VARCHAR(30) NOT NULL CHECK (adjustment_type IN ('salary_increase', 'salary_decrease', 'promotion', 'demotion', 'role_change', 'annual_review', 'market_adjustment', 'performance_adjustment')),

    -- Previous values (for comparison and rollback)
    previous_basic_salary DECIMAL(15,2),
    previous_allowances DECIMAL(15,2),
    previous_hourly_rate DECIMAL(10,2),
    previous_overtime_rate DECIMAL(10,2),

    -- New values (what the salary was changed to)
    new_basic_salary DECIMAL(15,2),
    new_allowances DECIMAL(15,2),
    new_hourly_rate DECIMAL(10,2),
    new_overtime_rate DECIMAL(10,2),

    -- Adjustment details (for reporting and analysis)
    percentage_change DECIMAL(5,2), -- Calculated percentage change
    amount_change DECIMAL(15,2),    -- Absolute amount change
    reason TEXT NOT NULL,           -- Business reason for adjustment
    justification TEXT,             -- Detailed justification

    -- Approval workflow (for compliance and audit)
    requested_by INTEGER REFERENCES users(id),
    approved_by INTEGER REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')),

    -- Effective dates (for historical reporting)
    effective_from DATE NOT NULL,
    effective_to DATE,

    -- Audit trail
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    updated_by INTEGER REFERENCES users(id)
);

-- Employee benefits
CREATE TABLE employee_benefits (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL REFERENCES users(id),
    benefit_type VARCHAR(50) NOT NULL,
    benefit_name VARCHAR(100) NOT NULL,
    description TEXT,
    value_type VARCHAR(20) CHECK (value_type IN ('fixed_amount', 'percentage', 'days', 'coverage')),
    value DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'KSH',
    provider VARCHAR(100),
    start_date DATE NOT NULL,
    end_date DATE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'expired')),
    employee_contribution DECIMAL(15,2) DEFAULT 0,
    employer_contribution DECIMAL(15,2) DEFAULT 0,
    coverage_details TEXT,
    dependents_covered INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 7. PERFORMANCE MANAGEMENT
-- =====================================================

-- Performance review templates
CREATE TABLE performance_review_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    review_type VARCHAR(20) CHECK (review_type IN ('annual', 'quarterly', 'probation', 'project')),
    criteria JSONB, -- Flexible criteria structure
    rating_scale INTEGER DEFAULT 5,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- Performance reviews
CREATE TABLE performance_reviews (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL REFERENCES users(id),
    reviewer_id INTEGER NOT NULL REFERENCES users(id),
    template_id INTEGER REFERENCES performance_review_templates(id),
    review_period_start DATE NOT NULL,
    review_period_end DATE NOT NULL,
    review_type VARCHAR(20) CHECK (review_type IN ('annual', 'quarterly', 'probation', 'project')),

    -- Overall ratings
    overall_rating DECIMAL(3,2),
    technical_skills DECIMAL(3,2),
    communication DECIMAL(3,2),
    teamwork DECIMAL(3,2),
    leadership DECIMAL(3,2),
    problem_solving DECIMAL(3,2),
    time_management DECIMAL(3,2),

    -- Comments and feedback
    strengths TEXT,
    areas_for_improvement TEXT,
    reviewer_comments TEXT,
    employee_comments TEXT,
    goals_for_next_period TEXT,

    -- Status and workflow
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'pending_employee', 'pending_manager', 'completed', 'cancelled')),
    employee_acknowledged BOOLEAN DEFAULT FALSE,
    employee_acknowledged_at TIMESTAMP WITH TIME ZONE,

    -- Dates
    due_date DATE,
    completed_date DATE,
    next_review_date DATE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Performance goals
CREATE TABLE performance_goals (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL REFERENCES users(id),
    review_id INTEGER REFERENCES performance_reviews(id),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    target_date DATE,
    priority VARCHAR(10) CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    status VARCHAR(20) DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'cancelled', 'overdue')),
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    completion_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- =====================================================
-- 8. TRAINING & DEVELOPMENT
-- =====================================================

-- Training modules
CREATE TABLE training_modules (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    content TEXT,
    duration_hours DECIMAL(5,2),
    difficulty_level VARCHAR(20) CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    category VARCHAR(100),
    prerequisites TEXT,
    learning_objectives TEXT,
    instructor_name VARCHAR(100),
    instructor_email VARCHAR(255),
    materials_url TEXT,
    is_mandatory BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- Training venues
CREATE TABLE training_venues (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(255),
    address TEXT,
    capacity INTEGER,
    equipment TEXT[],
    facilities TEXT[],
    hourly_rate DECIMAL(10,2),
    contact_person VARCHAR(100),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(255),
    status VARCHAR(20) DEFAULT 'available' CHECK (status IN ('available', 'occupied', 'maintenance', 'unavailable')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Employee training assignments
CREATE TABLE employee_training_assignments (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL REFERENCES users(id),
    training_module_id INTEGER NOT NULL REFERENCES training_modules(id),
    venue_id INTEGER REFERENCES training_venues(id),
    assigned_by INTEGER REFERENCES users(id),
    assigned_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    start_date DATE,
    end_date DATE,
    due_date DATE,
    status VARCHAR(20) DEFAULT 'assigned' CHECK (status IN ('assigned', 'in_progress', 'completed', 'cancelled', 'overdue')),
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    completion_date TIMESTAMP WITH TIME ZONE,
    score DECIMAL(5,2),
    feedback TEXT,
    certificate_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(employee_id, training_module_id)
);

-- Training sessions (scheduled training events)
CREATE TABLE training_sessions (
    id SERIAL PRIMARY KEY,
    training_module_id INTEGER NOT NULL REFERENCES training_modules(id),
    venue_id INTEGER REFERENCES training_venues(id),
    instructor_id INTEGER REFERENCES users(id),
    title VARCHAR(200) NOT NULL,
    start_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
    end_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
    max_participants INTEGER,
    current_participants INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- Training session participants
CREATE TABLE training_session_participants (
    id SERIAL PRIMARY KEY,
    session_id INTEGER NOT NULL REFERENCES training_sessions(id),
    employee_id INTEGER NOT NULL REFERENCES users(id),
    attendance_status VARCHAR(20) DEFAULT 'registered' CHECK (attendance_status IN ('registered', 'attended', 'absent', 'cancelled')),
    completion_status VARCHAR(20) DEFAULT 'not_started' CHECK (completion_status IN ('not_started', 'in_progress', 'completed', 'failed')),
    score DECIMAL(5,2),
    feedback TEXT,
    certificate_issued BOOLEAN DEFAULT FALSE,
    registered_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(session_id, employee_id)
);

-- =====================================================
-- 9. RECRUITMENT & JOB MANAGEMENT
-- =====================================================

-- Job postings
CREATE TABLE job_postings (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    department_id INTEGER REFERENCES departments(id),
    description TEXT NOT NULL,
    requirements TEXT,
    responsibilities TEXT,
    qualifications TEXT,
    salary_min DECIMAL(15,2),
    salary_max DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'KSH',
    location VARCHAR(255),
    employment_type VARCHAR(20) CHECK (employment_type IN ('full_time', 'part_time', 'contract', 'internship')),
    experience_level VARCHAR(20) CHECK (experience_level IN ('entry', 'junior', 'mid', 'senior', 'executive')),
    remote_allowed BOOLEAN DEFAULT FALSE,
    travel_required BOOLEAN DEFAULT FALSE,
    posted_date DATE DEFAULT CURRENT_DATE,
    closing_date DATE,
    application_deadline DATE,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'closed', 'filled')),
    positions_available INTEGER DEFAULT 1,
    positions_filled INTEGER DEFAULT 0,
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    hiring_manager_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- Job applications/candidates
CREATE TABLE job_applications (
    id SERIAL PRIMARY KEY,
    job_posting_id INTEGER NOT NULL REFERENCES job_postings(id),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    country VARCHAR(100),
    resume_url TEXT,
    cover_letter TEXT,
    portfolio_url TEXT,
    linkedin_profile TEXT,
    expected_salary DECIMAL(15,2),
    available_start_date DATE,
    visa_status VARCHAR(50),
    referral_source VARCHAR(100),
    status VARCHAR(20) DEFAULT 'applied' CHECK (status IN ('applied', 'screening', 'phone_interview', 'technical_interview', 'final_interview', 'reference_check', 'offer_made', 'offer_accepted', 'offer_declined', 'rejected', 'withdrawn')),
    stage VARCHAR(50),
    rating DECIMAL(3,2),
    notes TEXT,
    applied_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    assigned_recruiter_id INTEGER REFERENCES users(id)
);

-- Interview schedules
CREATE TABLE interview_schedules (
    id SERIAL PRIMARY KEY,
    application_id INTEGER NOT NULL REFERENCES job_applications(id),
    interview_type VARCHAR(50) NOT NULL, -- 'phone', 'video', 'in_person', 'technical', 'panel'
    interviewer_ids INTEGER[], -- Array of user IDs
    scheduled_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    location VARCHAR(255),
    meeting_link TEXT,
    agenda TEXT,
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'rescheduled', 'no_show')),
    feedback TEXT,
    rating DECIMAL(3,2),
    recommendation VARCHAR(20) CHECK (recommendation IN ('strong_hire', 'hire', 'no_hire', 'strong_no_hire')),
    next_steps TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- Candidate evaluation forms
CREATE TABLE candidate_evaluations (
    id SERIAL PRIMARY KEY,
    application_id INTEGER NOT NULL REFERENCES job_applications(id),
    interview_id INTEGER REFERENCES interview_schedules(id),
    evaluator_id INTEGER NOT NULL REFERENCES users(id),
    evaluation_criteria JSONB, -- Flexible criteria and scores
    overall_rating DECIMAL(3,2),
    technical_skills DECIMAL(3,2),
    communication_skills DECIMAL(3,2),
    cultural_fit DECIMAL(3,2),
    experience_relevance DECIMAL(3,2),
    strengths TEXT,
    concerns TEXT,
    recommendation VARCHAR(20) CHECK (recommendation IN ('strong_hire', 'hire', 'no_hire', 'strong_no_hire')),
    comments TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 10. DOCUMENT MANAGEMENT
-- =====================================================

-- Document categories
CREATE TABLE document_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    is_employee_accessible BOOLEAN DEFAULT TRUE,
    is_mandatory BOOLEAN DEFAULT FALSE,
    retention_period_months INTEGER,
    parent_category_id INTEGER REFERENCES document_categories(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Employee documents
CREATE TABLE employee_documents (
    id SERIAL PRIMARY KEY,
    employee_id INTEGER NOT NULL REFERENCES users(id),
    category_id INTEGER REFERENCES document_categories(id),
    document_name VARCHAR(255) NOT NULL,
    document_type VARCHAR(100), -- 'contract', 'id_copy', 'certificate', 'policy_acknowledgment'
    file_path TEXT NOT NULL,
    file_size BIGINT,
    file_type VARCHAR(50),
    description TEXT,
    is_confidential BOOLEAN DEFAULT FALSE,
    expiry_date DATE,
    version INTEGER DEFAULT 1,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'archived', 'expired', 'pending_review')),
    uploaded_by INTEGER REFERENCES users(id),
    approved_by INTEGER REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Company documents (policies, handbooks, etc.)
CREATE TABLE company_documents (
    id SERIAL PRIMARY KEY,
    category_id INTEGER REFERENCES document_categories(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    file_type VARCHAR(50),
    version VARCHAR(20) DEFAULT '1.0',
    is_public BOOLEAN DEFAULT FALSE,
    requires_acknowledgment BOOLEAN DEFAULT FALSE,
    effective_date DATE,
    expiry_date DATE,
    department_ids INTEGER[], -- Array of department IDs if not company-wide
    role_access VARCHAR(20)[] DEFAULT ARRAY['all'], -- Array of roles that can access
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('draft', 'active', 'archived', 'expired')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    approved_by INTEGER REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE
);

-- Document acknowledgments (for policies, handbooks)
CREATE TABLE document_acknowledgments (
    id SERIAL PRIMARY KEY,
    document_id INTEGER NOT NULL REFERENCES company_documents(id),
    employee_id INTEGER NOT NULL REFERENCES users(id),
    acknowledged_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    digital_signature TEXT,
    UNIQUE(document_id, employee_id)
);

-- =====================================================
-- 11. NOTIFICATIONS & COMMUNICATION
-- =====================================================

-- Notification templates
CREATE TABLE notification_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    event_type VARCHAR(100) NOT NULL, -- 'leave_request', 'payroll_generated', 'performance_review_due'
    subject_template TEXT NOT NULL,
    body_template TEXT NOT NULL,
    notification_channels VARCHAR(20)[] DEFAULT ARRAY['in_app'], -- 'in_app', 'email', 'sms', 'push'
    recipient_roles VARCHAR(20)[], -- Array of roles to notify
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Notifications
CREATE TABLE notifications (
    id SERIAL PRIMARY KEY,
    recipient_id INTEGER NOT NULL REFERENCES users(id),
    sender_id INTEGER REFERENCES users(id),
    template_id INTEGER REFERENCES notification_templates(id),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    notification_type VARCHAR(50) NOT NULL, -- 'info', 'warning', 'error', 'success'
    category VARCHAR(50), -- 'leave', 'payroll', 'performance', 'training', 'system'
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    channels VARCHAR(20)[] DEFAULT ARRAY['in_app'],
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP WITH TIME ZONE,
    action_url TEXT,
    action_label VARCHAR(100),
    metadata JSONB, -- Additional data for the notification
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Email logs
CREATE TABLE email_logs (
    id SERIAL PRIMARY KEY,
    notification_id INTEGER REFERENCES notifications(id),
    recipient_email VARCHAR(255) NOT NULL,
    sender_email VARCHAR(255),
    subject VARCHAR(255) NOT NULL,
    body TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'bounced')),
    provider_message_id VARCHAR(255),
    error_message TEXT,
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 12. AUDIT LOGS & SYSTEM TRACKING
-- =====================================================

-- Audit logs for tracking all system changes
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    action VARCHAR(50) NOT NULL, -- 'CREATE', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT'
    table_name VARCHAR(100) NOT NULL,
    record_id INTEGER,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- System activity logs
CREATE TABLE activity_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    activity_type VARCHAR(50) NOT NULL, -- 'page_view', 'file_download', 'report_generated', 'export'
    activity_description TEXT NOT NULL,
    module VARCHAR(50), -- 'payroll', 'attendance', 'leave', 'performance', 'training'
    metadata JSONB,
    ip_address INET,
    user_agent TEXT,
    duration_seconds INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- System settings
CREATE TABLE system_settings (
    id SERIAL PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(20) DEFAULT 'string' CHECK (setting_type IN ('string', 'integer', 'boolean', 'json', 'decimal')),
    description TEXT,
    category VARCHAR(50), -- 'payroll', 'attendance', 'leave', 'general', 'security'
    is_public BOOLEAN DEFAULT FALSE, -- Whether setting is visible to non-admin users
    is_editable BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER REFERENCES users(id)
);

-- Company information
CREATE TABLE company_info (
    id SERIAL PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL,
    company_logo TEXT,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Kenya',
    phone_number VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    tax_id VARCHAR(50),
    registration_number VARCHAR(50),
    industry VARCHAR(100),
    company_size VARCHAR(20) CHECK (company_size IN ('1-10', '11-50', '51-200', '201-500', '501-1000', '1000+')),
    founded_year INTEGER,
    description TEXT,
    mission_statement TEXT,
    vision_statement TEXT,
    values TEXT,
    timezone VARCHAR(50) DEFAULT 'Africa/Nairobi',
    currency VARCHAR(3) DEFAULT 'KSH',
    fiscal_year_start_month INTEGER DEFAULT 1,
    working_days_per_week INTEGER DEFAULT 5,
    working_hours_per_day DECIMAL(3,1) DEFAULT 8.0,
    overtime_threshold_hours DECIMAL(3,1) DEFAULT 8.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER REFERENCES users(id)
);

-- =====================================================
-- 13. EMPLOYEE ENGAGEMENT & WELLNESS
-- =====================================================

-- Employee surveys and feedback
CREATE TABLE employee_surveys (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    survey_type VARCHAR(50) CHECK (survey_type IN ('pulse', 'engagement', 'satisfaction', 'exit', 'feedback', 'culture')),
    questions JSONB NOT NULL, -- Array of questions with types and options
    target_audience VARCHAR(20) DEFAULT 'all' CHECK (target_audience IN ('all', 'department', 'role', 'custom')),
    department_ids INTEGER[], -- Array of department IDs if target_audience is 'department'
    role_filters VARCHAR(20)[], -- Array of roles if target_audience is 'role'
    employee_ids INTEGER[], -- Array of employee IDs if target_audience is 'custom'
    is_anonymous BOOLEAN DEFAULT TRUE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'completed', 'cancelled')),
    response_count INTEGER DEFAULT 0,
    target_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- Survey responses
CREATE TABLE survey_responses (
    id SERIAL PRIMARY KEY,
    survey_id INTEGER NOT NULL REFERENCES employee_surveys(id) ON DELETE CASCADE,
    employee_id INTEGER REFERENCES users(id), -- NULL if anonymous
    responses JSONB NOT NULL, -- Question ID -> Answer mapping
    completion_time_seconds INTEGER,
    ip_address INET,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(survey_id, employee_id) -- Prevent duplicate responses unless anonymous
);

-- Recognition and rewards system
CREATE TABLE recognition_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    points_value INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Employee recognition records
CREATE TABLE employee_recognitions (
    id SERIAL PRIMARY KEY,
    recipient_id INTEGER NOT NULL REFERENCES users(id),
    nominator_id INTEGER REFERENCES users(id), -- NULL for system-generated recognitions
    category_id INTEGER REFERENCES recognition_categories(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    recognition_type VARCHAR(50) CHECK (recognition_type IN ('peer_to_peer', 'manager_to_employee', 'milestone', 'achievement', 'system')),
    points_awarded INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT TRUE,
    milestone_type VARCHAR(50), -- 'work_anniversary', 'birthday', 'project_completion', 'goal_achievement'
    achievement_date DATE DEFAULT CURRENT_DATE,
    approved_by INTEGER REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'approved' CHECK (status IN ('pending', 'approved', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Employee assistance programs (EAP)
CREATE TABLE eap_resources (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100), -- 'mental_health', 'financial_wellness', 'legal_assistance', 'work_life_balance'
    resource_type VARCHAR(50) CHECK (resource_type IN ('article', 'video', 'webinar', 'contact', 'external_link', 'document')),
    content_url TEXT,
    contact_info TEXT,
    is_confidential BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    access_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- EAP resource access logs
CREATE TABLE eap_access_logs (
    id SERIAL PRIMARY KEY,
    resource_id INTEGER NOT NULL REFERENCES eap_resources(id),
    employee_id INTEGER REFERENCES users(id), -- NULL for anonymous access
    access_type VARCHAR(20) CHECK (access_type IN ('view', 'download', 'contact')),
    ip_address INET,
    user_agent TEXT,
    accessed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Company events and culture activities
CREATE TABLE company_events (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    event_type VARCHAR(50) CHECK (event_type IN ('meeting', 'training', 'social', 'team_building', 'celebration', 'announcement', 'wellness')),
    start_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
    end_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
    location VARCHAR(255),
    is_virtual BOOLEAN DEFAULT FALSE,
    meeting_link TEXT,
    max_participants INTEGER,
    current_participants INTEGER DEFAULT 0,
    is_mandatory BOOLEAN DEFAULT FALSE,
    department_ids INTEGER[], -- Array of department IDs if not company-wide
    role_filters VARCHAR(20)[], -- Array of roles if role-specific
    organizer_id INTEGER REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('draft', 'scheduled', 'in_progress', 'completed', 'cancelled')),
    registration_required BOOLEAN DEFAULT FALSE,
    registration_deadline TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- Event participants/registrations
CREATE TABLE event_participants (
    id SERIAL PRIMARY KEY,
    event_id INTEGER NOT NULL REFERENCES company_events(id) ON DELETE CASCADE,
    employee_id INTEGER NOT NULL REFERENCES users(id),
    registration_status VARCHAR(20) DEFAULT 'registered' CHECK (registration_status IN ('registered', 'confirmed', 'attended', 'absent', 'cancelled')),
    attendance_status VARCHAR(20) DEFAULT 'pending' CHECK (attendance_status IN ('pending', 'present', 'absent', 'late')),
    feedback_rating DECIMAL(3,2),
    feedback_comments TEXT,
    registered_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(event_id, employee_id)
);

-- Wellness programs and challenges
CREATE TABLE wellness_programs (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    program_type VARCHAR(50) CHECK (program_type IN ('fitness_challenge', 'mental_health', 'nutrition', 'step_counter', 'meditation', 'health_screening')),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    goal_type VARCHAR(50), -- 'steps', 'hours', 'points', 'completion'
    goal_target INTEGER,
    reward_points INTEGER DEFAULT 0,
    reward_description TEXT,
    is_team_based BOOLEAN DEFAULT FALSE,
    max_participants INTEGER,
    current_participants INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('draft', 'active', 'completed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- Wellness program participants
CREATE TABLE wellness_participants (
    id SERIAL PRIMARY KEY,
    program_id INTEGER NOT NULL REFERENCES wellness_programs(id) ON DELETE CASCADE,
    employee_id INTEGER NOT NULL REFERENCES users(id),
    team_name VARCHAR(100),
    current_progress INTEGER DEFAULT 0,
    goal_achieved BOOLEAN DEFAULT FALSE,
    points_earned INTEGER DEFAULT 0,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(program_id, employee_id)
);

-- Wellness activity logs
CREATE TABLE wellness_activity_logs (
    id SERIAL PRIMARY KEY,
    participant_id INTEGER NOT NULL REFERENCES wellness_participants(id) ON DELETE CASCADE,
    activity_date DATE NOT NULL,
    activity_value INTEGER NOT NULL, -- steps, hours, points, etc.
    notes TEXT,
    data_source VARCHAR(50) DEFAULT 'manual', -- 'manual', 'fitness_app', 'device_sync'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 14. WORKFLOW AUTOMATION & APPROVALS
-- =====================================================

-- Workflow templates
CREATE TABLE workflow_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    workflow_type VARCHAR(50) NOT NULL, -- 'leave_approval', 'expense_approval', 'document_approval', 'custom'
    trigger_event VARCHAR(100) NOT NULL, -- 'leave_request_submitted', 'expense_submitted', etc.
    steps JSONB NOT NULL, -- Array of workflow steps with conditions and actions
    is_active BOOLEAN DEFAULT TRUE,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- Workflow instances (active workflows)
CREATE TABLE workflow_instances (
    id SERIAL PRIMARY KEY,
    template_id INTEGER NOT NULL REFERENCES workflow_templates(id),
    entity_type VARCHAR(50) NOT NULL, -- 'leave_application', 'expense_claim', etc.
    entity_id INTEGER NOT NULL,
    initiated_by INTEGER REFERENCES users(id),
    current_step INTEGER DEFAULT 1,
    status VARCHAR(20) DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'cancelled', 'failed')),
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    due_date TIMESTAMP WITH TIME ZONE,
    metadata JSONB, -- Additional workflow data
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Workflow step executions
CREATE TABLE workflow_step_executions (
    id SERIAL PRIMARY KEY,
    workflow_instance_id INTEGER NOT NULL REFERENCES workflow_instances(id) ON DELETE CASCADE,
    step_number INTEGER NOT NULL,
    step_name VARCHAR(255) NOT NULL,
    assigned_to INTEGER REFERENCES users(id),
    action_type VARCHAR(50) NOT NULL, -- 'approval', 'review', 'notification', 'auto_action'
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'skipped', 'failed')),
    action_taken VARCHAR(50), -- 'approved', 'rejected', 'escalated', 'delegated'
    comments TEXT,
    due_date TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    escalated_to INTEGER REFERENCES users(id),
    escalated_at TIMESTAMP WITH TIME ZONE
);

-- Automated reminders and notifications
CREATE TABLE automated_reminders (
    id SERIAL PRIMARY KEY,
    reminder_type VARCHAR(50) NOT NULL, -- 'workflow_due', 'review_due', 'document_expiry', 'birthday', 'anniversary'
    entity_type VARCHAR(50), -- 'workflow_instance', 'performance_review', 'document', 'employee'
    entity_id INTEGER,
    recipient_id INTEGER REFERENCES users(id),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    reminder_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
    repeat_interval VARCHAR(20), -- 'daily', 'weekly', 'monthly', 'yearly'
    repeat_count INTEGER DEFAULT 1,
    sent_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'cancelled', 'completed')),
    last_sent_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 15. COMPANY MANAGEMENT & BILLING
-- =====================================================

-- Company subscription plans
CREATE TABLE subscription_plans (
    id SERIAL PRIMARY KEY,
    plan_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    max_employees INTEGER,
    features JSONB, -- Array of included features
    price_per_employee DECIMAL(10,2),
    price_per_month DECIMAL(10,2),
    billing_cycle VARCHAR(20) CHECK (billing_cycle IN ('monthly', 'quarterly', 'yearly')),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Company subscriptions
CREATE TABLE company_subscriptions (
    id SERIAL PRIMARY KEY,
    plan_id INTEGER NOT NULL REFERENCES subscription_plans(id),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    employee_count INTEGER NOT NULL,
    monthly_cost DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('trial', 'active', 'suspended', 'cancelled', 'expired')),
    auto_renew BOOLEAN DEFAULT TRUE,
    payment_method VARCHAR(50),
    billing_contact_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Billing invoices
CREATE TABLE billing_invoices (
    id SERIAL PRIMARY KEY,
    subscription_id INTEGER NOT NULL REFERENCES company_subscriptions(id),
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    billing_period_start DATE NOT NULL,
    billing_period_end DATE NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'KSH',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue', 'cancelled')),
    due_date DATE NOT NULL,
    paid_date DATE,
    payment_reference VARCHAR(100),
    payment_method VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Module settings and configurations
CREATE TABLE module_settings (
    id SERIAL PRIMARY KEY,
    module_name VARCHAR(50) NOT NULL, -- 'payroll', 'attendance', 'leave', 'performance', 'training', 'recruitment'
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(20) DEFAULT 'string' CHECK (setting_type IN ('string', 'integer', 'boolean', 'json', 'decimal')),
    description TEXT,
    is_required BOOLEAN DEFAULT FALSE,
    default_value TEXT,
    validation_rules JSONB, -- Validation rules for the setting
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER REFERENCES users(id),
    UNIQUE(module_name, setting_key)
);

-- Feature toggles
CREATE TABLE feature_toggles (
    id SERIAL PRIMARY KEY,
    feature_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    is_enabled BOOLEAN DEFAULT FALSE,
    rollout_percentage INTEGER DEFAULT 0 CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),
    target_roles VARCHAR(20)[], -- Array of roles that have access
    target_departments INTEGER[], -- Array of department IDs that have access
    environment VARCHAR(20) DEFAULT 'production' CHECK (environment IN ('development', 'staging', 'production')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER REFERENCES users(id)
);

-- =====================================================
-- 16. REPORTS & ANALYTICS
-- =====================================================

-- Saved reports
CREATE TABLE saved_reports (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    report_type VARCHAR(50) NOT NULL, -- 'payroll', 'attendance', 'leave', 'performance', 'custom'
    parameters JSONB, -- Report parameters and filters
    schedule_frequency VARCHAR(20), -- 'daily', 'weekly', 'monthly', 'quarterly', 'yearly'
    schedule_day INTEGER, -- Day of week/month for scheduled reports
    schedule_time TIME,
    recipients INTEGER[], -- Array of user IDs to receive the report
    is_active BOOLEAN DEFAULT TRUE,
    last_generated TIMESTAMP WITH TIME ZONE,
    next_generation TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

-- Report generation history
CREATE TABLE report_history (
    id SERIAL PRIMARY KEY,
    saved_report_id INTEGER REFERENCES saved_reports(id),
    generated_by INTEGER REFERENCES users(id),
    file_path TEXT,
    file_size BIGINT,
    generation_time_seconds INTEGER,
    parameters_used JSONB,
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'generating', 'completed', 'failed')),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 17. INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- User and authentication indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_employee_id ON users(employee_id);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);

-- Employee profile indexes
CREATE INDEX idx_employee_profiles_user_id ON employee_profiles(user_id);
CREATE INDEX idx_employee_profiles_department_id ON employee_profiles(department_id);
CREATE INDEX idx_employee_profiles_supervisor_id ON employee_profiles(supervisor_id);
CREATE INDEX idx_employee_profiles_status ON employee_profiles(status);
CREATE INDEX idx_employee_profiles_hire_date ON employee_profiles(hire_date);

-- Salary profile indexes
CREATE INDEX idx_salary_profiles_employee_id ON salary_profiles(employee_id);
CREATE INDEX idx_salary_profiles_effective_from ON salary_profiles(effective_from);
CREATE INDEX idx_salary_profiles_effective_to ON salary_profiles(effective_to);
CREATE INDEX idx_salary_profiles_is_active ON salary_profiles(is_active);

-- Bank profile indexes
CREATE INDEX idx_bank_profiles_employee_id ON bank_profiles(employee_id);
CREATE INDEX idx_bank_profiles_is_primary ON bank_profiles(is_primary);
CREATE INDEX idx_bank_profiles_is_active ON bank_profiles(is_active);

-- Emergency contact indexes
CREATE INDEX idx_emergency_contacts_employee_id ON emergency_contacts(employee_id);
CREATE INDEX idx_emergency_contacts_priority_order ON emergency_contacts(priority_order);

-- Salary adjustment indexes
CREATE INDEX idx_salary_adjustments_salary_profile_id ON salary_adjustments(salary_profile_id);
CREATE INDEX idx_salary_adjustments_adjustment_type ON salary_adjustments(adjustment_type);
CREATE INDEX idx_salary_adjustments_effective_from ON salary_adjustments(effective_from);
CREATE INDEX idx_salary_adjustments_status ON salary_adjustments(status);
CREATE INDEX idx_salary_adjustments_created_at ON salary_adjustments(created_at);

-- Payroll adjustment indexes (renamed from salary_adjustments)
CREATE INDEX idx_payroll_adjustments_employee_id ON payroll_adjustments(employee_id);
CREATE INDEX idx_payroll_adjustments_pay_cycle_id ON payroll_adjustments(pay_cycle_id);
CREATE INDEX idx_payroll_adjustments_adjustment_type ON payroll_adjustments(adjustment_type);

-- Overtime management indexes
CREATE INDEX idx_overtime_types_name ON overtime_types(name);
CREATE INDEX idx_overtime_types_is_active ON overtime_types(is_active);

CREATE INDEX idx_overtime_requests_employee_id ON overtime_requests(employee_id);
CREATE INDEX idx_overtime_requests_overtime_type_id ON overtime_requests(overtime_type_id);
CREATE INDEX idx_overtime_requests_request_date ON overtime_requests(request_date);
CREATE INDEX idx_overtime_requests_status ON overtime_requests(status);
CREATE INDEX idx_overtime_requests_supervisor_approved_by ON overtime_requests(supervisor_approved_by);
CREATE INDEX idx_overtime_requests_admin_approved_by ON overtime_requests(admin_approved_by);
CREATE INDEX idx_overtime_requests_created_at ON overtime_requests(created_at);

CREATE INDEX idx_overtime_records_employee_id ON overtime_records(employee_id);
CREATE INDEX idx_overtime_records_overtime_request_id ON overtime_records(overtime_request_id);
CREATE INDEX idx_overtime_records_overtime_type_id ON overtime_records(overtime_type_id);
CREATE INDEX idx_overtime_records_overtime_date ON overtime_records(overtime_date);
CREATE INDEX idx_overtime_records_status ON overtime_records(status);
CREATE INDEX idx_overtime_records_pay_cycle_id ON overtime_records(pay_cycle_id);
CREATE INDEX idx_overtime_records_approved_by ON overtime_records(approved_by);
CREATE INDEX idx_overtime_records_employee_date ON overtime_records(employee_id, overtime_date);

CREATE INDEX idx_overtime_approval_workflows_department_id ON overtime_approval_workflows(department_id);
CREATE INDEX idx_overtime_approval_workflows_is_active ON overtime_approval_workflows(is_active);

CREATE INDEX idx_overtime_budgets_department_id ON overtime_budgets(department_id);
CREATE INDEX idx_overtime_budgets_employee_id ON overtime_budgets(employee_id);
CREATE INDEX idx_overtime_budgets_budget_year ON overtime_budgets(budget_year);
CREATE INDEX idx_overtime_budgets_budget_month ON overtime_budgets(budget_month);
CREATE INDEX idx_overtime_budgets_is_active ON overtime_budgets(is_active);

-- Attendance indexes
CREATE INDEX idx_attendance_records_employee_id ON attendance_records(employee_id);
CREATE INDEX idx_attendance_records_date ON attendance_records(date);
CREATE INDEX idx_attendance_records_employee_date ON attendance_records(employee_id, date);
CREATE INDEX idx_biostar_events_employee_id ON biostar_events(employee_id);
CREATE INDEX idx_biostar_events_datetime ON biostar_events(event_datetime);

-- Leave management indexes
CREATE INDEX idx_leave_applications_employee_id ON leave_applications(employee_id);
CREATE INDEX idx_leave_applications_status ON leave_applications(status);
CREATE INDEX idx_leave_applications_start_date ON leave_applications(start_date);
CREATE INDEX idx_leave_balances_employee_id ON leave_balances(employee_id);
CREATE INDEX idx_leave_balances_year ON leave_balances(year);

-- Payroll indexes
CREATE INDEX idx_payroll_records_employee_id ON payroll_records(employee_id);
CREATE INDEX idx_payroll_records_pay_cycle_id ON payroll_records(pay_cycle_id);
CREATE INDEX idx_pay_cycles_start_date ON pay_cycles(start_date);
CREATE INDEX idx_pay_cycles_status ON pay_cycles(status);

-- Performance and training indexes
CREATE INDEX idx_performance_reviews_employee_id ON performance_reviews(employee_id);
CREATE INDEX idx_performance_reviews_reviewer_id ON performance_reviews(reviewer_id);
CREATE INDEX idx_performance_reviews_status ON performance_reviews(status);
CREATE INDEX idx_employee_training_assignments_employee_id ON employee_training_assignments(employee_id);
CREATE INDEX idx_employee_training_assignments_status ON employee_training_assignments(status);

-- Notification indexes
CREATE INDEX idx_notifications_recipient_id ON notifications(recipient_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notifications_category ON notifications(category);

-- Audit and activity log indexes
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_table_name ON audit_logs(table_name);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_activity_type ON activity_logs(activity_type);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);

-- Employee engagement and wellness indexes
CREATE INDEX idx_employee_surveys_status ON employee_surveys(status);
CREATE INDEX idx_employee_surveys_survey_type ON employee_surveys(survey_type);
CREATE INDEX idx_employee_surveys_start_date ON employee_surveys(start_date);
CREATE INDEX idx_survey_responses_survey_id ON survey_responses(survey_id);
CREATE INDEX idx_survey_responses_employee_id ON survey_responses(employee_id);
CREATE INDEX idx_employee_recognitions_recipient_id ON employee_recognitions(recipient_id);
CREATE INDEX idx_employee_recognitions_nominator_id ON employee_recognitions(nominator_id);
CREATE INDEX idx_employee_recognitions_achievement_date ON employee_recognitions(achievement_date);
CREATE INDEX idx_company_events_start_datetime ON company_events(start_datetime);
CREATE INDEX idx_company_events_event_type ON company_events(event_type);
CREATE INDEX idx_company_events_status ON company_events(status);
CREATE INDEX idx_event_participants_event_id ON event_participants(event_id);
CREATE INDEX idx_event_participants_employee_id ON event_participants(employee_id);
CREATE INDEX idx_wellness_programs_start_date ON wellness_programs(start_date);
CREATE INDEX idx_wellness_programs_program_type ON wellness_programs(program_type);
CREATE INDEX idx_wellness_participants_program_id ON wellness_participants(program_id);
CREATE INDEX idx_wellness_participants_employee_id ON wellness_participants(employee_id);

-- Workflow automation indexes
CREATE INDEX idx_workflow_instances_template_id ON workflow_instances(template_id);
CREATE INDEX idx_workflow_instances_entity_type ON workflow_instances(entity_type);
CREATE INDEX idx_workflow_instances_entity_id ON workflow_instances(entity_id);
CREATE INDEX idx_workflow_instances_status ON workflow_instances(status);
CREATE INDEX idx_workflow_step_executions_workflow_instance_id ON workflow_step_executions(workflow_instance_id);
CREATE INDEX idx_workflow_step_executions_assigned_to ON workflow_step_executions(assigned_to);
CREATE INDEX idx_workflow_step_executions_status ON workflow_step_executions(status);
CREATE INDEX idx_automated_reminders_reminder_datetime ON automated_reminders(reminder_datetime);
CREATE INDEX idx_automated_reminders_recipient_id ON automated_reminders(recipient_id);
CREATE INDEX idx_automated_reminders_status ON automated_reminders(status);

-- Company management and billing indexes
CREATE INDEX idx_company_subscriptions_plan_id ON company_subscriptions(plan_id);
CREATE INDEX idx_company_subscriptions_status ON company_subscriptions(status);
CREATE INDEX idx_company_subscriptions_end_date ON company_subscriptions(end_date);
CREATE INDEX idx_billing_invoices_subscription_id ON billing_invoices(subscription_id);
CREATE INDEX idx_billing_invoices_status ON billing_invoices(status);
CREATE INDEX idx_billing_invoices_due_date ON billing_invoices(due_date);
CREATE INDEX idx_module_settings_module_name ON module_settings(module_name);
CREATE INDEX idx_feature_toggles_feature_name ON feature_toggles(feature_name);
CREATE INDEX idx_feature_toggles_is_enabled ON feature_toggles(is_enabled);

-- =====================================================
-- 18. INITIAL DATA SETUP
-- =====================================================

-- Insert default leave types
INSERT INTO leave_types (name, description, max_days_per_year, is_paid) VALUES
('Annual Leave', 'Annual vacation leave', 21, TRUE),
('Sick Leave', 'Medical leave for illness', 14, TRUE),
('Maternity Leave', 'Maternity leave for new mothers', 90, TRUE),
('Paternity Leave', 'Paternity leave for new fathers', 14, TRUE),
('Compassionate Leave', 'Leave for family emergencies', 7, TRUE),
('Study Leave', 'Educational leave', 30, FALSE);

-- Insert default document categories
INSERT INTO document_categories (name, description, is_employee_accessible, is_mandatory) VALUES
('Employment Contracts', 'Employment agreements and contracts', TRUE, TRUE),
('Identification Documents', 'National ID, passport copies', TRUE, TRUE),
('Educational Certificates', 'Academic and professional certificates', TRUE, FALSE),
('Medical Records', 'Health certificates and medical reports', FALSE, FALSE),
('Company Policies', 'Company policies and procedures', TRUE, TRUE),
('Performance Reviews', 'Performance evaluation documents', TRUE, FALSE);

-- Insert default notification templates
INSERT INTO notification_templates (name, event_type, subject_template, body_template, notification_channels, recipient_roles) VALUES
('Leave Request Submitted', 'leave_request_submitted', 'Leave Request Submitted', 'A new leave request has been submitted by {{employee_name}} for {{leave_type}} from {{start_date}} to {{end_date}}.', ARRAY['in_app', 'email'], ARRAY['supervisor', 'hr']),
('Leave Request Approved', 'leave_request_approved', 'Leave Request Approved', 'Your leave request for {{leave_type}} from {{start_date}} to {{end_date}} has been approved.', ARRAY['in_app', 'email'], ARRAY['employee']),
('Payroll Generated', 'payroll_generated', 'Payroll Generated', 'Your payslip for {{pay_period}} is now available. Net salary: {{net_salary}}.', ARRAY['in_app', 'email'], ARRAY['employee']),
('Performance Review Due', 'performance_review_due', 'Performance Review Due', 'Your performance review is due on {{due_date}}. Please complete your self-assessment.', ARRAY['in_app', 'email'], ARRAY['employee']);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, category) VALUES
('company_name', 'WorkFlo Company', 'string', 'Company name', 'general'),
('default_currency', 'KSH', 'string', 'Default currency for the system', 'general'),
('working_hours_per_day', '8', 'decimal', 'Standard working hours per day', 'attendance'),
('overtime_threshold', '8', 'decimal', 'Hours threshold for overtime calculation', 'payroll'),
('leave_approval_required', 'true', 'boolean', 'Whether leave requests require approval', 'leave'),
('payroll_auto_calculation', 'true', 'boolean', 'Enable automatic payroll calculations', 'payroll'),
('biostar_integration_enabled', 'false', 'boolean', 'Enable BioStar biometric integration', 'attendance'),
('email_notifications_enabled', 'true', 'boolean', 'Enable email notifications', 'notifications');

-- Insert default recognition categories
INSERT INTO recognition_categories (name, description, icon, points_value) VALUES
('Employee of the Month', 'Outstanding performance recognition', 'trophy', 100),
('Team Player', 'Excellent collaboration and teamwork', 'users', 50),
('Innovation Award', 'Creative solutions and innovative thinking', 'lightbulb', 75),
('Customer Service Excellence', 'Exceptional customer service', 'heart', 60),
('Leadership Excellence', 'Outstanding leadership qualities', 'star', 80),
('Work Anniversary', 'Years of service milestone', 'calendar', 25);

-- Insert default subscription plans
INSERT INTO subscription_plans (plan_name, description, max_employees, features, price_per_employee, price_per_month, billing_cycle) VALUES
('Starter', 'Basic HR management for small teams', 25, '["employee_management", "attendance", "leave_management", "basic_payroll"]', 500.00, 10000.00, 'monthly'),
('Professional', 'Advanced HR features for growing companies', 100, '["employee_management", "attendance", "leave_management", "payroll", "performance", "training", "recruitment"]', 750.00, 25000.00, 'monthly'),
('Enterprise', 'Complete HR solution for large organizations', 1000, '["employee_management", "attendance", "leave_management", "payroll", "performance", "training", "recruitment", "wellness", "analytics", "api_access"]', 1000.00, 50000.00, 'monthly');

-- Insert default module settings
INSERT INTO module_settings (module_name, setting_key, setting_value, setting_type, description, is_required) VALUES
('payroll', 'tax_calculation_method', 'automatic', 'string', 'Method for calculating taxes', TRUE),
('payroll', 'nssf_rate', '6.0', 'decimal', 'NSSF contribution rate percentage', TRUE),
('payroll', 'nhif_rate', '2.75', 'decimal', 'NHIF contribution rate percentage', TRUE),
('payroll', 'housing_levy_rate', '1.5', 'decimal', 'Housing levy rate percentage', TRUE),
('attendance', 'grace_period_minutes', '15', 'integer', 'Grace period for late arrivals in minutes', FALSE),
('attendance', 'auto_checkout_hours', '12', 'integer', 'Automatic checkout after hours if not checked out', FALSE),
('leave', 'max_advance_days', '90', 'integer', 'Maximum days in advance for leave requests', FALSE),
('leave', 'min_notice_days', '3', 'integer', 'Minimum notice days for leave requests', FALSE),
('performance', 'review_frequency_months', '12', 'integer', 'Performance review frequency in months', FALSE),
('training', 'mandatory_completion_days', '30', 'integer', 'Days to complete mandatory training', FALSE),
('wellness', 'points_per_step', '0.01', 'decimal', 'Points awarded per step in wellness programs', FALSE),
('recruitment', 'application_retention_days', '365', 'integer', 'Days to retain job applications', FALSE);

-- Insert default feature toggles
INSERT INTO feature_toggles (feature_name, description, is_enabled, rollout_percentage) VALUES
('employee_self_service', 'Enable employee self-service portal', TRUE, 100),
('mobile_app_access', 'Enable mobile application access', TRUE, 100),
('biometric_integration', 'Enable biometric device integration', FALSE, 0),
('advanced_analytics', 'Enable advanced analytics and reporting', TRUE, 100),
('ai_recommendations', 'Enable AI-powered recommendations', FALSE, 0),
('multi_language_support', 'Enable multiple language support', FALSE, 0),
('api_access', 'Enable API access for integrations', TRUE, 50),
('wellness_programs', 'Enable employee wellness programs', TRUE, 100),
('recognition_system', 'Enable employee recognition system', TRUE, 100),
('workflow_automation', 'Enable workflow automation features', TRUE, 75),
('overtime_management', 'Enable overtime request and approval system', TRUE, 100);

-- Insert default overtime types
INSERT INTO overtime_types (name, description, rate_multiplier, max_hours_per_day, max_hours_per_week, max_hours_per_month, requires_pre_approval, auto_approve_threshold) VALUES
('Regular Overtime', 'Standard overtime during weekdays', 1.5, 4.0, 12.0, 40.0, TRUE, 2.0),
('Weekend Overtime', 'Overtime work during weekends', 2.0, 8.0, 16.0, 32.0, TRUE, 1.0),
('Holiday Overtime', 'Overtime work during public holidays', 2.5, 8.0, 8.0, 16.0, TRUE, 0.0),
('Emergency Overtime', 'Urgent overtime for emergencies', 1.5, 12.0, 20.0, 60.0, FALSE, 4.0),
('Project Overtime', 'Overtime for specific project deadlines', 1.5, 6.0, 15.0, 50.0, TRUE, 1.0);

-- Insert default overtime approval workflow
INSERT INTO overtime_approval_workflows (name, description, requires_supervisor_approval, requires_admin_approval, supervisor_approval_threshold, admin_approval_threshold, auto_approval_threshold, approval_deadline_hours, advance_notice_hours) VALUES
('Standard Approval Workflow', 'Default overtime approval process for all departments', TRUE, TRUE, 4.0, 8.0, 2.0, 24, 4),
('Emergency Approval Workflow', 'Fast-track approval for emergency overtime', TRUE, FALSE, 12.0, NULL, 4.0, 2, 0),
('Project Approval Workflow', 'Approval process for project-based overtime', TRUE, TRUE, 6.0, 12.0, 1.0, 48, 8);

-- =====================================================
-- END OF SCHEMA
-- =====================================================
