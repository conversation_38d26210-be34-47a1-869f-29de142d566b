'use client';

import React, { useState, useEffect } from 'react';
import { Users, Building, FileText, DollarSign, Calendar, TrendingUp } from 'lucide-react';
import { StatsCard } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { formatCurrency, formatDate } from '@/lib/utils';
import { mockApi } from '@/lib/mockApi';
import { SimpleCardSkeleton } from '@/components/ui/LoadingStates';
import ClientOnly from '@/components/ClientOnly';

// Dashboard stats interface
interface DashboardStats {
  total_employees: number;
  total_departments: number;
  pending_leaves: number;
  total_salary: number;
  recent_activities: Array<{
    id: number;
    type: string;
    description: string;
    user: string;
    timestamp: string;
  }>;
}

const mockActivities = [
  {
    id: 1,
    type: 'birthday',
    icon: '🎂',
    title: 'No Birthdays Today',
    color: 'text-blue-600',
  },
  {
    id: 2,
    type: 'sick',
    icon: '🤒',
    title: '<PERSON> is off sick today',
    color: 'text-yellow-600',
    avatar: '/api/placeholder/32/32',
  },
  {
    id: 3,
    type: 'parental',
    icon: '👶',
    title: '<PERSON> is on parenting leave today',
    color: 'text-green-600',
    avatar: '/api/placeholder/32/32',
  },
  {
    id: 4,
    type: 'leave',
    icon: '✈️',
    title: 'Danny Ward is away today',
    color: 'text-red-600',
    avatar: '/api/placeholder/32/32',
  },
  {
    id: 5,
    type: 'wfh',
    icon: '🏠',
    title: 'You are working from home today',
    color: 'text-purple-600',
    avatar: '/api/placeholder/32/32',
  },
];

const mockTeamLeads = [
  {
    id: 1,
    name: 'Maria Cotton',
    role: 'PHP Team Lead',
    avatar: '/api/placeholder/40/40',
    status: 'online',
  },
  {
    id: 2,
    name: 'Danny Ward',
    role: 'Design Team Lead',
    avatar: '/api/placeholder/40/40',
    status: 'away',
  },
  {
    id: 3,
    name: 'Linda Craver',
    role: 'iOS Team Lead',
    avatar: '/api/placeholder/40/40',
    status: 'online',
  },
  {
    id: 4,
    name: 'John Gibbs',
    role: 'Business Team Lead',
    avatar: '/api/placeholder/40/40',
    status: 'offline',
  },
];

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState<Date | null>(null);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  // Initialize time on client side to avoid hydration mismatch
  useEffect(() => {
    setCurrentTime(new Date());
  }, []);

  // Load dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        const dashboardStats = await mockApi.getDashboardStats();
        setStats(dashboardStats);
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  // Update time every minute
  useEffect(() => {
    if (!currentTime) return;

    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, [currentTime]);

  return (
    <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Welcome back, {user?.first_name || 'Admin'}!
              </h1>
              <ClientOnly fallback={<p className="text-gray-600 mt-1">Loading...</p>}>
                <p className="text-gray-600 mt-1">
                  {currentTime ? formatDate(currentTime.toISOString(), 'EEEE, MMMM dd, yyyy') : 'Loading...'}
                </p>
              </ClientOnly>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">Current Time</p>
              <ClientOnly fallback={<p className="text-lg font-semibold text-gray-900">--:--</p>}>
                <p className="text-lg font-semibold text-gray-900">
                  {currentTime ? formatDate(currentTime.toISOString(), 'HH:mm') : '--:--'}
                </p>
              </ClientOnly>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {loading ? (
            // Loading skeleton
            Array.from({ length: 4 }).map((_, index) => (
              <SimpleCardSkeleton key={index} />
            ))
          ) : stats ? (
            <>
              <StatsCard
                title="Employees"
                value={stats.total_employees.toLocaleString()}
                icon={<Users />}
                color="primary"
                trend={{ value: 5.2, isPositive: true }}
              />
              <StatsCard
                title="Departments"
                value={stats.total_departments}
                icon={<Building />}
                color="warning"
                trend={{ value: 2.1, isPositive: true }}
              />
              <StatsCard
                title="Pending Leaves"
                value={stats.pending_leaves}
                icon={<FileText />}
                color="danger"
                trend={{ value: 1.5, isPositive: false }}
              />
              <StatsCard
                title="Total Salary"
                value={formatCurrency(stats.total_salary)}
                icon={<DollarSign />}
                color="success"
                trend={{ value: 8.3, isPositive: true }}
              />
            </>
          ) : (
            // Error state
            <div className="col-span-4 text-center py-8">
              <p className="text-gray-500">Failed to load dashboard data</p>
            </div>
          )}
        </div>

        {/* Charts and Activities */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Today's Activities */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Today</h3>
                <Button variant="secondary" size="sm">
                  <TrendingUp className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {mockActivities.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-3">
                    <div className={`text-2xl ${activity.color}`}>
                      {activity.icon}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {activity.title}
                      </p>
                    </div>
                    {activity.avatar && (
                      <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Team Leads */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Team Leads</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {mockTeamLeads.map((lead) => (
                  <div key={lead.id} className="flex items-center space-x-3">
                    <div className="relative">
                      <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
                      <div
                        className={`absolute bottom-0 right-0 w-3 h-3 rounded-full border-2 border-white ${
                          lead.status === 'online'
                            ? 'bg-green-400'
                            : lead.status === 'away'
                            ? 'bg-yellow-400'
                            : 'bg-gray-400'
                        }`}
                      ></div>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {lead.name}
                      </p>
                      <p className="text-xs text-gray-500">{lead.role}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="primary" className="h-20 flex-col">
              <Users className="h-6 w-6 mb-2" />
              <span className="text-sm">Add Employee</span>
            </Button>
            <Button variant="secondary" className="h-20 flex-col">
              <FileText className="h-6 w-6 mb-2" />
              <span className="text-sm">Apply Leave</span>
            </Button>
            <Button variant="secondary" className="h-20 flex-col">
              <Calendar className="h-6 w-6 mb-2" />
              <span className="text-sm">Schedule Meeting</span>
            </Button>
            <Button variant="secondary" className="h-20 flex-col">
              <TrendingUp className="h-6 w-6 mb-2" />
              <span className="text-sm">View Reports</span>
            </Button>
          </div>
        </div>
      </div>
  );
};

export default DashboardPage;
