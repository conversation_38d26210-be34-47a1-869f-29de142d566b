'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/providers/AuthProvider';
import { formatDate } from '@/lib/utils';
import {
  Users,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Award,
  Bell,
  ChevronRight,
  UserCheck,
  CalendarCheck,
  ClipboardList,
  BarChart3,
  Activity,
  Target
} from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<any>;
  color: string;
  change?: string;
  trend?: 'up' | 'down' | 'neutral';
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon: Icon, color, change, trend }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl sm:text-3xl font-bold text-gray-900 mt-1">{value}</p>
          {change && (
            <div className={`flex items-center mt-2 text-sm ${
              trend === 'up' ? 'text-green-600' : trend === 'down' ? 'text-red-600' : 'text-gray-600'
            }`}>
              <TrendingUp className={`h-4 w-4 mr-1 ${trend === 'down' ? 'rotate-180' : ''}`} />
              {change}
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
      </div>
    </div>
  );
};

interface Card {
  children: React.ReactNode;
  className?: string;
}

const Card: React.FC<Card> = ({ children, className = '' }) => {
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {children}
    </div>
  );
};

const SupervisorDashboard: React.FC = () => {
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Mock data for supervisor dashboard
  const teamStats = {
    totalTeamMembers: 12,
    presentToday: 10,
    onLeave: 2,
    pendingApprovals: 5,
    overtimeRequests: 3,
    teamPerformance: 87,
    completedTasks: 45,
    upcomingDeadlines: 8
  };

  const recentActivities = [
    {
      id: 1,
      type: 'leave_request',
      message: 'John Doe submitted a leave request for Dec 25-27',
      time: '2 hours ago',
      icon: Calendar,
      color: 'text-blue-600'
    },
    {
      id: 2,
      type: 'overtime',
      message: 'Sarah Wilson applied for overtime this weekend',
      time: '4 hours ago',
      icon: Clock,
      color: 'text-orange-600'
    },
    {
      id: 3,
      type: 'performance',
      message: 'Team completed Q4 performance reviews',
      time: '1 day ago',
      icon: Award,
      color: 'text-green-600'
    },
    {
      id: 4,
      type: 'attendance',
      message: 'Mike Johnson checked in late today',
      time: '2 days ago',
      icon: UserCheck,
      color: 'text-red-600'
    }
  ];

  const pendingApprovals = [
    {
      id: 1,
      type: 'Leave Request',
      employee: 'John Doe',
      details: 'Annual Leave - Dec 25-27, 2024',
      priority: 'high',
      submitted: '2 hours ago'
    },
    {
      id: 2,
      type: 'Overtime',
      employee: 'Sarah Wilson',
      details: 'Weekend overtime - Dec 21-22',
      priority: 'medium',
      submitted: '4 hours ago'
    },
    {
      id: 3,
      type: 'Leave Request',
      employee: 'Mike Johnson',
      details: 'Sick Leave - Dec 20, 2024',
      priority: 'high',
      submitted: '1 day ago'
    }
  ];

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-4 sm:p-6 text-white">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex-1">
            <h1 className="text-xl sm:text-2xl font-bold leading-tight">
              Good {currentTime.getHours() < 12 ? 'Morning' : currentTime.getHours() < 18 ? 'Afternoon' : 'Evening'}, {user?.first_name}!
            </h1>
            <p className="text-orange-100 mt-1 text-sm sm:text-base">
              {formatDate(new Date().toISOString(), 'EEEE, MMMM dd, yyyy')}
            </p>
            <div className="flex flex-col sm:flex-row sm:items-center mt-3 sm:mt-2 space-y-2 sm:space-y-0 sm:space-x-4">
              <div className="flex items-center text-orange-100">
                <Users className="h-4 w-4 mr-2" />
                <span className="text-sm">Managing {teamStats.totalTeamMembers} team members</span>
              </div>
              <div className="flex items-center text-orange-100">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">{teamStats.pendingApprovals} pending approvals</span>
              </div>
            </div>
          </div>
          <div className="text-center sm:text-right">
            <div className="text-2xl sm:text-3xl font-bold">
              {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </div>
            <div className="text-orange-100 text-sm">
              Current Time
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <StatCard
          title="Team Members"
          value={teamStats.totalTeamMembers}
          icon={Users}
          color="bg-blue-500"
          change="+2 this month"
          trend="up"
        />
        <StatCard
          title="Present Today"
          value={teamStats.presentToday}
          icon={UserCheck}
          color="bg-green-500"
          change="83% attendance"
          trend="up"
        />
        <StatCard
          title="Pending Approvals"
          value={teamStats.pendingApprovals}
          icon={AlertCircle}
          color="bg-orange-500"
          change="2 urgent"
          trend="neutral"
        />
        <StatCard
          title="Team Performance"
          value={`${teamStats.teamPerformance}%`}
          icon={TrendingUp}
          color="bg-purple-500"
          change="+5% this week"
          trend="up"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        {/* Pending Approvals */}
        <Card>
          <div className="p-4 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <AlertCircle className="h-5 w-5 mr-2 text-orange-500" />
                Pending Approvals
              </h3>
              <span className="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                {pendingApprovals.length} pending
              </span>
            </div>
            <div className="space-y-3">
              {pendingApprovals.map((approval) => (
                <div key={approval.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">{approval.employee}</span>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        approval.priority === 'high' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {approval.priority}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{approval.details}</p>
                    <p className="text-xs text-gray-500 mt-1">{approval.submitted}</p>
                  </div>
                  <ChevronRight className="h-4 w-4 text-gray-400" />
                </div>
              ))}
            </div>
            <div className="mt-4">
              <button className="w-full text-center text-orange-600 hover:text-orange-700 font-medium text-sm py-2 border border-orange-200 rounded-lg hover:bg-orange-50 transition-colors">
                View All Approvals
              </button>
            </div>
          </div>
        </Card>

        {/* Recent Activities */}
        <Card>
          <div className="p-4 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Activity className="h-5 w-5 mr-2 text-blue-500" />
                Recent Activities
              </h3>
              <Bell className="h-5 w-5 text-gray-400" />
            </div>
            <div className="space-y-3">
              {recentActivities.map((activity) => {
                const Icon = activity.icon;
                return (
                  <div key={activity.id} className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
                    <div className="flex-shrink-0">
                      <Icon className={`h-5 w-5 ${activity.color}`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">{activity.message}</p>
                      <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                    </div>
                  </div>
                );
              })}
            </div>
            <div className="mt-4">
              <button className="w-full text-center text-blue-600 hover:text-blue-700 font-medium text-sm py-2 border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors">
                View All Activities
              </button>
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <div className="p-4 sm:p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Target className="h-5 w-5 mr-2 text-green-500" />
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <CalendarCheck className="h-6 w-6 text-green-600 mr-3" />
              <span className="font-medium text-gray-900">Approve Leave</span>
            </button>
            <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Clock className="h-6 w-6 text-orange-600 mr-3" />
              <span className="font-medium text-gray-900">Manage Overtime</span>
            </button>
            <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <BarChart3 className="h-6 w-6 text-blue-600 mr-3" />
              <span className="font-medium text-gray-900">View Reports</span>
            </button>
            <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Users className="h-6 w-6 text-purple-600 mr-3" />
              <span className="font-medium text-gray-900">Team Overview</span>
            </button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SupervisorDashboard;
