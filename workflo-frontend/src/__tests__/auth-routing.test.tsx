/**
 * Test file for role-based authentication routing
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';

// Mock the next/navigation module
const mockPush = jest.fn();
const mockReplace = jest.fn();

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: mockReplace,
    back: jest.fn(),
  }),
  usePathname: () => '/login',
}));

// Mock the auth provider
const mockLogin = jest.fn();
const mockUser = { id: 1, email: '<EMAIL>', role: 'hr', first_name: 'Test', last_name: 'User' };

jest.mock('@/providers/AuthProvider', () => ({
  useAuth: () => ({
    login: mockLogin,
    user: mockUser,
    isAuthenticated: false,
    isLoading: false,
    error: null,
    clearError: jest.fn(),
  }),
}));

describe('Role-based Authentication Routing', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Role-based redirect paths', () => {
    it('should redirect admin roles to /dashboard', () => {
      const adminRoles = ['hr', 'supervisor', 'admin', 'accountant'];
      
      adminRoles.forEach(role => {
        const user = { ...mockUser, role };
        const getRedirectPath = (user: any): string => {
          if (!user) return '/login';
          
          const adminRoles = ['hr', 'supervisor', 'admin', 'accountant'];
          if (adminRoles.includes(user.role)) {
            return '/dashboard';
          }
          
          if (user.role === 'employee') {
            return '/staff';
          }
          
          return '/dashboard';
        };

        expect(getRedirectPath(user)).toBe('/dashboard');
      });
    });

    it('should redirect employee role to /staff', () => {
      const user = { ...mockUser, role: 'employee' };
      const getRedirectPath = (user: any): string => {
        if (!user) return '/login';
        
        const adminRoles = ['hr', 'supervisor', 'admin', 'accountant'];
        if (adminRoles.includes(user.role)) {
          return '/dashboard';
        }
        
        if (user.role === 'employee') {
          return '/staff';
        }
        
        return '/dashboard';
      };

      expect(getRedirectPath(user)).toBe('/staff');
    });

    it('should redirect to /login for null user', () => {
      const getRedirectPath = (user: any): string => {
        if (!user) return '/login';
        
        const adminRoles = ['hr', 'supervisor', 'admin', 'accountant'];
        if (adminRoles.includes(user.role)) {
          return '/dashboard';
        }
        
        if (user.role === 'employee') {
          return '/staff';
        }
        
        return '/dashboard';
      };

      expect(getRedirectPath(null)).toBe('/login');
    });
  });

  describe('Login credentials', () => {
    it('should have correct demo credentials', () => {
      const demoCredentials = [
        { email: '<EMAIL>', password: 'admin123', role: 'hr' },
        { email: '<EMAIL>', password: 'employee123', role: 'employee' }
      ];

      expect(demoCredentials[0].email).toBe('<EMAIL>');
      expect(demoCredentials[0].password).toBe('admin123');
      expect(demoCredentials[1].email).toBe('<EMAIL>');
      expect(demoCredentials[1].password).toBe('employee123');
    });
  });

  describe('Password visibility toggle', () => {
    it('should toggle password visibility state', () => {
      let showPassword = false;
      const togglePassword = () => {
        showPassword = !showPassword;
      };

      expect(showPassword).toBe(false);
      togglePassword();
      expect(showPassword).toBe(true);
      togglePassword();
      expect(showPassword).toBe(false);
    });
  });
});
