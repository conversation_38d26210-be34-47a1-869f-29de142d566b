'use client';

import React from 'react';
import { User, Building, Settings, FileText, Shield, Clock } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';

const AccountantInfoPage: React.FC = () => {
  const { user } = useAuth();

  const infoSections = [
    {
      title: 'Profile',
      description: 'Personal information and contact details',
      icon: User,
      href: '/accountant/info/profile',
      color: 'bg-blue-100 text-blue-600'
    },
    {
      title: 'Employment',
      description: 'Job details, department, and role information',
      icon: Building,
      href: '/accountant/info/employment',
      color: 'bg-green-100 text-green-600'
    },
    {
      title: 'Settings',
      description: 'Account preferences and security settings',
      icon: Settings,
      href: '/accountant/info/settings',
      color: 'bg-purple-100 text-purple-600'
    },
    {
      title: 'Documents',
      description: 'Personal documents and certifications',
      icon: FileText,
      href: '/accountant/info/documents',
      color: 'bg-orange-100 text-orange-600'
    },
    {
      title: 'Permissions',
      description: 'Access rights and system permissions',
      icon: Shield,
      href: '/accountant/info/permissions',
      color: 'bg-red-100 text-red-600'
    },
    {
      title: 'Activity Log',
      description: 'Recent account activity and login history',
      icon: Clock,
      href: '/accountant/info/activity',
      color: 'bg-gray-100 text-gray-600'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Personal Information</h1>
          <p className="text-gray-600">Manage your profile, settings, and account information</p>
        </div>
      </div>

      {/* User Summary Card */}
      <Card className="p-6">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center">
            <User className="h-8 w-8 text-white" />
          </div>
          <div className="flex-1">
            <h2 className="text-xl font-semibold text-gray-900">{user?.email}</h2>
            <p className="text-gray-600">Accountant</p>
            <p className="text-sm text-gray-500 mt-1">
              Member since {new Date().toLocaleDateString()}
            </p>
          </div>
          <Button variant="outline">
            Edit Profile
          </Button>
        </div>
      </Card>

      {/* Info Sections Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {infoSections.map((section) => (
          <Card key={section.title} className="p-6 hover:shadow-lg transition-shadow cursor-pointer group">
            <div className="flex items-start space-x-4">
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${section.color}`}>
                <section.icon className="h-6 w-6" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-orange-600 transition-colors">
                  {section.title}
                </h3>
                <p className="text-gray-600 text-sm mt-1">
                  {section.description}
                </p>
                <Button
                  variant="ghost"
                  size="sm"
                  className="mt-3 p-0 h-auto text-orange-600 hover:text-orange-700"
                >
                  Manage →
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6 text-center">
          <div className="text-2xl font-bold text-gray-900">156</div>
          <div className="text-sm text-gray-600">Employees Managed</div>
        </Card>

        <Card className="p-6 text-center">
          <div className="text-2xl font-bold text-gray-900">24</div>
          <div className="text-sm text-gray-600">Payroll Cycles Completed</div>
        </Card>

        <Card className="p-6 text-center">
          <div className="text-2xl font-bold text-gray-900">98%</div>
          <div className="text-sm text-gray-600">Accuracy Rate</div>
        </Card>
      </div>
    </div>
  );
};

export default AccountantInfoPage;
