interface Employee {
  id: number;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  date_of_birth: string;
  gender: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  nationality: string;
  marital_status: 'single' | 'married' | 'divorced' | 'widowed' | 'separated';
  emergency_contact: {
    name: string;
    relationship: string;
    phone: string;
    email?: string;
  };
  address: {
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
  position: {
    title: string;
    department: string;
    manager: string;
    employment_type: 'full_time' | 'part_time' | 'contract' | 'intern';
    start_date: string;
    end_date?: string;
    salary: number;
    currency: string;
  };
  skills: string[];
  education: {
    degree: string;
    institution: string;
    graduation_year: number;
    field_of_study: string;
  }[];
  certifications: {
    name: string;
    issuer: string;
    issue_date: string;
    expiry_date?: string;
    credential_id?: string;
  }[];
  work_experience: {
    company: string;
    position: string;
    start_date: string;
    end_date?: string;
    description: string;
  }[];
  profile_picture?: string;
  status: 'active' | 'inactive' | 'terminated' | 'on_leave';
  created_date: string;
  updated_date: string;
}

interface CreateEmployeeRequest {
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  date_of_birth: string;
  gender: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  nationality: string;
  marital_status: 'single' | 'married' | 'divorced' | 'widowed' | 'separated';
  emergency_contact: {
    name: string;
    relationship: string;
    phone: string;
    email?: string;
  };
  address: {
    street: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
  position: {
    title: string;
    department: string;
    manager: string;
    employment_type: 'full_time' | 'part_time' | 'contract' | 'intern';
    start_date: string;
    salary: number;
    currency: string;
  };
  skills: string[];
  education: {
    degree: string;
    institution: string;
    graduation_year: number;
    field_of_study: string;
  }[];
  certifications: {
    name: string;
    issuer: string;
    issue_date: string;
    expiry_date?: string;
    credential_id?: string;
  }[];
  work_experience: {
    company: string;
    position: string;
    start_date: string;
    end_date?: string;
    description: string;
  }[];
  profile_picture?: string;
}

interface UpdateEmployeeRequest extends Partial<CreateEmployeeRequest> {
  id: number;
}

interface EmployeeSearchFilters {
  search?: string;
  department?: string;
  position?: string;
  status?: string;
  employment_type?: string;
  manager?: string;
}

class EmployeeService {
  private employees: Employee[] = [];
  private nextId = 1;

  constructor() {
    // Initialize with mock data
    this.employees = this.getMockEmployees();
    this.nextId = Math.max(...this.employees.map(e => e.id)) + 1;
  }

  // CREATE - Add new employee
  async createEmployee(data: CreateEmployeeRequest): Promise<Employee> {
    const newEmployee: Employee = {
      id: this.nextId++,
      ...data,
      status: 'active',
      created_date: new Date().toISOString(),
      updated_date: new Date().toISOString()
    };

    this.employees.push(newEmployee);
    return newEmployee;
  }

  // READ - Get all employees with optional filters
  async getEmployees(filters?: EmployeeSearchFilters): Promise<Employee[]> {
    let filteredEmployees = [...this.employees];

    if (filters) {
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredEmployees = filteredEmployees.filter(emp =>
          emp.first_name.toLowerCase().includes(searchLower) ||
          emp.last_name.toLowerCase().includes(searchLower) ||
          emp.email.toLowerCase().includes(searchLower) ||
          emp.employee_id.toLowerCase().includes(searchLower)
        );
      }

      if (filters.department) {
        filteredEmployees = filteredEmployees.filter(emp =>
          emp.position.department === filters.department
        );
      }

      if (filters.position) {
        filteredEmployees = filteredEmployees.filter(emp =>
          emp.position.title.toLowerCase().includes(filters.position!.toLowerCase())
        );
      }

      if (filters.status) {
        filteredEmployees = filteredEmployees.filter(emp =>
          emp.status === filters.status
        );
      }

      if (filters.employment_type) {
        filteredEmployees = filteredEmployees.filter(emp =>
          emp.position.employment_type === filters.employment_type
        );
      }

      if (filters.manager) {
        filteredEmployees = filteredEmployees.filter(emp =>
          emp.position.manager.toLowerCase().includes(filters.manager!.toLowerCase())
        );
      }
    }

    return filteredEmployees;
  }

  // READ - Get employee by ID
  async getEmployeeById(id: number): Promise<Employee | null> {
    const employee = this.employees.find(emp => emp.id === id);
    return employee || null;
  }

  // READ - Get employee by employee ID
  async getEmployeeByEmployeeId(employeeId: string): Promise<Employee | null> {
    const employee = this.employees.find(emp => emp.employee_id === employeeId);
    return employee || null;
  }

  // UPDATE - Update employee
  async updateEmployee(data: UpdateEmployeeRequest): Promise<Employee | null> {
    const index = this.employees.findIndex(emp => emp.id === data.id);

    if (index === -1) {
      return null;
    }

    const updatedEmployee: Employee = {
      ...this.employees[index],
      ...data,
      updated_date: new Date().toISOString()
    };

    this.employees[index] = updatedEmployee;
    return updatedEmployee;
  }

  // DELETE - Delete employee (soft delete by changing status)
  async deleteEmployee(id: number): Promise<boolean> {
    const index = this.employees.findIndex(emp => emp.id === id);

    if (index === -1) {
      return false;
    }

    // Soft delete by changing status
    this.employees[index] = {
      ...this.employees[index],
      status: 'terminated',
      updated_date: new Date().toISOString()
    };

    return true;
  }

  // DELETE - Permanently delete employee
  async permanentlyDeleteEmployee(id: number): Promise<boolean> {
    const index = this.employees.findIndex(emp => emp.id === id);

    if (index === -1) {
      return false;
    }

    this.employees.splice(index, 1);
    return true;
  }

  // UTILITY - Get departments
  async getDepartments(): Promise<string[]> {
    const departments = [...new Set(this.employees.map(emp => emp.position.department))];
    return departments.sort();
  }

  // UTILITY - Get managers
  async getManagers(): Promise<string[]> {
    const managers = [...new Set(this.employees.map(emp => emp.position.manager))];
    return managers.sort();
  }

  // UTILITY - Get employee statistics
  async getEmployeeStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    terminated: number;
    on_leave: number;
    by_department: Record<string, number>;
    by_employment_type: Record<string, number>;
  }> {
    const stats = {
      total: this.employees.length,
      active: this.employees.filter(e => e.status === 'active').length,
      inactive: this.employees.filter(e => e.status === 'inactive').length,
      terminated: this.employees.filter(e => e.status === 'terminated').length,
      on_leave: this.employees.filter(e => e.status === 'on_leave').length,
      by_department: {} as Record<string, number>,
      by_employment_type: {} as Record<string, number>
    };

    // Count by department
    this.employees.forEach(emp => {
      stats.by_department[emp.position.department] =
        (stats.by_department[emp.position.department] || 0) + 1;
    });

    // Count by employment type
    this.employees.forEach(emp => {
      stats.by_employment_type[emp.position.employment_type] =
        (stats.by_employment_type[emp.position.employment_type] || 0) + 1;
    });

    return stats;
  }

  private getMockEmployees(): Employee[] {
    return [
      {
        id: 1,
        employee_id: 'EMP001',
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        phone: '******-0123',
        date_of_birth: '1990-05-15',
        gender: 'male',
        nationality: 'American',
        marital_status: 'married',
        emergency_contact: {
          name: 'Jane Doe',
          relationship: 'Spouse',
          phone: '******-0124',
          email: '<EMAIL>'
        },
        address: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          postal_code: '10001',
          country: 'United States'
        },
        position: {
          title: 'Senior Software Engineer',
          department: 'Engineering',
          manager: 'Sarah Johnson',
          employment_type: 'full_time',
          start_date: '2022-01-15',
          salary: 12500000,
          currency: 'KSH'
        },
        skills: ['JavaScript', 'React', 'Node.js', 'Python', 'AWS'],
        education: [
          {
            degree: 'Bachelor of Science',
            institution: 'MIT',
            graduation_year: 2012,
            field_of_study: 'Computer Science'
          }
        ],
        certifications: [
          {
            name: 'AWS Certified Solutions Architect',
            issuer: 'Amazon Web Services',
            issue_date: '2023-03-15',
            expiry_date: '2026-03-15',
            credential_id: 'AWS-SA-12345'
          }
        ],
        work_experience: [
          {
            company: 'Tech Corp',
            position: 'Software Engineer',
            start_date: '2020-06-01',
            end_date: '2022-01-14',
            description: 'Developed web applications using React and Node.js'
          }
        ],
        profile_picture: '/api/placeholder/150/150',
        status: 'active',
        created_date: '2022-01-15T00:00:00Z',
        updated_date: '2024-01-15T00:00:00Z'
      },
      {
        id: 2,
        employee_id: 'EMP002',
        first_name: 'Sarah',
        last_name: 'Johnson',
        email: '<EMAIL>',
        phone: '******-0125',
        date_of_birth: '1985-08-22',
        gender: 'female',
        nationality: 'American',
        marital_status: 'single',
        emergency_contact: {
          name: 'Robert Johnson',
          relationship: 'Father',
          phone: '******-0126'
        },
        address: {
          street: '456 Oak Ave',
          city: 'San Francisco',
          state: 'CA',
          postal_code: '94102',
          country: 'United States'
        },
        position: {
          title: 'Engineering Manager',
          department: 'Engineering',
          manager: 'Michael Chen',
          employment_type: 'full_time',
          start_date: '2020-03-01',
          salary: 16500000,
          currency: 'KSH'
        },
        skills: ['Leadership', 'Project Management', 'Java', 'Python', 'Agile'],
        education: [
          {
            degree: 'Master of Science',
            institution: 'Stanford University',
            graduation_year: 2008,
            field_of_study: 'Computer Science'
          }
        ],
        certifications: [
          {
            name: 'PMP Certification',
            issuer: 'Project Management Institute',
            issue_date: '2021-05-10',
            expiry_date: '2024-05-10',
            credential_id: 'PMP-67890'
          }
        ],
        work_experience: [
          {
            company: 'Innovation Labs',
            position: 'Senior Software Engineer',
            start_date: '2015-09-01',
            end_date: '2020-02-28',
            description: 'Led development teams and architected scalable systems'
          }
        ],
        profile_picture: '/api/placeholder/150/150',
        status: 'active',
        created_date: '2020-03-01T00:00:00Z',
        updated_date: '2024-01-15T00:00:00Z'
      },
      {
        id: 3,
        employee_id: 'EMP003',
        first_name: 'Michael',
        last_name: 'Chen',
        email: '<EMAIL>',
        phone: '******-0127',
        date_of_birth: '1982-12-10',
        gender: 'male',
        nationality: 'Canadian',
        marital_status: 'married',
        emergency_contact: {
          name: 'Lisa Chen',
          relationship: 'Spouse',
          phone: '******-0128',
          email: '<EMAIL>'
        },
        address: {
          street: '789 Pine St',
          city: 'Seattle',
          state: 'WA',
          postal_code: '98101',
          country: 'United States'
        },
        position: {
          title: 'VP of Engineering',
          department: 'Engineering',
          manager: 'CEO',
          employment_type: 'full_time',
          start_date: '2018-06-15',
          salary: 23700000,
          currency: 'KSH'
        },
        skills: ['Strategic Planning', 'Team Leadership', 'Architecture', 'DevOps', 'Cloud Computing'],
        education: [
          {
            degree: 'PhD',
            institution: 'University of Toronto',
            graduation_year: 2007,
            field_of_study: 'Computer Engineering'
          }
        ],
        certifications: [
          {
            name: 'Certified Kubernetes Administrator',
            issuer: 'Cloud Native Computing Foundation',
            issue_date: '2022-08-20',
            expiry_date: '2025-08-20',
            credential_id: 'CKA-54321'
          }
        ],
        work_experience: [
          {
            company: 'Global Tech Solutions',
            position: 'Director of Engineering',
            start_date: '2012-04-01',
            end_date: '2018-06-14',
            description: 'Managed engineering teams across multiple products and platforms'
          }
        ],
        profile_picture: '/api/placeholder/150/150',
        status: 'active',
        created_date: '2018-06-15T00:00:00Z',
        updated_date: '2024-01-15T00:00:00Z'
      }
    ];
  }
}

// Export singleton instance
export const employeeService = new EmployeeService();
export type { Employee, CreateEmployeeRequest, UpdateEmployeeRequest, EmployeeSearchFilters };
