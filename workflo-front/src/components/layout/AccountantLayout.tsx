'use client';

import React from 'react';
import AccountantHeader from './AccountantHeader';
import AccountantDynamicSidebar from './AccountantDynamicSidebar';

interface AccountantLayoutProps {
  children: React.ReactNode;
}

export const AccountantLayout: React.FC<AccountantLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <AccountantHeader />

      <div className="flex">
        <AccountantDynamicSidebar />

        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AccountantLayout;
