35ea97cf7e89bc3b217f7ed3cc674629
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _react1 = require("@testing-library/react");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Simple test to verify Jest and React Testing Library are working
describe('Basic Test Setup', ()=>{
    it('should render a simple component', ()=>{
        const TestComponent = ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Hello, World!"
            });
        (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestComponent, {}));
        expect(_react1.screen.getByText('Hello, World!')).toBeInTheDocument();
    });
    it('should handle basic assertions', ()=>{
        expect(1 + 1).toBe(2);
        expect('hello').toMatch(/hello/);
        expect([
            1,
            2,
            3
        ]).toHaveLength(3);
    });
    it('should mock functions', ()=>{
        const mockFn = jest.fn();
        mockFn('test');
        expect(mockFn).toHaveBeenCalledWith('test');
        expect(mockFn).toHaveBeenCalledTimes(1);
    });
});

//# sourceMappingURL=data:application/json;base64,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