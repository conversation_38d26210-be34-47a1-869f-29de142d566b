# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <grg<PERSON><EMAIL>>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2022-01-13 18:20+0000\n"
"Last-Translator: arneatec <<EMAIL>>\n"
"Language-Team: Bulgarian (http://www.transifex.com/django/django/language/"
"bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Типове съдържание"

msgid "python model class name"
msgstr "име на класа на модела в Python"

msgid "content type"
msgstr "тип на съдържанието"

msgid "content types"
msgstr "типове съдържание"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Обект с тип на съдържанието %(ct_id)s няма асоцииран модел."

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Обект %(obj_id)s с тип на съдържанието %(ct_id)s не съществува."

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s обекти нямат метода get_absolute_url()"
