// Simple test to verify mock API works
const { mockApi } = require('./src/lib/mockApi.ts');

async function testMockApi() {
  try {
    console.log('Testing mock API...');
    
    // Test login
    const tokens = await mockApi.login({
      email: '<EMAIL>',
      password: 'admin123'
    });
    console.log('Login successful:', tokens);
    
    // Test get current user
    const user = await mockApi.getCurrentUser();
    console.log('Current user:', user);
    
    console.log('Mock API test completed successfully!');
  } catch (error) {
    console.error('Mock API test failed:', error);
  }
}

testMockApi();
