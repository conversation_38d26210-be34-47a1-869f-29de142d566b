{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/__tests__/biostar-integration.test.tsx"], "sourcesContent": ["import { renderHook, waitFor } from '@testing-library/react';\nimport { useBiostarAttendance } from '@/hooks/useBiostarAttendance';\nimport { attendanceService } from '@/lib/attendanceService';\n\n// Mock the attendance service\njest.mock('@/lib/attendanceService', () => ({\n  attendanceService: {\n    getTodayAttendance: jest.fn(),\n    getAttendanceSummary: jest.fn(),\n    getAttendanceRange: jest.fn(),\n    getDevices: jest.fn(),\n    startRealTimeMonitoring: jest.fn(),\n    stopRealTimeMonitoring: jest.fn(),\n  },\n}));\n\nconst mockAttendanceService = attendanceService as jest.Mocked<typeof attendanceService>;\n\ndescribe('BioStar Integration', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('useBiostarAttendance Hook', () => {\n    const mockEmployeeId = 'EMP001';\n\n    const mockTodayAttendance = {\n      id: 'att-001',\n      employee_id: mockEmployeeId,\n      employee_name: '<PERSON>',\n      date: '2024-12-21',\n      first_in: '2024-12-21T08:30:00Z',\n      last_out: '2024-12-21T17:30:00Z',\n      total_hours: 8,\n      break_time: 60,\n      overtime: 0,\n      status: 'PRESENT' as const,\n      events: [],\n      biostar_synced: true,\n    };\n\n    const mockSummary = {\n      todayStatus: 'PRESENT' as const,\n      checkInTime: '2024-12-21T08:30:00Z',\n      checkOutTime: '2024-12-21T17:30:00Z',\n      hoursWorked: 8,\n      breakTime: 60,\n      overtime: 0,\n      weeklyHours: 40,\n      monthlyAttendance: 20,\n    };\n\n    const mockDevices = [\n      {\n        id: 'dev-001',\n        name: 'Main Entrance',\n        ip: '*************',\n        port: 8080,\n        status: 'ONLINE' as const,\n        type: 'Fingerprint',\n        location: 'Main Building',\n      },\n    ];\n\n    it('should load attendance data successfully', async () => {\n      mockAttendanceService.getTodayAttendance.mockResolvedValue(mockTodayAttendance);\n      mockAttendanceService.getAttendanceSummary.mockResolvedValue(mockSummary);\n      mockAttendanceService.getAttendanceRange.mockResolvedValue([mockTodayAttendance]);\n      mockAttendanceService.getDevices.mockResolvedValue(mockDevices);\n\n      const { result } = renderHook(() =>\n        useBiostarAttendance({\n          employeeId: mockEmployeeId,\n          autoRefresh: false,\n          enableRealTime: false,\n        })\n      );\n\n      // Initially loading\n      expect(result.current.loading).toBe(true);\n      expect(result.current.connected).toBe(false);\n\n      // Wait for data to load\n      await waitFor(() => {\n        expect(result.current.loading).toBe(false);\n      });\n\n      // Check that data was loaded\n      expect(result.current.todayAttendance).toEqual(mockTodayAttendance);\n      expect(result.current.summary).toEqual(mockSummary);\n      expect(result.current.devices).toEqual(mockDevices);\n      expect(result.current.connected).toBe(true);\n      expect(result.current.error).toBeNull();\n    });\n\n    it('should handle errors gracefully', async () => {\n      const errorMessage = 'BioStar connection failed';\n      mockAttendanceService.getTodayAttendance.mockRejectedValue(new Error(errorMessage));\n\n      const { result } = renderHook(() =>\n        useBiostarAttendance({\n          employeeId: mockEmployeeId,\n          autoRefresh: false,\n          enableRealTime: false,\n        })\n      );\n\n      await waitFor(() => {\n        expect(result.current.loading).toBe(false);\n      });\n\n      expect(result.current.connected).toBe(false);\n      expect(result.current.error).toBe(errorMessage);\n      expect(result.current.todayAttendance).toBeNull();\n    });\n\n    it('should refresh data when refresh is called', async () => {\n      mockAttendanceService.getTodayAttendance.mockResolvedValue(mockTodayAttendance);\n      mockAttendanceService.getAttendanceSummary.mockResolvedValue(mockSummary);\n      mockAttendanceService.getAttendanceRange.mockResolvedValue([mockTodayAttendance]);\n      mockAttendanceService.getDevices.mockResolvedValue(mockDevices);\n\n      const { result } = renderHook(() =>\n        useBiostarAttendance({\n          employeeId: mockEmployeeId,\n          autoRefresh: false,\n          enableRealTime: false,\n        })\n      );\n\n      await waitFor(() => {\n        expect(result.current.loading).toBe(false);\n      });\n\n      // Clear mocks to track refresh calls\n      jest.clearAllMocks();\n\n      // Call refresh\n      await result.current.refresh();\n\n      // Verify that services were called again\n      expect(mockAttendanceService.getTodayAttendance).toHaveBeenCalledWith(mockEmployeeId);\n      expect(mockAttendanceService.getAttendanceSummary).toHaveBeenCalledWith(mockEmployeeId);\n    });\n\n    it('should get attendance range', async () => {\n      const startDate = '2024-12-01';\n      const endDate = '2024-12-21';\n      const mockRangeData = [mockTodayAttendance];\n\n      mockAttendanceService.getAttendanceRange.mockResolvedValue(mockRangeData);\n\n      const { result } = renderHook(() =>\n        useBiostarAttendance({\n          employeeId: mockEmployeeId,\n          autoRefresh: false,\n          enableRealTime: false,\n        })\n      );\n\n      await waitFor(() => {\n        expect(result.current.loading).toBe(false);\n      });\n\n      const rangeData = await result.current.getAttendanceRange(startDate, endDate);\n\n      expect(mockAttendanceService.getAttendanceRange).toHaveBeenCalledWith(\n        mockEmployeeId,\n        startDate,\n        endDate\n      );\n      expect(rangeData).toEqual(mockRangeData);\n    });\n\n    it('should handle real-time monitoring', async () => {\n      mockAttendanceService.getTodayAttendance.mockResolvedValue(mockTodayAttendance);\n      mockAttendanceService.getAttendanceSummary.mockResolvedValue(mockSummary);\n      mockAttendanceService.getAttendanceRange.mockResolvedValue([mockTodayAttendance]);\n      mockAttendanceService.getDevices.mockResolvedValue(mockDevices);\n\n      const { result } = renderHook(() =>\n        useBiostarAttendance({\n          employeeId: mockEmployeeId,\n          autoRefresh: false,\n          enableRealTime: true,\n        })\n      );\n\n      await waitFor(() => {\n        expect(result.current.loading).toBe(false);\n      });\n\n      // Verify real-time monitoring was started\n      expect(mockAttendanceService.startRealTimeMonitoring).toHaveBeenCalled();\n    });\n\n    it('should not load data without employee ID', () => {\n      const { result } = renderHook(() =>\n        useBiostarAttendance({\n          employeeId: undefined,\n          autoRefresh: false,\n          enableRealTime: false,\n        })\n      );\n\n      expect(result.current.loading).toBe(true);\n      expect(mockAttendanceService.getTodayAttendance).not.toHaveBeenCalled();\n    });\n  });\n\n  describe('Attendance Service Integration', () => {\n    it('should be properly mocked', () => {\n      expect(mockAttendanceService.getTodayAttendance).toBeDefined();\n      expect(mockAttendanceService.getAttendanceSummary).toBeDefined();\n      expect(mockAttendanceService.getAttendanceRange).toBeDefined();\n      expect(mockAttendanceService.getDevices).toBeDefined();\n      expect(mockAttendanceService.startRealTimeMonitoring).toBeDefined();\n      expect(mockAttendanceService.stopRealTimeMonitoring).toBeDefined();\n    });\n  });\n});\n\ndescribe('BioStar API Integration', () => {\n  it('should handle authentication flow', () => {\n    // This would test the actual BioStar API integration\n    // For now, we'll just verify the service structure\n    expect(attendanceService).toBeDefined();\n  });\n\n  it('should handle connection failures', () => {\n    // Test connection failure scenarios\n    expect(true).toBe(true); // Placeholder\n  });\n\n  it('should handle data synchronization', () => {\n    // Test data sync between BioStar and local system\n    expect(true).toBe(true); // Placeholder\n  });\n});\n"], "names": ["jest", "mock", "attendanceService", "getTodayAttendance", "fn", "getAttendanceSummary", "getAttendanceRange", "getDevices", "startRealTimeMonitoring", "stopRealTimeMonitoring", "mockAttendanceService", "describe", "beforeEach", "clearAllMocks", "mockEmployeeId", "mockTodayAttendance", "id", "employee_id", "employee_name", "date", "first_in", "last_out", "total_hours", "break_time", "overtime", "status", "events", "biostar_synced", "mockSummary", "todayStatus", "checkInTime", "checkOutTime", "hoursWorked", "breakTime", "weeklyHours", "monthlyAttendance", "mockDevices", "name", "ip", "port", "type", "location", "it", "mockResolvedValue", "result", "renderHook", "useBiostarAttendance", "employeeId", "autoRefresh", "enableRealTime", "expect", "current", "loading", "toBe", "connected", "waitFor", "todayAttendance", "toEqual", "summary", "devices", "error", "toBeNull", "errorMessage", "mockRejectedValue", "Error", "refresh", "toHaveBeenCalledWith", "startDate", "endDate", "mockRangeData", "rangeData", "toHaveBeenCalled", "undefined", "not", "toBeDefined"], "mappings": ";AAIA,8BAA8B;AAC9BA,KAAKC,IAAI,CAAC,2BAA2B,IAAO,CAAA;QAC1CC,mBAAmB;YACjBC,oBAAoBH,KAAKI,EAAE;YAC3BC,sBAAsBL,KAAKI,EAAE;YAC7BE,oBAAoBN,KAAKI,EAAE;YAC3BG,YAAYP,KAAKI,EAAE;YACnBI,yBAAyBR,KAAKI,EAAE;YAChCK,wBAAwBT,KAAKI,EAAE;QACjC;IACF,CAAA;;;;uBAdoC;sCACC;mCACH;AAclC,MAAMM,wBAAwBR,oCAAiB;AAE/CS,SAAS,uBAAuB;IAC9BC,WAAW;QACTZ,KAAKa,aAAa;IACpB;IAEAF,SAAS,6BAA6B;QACpC,MAAMG,iBAAiB;QAEvB,MAAMC,sBAAsB;YAC1BC,IAAI;YACJC,aAAaH;YACbI,eAAe;YACfC,MAAM;YACNC,UAAU;YACVC,UAAU;YACVC,aAAa;YACbC,YAAY;YACZC,UAAU;YACVC,QAAQ;YACRC,QAAQ,EAAE;YACVC,gBAAgB;QAClB;QAEA,MAAMC,cAAc;YAClBC,aAAa;YACbC,aAAa;YACbC,cAAc;YACdC,aAAa;YACbC,WAAW;YACXT,UAAU;YACVU,aAAa;YACbC,mBAAmB;QACrB;QAEA,MAAMC,cAAc;YAClB;gBACEpB,IAAI;gBACJqB,MAAM;gBACNC,IAAI;gBACJC,MAAM;gBACNd,QAAQ;gBACRe,MAAM;gBACNC,UAAU;YACZ;SACD;QAEDC,GAAG,4CAA4C;YAC7ChC,sBAAsBP,kBAAkB,CAACwC,iBAAiB,CAAC5B;YAC3DL,sBAAsBL,oBAAoB,CAACsC,iBAAiB,CAACf;YAC7DlB,sBAAsBJ,kBAAkB,CAACqC,iBAAiB,CAAC;gBAAC5B;aAAoB;YAChFL,sBAAsBH,UAAU,CAACoC,iBAAiB,CAACP;YAEnD,MAAM,EAAEQ,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAC5BC,IAAAA,0CAAoB,EAAC;oBACnBC,YAAYjC;oBACZkC,aAAa;oBACbC,gBAAgB;gBAClB;YAGF,oBAAoB;YACpBC,OAAON,OAAOO,OAAO,CAACC,OAAO,EAAEC,IAAI,CAAC;YACpCH,OAAON,OAAOO,OAAO,CAACG,SAAS,EAAED,IAAI,CAAC;YAEtC,wBAAwB;YACxB,MAAME,IAAAA,cAAO,EAAC;gBACZL,OAAON,OAAOO,OAAO,CAACC,OAAO,EAAEC,IAAI,CAAC;YACtC;YAEA,6BAA6B;YAC7BH,OAAON,OAAOO,OAAO,CAACK,eAAe,EAAEC,OAAO,CAAC1C;YAC/CmC,OAAON,OAAOO,OAAO,CAACO,OAAO,EAAED,OAAO,CAAC7B;YACvCsB,OAAON,OAAOO,OAAO,CAACQ,OAAO,EAAEF,OAAO,CAACrB;YACvCc,OAAON,OAAOO,OAAO,CAACG,SAAS,EAAED,IAAI,CAAC;YACtCH,OAAON,OAAOO,OAAO,CAACS,KAAK,EAAEC,QAAQ;QACvC;QAEAnB,GAAG,mCAAmC;YACpC,MAAMoB,eAAe;YACrBpD,sBAAsBP,kBAAkB,CAAC4D,iBAAiB,CAAC,IAAIC,MAAMF;YAErE,MAAM,EAAElB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAC5BC,IAAAA,0CAAoB,EAAC;oBACnBC,YAAYjC;oBACZkC,aAAa;oBACbC,gBAAgB;gBAClB;YAGF,MAAMM,IAAAA,cAAO,EAAC;gBACZL,OAAON,OAAOO,OAAO,CAACC,OAAO,EAAEC,IAAI,CAAC;YACtC;YAEAH,OAAON,OAAOO,OAAO,CAACG,SAAS,EAAED,IAAI,CAAC;YACtCH,OAAON,OAAOO,OAAO,CAACS,KAAK,EAAEP,IAAI,CAACS;YAClCZ,OAAON,OAAOO,OAAO,CAACK,eAAe,EAAEK,QAAQ;QACjD;QAEAnB,GAAG,8CAA8C;YAC/ChC,sBAAsBP,kBAAkB,CAACwC,iBAAiB,CAAC5B;YAC3DL,sBAAsBL,oBAAoB,CAACsC,iBAAiB,CAACf;YAC7DlB,sBAAsBJ,kBAAkB,CAACqC,iBAAiB,CAAC;gBAAC5B;aAAoB;YAChFL,sBAAsBH,UAAU,CAACoC,iBAAiB,CAACP;YAEnD,MAAM,EAAEQ,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAC5BC,IAAAA,0CAAoB,EAAC;oBACnBC,YAAYjC;oBACZkC,aAAa;oBACbC,gBAAgB;gBAClB;YAGF,MAAMM,IAAAA,cAAO,EAAC;gBACZL,OAAON,OAAOO,OAAO,CAACC,OAAO,EAAEC,IAAI,CAAC;YACtC;YAEA,qCAAqC;YACrCrD,KAAKa,aAAa;YAElB,eAAe;YACf,MAAM+B,OAAOO,OAAO,CAACc,OAAO;YAE5B,yCAAyC;YACzCf,OAAOxC,sBAAsBP,kBAAkB,EAAE+D,oBAAoB,CAACpD;YACtEoC,OAAOxC,sBAAsBL,oBAAoB,EAAE6D,oBAAoB,CAACpD;QAC1E;QAEA4B,GAAG,+BAA+B;YAChC,MAAMyB,YAAY;YAClB,MAAMC,UAAU;YAChB,MAAMC,gBAAgB;gBAACtD;aAAoB;YAE3CL,sBAAsBJ,kBAAkB,CAACqC,iBAAiB,CAAC0B;YAE3D,MAAM,EAAEzB,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAC5BC,IAAAA,0CAAoB,EAAC;oBACnBC,YAAYjC;oBACZkC,aAAa;oBACbC,gBAAgB;gBAClB;YAGF,MAAMM,IAAAA,cAAO,EAAC;gBACZL,OAAON,OAAOO,OAAO,CAACC,OAAO,EAAEC,IAAI,CAAC;YACtC;YAEA,MAAMiB,YAAY,MAAM1B,OAAOO,OAAO,CAAC7C,kBAAkB,CAAC6D,WAAWC;YAErElB,OAAOxC,sBAAsBJ,kBAAkB,EAAE4D,oBAAoB,CACnEpD,gBACAqD,WACAC;YAEFlB,OAAOoB,WAAWb,OAAO,CAACY;QAC5B;QAEA3B,GAAG,sCAAsC;YACvChC,sBAAsBP,kBAAkB,CAACwC,iBAAiB,CAAC5B;YAC3DL,sBAAsBL,oBAAoB,CAACsC,iBAAiB,CAACf;YAC7DlB,sBAAsBJ,kBAAkB,CAACqC,iBAAiB,CAAC;gBAAC5B;aAAoB;YAChFL,sBAAsBH,UAAU,CAACoC,iBAAiB,CAACP;YAEnD,MAAM,EAAEQ,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAC5BC,IAAAA,0CAAoB,EAAC;oBACnBC,YAAYjC;oBACZkC,aAAa;oBACbC,gBAAgB;gBAClB;YAGF,MAAMM,IAAAA,cAAO,EAAC;gBACZL,OAAON,OAAOO,OAAO,CAACC,OAAO,EAAEC,IAAI,CAAC;YACtC;YAEA,0CAA0C;YAC1CH,OAAOxC,sBAAsBF,uBAAuB,EAAE+D,gBAAgB;QACxE;QAEA7B,GAAG,4CAA4C;YAC7C,MAAM,EAAEE,MAAM,EAAE,GAAGC,IAAAA,iBAAU,EAAC,IAC5BC,IAAAA,0CAAoB,EAAC;oBACnBC,YAAYyB;oBACZxB,aAAa;oBACbC,gBAAgB;gBAClB;YAGFC,OAAON,OAAOO,OAAO,CAACC,OAAO,EAAEC,IAAI,CAAC;YACpCH,OAAOxC,sBAAsBP,kBAAkB,EAAEsE,GAAG,CAACF,gBAAgB;QACvE;IACF;IAEA5D,SAAS,kCAAkC;QACzC+B,GAAG,6BAA6B;YAC9BQ,OAAOxC,sBAAsBP,kBAAkB,EAAEuE,WAAW;YAC5DxB,OAAOxC,sBAAsBL,oBAAoB,EAAEqE,WAAW;YAC9DxB,OAAOxC,sBAAsBJ,kBAAkB,EAAEoE,WAAW;YAC5DxB,OAAOxC,sBAAsBH,UAAU,EAAEmE,WAAW;YACpDxB,OAAOxC,sBAAsBF,uBAAuB,EAAEkE,WAAW;YACjExB,OAAOxC,sBAAsBD,sBAAsB,EAAEiE,WAAW;QAClE;IACF;AACF;AAEA/D,SAAS,2BAA2B;IAClC+B,GAAG,qCAAqC;QACtC,qDAAqD;QACrD,mDAAmD;QACnDQ,OAAOhD,oCAAiB,EAAEwE,WAAW;IACvC;IAEAhC,GAAG,qCAAqC;QACtC,oCAAoC;QACpCQ,OAAO,MAAMG,IAAI,CAAC,OAAO,cAAc;IACzC;IAEAX,GAAG,sCAAsC;QACvC,kDAAkD;QAClDQ,OAAO,MAAMG,IAAI,CAAC,OAAO,cAAc;IACzC;AACF"}