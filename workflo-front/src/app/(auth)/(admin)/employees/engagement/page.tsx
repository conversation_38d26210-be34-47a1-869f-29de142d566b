'use client';

import React, { useState, useEffect } from 'react';
import { 
  Heart, 
  Plus, 
  Search, 
  MessageSquare,
  Award,
  Headphones,
  Calendar,
  Users,
  TrendingUp,
  Star,
  Gift,
  Coffee,
  Smile,
  Activity,
  Bell,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  ThumbsUp,
  MessageCircle,
  PartyPopper,
  Target,
  Zap
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useAuth } from '@/providers/AuthProvider';
import { cn, formatDate } from '@/lib/utils';

interface Survey {
  id: number;
  title: string;
  description: string;
  type: 'pulse' | 'engagement' | 'feedback' | 'anonymous';
  status: 'active' | 'closed' | 'draft';
  responses_count: number;
  target_audience: string;
  created_date: string;
  closing_date?: string;
  avg_rating?: number;
}

interface Recognition {
  id: number;
  giver_name: string;
  receiver_name: string;
  type: 'peer_recognition' | 'milestone' | 'achievement' | 'appreciation';
  title: string;
  message: string;
  points_awarded: number;
  date: string;
  is_public: boolean;
}

interface WellnessProgram {
  id: number;
  title: string;
  description: string;
  category: 'mental_health' | 'physical_wellness' | 'work_life_balance' | 'counseling';
  provider: string;
  availability: 'always' | 'scheduled' | 'on_demand';
  participants_count: number;
  rating: number;
  status: 'active' | 'inactive';
}

interface CompanyEvent {
  id: number;
  title: string;
  description: string;
  type: 'social' | 'team_building' | 'announcement' | 'celebration' | 'training';
  date: string;
  location: string;
  attendees_count: number;
  max_attendees?: number;
  organizer: string;
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
}

const EngagementWellnessPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'surveys' | 'recognition' | 'wellness' | 'events'>('surveys');
  const [surveys, setSurveys] = useState<Survey[]>([]);
  const [recognitions, setRecognitions] = useState<Recognition[]>([]);
  const [wellnessPrograms, setWellnessPrograms] = useState<WellnessProgram[]>([]);
  const [events, setEvents] = useState<CompanyEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Mock data
  const mockSurveys: Survey[] = [
    {
      id: 1,
      title: 'Q1 Employee Engagement Survey',
      description: 'Quarterly survey to measure employee satisfaction and engagement levels',
      type: 'engagement',
      status: 'active',
      responses_count: 45,
      target_audience: 'All Employees',
      created_date: '2024-01-01T00:00:00Z',
      closing_date: '2024-01-31T00:00:00Z',
      avg_rating: 4.2
    },
    {
      id: 2,
      title: 'Remote Work Feedback',
      description: 'Anonymous feedback on remote work policies and tools',
      type: 'anonymous',
      status: 'active',
      responses_count: 32,
      target_audience: 'Remote Workers',
      created_date: '2024-01-10T00:00:00Z',
      closing_date: '2024-01-25T00:00:00Z',
      avg_rating: 3.8
    },
    {
      id: 3,
      title: 'Weekly Pulse Check',
      description: 'Quick weekly check-in on team morale and workload',
      type: 'pulse',
      status: 'active',
      responses_count: 78,
      target_audience: 'All Teams',
      created_date: '2024-01-15T00:00:00Z',
      avg_rating: 4.0
    }
  ];

  const mockRecognitions: Recognition[] = [
    {
      id: 1,
      giver_name: 'Sarah Johnson',
      receiver_name: 'John Smith',
      type: 'peer_recognition',
      title: 'Outstanding Leadership',
      message: 'John showed exceptional leadership during the project crisis and helped the team deliver on time.',
      points_awarded: 100,
      date: '2024-01-15T00:00:00Z',
      is_public: true
    },
    {
      id: 2,
      giver_name: 'HR Team',
      receiver_name: 'Maria Cotton',
      type: 'milestone',
      title: '2 Years Anniversary',
      message: 'Congratulations on completing 2 successful years with the company!',
      points_awarded: 200,
      date: '2024-01-14T00:00:00Z',
      is_public: true
    },
    {
      id: 3,
      giver_name: 'David Wilson',
      receiver_name: 'Emily Davis',
      type: 'achievement',
      title: 'Innovation Award',
      message: 'Emily\'s innovative solution saved the company $50K in operational costs.',
      points_awarded: 500,
      date: '2024-01-12T00:00:00Z',
      is_public: true
    }
  ];

  const mockWellnessPrograms: WellnessProgram[] = [
    {
      id: 1,
      title: 'Mental Health Support',
      description: 'Free counseling sessions and mental health resources',
      category: 'mental_health',
      provider: 'BetterHelp',
      availability: 'always',
      participants_count: 25,
      rating: 4.8,
      status: 'active'
    },
    {
      id: 2,
      title: 'Fitness Membership',
      description: 'Subsidized gym membership and fitness classes',
      category: 'physical_wellness',
      provider: 'Local Fitness Centers',
      availability: 'always',
      participants_count: 40,
      rating: 4.5,
      status: 'active'
    },
    {
      id: 3,
      title: 'Work-Life Balance Workshop',
      description: 'Monthly workshops on maintaining healthy work-life balance',
      category: 'work_life_balance',
      provider: 'Internal HR',
      availability: 'scheduled',
      participants_count: 30,
      rating: 4.3,
      status: 'active'
    }
  ];

  const mockEvents: CompanyEvent[] = [
    {
      id: 1,
      title: 'Team Building Retreat',
      description: 'Annual company retreat with team building activities and workshops',
      type: 'team_building',
      date: '2024-02-15T00:00:00Z',
      location: 'Mountain Resort',
      attendees_count: 45,
      max_attendees: 50,
      organizer: 'HR Team',
      status: 'upcoming'
    },
    {
      id: 2,
      title: 'Coffee Chat Friday',
      description: 'Weekly informal coffee session for cross-team networking',
      type: 'social',
      date: '2024-01-19T00:00:00Z',
      location: 'Office Cafeteria',
      attendees_count: 25,
      organizer: 'Social Committee',
      status: 'upcoming'
    },
    {
      id: 3,
      title: 'Q4 Results Announcement',
      description: 'Company-wide meeting to share Q4 results and future plans',
      type: 'announcement',
      date: '2024-01-20T00:00:00Z',
      location: 'Main Conference Room',
      attendees_count: 80,
      organizer: 'Executive Team',
      status: 'upcoming'
    }
  ];

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setTimeout(() => {
        setSurveys(mockSurveys);
        setRecognitions(mockRecognitions);
        setWellnessPrograms(mockWellnessPrograms);
        setEvents(mockEvents);
        setLoading(false);
      }, 1000);
    };

    loadData();
  }, []);

  const totalSurveys = surveys.length;
  const activeSurveys = surveys.filter(s => s.status === 'active').length;
  const totalRecognitions = recognitions.length;
  const activeWellnessPrograms = wellnessPrograms.filter(p => p.status === 'active').length;
  const upcomingEvents = events.filter(e => e.status === 'upcoming').length;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': case 'upcoming': return 'bg-green-100 text-green-800';
      case 'closed': case 'completed': return 'bg-gray-100 text-gray-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'ongoing': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'pulse': return 'bg-blue-100 text-blue-800';
      case 'engagement': return 'bg-green-100 text-green-800';
      case 'feedback': return 'bg-yellow-100 text-yellow-800';
      case 'anonymous': return 'bg-purple-100 text-purple-800';
      case 'peer_recognition': return 'bg-blue-100 text-blue-800';
      case 'milestone': return 'bg-yellow-100 text-yellow-800';
      case 'achievement': return 'bg-green-100 text-green-800';
      case 'appreciation': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Employee Engagement & Wellness</h1>
            <p className="text-gray-600 mt-1">
              Foster employee engagement, recognition, wellness, and company culture
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-3">
            <Button variant="secondary">
              <MessageSquare className="h-4 w-4 mr-2" />
              Create Survey
            </Button>
            <Button variant="primary">
              <Plus className="h-4 w-4 mr-2" />
              New Initiative
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <MessageSquare className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Surveys</p>
                <p className="text-2xl font-semibold text-gray-900">{activeSurveys}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Award className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Recognitions</p>
                <p className="text-2xl font-semibold text-gray-900">{totalRecognitions}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Heart className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Wellness Programs</p>
                <p className="text-2xl font-semibold text-gray-900">{activeWellnessPrograms}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Upcoming Events</p>
                <p className="text-2xl font-semibold text-gray-900">{upcomingEvents}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Engagement Score</p>
                <p className="text-2xl font-semibold text-gray-900">4.1</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { key: 'surveys', label: 'Feedback & Surveys', icon: MessageSquare },
              { key: 'recognition', label: 'Recognition & Rewards', icon: Award },
              { key: 'wellness', label: 'Wellness Programs', icon: Heart },
              { key: 'events', label: 'Culture & Events', icon: PartyPopper },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={cn(
                    'py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2',
                    activeTab === tab.key
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Search */}
        <div className="p-6 border-b border-gray-200">
          <div className="w-full max-w-md">
            <Input
              placeholder={`Search ${activeTab}...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
            />
          </div>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'surveys' && (
            <div className="space-y-4">
              {loading ? (
                Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="animate-pulse border border-gray-200 rounded-lg p-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))
              ) : (
                surveys.map((survey) => (
                  <SurveyCard key={survey.id} survey={survey} />
                ))
              )}
            </div>
          )}

          {activeTab === 'recognition' && (
            <div className="space-y-4">
              {loading ? (
                Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="animate-pulse border border-gray-200 rounded-lg p-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))
              ) : (
                recognitions.map((recognition) => (
                  <RecognitionCard key={recognition.id} recognition={recognition} />
                ))
              )}
            </div>
          )}

          {activeTab === 'wellness' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {loading ? (
                Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="animate-pulse border border-gray-200 rounded-lg p-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))
              ) : (
                wellnessPrograms.map((program) => (
                  <WellnessProgramCard key={program.id} program={program} />
                ))
              )}
            </div>
          )}

          {activeTab === 'events' && (
            <div className="space-y-4">
              {loading ? (
                Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="animate-pulse border border-gray-200 rounded-lg p-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))
              ) : (
                events.map((event) => (
                  <EventCard key={event.id} event={event} />
                ))
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Survey Card Component
interface SurveyCardProps {
  survey: Survey;
}

const SurveyCard: React.FC<SurveyCardProps> = ({ survey }) => {
  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-semibold text-gray-900">{survey.title}</h3>
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                survey.status === 'active' ? 'bg-green-100 text-green-800' :
                survey.status === 'closed' ? 'bg-gray-100 text-gray-800' :
                'bg-yellow-100 text-yellow-800'
              )}>
                {survey.status}
              </span>
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                survey.type === 'pulse' ? 'bg-blue-100 text-blue-800' :
                survey.type === 'engagement' ? 'bg-green-100 text-green-800' :
                survey.type === 'feedback' ? 'bg-yellow-100 text-yellow-800' :
                'bg-purple-100 text-purple-800'
              )}>
                {survey.type.replace('_', ' ')}
              </span>
            </div>
            
            <p className="text-sm text-gray-600 mb-4">{survey.description}</p>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
              <div>
                <span className="font-medium">Responses:</span> {survey.responses_count}
              </div>
              <div>
                <span className="font-medium">Target:</span> {survey.target_audience}
              </div>
              <div>
                <span className="font-medium">Created:</span> {formatDate(survey.created_date)}
              </div>
              {survey.avg_rating && (
                <div className="flex items-center">
                  <span className="font-medium mr-1">Rating:</span>
                  <Star className="h-4 w-4 text-yellow-400 mr-1" />
                  {survey.avg_rating}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

// Recognition Card Component
interface RecognitionCardProps {
  recognition: Recognition;
}

const RecognitionCard: React.FC<RecognitionCardProps> = ({ recognition }) => {
  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-semibold text-gray-900">{recognition.title}</h3>
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                recognition.type === 'peer_recognition' ? 'bg-blue-100 text-blue-800' :
                recognition.type === 'milestone' ? 'bg-yellow-100 text-yellow-800' :
                recognition.type === 'achievement' ? 'bg-green-100 text-green-800' :
                'bg-pink-100 text-pink-800'
              )}>
                {recognition.type.replace('_', ' ')}
              </span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                <Gift className="h-3 w-3 mr-1" />
                {recognition.points_awarded} pts
              </span>
            </div>
            
            <div className="text-sm text-gray-600 mb-3">
              <span className="font-medium">{recognition.giver_name}</span> recognized{' '}
              <span className="font-medium">{recognition.receiver_name}</span>
            </div>
            
            <p className="text-sm text-gray-700 mb-3">{recognition.message}</p>
            
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>{formatDate(recognition.date)}</span>
              {recognition.is_public && (
                <span className="inline-flex items-center">
                  <Eye className="h-4 w-4 mr-1" />
                  Public
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

// Wellness Program Card Component
interface WellnessProgramCardProps {
  program: WellnessProgram;
}

const WellnessProgramCard: React.FC<WellnessProgramCardProps> = ({ program }) => {
  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">{program.title}</h3>
          <span className={cn(
            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
            program.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          )}>
            {program.status}
          </span>
        </div>
        
        <p className="text-sm text-gray-600 mb-4">{program.description}</p>
        
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center justify-between">
            <span>Provider:</span>
            <span className="font-medium">{program.provider}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Availability:</span>
            <span className="font-medium capitalize">{program.availability.replace('_', ' ')}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Participants:</span>
            <span className="font-medium">{program.participants_count}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Rating:</span>
            <div className="flex items-center">
              <Star className="h-4 w-4 text-yellow-400 mr-1" />
              <span className="font-medium">{program.rating}</span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

// Event Card Component
interface EventCardProps {
  event: CompanyEvent;
}

const EventCard: React.FC<EventCardProps> = ({ event }) => {
  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-semibold text-gray-900">{event.title}</h3>
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                event.status === 'upcoming' ? 'bg-green-100 text-green-800' :
                event.status === 'ongoing' ? 'bg-blue-100 text-blue-800' :
                event.status === 'completed' ? 'bg-gray-100 text-gray-800' :
                'bg-red-100 text-red-800'
              )}>
                {event.status}
              </span>
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                event.type === 'social' ? 'bg-pink-100 text-pink-800' :
                event.type === 'team_building' ? 'bg-blue-100 text-blue-800' :
                event.type === 'announcement' ? 'bg-yellow-100 text-yellow-800' :
                event.type === 'celebration' ? 'bg-green-100 text-green-800' :
                'bg-purple-100 text-purple-800'
              )}>
                {event.type.replace('_', ' ')}
              </span>
            </div>
            
            <p className="text-sm text-gray-600 mb-4">{event.description}</p>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
              <div>
                <span className="font-medium">Date:</span> {formatDate(event.date)}
              </div>
              <div>
                <span className="font-medium">Location:</span> {event.location}
              </div>
              <div>
                <span className="font-medium">Attendees:</span> {event.attendees_count}
                {event.max_attendees && `/${event.max_attendees}`}
              </div>
              <div>
                <span className="font-medium">Organizer:</span> {event.organizer}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default EngagementWellnessPage;
