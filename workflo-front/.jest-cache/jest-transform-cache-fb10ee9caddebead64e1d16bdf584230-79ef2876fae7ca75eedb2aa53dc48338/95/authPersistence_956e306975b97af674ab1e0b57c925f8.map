{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/lib/authPersistence.ts"], "sourcesContent": ["/**\n * Simple auth persistence without Zustand middleware\n * This prevents hydration issues while maintaining auth state\n */\n\nimport { User } from '@/types';\n\nconst AUTH_STORAGE_KEY = 'workflo-auth';\n\ninterface PersistedAuthState {\n  user: User | null;\n  isAuthenticated: boolean;\n}\n\nexport class AuthPersistence {\n  static save(user: User | null, isAuthenticated: boolean): void {\n    if (typeof window === 'undefined') return;\n    \n    try {\n      const state: PersistedAuthState = {\n        user,\n        isAuthenticated,\n      };\n      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(state));\n    } catch (error) {\n      console.warn('Failed to save auth state:', error);\n    }\n  }\n\n  static load(): PersistedAuthState | null {\n    if (typeof window === 'undefined') return null;\n    \n    try {\n      const stored = localStorage.getItem(AUTH_STORAGE_KEY);\n      if (!stored) return null;\n      \n      return JSON.parse(stored);\n    } catch (error) {\n      console.warn('Failed to load auth state:', error);\n      return null;\n    }\n  }\n\n  static clear(): void {\n    if (typeof window === 'undefined') return;\n    \n    try {\n      localStorage.removeItem(AUTH_STORAGE_KEY);\n    } catch (error) {\n      console.warn('Failed to clear auth state:', error);\n    }\n  }\n}\n"], "names": ["AuthPersistence", "AUTH_STORAGE_KEY", "save", "user", "isAuthenticated", "window", "state", "localStorage", "setItem", "JSON", "stringify", "error", "console", "warn", "load", "stored", "getItem", "parse", "clear", "removeItem"], "mappings": "AAAA;;;CAGC;;;;+BAWYA;;;eAAAA;;;AAPb,MAAMC,mBAAmB;AAOlB,MAAMD;IACX,OAAOE,KAAKC,IAAiB,EAAEC,eAAwB,EAAQ;QAC7D,IAAI,OAAOC,WAAW,aAAa;QAEnC,IAAI;YACF,MAAMC,QAA4B;gBAChCH;gBACAC;YACF;YACAG,aAAaC,OAAO,CAACP,kBAAkBQ,KAAKC,SAAS,CAACJ;QACxD,EAAE,OAAOK,OAAO;YACdC,QAAQC,IAAI,CAAC,8BAA8BF;QAC7C;IACF;IAEA,OAAOG,OAAkC;QACvC,IAAI,OAAOT,WAAW,aAAa,OAAO;QAE1C,IAAI;YACF,MAAMU,SAASR,aAAaS,OAAO,CAACf;YACpC,IAAI,CAACc,QAAQ,OAAO;YAEpB,OAAON,KAAKQ,KAAK,CAACF;QACpB,EAAE,OAAOJ,OAAO;YACdC,QAAQC,IAAI,CAAC,8BAA8BF;YAC3C,OAAO;QACT;IACF;IAEA,OAAOO,QAAc;QACnB,IAAI,OAAOb,WAAW,aAAa;QAEnC,IAAI;YACFE,aAAaY,UAAU,CAAClB;QAC1B,EAAE,OAAOU,OAAO;YACdC,QAAQC,IAAI,CAAC,+BAA+BF;QAC9C;IACF;AACF"}