=====================================================
WORKFLO-FRONT DATABASE ERD (Entity Relationship Diagram)
=====================================================

LEGEND:
- PK = Primary Key
- FK = Foreign Key
- UK = Unique Key
- ||--o{ = One-to-Many relationship
- ||--|| = One-to-One relationship
- }o--o{ = Many-to-Many relationship

=====================================================
1. CORE AUTHENTICATION & USER MANAGEMENT
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                            USERS                                │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • phone_number                     │
│ • email (UK)                 • profile_picture                  │
│ • password_hash              • role                             │
│ • first_name                 • is_active                        │
│ • last_name                  • is_deleted                       │
│ • employee_id (UK)           • date_joined                      │
│ • created_at                 • last_login                       │
│ • updated_at                 • created_by (FK → users.id)       │
│ • deleted_at                 • updated_by (FK → users.id)       │
│ • deleted_by (FK → users.id)                                    │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │               │               │
                    ▼               ▼               ▼
        ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
        │ USER_SESSIONS   │ │PASSWORD_RESET_  │ │ EMPLOYEE_       │
        │                 │ │TOKENS           │ │ PROFILES        │
        ├─────────────────┤ ├─────────────────┤ ├─────────────────┤
        │ • id (PK)       │ │ • id (PK)       │ │ • id (PK)       │
        │ • user_id (FK)  │ │ • user_id (FK)  │ │ • user_id (FK)  │
        │ • access_token  │ │ • token (UK)    │ │ • department_id │
        │ • refresh_token │ │ • expires_at    │ │ • job_title     │
        │ • expires_at    │ │ • used          │ │ • hire_date     │
        │ • created_at    │ │ • created_at    │ │ • salary        │
        │ • last_used     │ └─────────────────┘ │ • status        │
        │ • ip_address    │                     │ • (+ 30 fields) │
        │ • user_agent    │                     └─────────────────┘
        └─────────────────┘

=====================================================
2. ORGANIZATIONAL STRUCTURE
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                         DEPARTMENTS                             │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • budget                           │
│ • name (UK)                  • location                         │
│ • description                • is_active                        │
│ • supervisor_id (FK → users.id)                                 │
│ • parent_department_id (FK → departments.id) [SELF-REFERENCE]   │
│ • created_at                 • updated_at                       │
│ • created_by (FK → users.id) • updated_by (FK → users.id)       │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      EMPLOYEE_PROFILES                         │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • nssf_number (Kenya)              │
│ • user_id (FK → users.id) UK • nhif_number (Kenya)              │
│ • department_id (FK → departments.id)                          │
│ • supervisor_id (FK → users.id)                                │
│ • job_title                  • kra_pin (Kenya)                  │
│ • hire_date                  • national_id                      │
│ • employment_type            • date_of_birth                    │
│ • work_location              • gender                           │
│ • salary                     • marital_status                   │
│ • hourly_rate                • nationality                      │
│ • currency (KSH)             • address                          │
│ • pay_frequency              • emergency_contact_name           │
│ • bank_name                  • emergency_contact_phone          │
│ • bank_account               • status                           │
│ • bank_branch                • termination_date                 │
└─────────────────────────────────────────────────────────────────┘

=====================================================
3. ATTENDANCE & TIME TRACKING
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                     ATTENDANCE_RECORDS                         │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • break_time                       │
│ • employee_id (FK → users.id)• status                          │
│ • date (UK with employee_id) • notes                           │
│ • check_in                   • biostar_synced                   │
│ • check_out                  • biostar_event_ids[]             │
│ • break_start                • created_at                       │
│ • break_end                  • updated_at                       │
│ • total_hours                                                   │
│ • regular_hours                                                 │
│ • overtime_hours                                                │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                       BIOSTAR_EVENTS                           │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • location                         │
│ • biostar_event_id (UK)      • processed                       │
│ • employee_id (FK → users.id)• attendance_record_id (FK)       │
│ • device_id                  • created_at                       │
│ • device_name                                                   │
│ • event_type (ENTRY/EXIT)                                       │
│ • event_datetime                                                │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                      BIOSTAR_DEVICES                           │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • device_type                      │
│ • biostar_device_id (UK)     • status                          │
│ • name                       • last_seen                        │
│ • ip_address                 • created_at                       │
│ • port                       • updated_at                       │
│ • location                                                      │
└─────────────────────────────────────────────────────────────────┘

=====================================================
4. LEAVE MANAGEMENT
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                        LEAVE_TYPES                             │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • requires_approval                │
│ • name (UK)                  • is_paid                          │
│ • description                • is_active                        │
│ • max_days_per_year          • created_at                       │
│ • carry_forward_allowed                                         │
│ • max_carry_forward_days                                        │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │ ||--o{        │ ||--o{        │
                    ▼               ▼               │
        ┌─────────────────┐ ┌─────────────────┐     │
        │ LEAVE_BALANCES  │ │ LEAVE_          │     │
        │                 │ │ APPLICATIONS    │     │
        ├─────────────────┤ ├─────────────────┤     │
        │ • id (PK)       │ │ • id (PK)       │     │
        │ • employee_id   │ │ • employee_id   │     │
        │ • leave_type_id │ │ • leave_type_id │     │
        │ • year          │ │ • start_date    │     │
        │ • allocated_days│ │ • end_date      │     │
        │ • used_days     │ │ • days_requested│     │
        │ • pending_days  │ │ • reason        │     │
        │ • remaining_days│ │ • status        │     │
        │   (COMPUTED)    │ │ • approved_by   │     │
        └─────────────────┘ │ • approved_at   │     │
                            │ • reviewer_     │     │
                            │   comments      │     │
                            └─────────────────┘     │
                                                    │
                                                    ▼
                                        ┌─────────────────┐
                                        │ COMPANY_        │
                                        │ HOLIDAYS        │
                                        ├─────────────────┤
                                        │ • id (PK)       │
                                        │ • name          │
                                        │ • date          │
                                        │ • description   │
                                        │ • is_recurring  │
                                        │ • applies_to_all│
                                        │ • department_ids│
                                        │ • created_by    │
                                        └─────────────────┘

=====================================================
5. PAYROLL SYSTEM
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                         PAY_CYCLES                              │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • total_net_amount                 │
│ • pay_period                 • total_deductions                 │
│ • start_date                 • notes                            │
│ • end_date                   • created_at                       │
│ • pay_date                   • updated_at                       │
│ • status                     • created_by (FK → users.id)       │
│ • total_employees            • processed_by (FK → users.id)     │
│ • total_gross_amount         • processed_at                     │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │ ||--o{        │ ||--o{        │
                    ▼               ▼               │
        ┌─────────────────┐ ┌─────────────────┐     │
        │ PAYROLL_RECORDS │ │ SALARY_         │     │
        │                 │ │ ADJUSTMENTS     │     │
        ├─────────────────┤ ├─────────────────┤     │
        │ • id (PK)       │ │ • id (PK)       │     │
        │ • employee_id   │ │ • employee_id   │     │
        │ • pay_cycle_id  │ │ • pay_cycle_id  │     │
        │ • basic_salary  │ │ • adjustment_   │     │
        │ • allowances    │ │   type          │     │
        │ • overtime_amt  │ │ • category      │     │
        │ • bonuses       │ │ • amount        │     │
        │ • gross_salary  │ │ • description   │     │
        │ • tax_deduction │ │ • is_recurring  │     │
        │ • nssf_deduction│ │ • effective_date│     │
        │ • nhif_deduction│ │ • end_date      │     │
        │ • housing_levy  │ └─────────────────┘     │
        │ • net_salary    │                         │
        │ • payment_status│                         │
        └─────────────────┘                         │
                                                    │
                                                    ▼
                                        ┌─────────────────┐
                                        │ EMPLOYEE_       │
                                        │ BENEFITS        │
                                        ├─────────────────┤
                                        │ • id (PK)       │
                                        │ • employee_id   │
                                        │ • benefit_type  │
                                        │ • benefit_name  │
                                        │ • value_type    │
                                        │ • value         │
                                        │ • provider      │
                                        │ • start_date    │
                                        │ • end_date      │
                                        │ • status        │
                                        │ • employee_     │
                                        │   contribution  │
                                        │ • employer_     │
                                        │   contribution  │
                                        └─────────────────┘

=====================================================
6. PERFORMANCE MANAGEMENT
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                PERFORMANCE_REVIEW_TEMPLATES                     │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • rating_scale                     │
│ • name                       • is_active                        │
│ • description                • created_at                       │
│ • review_type                • created_by (FK → users.id)       │
│ • criteria (JSONB)                                              │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                     PERFORMANCE_REVIEWS                        │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • reviewer_comments                │
│ • employee_id (FK → users.id)• employee_comments               │
│ • reviewer_id (FK → users.id)• goals_for_next_period           │
│ • template_id (FK)           • status                          │
│ • review_period_start        • employee_acknowledged           │
│ • review_period_end          • employee_acknowledged_at        │
│ • review_type                • due_date                        │
│ • overall_rating             • completed_date                  │
│ • technical_skills           • next_review_date                │
│ • communication              • created_at                      │
│ • teamwork                   • updated_at                      │
│ • leadership                                                   │
│ • problem_solving                                              │
│ • time_management                                              │
│ • strengths                                                    │
│ • areas_for_improvement                                        │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      PERFORMANCE_GOALS                         │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • progress_percentage              │
│ • employee_id (FK → users.id)• completion_date                 │
│ • review_id (FK)             • notes                           │
│ • title                      • created_at                      │
│ • description                • updated_at                      │
│ • target_date                • created_by (FK → users.id)      │
│ • priority                                                     │
│ • status                                                       │
└─────────────────────────────────────────────────────────────────┘

=====================================================
7. TRAINING & DEVELOPMENT
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                      TRAINING_MODULES                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • instructor_email                 │
│ • title                      • materials_url                    │
│ • description                • is_mandatory                     │
│ • content                    • is_active                        │
│ • duration_hours             • created_at                       │
│ • difficulty_level           • updated_at                       │
│ • category                   • created_by (FK → users.id)       │
│ • prerequisites                                                 │
│ • learning_objectives                                           │
│ • instructor_name                                               │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │ ||--o{        │ ||--o{        │
                    ▼               ▼               │
        ┌─────────────────┐ ┌─────────────────┐     │
        │ EMPLOYEE_       │ │ TRAINING_       │     │
        │ TRAINING_       │ │ SESSIONS        │     │
        │ ASSIGNMENTS     │ │                 │     │
        ├─────────────────┤ ├─────────────────┤     │
        │ • id (PK)       │ │ • id (PK)       │     │
        │ • employee_id   │ │ • training_     │     │
        │ • training_     │ │   module_id     │     │
        │   module_id     │ │ • venue_id      │     │
        │ • venue_id      │ │ • instructor_id │     │
        │ • assigned_by   │ │ • title         │     │
        │ • assigned_date │ │ • start_datetime│     │
        │ • start_date    │ │ • end_datetime  │     │
        │ • end_date      │ │ • max_          │     │
        │ • due_date      │ │   participants  │     │
        │ • status        │ │ • current_      │     │
        │ • progress_%    │ │   participants  │     │
        │ • completion_   │ │ • status        │     │
        │   date          │ │ • notes         │     │
        │ • score         │ │ • created_at    │     │
        │ • feedback      │ │ • created_by    │     │
        │ • certificate_ │ └─────────────────┘     │
        │   url           │           │             │
        └─────────────────┘           │ ||--o{      │
                                      ▼             │
                            ┌─────────────────┐     │
                            │ TRAINING_       │     │
                            │ SESSION_        │     │
                            │ PARTICIPANTS    │     │
                            ├─────────────────┤     │
                            │ • id (PK)       │     │
                            │ • session_id    │     │
                            │ • employee_id   │     │
                            │ • attendance_   │     │
                            │   status        │     │
                            │ • completion_   │     │
                            │   status        │     │
                            │ • score         │     │
                            │ • feedback      │     │
                            │ • certificate_  │     │
                            │   issued        │     │
                            │ • registered_at │     │
                            └─────────────────┘     │
                                                    │
                                                    ▼
                                        ┌─────────────────┐
                                        │ TRAINING_VENUES │
                                        ├─────────────────┤
                                        │ • id (PK)       │
                                        │ • name          │
                                        │ • location      │
                                        │ • address       │
                                        │ • capacity      │
                                        │ • equipment[]   │
                                        │ • facilities[]  │
                                        │ • hourly_rate   │
                                        │ • contact_person│
                                        │ • contact_phone │
                                        │ • contact_email │
                                        │ • status        │
                                        │ • created_at    │
                                        │ • updated_at    │
                                        └─────────────────┘

=====================================================
8. RECRUITMENT & JOB MANAGEMENT
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                        JOB_POSTINGS                             │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • application_deadline             │
│ • title                      • status                           │
│ • department_id (FK)         • positions_available             │
│ • description                • positions_filled                │
│ • requirements               • priority                         │
│ • responsibilities           • hiring_manager_id (FK)           │
│ • qualifications             • created_at                       │
│ • salary_min                 • updated_at                       │
│ • salary_max                 • created_by (FK → users.id)       │
│ • currency                                                      │
│ • location                                                      │
│ • employment_type                                               │
│ • experience_level                                              │
│ • remote_allowed                                                │
│ • travel_required                                               │
│ • posted_date                                                   │
│ • closing_date                                                  │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      JOB_APPLICATIONS                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • visa_status                      │
│ • job_posting_id (FK)        • referral_source                 │
│ • first_name                 • status                          │
│ • last_name                  • stage                           │
│ • email                      • rating                          │
│ • phone_number               • notes                           │
│ • address                    • applied_date                    │
│ • city                       • last_updated                    │
│ • country                    • assigned_recruiter_id (FK)      │
│ • resume_url                                                   │
│ • cover_letter                                                 │
│ • portfolio_url                                                │
│ • linkedin_profile                                             │
│ • expected_salary                                              │
│ • available_start_date                                         │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │ ||--o{        │ ||--o{        │
                    ▼               ▼               │
        ┌─────────────────┐ ┌─────────────────┐     │
        │ INTERVIEW_      │ │ CANDIDATE_      │     │
        │ SCHEDULES       │ │ EVALUATIONS     │     │
        ├─────────────────┤ ├─────────────────┤     │
        │ • id (PK)       │ │ • id (PK)       │     │
        │ • application_id│ │ • application_id│     │
        │ • interview_type│ │ • interview_id  │     │
        │ • interviewer_  │ │ • evaluator_id  │     │
        │   ids[]         │ │ • evaluation_   │     │
        │ • scheduled_    │ │   criteria      │     │
        │   datetime      │ │   (JSONB)       │     │
        │ • duration_     │ │ • overall_rating│     │
        │   minutes       │ │ • technical_    │     │
        │ • location      │ │   skills        │     │
        │ • meeting_link  │ │ • communication_│     │
        │ • agenda        │ │   skills        │     │
        │ • status        │ │ • cultural_fit  │     │
        │ • feedback      │ │ • experience_   │     │
        │ • rating        │ │   relevance     │     │
        │ • recommendation│ │ • strengths     │     │
        │ • next_steps    │ │ • concerns      │     │
        │ • created_at    │ │ • recommendation│     │
        │ • updated_at    │ │ • comments      │     │
        │ • created_by    │ │ • created_at    │     │
        └─────────────────┘ └─────────────────┘     │
                    │                               │
                    └───────────────────────────────┘
                            interview_id (FK)

=====================================================
9. DOCUMENT MANAGEMENT
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                    DOCUMENT_CATEGORIES                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • retention_period_months          │
│ • name (UK)                  • parent_category_id (FK)          │
│ • description                  [SELF-REFERENCE]                 │
│ • is_employee_accessible     • created_at                       │
│ • is_mandatory                                                  │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │ ||--o{        │ ||--o{        │
                    ▼               ▼               │
        ┌─────────────────┐ ┌─────────────────┐     │
        │ EMPLOYEE_       │ │ COMPANY_        │     │
        │ DOCUMENTS       │ │ DOCUMENTS       │     │
        ├─────────────────┤ ├─────────────────┤     │
        │ • id (PK)       │ │ • id (PK)       │     │
        │ • employee_id   │ │ • category_id   │     │
        │ • category_id   │ │ • title         │     │
        │ • document_name │ │ • description   │     │
        │ • document_type │ │ • file_path     │     │
        │ • file_path     │ │ • file_size     │     │
        │ • file_size     │ │ • file_type     │     │
        │ • file_type     │ │ • version       │     │
        │ • description   │ │ • is_public     │     │
        │ • is_confidential│ │ • requires_     │     │
        │ • expiry_date   │ │   acknowledgment│     │
        │ • version       │ │ • effective_date│     │
        │ • status        │ │ • expiry_date   │     │
        │ • uploaded_by   │ │ • department_   │     │
        │ • approved_by   │ │   ids[]         │     │
        │ • approved_at   │ │ • role_access[] │     │
        │ • created_at    │ │ • status        │     │
        │ • updated_at    │ │ • created_at    │     │
        └─────────────────┘ │ • updated_at    │     │
                            │ • created_by    │     │
                            │ • approved_by   │     │
                            │ • approved_at   │     │
                            └─────────────────┘     │
                                      │             │
                                      │ ||--o{      │
                                      ▼             │
                            ┌─────────────────┐     │
                            │ DOCUMENT_       │     │
                            │ ACKNOWLEDGMENTS │     │
                            ├─────────────────┤     │
                            │ • id (PK)       │     │
                            │ • document_id   │     │
                            │ • employee_id   │     │
                            │ • acknowledged_ │     │
                            │   at            │     │
                            │ • ip_address    │     │
                            │ • user_agent    │     │
                            │ • digital_      │     │
                            │   signature     │     │
                            └─────────────────┘     │
                                                    │
                                                    │
=====================================================
10. NOTIFICATIONS & COMMUNICATION
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                   NOTIFICATION_TEMPLATES                       │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • notification_channels[]          │
│ • name (UK)                  • recipient_roles[]               │
│ • description                • is_active                        │
│ • event_type                 • created_at                       │
│ • subject_template           • updated_at                       │
│ • body_template                                                 │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                       NOTIFICATIONS                            │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • channels[]                       │
│ • recipient_id (FK → users.id)• is_read                        │
│ • sender_id (FK → users.id)  • read_at                         │
│ • template_id (FK)           • action_url                      │
│ • title                      • action_label                    │
│ • message                    • metadata (JSONB)                │
│ • notification_type          • expires_at                      │
│ • category                   • created_at                      │
│ • priority                                                     │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                         EMAIL_LOGS                             │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • provider_message_id              │
│ • notification_id (FK)       • error_message                   │
│ • recipient_email            • sent_at                         │
│ • sender_email               • delivered_at                    │
│ • subject                    • opened_at                       │
│ • body                       • clicked_at                      │
│ • status                     • created_at                      │
└─────────────────────────────────────────────────────────────────┘

=====================================================
11. AUDIT LOGS & SYSTEM TRACKING
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                         AUDIT_LOGS                             │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • user_agent                       │
│ • user_id (FK → users.id)    • session_id                      │
│ • action                     • description                      │
│ • table_name                 • created_at                       │
│ • record_id                                                     │
│ • old_values (JSONB)                                            │
│ • new_values (JSONB)                                            │
│ • ip_address                                                    │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                       ACTIVITY_LOGS                            │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • metadata (JSONB)                 │
│ • user_id (FK → users.id)    • ip_address                      │
│ • activity_type              • user_agent                       │
│ • activity_description       • duration_seconds                │
│ • module                     • created_at                       │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                       SYSTEM_SETTINGS                          │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • category                         │
│ • setting_key (UK)           • is_public                        │
│ • setting_value              • is_editable                      │
│ • setting_type               • created_at                       │
│ • description                • updated_at                       │
│                              • updated_by (FK → users.id)       │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                        COMPANY_INFO                            │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • industry                         │
│ • company_name               • company_size                     │
│ • company_logo               • founded_year                     │
│ • address                    • description                      │
│ • city                       • mission_statement                │
│ • state                      • vision_statement                 │
│ • postal_code                • values                           │
│ • country                    • timezone                         │
│ • phone_number               • currency                         │
│ • email                      • fiscal_year_start_month          │
│ • website                    • working_days_per_week            │
│ • tax_id                     • working_hours_per_day            │
│ • registration_number        • overtime_threshold_hours         │
│                              • created_at                       │
│                              • updated_at                       │
│                              • updated_by (FK → users.id)       │
└─────────────────────────────────────────────────────────────────┘

=====================================================
12. REPORTS & ANALYTICS
=====================================================

┌─────────────────────────────────────────────────────────────────┐
│                        SAVED_REPORTS                           │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • recipients[]                     │
│ • name                       • is_active                        │
│ • description                • last_generated                   │
│ • report_type                • next_generation                  │
│ • parameters (JSONB)         • created_at                       │
│ • schedule_frequency         • updated_at                       │
│ • schedule_day               • created_by (FK → users.id)       │
│ • schedule_time                                                 │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ ||--o{
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                       REPORT_HISTORY                           │
├─────────────────────────────────────────────────────────────────┤
│ • id (PK)                    • parameters_used (JSONB)          │
│ • saved_report_id (FK)       • status                          │
│ • generated_by (FK → users.id)• error_message                  │
│ • file_path                  • created_at                       │
│ • file_size                                                     │
│ • generation_time_seconds                                       │
└─────────────────────────────────────────────────────────────────┘

=====================================================
13. KEY RELATIONSHIPS SUMMARY
=====================================================

CENTRAL HUB: USERS TABLE
┌─────────────────────────────────────────────────────────────────┐
│                         USERS (Central Entity)                 │
│                                                                 │
│  Connected to ALL major entities through foreign keys:         │
│                                                                 │
│  • user_sessions (1:M)           • performance_reviews (1:M)   │
│  • password_reset_tokens (1:M)   • performance_goals (1:M)     │
│  • employee_profiles (1:1)       • training_assignments (1:M)  │
│  • departments (1:M supervisor)  • training_sessions (1:M)     │
│  • attendance_records (1:M)      • training_participants (1:M) │
│  • biostar_events (1:M)          • job_postings (1:M)          │
│  • leave_balances (1:M)          • job_applications (1:M)      │
│  • leave_applications (1:M)      • interview_schedules (1:M)   │
│  • pay_cycles (1:M)              • candidate_evaluations (1:M) │
│  • payroll_records (1:M)         • employee_documents (1:M)    │
│  • salary_adjustments (1:M)      • company_documents (1:M)     │
│  • employee_benefits (1:M)       • document_acknowledgments(1:M)│
│                                  • notifications (1:M)         │
│                                  • audit_logs (1:M)            │
│                                  • activity_logs (1:M)         │
│                                  • saved_reports (1:M)         │
│                                  • report_history (1:M)        │
└─────────────────────────────────────────────────────────────────┘

MAJOR RELATIONSHIP PATTERNS:
═══════════════════════════════════════════════════════════════════

1. ONE-TO-ONE RELATIONSHIPS:
   • users ||--|| employee_profiles (extends user data)

2. ONE-TO-MANY RELATIONSHIPS (Most Common):
   • users ||--o{ [most tables] (user as owner/creator)
   • departments ||--o{ employee_profiles
   • leave_types ||--o{ leave_balances
   • leave_types ||--o{ leave_applications
   • pay_cycles ||--o{ payroll_records
   • training_modules ||--o{ employee_training_assignments
   • job_postings ||--o{ job_applications
   • document_categories ||--o{ employee_documents
   • document_categories ||--o{ company_documents
   • notification_templates ||--o{ notifications
   • saved_reports ||--o{ report_history

3. SELF-REFERENCING RELATIONSHIPS:
   • departments.parent_department_id → departments.id
   • document_categories.parent_category_id → document_categories.id
   • users.created_by → users.id (audit trail)
   • users.updated_by → users.id (audit trail)

4. MANY-TO-MANY RELATIONSHIPS (via Junction Tables):
   • training_sessions }o--o{ users (via training_session_participants)
   • company_documents }o--o{ users (via document_acknowledgments)

5. COMPLEX RELATIONSHIPS:
   • job_applications → interview_schedules → candidate_evaluations
   • attendance_records ← biostar_events (BioStar integration)
   • performance_reviews → performance_goals (goal tracking)

=====================================================
14. SPECIAL FEATURES & NOTES
=====================================================

KENYAN COMPLIANCE FIELDS:
• employee_profiles.nssf_number (NSSF registration)
• employee_profiles.nhif_number (NHIF registration)
• employee_profiles.kra_pin (Kenya Revenue Authority PIN)
• employee_profiles.national_id (National ID number)
• payroll_records.nssf_deduction (6% NSSF deduction)
• payroll_records.nhif_deduction (2.75% NHIF/SHA deduction)
• payroll_records.housing_levy (1.5% Housing Levy)
• Default currency: KSH (Kenyan Shilling)

BIOSTAR INTEGRATION:
• biostar_devices (device management)
• biostar_events (biometric events)
• attendance_records.biostar_synced (sync status)
• attendance_records.biostar_event_ids[] (linked events)

FLEXIBLE DATA STRUCTURES:
• performance_review_templates.criteria (JSONB)
• candidate_evaluations.evaluation_criteria (JSONB)
• notifications.metadata (JSONB)
• audit_logs.old_values/new_values (JSONB)
• saved_reports.parameters (JSONB)

ARRAY FIELDS:
• company_holidays.department_ids[] (department targeting)
• company_documents.role_access[] (role-based access)
• interview_schedules.interviewer_ids[] (multiple interviewers)
• training_venues.equipment[] (equipment list)
• training_venues.facilities[] (facility list)
• notification_templates.notification_channels[] (multi-channel)
• notification_templates.recipient_roles[] (role targeting)

AUDIT TRAIL:
• Most tables include: created_at, updated_at, created_by, updated_by
• audit_logs table tracks all changes
• activity_logs table tracks user activities
• Soft delete support with deleted_at, deleted_by fields

COMPUTED FIELDS:
• leave_balances.remaining_days (auto-calculated)

UNIQUE CONSTRAINTS:
• users.email, users.employee_id
• attendance_records(employee_id, date)
• leave_balances(employee_id, leave_type_id, year)
• payroll_records(employee_id, pay_cycle_id)
• employee_training_assignments(employee_id, training_module_id)
• training_session_participants(session_id, employee_id)
• document_acknowledgments(document_id, employee_id)

=====================================================
END OF ERD DIAGRAM
=====================================================
