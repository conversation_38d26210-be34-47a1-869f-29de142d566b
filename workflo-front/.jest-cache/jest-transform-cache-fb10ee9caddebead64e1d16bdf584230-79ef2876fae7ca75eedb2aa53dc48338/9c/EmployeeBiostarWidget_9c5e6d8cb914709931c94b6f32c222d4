1c7211f20ed8226d13300229c7b5e1b0
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _link = /*#__PURE__*/ _interop_require_default(require("next/link"));
const _lucidereact = require("lucide-react");
const _Card = /*#__PURE__*/ _interop_require_default(require("../ui/Card"));
const _Button = /*#__PURE__*/ _interop_require_default(require("../ui/Button"));
const _utils = require("../../lib/utils");
const _useBiostarAttendance = require("../../hooks/useBiostarAttendance");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const EmployeeBiostarWidget = ({ employeeId, showFullDetails = false, className })=>{
    const [recentEvents, setRecentEvents] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(true);
    const { todayAttendance, devices, connected: biostarConnected, error: connectionError, summary } = (0, _useBiostarAttendance.useBiostarAttendance)({
        employeeId,
        autoRefresh: true,
        enableRealTime: true
    });
    (0, _react.useEffect)(()=>{
        // Simulate loading recent events
        setLoading(false);
        // Mock recent events - in real app, this would come from the hook
        setRecentEvents([
            {
                id: '1',
                user_id: employeeId || '',
                device_id: 'dev-001',
                event_type: 'ENTRY',
                datetime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                user_name: 'Current User',
                device_name: 'Main Entrance'
            },
            {
                id: '2',
                user_id: employeeId || '',
                device_id: 'dev-002',
                event_type: 'EXIT',
                datetime: new Date(Date.now() - 10 * 60 * 60 * 1000).toISOString(),
                user_name: 'Current User',
                device_name: 'Main Entrance'
            }
        ]);
    }, [
        employeeId
    ]);
    const getEventTypeIcon = (eventType)=>{
        switch(eventType){
            case 'ENTRY':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                    className: "h-4 w-4 text-green-500"
                });
            case 'EXIT':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Activity, {
                    className: "h-4 w-4 text-blue-500"
                });
            case 'DENIED':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertCircle, {
                    className: "h-4 w-4 text-red-500"
                });
            default:
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                    className: "h-4 w-4 text-gray-500"
                });
        }
    };
    const getEventTypeColor = (eventType)=>{
        switch(eventType){
            case 'ENTRY':
                return 'bg-green-100 text-green-800';
            case 'EXIT':
                return 'bg-blue-100 text-blue-800';
            case 'DENIED':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    if (loading) {
        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
            className: className,
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "p-6",
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "flex items-center justify-center",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.RefreshCw, {
                            className: "h-6 w-6 animate-spin text-gray-400"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                            className: "ml-2 text-gray-600",
                            children: "Loading BioStar data..."
                        })
                    ]
                })
            })
        });
    }
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
        className: className,
        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            className: "p-6",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "flex items-center justify-between mb-4",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "p-2 bg-purple-100 rounded-lg",
                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Fingerprint, {
                                        className: "h-5 w-5 text-purple-600"
                                    })
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                            className: "text-lg font-medium text-gray-900",
                                            children: "BioStar Profile"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                biostarConnected ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Wifi, {
                                                    className: "h-3 w-3 text-green-500"
                                                }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.WifiOff, {
                                                    className: "h-3 w-3 text-red-500"
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                    className: "text-xs text-gray-600",
                                                    children: biostarConnected ? 'Connected' : 'Disconnected'
                                                })
                                            ]
                                        })
                                    ]
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_link.default, {
                            href: "/staff/info/biostar",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_Button.default, {
                                variant: "secondary",
                                size: "sm",
                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.ArrowRight, {
                                    className: "h-4 w-4"
                                })
                            })
                        })
                    ]
                }),
                connectionError && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "mb-4 p-3 bg-red-50 border border-red-200 rounded-lg",
                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "flex items-center",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertCircle, {
                                className: "h-4 w-4 text-red-500 mr-2"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                className: "text-sm text-red-700",
                                children: "Connection Error"
                            })
                        ]
                    })
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "grid grid-cols-2 gap-4 mb-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "text-center p-3 bg-gray-50 rounded-lg",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "flex items-center justify-center mb-1",
                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Fingerprint, {
                                        className: "h-4 w-4 text-blue-600"
                                    })
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "text-lg font-bold text-gray-900",
                                    children: "2"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "text-xs text-gray-600",
                                    children: "Fingerprints"
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "text-center p-3 bg-gray-50 rounded-lg",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "flex items-center justify-center mb-1",
                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Eye, {
                                        className: "h-4 w-4 text-purple-600"
                                    })
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "text-lg font-bold text-gray-900",
                                    children: "1"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "text-xs text-gray-600",
                                    children: "Face Template"
                                })
                            ]
                        })
                    ]
                }),
                todayAttendance && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "mb-6 p-3 border border-gray-200 rounded-lg",
                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                        className: "font-medium text-gray-900",
                                        children: "Today's Status"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                        className: "text-sm text-gray-600",
                                        children: todayAttendance.first_in ? `Checked in at ${new Date(todayAttendance.first_in).toLocaleTimeString([], {
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        })}` : 'Not checked in'
                                    })
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                className: (0, _utils.cn)('inline-flex items-center px-2 py-1 rounded-full text-xs font-medium', todayAttendance.status === 'PRESENT' ? 'bg-green-100 text-green-800' : todayAttendance.status === 'LATE' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'),
                                children: todayAttendance.status
                            })
                        ]
                    })
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "space-y-3",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                            className: "font-medium text-gray-900",
                            children: "Recent Access"
                        }),
                        recentEvents.length === 0 ? /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                            className: "text-sm text-gray-600 italic",
                            children: "No recent access events"
                        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "space-y-2",
                            children: recentEvents.slice(0, showFullDetails ? 5 : 2).map((event, index)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between p-2 bg-gray-50 rounded",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                getEventTypeIcon(event.event_type),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "text-sm font-medium text-gray-900",
                                                            children: event.event_type === 'ENTRY' ? 'Check In' : event.event_type === 'EXIT' ? 'Check Out' : 'Access Denied'
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                            className: "text-xs text-gray-600",
                                                            children: (0, _utils.formatDate)(event.datetime, 'MMM dd, HH:mm')
                                                        })
                                                    ]
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: (0, _utils.cn)('inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium', getEventTypeColor(event.event_type)),
                                            children: event.event_type
                                        })
                                    ]
                                }, `${event.id}-${index}`))
                        })
                    ]
                }),
                devices.length > 0 && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "mt-4 pt-4 border-t border-gray-200",
                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                className: "text-sm text-gray-600",
                                children: "Devices Online"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center space-x-1",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Monitor, {
                                        className: "h-3 w-3 text-gray-500"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                                        className: "text-sm font-medium",
                                        children: [
                                            devices.filter((d)=>d.status === 'ONLINE').length,
                                            "/",
                                            devices.length
                                        ]
                                    })
                                ]
                            })
                        ]
                    })
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "mt-4 pt-4 border-t border-gray-200",
                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_link.default, {
                        href: "/staff/info/biostar",
                        className: "block w-full text-center py-2 text-sm text-purple-600 hover:text-purple-700 font-medium",
                        children: "View Full BioStar Profile →"
                    })
                })
            ]
        })
    });
};
const _default = EmployeeBiostarWidget;

//# sourceMappingURL=data:application/json;base64,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