'use client';

import React, { useState } from 'react';
import SupervisorEnhancedSidebar from '@/components/layout/SupervisorEnhancedSidebar';
import { Menu, X, Eye, Settings, Info } from 'lucide-react';

const SupervisorSidebarDemo: React.FC = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Demo Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={toggleSidebar}
                className="p-2 rounded-lg text-gray-600 hover:bg-gray-100 transition-colors"
              >
                <Menu className="h-6 w-6" />
              </button>
              <h1 className="text-xl font-semibold text-gray-900">
                Enhanced Supervisor Sidebar Demo
              </h1>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">Demo Mode</span>
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex relative">
        {/* Enhanced Sidebar */}
        <SupervisorEnhancedSidebar isOpen={isSidebarOpen} onClose={closeSidebar} />

        {/* Main Content */}
        <main className="flex-1 min-h-[calc(100vh-64px)] w-full overflow-x-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="space-y-8">
              {/* Demo Introduction */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <Info className="h-8 w-8 text-blue-500" />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">
                      Enhanced Dynamic Sidebar for Supervisors
                    </h2>
                    <p className="text-gray-600 mb-4">
                      This enhanced sidebar provides a comprehensive navigation experience for supervisors with the following features:
                    </p>
                    <ul className="space-y-2 text-gray-600">
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span><strong>Dynamic Navigation:</strong> Automatically highlights active pages and sections</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span><strong>Quick Actions:</strong> Priority-based action items with badges and notifications</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span><strong>Search Functionality:</strong> Search through navigation items and descriptions</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span><strong>Team Overview:</strong> Real-time team statistics and pending actions</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        <span><strong>Recent Activity:</strong> Timeline of recent team activities and approvals</span>
                      </li>
                      <li className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span><strong>View Modes:</strong> Switch between list and grid views for different layouts</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Feature Highlights */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Eye className="h-5 w-5 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">Smart Navigation</h3>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Hierarchical navigation with expandable sections, active state highlighting, and contextual badges for pending actions.
                  </p>
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <Settings className="h-5 w-5 text-green-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Priority-based action items with color-coded badges, direct links to approval workflows, and real-time updates.
                  </p>
                </div>

                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Info className="h-5 w-5 text-purple-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">Team Insights</h3>
                  </div>
                  <p className="text-gray-600 text-sm">
                    Real-time team statistics, attendance overview, pending approvals, and recent activity timeline.
                  </p>
                </div>
              </div>

              {/* Usage Instructions */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">How to Use</h3>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                    <div>
                      <p className="font-medium text-gray-900">Open the Sidebar</p>
                      <p className="text-gray-600 text-sm">Click the menu button in the top-left corner to open the enhanced sidebar.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
                    <div>
                      <p className="font-medium text-gray-900">Explore Quick Actions</p>
                      <p className="text-gray-600 text-sm">Use the priority-based quick actions at the top for immediate access to pending tasks.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
                    <div>
                      <p className="font-medium text-gray-900">Navigate Sections</p>
                      <p className="text-gray-600 text-sm">Expand navigation sections to access all supervisor features and team management tools.</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">4</div>
                    <div>
                      <p className="font-medium text-gray-900">Monitor Team Status</p>
                      <p className="text-gray-600 text-sm">Check the team overview section for real-time statistics and pending actions.</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Technical Details */}
              <div className="bg-gray-50 rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Technical Implementation</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Features</h4>
                    <ul className="space-y-1 text-sm text-gray-600">
                      <li>• Responsive design with mobile-first approach</li>
                      <li>• TypeScript for type safety</li>
                      <li>• Tailwind CSS for styling</li>
                      <li>• Lucide React icons</li>
                      <li>• Next.js routing integration</li>
                      <li>• State management with React hooks</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Components</h4>
                    <ul className="space-y-1 text-sm text-gray-600">
                      <li>• SupervisorEnhancedSidebar.tsx</li>
                      <li>• SupervisorSidebarRouter.tsx</li>
                      <li>• SupervisorLayout.tsx</li>
                      <li>• Integrated with auth system</li>
                      <li>• Dynamic route highlighting</li>
                      <li>• Collapsible sections</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default SupervisorSidebarDemo;
