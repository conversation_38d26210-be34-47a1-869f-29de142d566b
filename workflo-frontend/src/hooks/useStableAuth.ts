'use client';

import { useEffect, useState, useRef } from 'react';
import { useAuth } from '@/store/authStore';

interface StableAuthState {
  isAuthenticated: boolean;
  user: any;
  isLoading: boolean;
  isHydrated: boolean;
  isInitialized: boolean;
  error: string | null;
}

/**
 * A stable auth hook that prevents unnecessary re-renders and flickering
 * by maintaining consistent state during transitions
 */
export const useStableAuth = () => {
  const auth = useAuth();
  const [stableState, setStableState] = useState<StableAuthState>({
    isAuthenticated: false,
    user: null,
    isLoading: true,
    isHydrated: false,
    isInitialized: false,
    error: null,
  });

  const prevAuthRef = useRef(auth);
  const initializationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const prevAuth = prevAuthRef.current;
    const currentAuth = auth;

    // Only update state if there are meaningful changes
    const hasChanged =
      prevAuth.isAuthenticated !== currentAuth.isAuthenticated ||
      prevAuth.user?.id !== currentAuth.user?.id ||
      prevAuth.isLoading !== currentAuth.isLoading ||
      prevAuth.isHydrated !== currentAuth.isHydrated ||
      prevAuth.error !== currentAuth.error;

    if (hasChanged) {
      setStableState(prev => ({
        ...prev,
        isAuthenticated: currentAuth.isAuthenticated,
        user: currentAuth.user,
        isLoading: currentAuth.isLoading,
        isHydrated: currentAuth.isHydrated,
        error: currentAuth.error,
      }));

      prevAuthRef.current = currentAuth;
    }

    // Set initialized state after hydration and initial auth check
    if (currentAuth.isHydrated && !stableState.isInitialized) {
      if (initializationTimeoutRef.current) {
        clearTimeout(initializationTimeoutRef.current);
      }

      initializationTimeoutRef.current = setTimeout(() => {
        setStableState(prev => ({
          ...prev,
          isInitialized: true,
        }));
      }, 100); // Small delay to ensure auth state is stable
    }

    return () => {
      if (initializationTimeoutRef.current) {
        clearTimeout(initializationTimeoutRef.current);
      }
    };
  }, [auth, stableState.isInitialized]);

  return {
    ...stableState,
    login: auth.login,
    logout: auth.logout,
    getCurrentUser: auth.getCurrentUser,
    clearError: auth.clearError,
  };
};
