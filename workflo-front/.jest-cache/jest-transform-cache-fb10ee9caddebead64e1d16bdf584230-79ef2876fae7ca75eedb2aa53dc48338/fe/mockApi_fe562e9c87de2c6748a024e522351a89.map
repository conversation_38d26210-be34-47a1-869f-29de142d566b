{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/lib/mockApi.ts"], "sourcesContent": ["import { AuthTokens, User, Employee, Department, LeaveApplication } from '@/types';\n\n// Mock data\nconst mockUsers: User[] = [\n  {\n    id: 1,\n    email: '<EMAIL>',\n    first_name: 'Admin',\n    last_name: 'User',\n    employee_id: 'ADM001',\n    role: 'hr',\n    is_active: true,\n    date_joined: '2023-01-01',\n    department: {\n      id: 1,\n      name: 'Human Resources',\n      description: 'HR Department',\n      created_at: '2023-01-01',\n      updated_at: '2023-01-01',\n    },\n  },\n  {\n    id: 2,\n    email: '<EMAIL>',\n    first_name: '<PERSON>',\n    last_name: '<PERSON><PERSON>',\n    employee_id: 'EMP001',\n    role: 'employee',\n    is_active: true,\n    date_joined: '2023-02-01',\n    department: {\n      id: 2,\n      name: 'Engineering',\n      description: 'Engineering Department',\n      created_at: '2023-01-01',\n      updated_at: '2023-01-01',\n    },\n  },\n];\n\nconst mockEmployees: Employee[] = [\n  {\n    ...mockUsers[1],\n    job_title: 'Software Engineer',\n    position: 'Software Engineer',\n    department_id: 2,\n    department_name: 'Engineering',\n    hire_date: '2023-02-01',\n    employment_type: 'full_time',\n    work_location: 'office',\n    salary: 75000,\n    hourly_rate: 36,\n    currency: 'USD',\n    pay_frequency: 'monthly',\n    bank_name: 'Chase Bank',\n    bank_account: '****1234',\n    nssf_number: 'NSSF001',\n    nhif_number: 'NHIF001',\n    kra_pin: 'KRA001',\n    national_id: '********',\n    date_of_birth: '1990-05-15',\n    gender: 'male',\n    marital_status: 'single',\n    nationality: 'American',\n    address: '123 Main St',\n    city: 'San Francisco',\n    state: 'CA',\n    postal_code: '94105',\n    country: 'USA',\n    emergency_contact_name: 'Jane Doe',\n    emergency_contact_phone: '******-0199',\n    emergency_contact_relationship: 'Sister',\n    status: 'active',\n    is_deleted: false,\n    created_by: 1,\n    updated_by: 1,\n  },\n  {\n    id: 3,\n    email: '<EMAIL>',\n    first_name: 'Maria',\n    last_name: 'Cotton',\n    employee_id: 'EMP002',\n    role: 'employee',\n    job_title: 'PHP Team Lead',\n    position: 'PHP Team Lead',\n    department_id: 2,\n    department_name: 'Engineering',\n    hire_date: '2023-01-15',\n    employment_type: 'full_time',\n    work_location: 'office',\n    salary: 85000,\n    hourly_rate: 41,\n    currency: 'USD',\n    pay_frequency: 'monthly',\n    bank_name: 'Wells Fargo',\n    bank_account: '****5678',\n    nssf_number: 'NSSF002',\n    nhif_number: 'NHIF002',\n    kra_pin: 'KRA002',\n    national_id: '********',\n    date_of_birth: '1988-08-22',\n    gender: 'female',\n    marital_status: 'married',\n    nationality: 'American',\n    address: '456 Oak Ave',\n    city: 'San Francisco',\n    state: 'CA',\n    postal_code: '94107',\n    country: 'USA',\n    emergency_contact_name: 'Carlos Cotton',\n    emergency_contact_phone: '******-0298',\n    emergency_contact_relationship: 'Spouse',\n    status: 'active',\n    is_active: true,\n    date_joined: '2023-01-15',\n    is_deleted: false,\n    created_by: 1,\n    updated_by: 1,\n    department: {\n      id: 2,\n      name: 'Engineering',\n      description: 'Engineering Department',\n      created_at: '2023-01-01',\n      updated_at: '2023-01-01',\n    },\n  },\n];\n\n// Mock API delay\nconst delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\n\n// Mock API responses\nexport const mockApi = {\n  // Authentication\n  login: async (credentials: { email: string; password: string }): Promise<AuthTokens> => {\n    await delay(1000); // Simulate network delay\n\n    const validCredentials = [\n      { email: '<EMAIL>', password: 'admin123' },\n      { email: '<EMAIL>', password: 'employee123' },\n    ];\n\n    const isValid = validCredentials.some(\n      cred => cred.email === credentials.email && cred.password === credentials.password\n    );\n\n    if (!isValid) {\n      throw new Error('Invalid email or password');\n    }\n\n    return {\n      access: 'mock-access-token-' + Date.now(),\n      refresh: 'mock-refresh-token-' + Date.now(),\n    };\n  },\n\n  getCurrentUser: async (): Promise<User> => {\n    await delay(500);\n\n    // Check if we're in browser environment\n    if (typeof window === 'undefined') {\n      throw new Error('Not in browser environment');\n    }\n\n    // Get user based on stored email (in real app, this would be from token)\n    const storedEmail = localStorage.getItem('mock_user_email');\n    if (!storedEmail) {\n      throw new Error('No user email stored');\n    }\n\n    const user = mockUsers.find(u => u.email === storedEmail);\n\n    if (!user) {\n      throw new Error('User not found');\n    }\n\n    return user;\n  },\n\n  // Employees\n  getEmployees: async (): Promise<Employee[]> => {\n    await delay(800);\n    return mockEmployees;\n  },\n\n  getEmployeeById: async (id: number): Promise<Employee> => {\n    await delay(500);\n    const employee = mockEmployees.find(emp => emp.id === id);\n    if (!employee) {\n      throw new Error('Employee not found');\n    }\n    return employee;\n  },\n\n  // Departments\n  getDepartments: async (): Promise<Department[]> => {\n    await delay(500);\n    return [\n      {\n        id: 1,\n        name: 'Human Resources',\n        description: 'HR Department',\n        created_at: '2023-01-01',\n        updated_at: '2023-01-01',\n      },\n      {\n        id: 2,\n        name: 'Engineering',\n        description: 'Engineering Department',\n        created_at: '2023-01-01',\n        updated_at: '2023-01-01',\n      },\n      {\n        id: 3,\n        name: 'Design',\n        description: 'Design Department',\n        created_at: '2023-01-01',\n        updated_at: '2023-01-01',\n      },\n    ];\n  },\n\n  // Leave Applications\n  getLeaveApplications: async (): Promise<LeaveApplication[]> => {\n    await delay(600);\n    return [\n      {\n        id: 1,\n        employee: 2,\n        employee_name: 'John Doe',\n        leave_type: 'annual',\n        start_date: '2024-02-15',\n        end_date: '2024-02-20',\n        days_requested: 5,\n        reason: 'Family vacation',\n        status: 'approved',\n        approved_by: 1,\n        approved_by_name: 'Admin User',\n        applied_date: '2024-02-01',\n      },\n      {\n        id: 2,\n        employee: 3,\n        employee_name: 'Maria Cotton',\n        leave_type: 'sick',\n        start_date: '2024-02-10',\n        end_date: '2024-02-12',\n        days_requested: 3,\n        reason: 'Medical appointment',\n        status: 'pending',\n        applied_date: '2024-02-08',\n      },\n    ];\n  },\n\n  // Dashboard Stats\n  getDashboardStats: async () => {\n    await delay(700);\n    return {\n      total_employees: mockEmployees.length,\n      total_departments: 3,\n      pending_leaves: 1,\n      total_salary: mockEmployees.reduce((sum, emp) => sum + (emp.salary || 0), 0),\n      recent_activities: [\n        {\n          id: 1,\n          type: 'leave' as const,\n          description: 'Maria Cotton applied for sick leave',\n          user: 'Maria Cotton',\n          timestamp: new Date().toISOString(),\n        },\n        {\n          id: 2,\n          type: 'attendance' as const,\n          description: 'John Doe checked in',\n          user: 'John Doe',\n          timestamp: new Date(Date.now() - 3600000).toISOString(),\n        },\n      ],\n    };\n  },\n};\n\n// Helper function to store user email for mock authentication\nexport const setMockUserEmail = (email: string) => {\n  if (typeof window !== 'undefined') {\n    localStorage.setItem('mock_user_email', email);\n  }\n};\n\nexport const clearMockUserEmail = () => {\n  if (typeof window !== 'undefined') {\n    localStorage.removeItem('mock_user_email');\n  }\n};\n"], "names": ["clearMockUserEmail", "mockApi", "setMockUserEmail", "mockUsers", "id", "email", "first_name", "last_name", "employee_id", "role", "is_active", "date_joined", "department", "name", "description", "created_at", "updated_at", "mockEmployees", "job_title", "position", "department_id", "department_name", "hire_date", "employment_type", "work_location", "salary", "hourly_rate", "currency", "pay_frequency", "bank_name", "bank_account", "nssf_number", "nhif_number", "kra_pin", "national_id", "date_of_birth", "gender", "marital_status", "nationality", "address", "city", "state", "postal_code", "country", "emergency_contact_name", "emergency_contact_phone", "emergency_contact_relationship", "status", "is_deleted", "created_by", "updated_by", "delay", "ms", "Promise", "resolve", "setTimeout", "login", "credentials", "validCredentials", "password", "<PERSON><PERSON><PERSON><PERSON>", "some", "cred", "Error", "access", "Date", "now", "refresh", "getCurrentUser", "window", "storedEmail", "localStorage", "getItem", "user", "find", "u", "getEmployees", "getEmployeeById", "employee", "emp", "getDepartments", "getLeaveApplications", "employee_name", "leave_type", "start_date", "end_date", "days_requested", "reason", "approved_by", "approved_by_name", "applied_date", "getDashboardStats", "total_employees", "length", "total_departments", "pending_leaves", "total_salary", "reduce", "sum", "recent_activities", "type", "timestamp", "toISOString", "setItem", "removeItem"], "mappings": ";;;;;;;;;;;IAmSaA,kBAAkB;eAAlBA;;IA9JAC,OAAO;eAAPA;;IAwJAC,gBAAgB;eAAhBA;;;AA3Rb,YAAY;AACZ,MAAMC,YAAoB;IACxB;QACEC,IAAI;QACJC,OAAO;QACPC,YAAY;QACZC,WAAW;QACXC,aAAa;QACbC,MAAM;QACNC,WAAW;QACXC,aAAa;QACbC,YAAY;YACVR,IAAI;YACJS,MAAM;YACNC,aAAa;YACbC,YAAY;YACZC,YAAY;QACd;IACF;IACA;QACEZ,IAAI;QACJC,OAAO;QACPC,YAAY;QACZC,WAAW;QACXC,aAAa;QACbC,MAAM;QACNC,WAAW;QACXC,aAAa;QACbC,YAAY;YACVR,IAAI;YACJS,MAAM;YACNC,aAAa;YACbC,YAAY;YACZC,YAAY;QACd;IACF;CACD;AAED,MAAMC,gBAA4B;IAChC;QACE,GAAGd,SAAS,CAAC,EAAE;QACfe,WAAW;QACXC,UAAU;QACVC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,iBAAiB;QACjBC,eAAe;QACfC,QAAQ;QACRC,aAAa;QACbC,UAAU;QACVC,eAAe;QACfC,WAAW;QACXC,cAAc;QACdC,aAAa;QACbC,aAAa;QACbC,SAAS;QACTC,aAAa;QACbC,eAAe;QACfC,QAAQ;QACRC,gBAAgB;QAChBC,aAAa;QACbC,SAAS;QACTC,MAAM;QACNC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,wBAAwB;QACxBC,yBAAyB;QACzBC,gCAAgC;QAChCC,QAAQ;QACRC,YAAY;QACZC,YAAY;QACZC,YAAY;IACd;IACA;QACE9C,IAAI;QACJC,OAAO;QACPC,YAAY;QACZC,WAAW;QACXC,aAAa;QACbC,MAAM;QACNS,WAAW;QACXC,UAAU;QACVC,eAAe;QACfC,iBAAiB;QACjBC,WAAW;QACXC,iBAAiB;QACjBC,eAAe;QACfC,QAAQ;QACRC,aAAa;QACbC,UAAU;QACVC,eAAe;QACfC,WAAW;QACXC,cAAc;QACdC,aAAa;QACbC,aAAa;QACbC,SAAS;QACTC,aAAa;QACbC,eAAe;QACfC,QAAQ;QACRC,gBAAgB;QAChBC,aAAa;QACbC,SAAS;QACTC,MAAM;QACNC,OAAO;QACPC,aAAa;QACbC,SAAS;QACTC,wBAAwB;QACxBC,yBAAyB;QACzBC,gCAAgC;QAChCC,QAAQ;QACRrC,WAAW;QACXC,aAAa;QACbqC,YAAY;QACZC,YAAY;QACZC,YAAY;QACZtC,YAAY;YACVR,IAAI;YACJS,MAAM;YACNC,aAAa;YACbC,YAAY;YACZC,YAAY;QACd;IACF;CACD;AAED,iBAAiB;AACjB,MAAMmC,QAAQ,CAACC,KAAe,IAAIC,QAAQC,CAAAA,UAAWC,WAAWD,SAASF;AAGlE,MAAMnD,UAAU;IACrB,iBAAiB;IACjBuD,OAAO,OAAOC;QACZ,MAAMN,MAAM,OAAO,yBAAyB;QAE5C,MAAMO,mBAAmB;YACvB;gBAAErD,OAAO;gBAAsBsD,UAAU;YAAW;YACpD;gBAAEtD,OAAO;gBAAyBsD,UAAU;YAAc;SAC3D;QAED,MAAMC,UAAUF,iBAAiBG,IAAI,CACnCC,CAAAA,OAAQA,KAAKzD,KAAK,KAAKoD,YAAYpD,KAAK,IAAIyD,KAAKH,QAAQ,KAAKF,YAAYE,QAAQ;QAGpF,IAAI,CAACC,SAAS;YACZ,MAAM,IAAIG,MAAM;QAClB;QAEA,OAAO;YACLC,QAAQ,uBAAuBC,KAAKC,GAAG;YACvCC,SAAS,wBAAwBF,KAAKC,GAAG;QAC3C;IACF;IAEAE,gBAAgB;QACd,MAAMjB,MAAM;QAEZ,wCAAwC;QACxC,IAAI,OAAOkB,WAAW,aAAa;YACjC,MAAM,IAAIN,MAAM;QAClB;QAEA,yEAAyE;QACzE,MAAMO,cAAcC,aAAaC,OAAO,CAAC;QACzC,IAAI,CAACF,aAAa;YAChB,MAAM,IAAIP,MAAM;QAClB;QAEA,MAAMU,OAAOtE,UAAUuE,IAAI,CAACC,CAAAA,IAAKA,EAAEtE,KAAK,KAAKiE;QAE7C,IAAI,CAACG,MAAM;YACT,MAAM,IAAIV,MAAM;QAClB;QAEA,OAAOU;IACT;IAEA,YAAY;IACZG,cAAc;QACZ,MAAMzB,MAAM;QACZ,OAAOlC;IACT;IAEA4D,iBAAiB,OAAOzE;QACtB,MAAM+C,MAAM;QACZ,MAAM2B,WAAW7D,cAAcyD,IAAI,CAACK,CAAAA,MAAOA,IAAI3E,EAAE,KAAKA;QACtD,IAAI,CAAC0E,UAAU;YACb,MAAM,IAAIf,MAAM;QAClB;QACA,OAAOe;IACT;IAEA,cAAc;IACdE,gBAAgB;QACd,MAAM7B,MAAM;QACZ,OAAO;YACL;gBACE/C,IAAI;gBACJS,MAAM;gBACNC,aAAa;gBACbC,YAAY;gBACZC,YAAY;YACd;YACA;gBACEZ,IAAI;gBACJS,MAAM;gBACNC,aAAa;gBACbC,YAAY;gBACZC,YAAY;YACd;YACA;gBACEZ,IAAI;gBACJS,MAAM;gBACNC,aAAa;gBACbC,YAAY;gBACZC,YAAY;YACd;SACD;IACH;IAEA,qBAAqB;IACrBiE,sBAAsB;QACpB,MAAM9B,MAAM;QACZ,OAAO;YACL;gBACE/C,IAAI;gBACJ0E,UAAU;gBACVI,eAAe;gBACfC,YAAY;gBACZC,YAAY;gBACZC,UAAU;gBACVC,gBAAgB;gBAChBC,QAAQ;gBACRxC,QAAQ;gBACRyC,aAAa;gBACbC,kBAAkB;gBAClBC,cAAc;YAChB;YACA;gBACEtF,IAAI;gBACJ0E,UAAU;gBACVI,eAAe;gBACfC,YAAY;gBACZC,YAAY;gBACZC,UAAU;gBACVC,gBAAgB;gBAChBC,QAAQ;gBACRxC,QAAQ;gBACR2C,cAAc;YAChB;SACD;IACH;IAEA,kBAAkB;IAClBC,mBAAmB;QACjB,MAAMxC,MAAM;QACZ,OAAO;YACLyC,iBAAiB3E,cAAc4E,MAAM;YACrCC,mBAAmB;YACnBC,gBAAgB;YAChBC,cAAc/E,cAAcgF,MAAM,CAAC,CAACC,KAAKnB,MAAQmB,MAAOnB,CAAAA,IAAItD,MAAM,IAAI,CAAA,GAAI;YAC1E0E,mBAAmB;gBACjB;oBACE/F,IAAI;oBACJgG,MAAM;oBACNtF,aAAa;oBACb2D,MAAM;oBACN4B,WAAW,IAAIpC,OAAOqC,WAAW;gBACnC;gBACA;oBACElG,IAAI;oBACJgG,MAAM;oBACNtF,aAAa;oBACb2D,MAAM;oBACN4B,WAAW,IAAIpC,KAAKA,KAAKC,GAAG,KAAK,SAASoC,WAAW;gBACvD;aACD;QACH;IACF;AACF;AAGO,MAAMpG,mBAAmB,CAACG;IAC/B,IAAI,OAAOgE,WAAW,aAAa;QACjCE,aAAagC,OAAO,CAAC,mBAAmBlG;IAC1C;AACF;AAEO,MAAML,qBAAqB;IAChC,IAAI,OAAOqE,WAAW,aAAa;QACjCE,aAAaiC,UAAU,CAAC;IAC1B;AACF"}