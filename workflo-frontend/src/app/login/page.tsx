'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { Eye, EyeOff, Mail, Lock, CheckCircle } from 'lucide-react';
import { useAuth } from '@/providers/AuthProvider';
import { LoginCredentials, User } from '@/types';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { EnhancedLoadingSpinner } from '@/components/ui/SplashScreen';

const LoginPage: React.FC = () => {
  const router = useRouter();
  const { login, isAuthenticated, error, clearError, isLoading, user } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [loginSuccess, setLoginSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginCredentials>();

  // Helper function to determine redirect path based on user role
  const getRedirectPath = (user: User | null): string => {
    if (!user) return '/login';

    // Role-based routing
    switch (user.role) {
      case 'supervisor':
        return '/supervisor';
      case 'employee':
        return '/staff';
      case 'accountant':
        return '/accountant';
      case 'hr':
      case 'admin':
        return '/dashboard';
      default:
        return '/dashboard';
    }
  };

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      const redirectPath = getRedirectPath(user);
      router.push(redirectPath);
    }
  }, [isAuthenticated, user, router]);

  // Clear errors when component mounts
  useEffect(() => {
    clearError();
  }, [clearError]);

  const onSubmit = async (data: LoginCredentials) => {
    try {
      await login(data);
      setLoginSuccess(true);
      // Role-based redirection will be handled by the useEffect above
    } catch (error) {
      // Error is handled by the auth store
      console.error('Login failed:', error);
      setLoginSuccess(false);
    }
  };

  if (isAuthenticated || loginSuccess) {
    const getRedirectMessage = (user: User | null): string => {
      if (!user) return 'Redirecting...';

      switch (user.role) {
        case 'supervisor':
          return 'Redirecting to supervisor dashboard...';
        case 'employee':
          return 'Redirecting to staff dashboard...';
        case 'hr':
        case 'admin':
          return 'Redirecting to admin dashboard...';
        case 'accountant':
          return 'Redirecting to accountant dashboard...';
        default:
          return 'Redirecting to dashboard...';
      }
    };

    const redirectMessage = getRedirectMessage(user);

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="h-8 w-8 text-white" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Login Successful!</h2>
          <p className="text-gray-600 mb-4">{redirectMessage}</p>
          <EnhancedLoadingSpinner size="md" className="mx-auto" />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-xl">W</span>
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Sign in to WorkFlow
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Welcome back! Please sign in to your account.
        </p>
      </div>

      {/* Login Form */}
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            {/* Email Field */}
            <Input
              label="Email Address"
              type="email"
              autoComplete="email"
              required
              leftIcon={<Mail className="h-5 w-5" />}
              error={errors.email?.message}
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address',
                },
              })}
            />

            {/* Password Field */}
            <div className="relative">
              <Input
                label="Password"
                type={showPassword ? 'text' : 'password'}
                autoComplete="current-password"
                required
                leftIcon={<Lock className="h-5 w-5" />}
                rightIcon={
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5" />
                    ) : (
                      <Eye className="h-5 w-5" />
                    )}
                  </button>
                }
                error={errors.password?.message}
                {...register('password', {
                  required: 'Password is required',
                  minLength: {
                    value: 6,
                    message: 'Password must be at least 6 characters',
                  },
                })}
              />
            </div>

            {/* Error Message */}
            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="text-sm text-red-700">{error}</div>
              </div>
            )}

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <a
                  href="#"
                  className="font-medium text-orange-600 hover:text-orange-500"
                >
                  Forgot your password?
                </a>
              </div>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full"
              loading={isLoading}
              disabled={isLoading}
            >
              Sign in
            </Button>
          </form>

          {/* Demo Credentials */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">Demo Credentials</span>
              </div>
            </div>

            <div className="mt-4 space-y-2 text-sm text-gray-600">
              <div className="bg-orange-50 p-3 rounded-md border border-orange-200">
                <p className="font-medium text-orange-800">🎯 Supervisor (Recommended):</p>
                <p className="text-orange-700">Email: <EMAIL></p>
                <p className="text-orange-700">Password: supervisor123</p>
                <p className="text-xs text-orange-600 mt-1">Access team management features</p>
              </div>
              <div className="bg-blue-50 p-3 rounded-md border border-blue-200">
                <p className="font-medium text-blue-800">HR Admin:</p>
                <p className="text-blue-700">Email: <EMAIL></p>
                <p className="text-blue-700">Password: admin123</p>
                <p className="text-xs text-blue-600 mt-1">Full admin access</p>
              </div>
              <div className="bg-green-50 p-3 rounded-md border border-green-200">
                <p className="font-medium text-green-800">Employee:</p>
                <p className="text-green-700">Email: <EMAIL></p>
                <p className="text-green-700">Password: employee123</p>
                <p className="text-xs text-green-600 mt-1">Staff portal access</p>
              </div>
              <div className="bg-purple-50 p-3 rounded-md border border-purple-200">
                <p className="font-medium text-purple-800">Accountant:</p>
                <p className="text-purple-700">Email: <EMAIL></p>
                <p className="text-purple-700">Password: accountant123</p>
                <p className="text-xs text-purple-600 mt-1">Financial management access</p>
              </div>
            </div>

            {/* Quick Test Buttons */}
            <div className="mt-4 space-y-2">
              <Button
                type="button"
                variant="primary"
                className="w-full bg-orange-500 hover:bg-orange-600 text-white"
                onClick={async () => {
                  console.log('Quick test: Attempting supervisor login...');
                  try {
                    await login({ email: '<EMAIL>', password: 'supervisor123' });
                    console.log('Quick test: Supervisor login successful!');
                  } catch (error) {
                    console.error('Quick test: Supervisor login failed:', error);
                  }
                }}
                disabled={isLoading}
              >
                🎯 Quick Test - Login as Supervisor
              </Button>
              <Button
                type="button"
                variant="secondary"
                className="w-full"
                onClick={async () => {
                  console.log('Quick test: Attempting admin login...');
                  try {
                    await login({ email: '<EMAIL>', password: 'admin123' });
                    console.log('Quick test: Admin login successful!');
                  } catch (error) {
                    console.error('Quick test: Admin login failed:', error);
                  }
                }}
                disabled={isLoading}
              >
                Quick Test - Login as Admin
              </Button>
              <Button
                type="button"
                variant="secondary"
                className="w-full bg-purple-500 hover:bg-purple-600 text-white"
                onClick={async () => {
                  console.log('Quick test: Attempting accountant login...');
                  try {
                    await login({ email: '<EMAIL>', password: 'accountant123' });
                    console.log('Quick test: Accountant login successful!');
                  } catch (error) {
                    console.error('Quick test: Accountant login failed:', error);
                  }
                }}
                disabled={isLoading}
              >
                💼 Quick Test - Login as Accountant
              </Button>
              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={async () => {
                  console.log('Quick test: Attempting employee login...');
                  try {
                    await login({ email: '<EMAIL>', password: 'employee123' });
                    console.log('Quick test: Employee login successful!');
                  } catch (error) {
                    console.error('Quick test: Employee login failed:', error);
                  }
                }}
                disabled={isLoading}
              >
                Quick Test - Login as Employee
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8 text-center text-sm text-gray-600">
        <p>
          Don't have an account?{' '}
          <a href="#" className="font-medium text-orange-600 hover:text-orange-500">
            Contact your HR administrator
          </a>
        </p>
      </div>
    </div>
  );
};

export default LoginPage;
