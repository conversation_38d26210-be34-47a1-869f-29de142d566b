dbfb2e1f9d0f974a61ecb95ea982d168
"use strict";
// Mock the hooks and dependencies
jest.mock('@/hooks/useBiostarAttendance', ()=>({
        useBiostarAttendance: ()=>({
                summary: {
                    checkInTime: '08:30 AM',
                    hoursWorked: 8.5,
                    todayStatus: 'PRESENT',
                    weeklyHours: 40.0,
                    monthlyAttendance: 22
                },
                realtimeUpdates: [],
                connected: true,
                loading: false
            })
    }));
jest.mock('@/providers/AuthProvider', ()=>({
        useAuth: ()=>({
                user: {
                    first_name: '<PERSON>',
                    last_name: '<PERSON><PERSON>',
                    employee_id: 'EMP001'
                },
                isAuthenticated: true,
                isLoading: false
            })
    }));
// Mock Next.js router
jest.mock('next/navigation', ()=>({
        usePathname: ()=>'/staff',
        useRouter: ()=>({
                push: jest.fn(),
                replace: jest.fn(),
                back: jest.fn()
            })
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _react1 = require("@testing-library/react");
require("@testing-library/jest-dom");
const _page = /*#__PURE__*/ _interop_require_default(require("../app/(auth)/(staff)/staff/page"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
describe('Mobile Responsive Staff Dashboard', ()=>{
    beforeEach(()=>{
        // Reset viewport to mobile size
        Object.defineProperty(window, 'innerWidth', {
            writable: true,
            configurable: true,
            value: 375
        });
        Object.defineProperty(window, 'innerHeight', {
            writable: true,
            configurable: true,
            value: 667
        });
    });
    it('renders staff dashboard with mobile-friendly layout', ()=>{
        (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
        // Check if welcome header is present
        expect(_react1.screen.getByText(/Good/)).toBeInTheDocument();
        expect(_react1.screen.getByText(/John/)).toBeInTheDocument();
        // Check if stats cards are present
        expect(_react1.screen.getByText('Check-in Time')).toBeInTheDocument();
        expect(_react1.screen.getByText('Hours Worked')).toBeInTheDocument();
        expect(_react1.screen.getByText('Tasks Completed')).toBeInTheDocument();
        expect(_react1.screen.getByText('Weekly Hours')).toBeInTheDocument();
        // Check if activities section is present
        expect(_react1.screen.getByText("Today's Activities")).toBeInTheDocument();
        // Check if upcoming events section is present
        expect(_react1.screen.getByText('Upcoming Events')).toBeInTheDocument();
        // Check if quick actions section is present
        expect(_react1.screen.getByText('Quick Actions')).toBeInTheDocument();
    });
    it('has proper responsive classes for mobile', ()=>{
        const { container } = (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
        // Check for responsive spacing classes
        const mainContainer = container.querySelector('.space-y-4');
        expect(mainContainer).toBeInTheDocument();
        // Check for responsive grid classes
        const statsGrid = container.querySelector('.grid-cols-1');
        expect(statsGrid).toBeInTheDocument();
    });
    it('displays BioStar connection status', ()=>{
        (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
        expect(_react1.screen.getByText('BioStar Connected')).toBeInTheDocument();
    });
    it('shows current time', ()=>{
        (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {}));
        expect(_react1.screen.getByText('Current Time')).toBeInTheDocument();
    });
});

//# sourceMappingURL=data:application/json;base64,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