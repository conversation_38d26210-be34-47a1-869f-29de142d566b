# This file is distributed under the same license as the Django package.
#
# Translators:
# 0d21a39e384d88c2313b89b5042c04cb, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
# <PERSON><PERSON>o <<EMAIL>>, 2018,2020
# <AUTHOR> <EMAIL>, 2015
# <PERSON> <<EMAIL>>, 2023
# Mattia Pro<PERSON>pio <<EMAIL>>, 2015
# <PERSON> <<EMAIL>>, 2015-2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 02:13-0600\n"
"PO-Revision-Date: 2023-04-19 09:22+0000\n"
"Last-Translator: <PERSON> <pao<PERSON>@melchiorre.org>, 2023\n"
"Language-Team: Italian (http://www.transifex.com/django/django/language/"
"it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: it\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "PostgreSQL extensions"
msgstr "Estensioni per PostgreSQL"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "L'elemento %(nth)s dell'array non è stato convalidato:"

msgid "Nested arrays must have the same length."
msgstr "Gli array annidati devono avere la stessa lunghezza."

msgid "Map of strings to strings/nulls"
msgstr "Mappa di stringhe a stringhe/null"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "Il valore di \"%(key)s\" non è una stringa o nullo."

msgid "Could not load JSON data."
msgstr "Caricamento dati JSON fallito."

msgid "Input must be a JSON dictionary."
msgstr "L'input deve essere un dizionario JSON."

msgid "Enter two valid values."
msgstr "Inserisci due valori validi."

msgid "The start of the range must not exceed the end of the range."
msgstr ""
"Il valore iniziale dell'intervallo non può essere superiore al valore finale."

msgid "Enter two whole numbers."
msgstr "Inserisci due numeri interi."

msgid "Enter two numbers."
msgstr "Inserisci due numeri."

msgid "Enter two valid date/times."
msgstr "Inserisci due valori data/ora validi."

msgid "Enter two valid dates."
msgstr "Inserisci due date valide."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"La lista contiene %(show_value)d oggetto, non dovrebbe contenerne più di "
"%(limit_value)d."
msgstr[1] ""
"La lista contiene %(show_value)d elementi, e dovrebbe contenerne al massimo "
"%(limit_value)d."
msgstr[2] ""
"La lista contiene %(show_value)d elementi, e dovrebbe contenerne al massimo "
"%(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"La lista contiene %(show_value)d oggetto, non dovrebbe contenerne meno di "
"%(limit_value)d."
msgstr[1] ""
"La lista contiene %(show_value)d oggetti, e dovrebbe contenerne almeno "
"%(limit_value)d."
msgstr[2] ""
"La lista contiene %(show_value)d oggetti, e dovrebbe contenerne almeno "
"%(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Alcune chiavi risultano mancanti: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Sono state fornite alcune chiavi sconosciute: %(keys)s"

#, python-format
msgid ""
"Ensure that the upper bound of the range is not greater than %(limit_value)s."
msgstr ""
"Assicurarsi che il limite superiore dell'intervallo non sia maggiore di "
"%(limit_value)s."

#, python-format
msgid ""
"Ensure that the lower bound of the range is not less than %(limit_value)s."
msgstr ""
"Assicurarsi che il limite inferiore dell'intervallo non sia inferiore a "
"%(limit_value)s."
