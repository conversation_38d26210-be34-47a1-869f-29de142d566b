import { biostarApi } from './biostarApi';
import { 
  AttendanceRecord, 
  BiometricEvent, 
  RealTimeAttendance, 
  BiometricUser,
  BiometricDevice 
} from '@/types';
import { format, startOfDay, endOfDay, parseISO, differenceInHours } from 'date-fns';

export class AttendanceService {
  private static instance: AttendanceService;
  private realtimeListeners: ((data: RealTimeAttendance) => void)[] = [];
  private pollingInterval: NodeJS.Timeout | null = null;

  static getInstance(): AttendanceService {
    if (!AttendanceService.instance) {
      AttendanceService.instance = new AttendanceService();
    }
    return AttendanceService.instance;
  }

  // Get today's attendance for current user
  async getTodayAttendance(employeeId: string): Promise<AttendanceRecord | null> {
    try {
      const today = format(new Date(), 'yyyy-MM-dd');
      const startDate = format(startOfDay(new Date()), "yyyy-MM-dd'T'HH:mm:ss");
      const endDate = format(endOfDay(new Date()), "yyyy-MM-dd'T'HH:mm:ss");

      // Get events for today
      const eventsResponse = await biostarApi.getEvents(
        startDate,
        endDate,
        employeeId,
        undefined,
        100,
        0
      );

      if (!eventsResponse.results || eventsResponse.results.length === 0) {
        return null;
      }

      return this.processAttendanceEvents(employeeId, today, eventsResponse.results);
    } catch (error) {
      console.error('Error fetching today\'s attendance:', error);
      return null;
    }
  }

  // Get attendance for a date range
  async getAttendanceRange(
    employeeId: string, 
    startDate: string, 
    endDate: string
  ): Promise<AttendanceRecord[]> {
    try {
      const eventsResponse = await biostarApi.getEvents(
        `${startDate}T00:00:00`,
        `${endDate}T23:59:59`,
        employeeId,
        undefined,
        1000,
        0
      );

      if (!eventsResponse.results) {
        return [];
      }

      // Group events by date
      const eventsByDate = this.groupEventsByDate(eventsResponse.results);
      
      // Process each date
      const attendanceRecords: AttendanceRecord[] = [];
      for (const [date, events] of Object.entries(eventsByDate)) {
        const record = this.processAttendanceEvents(employeeId, date, events);
        if (record) {
          attendanceRecords.push(record);
        }
      }

      return attendanceRecords.sort((a, b) => b.date.localeCompare(a.date));
    } catch (error) {
      console.error('Error fetching attendance range:', error);
      return [];
    }
  }

  // Get real-time attendance updates
  async getRealTimeUpdates(): Promise<RealTimeAttendance[]> {
    try {
      const events = await biostarApi.getRealtimeEvents(20);
      
      return events.map(event => ({
        employee_id: event.user_id,
        employee_name: event.user_name || 'Unknown',
        event_type: this.mapEventType(event.event_type),
        timestamp: event.datetime,
        device_name: event.device_name || 'Unknown Device',
        location: 'Main Office' // Default location
      }));
    } catch (error) {
      console.error('Error fetching real-time updates:', error);
      return [];
    }
  }

  // Start real-time monitoring
  startRealTimeMonitoring(callback: (data: RealTimeAttendance) => void): void {
    this.realtimeListeners.push(callback);
    
    if (!this.pollingInterval) {
      this.pollingInterval = setInterval(async () => {
        try {
          const updates = await this.getRealTimeUpdates();
          updates.forEach(update => {
            this.realtimeListeners.forEach(listener => listener(update));
          });
        } catch (error) {
          console.error('Real-time monitoring error:', error);
        }
      }, 30000); // Poll every 30 seconds
    }
  }

  // Stop real-time monitoring
  stopRealTimeMonitoring(callback?: (data: RealTimeAttendance) => void): void {
    if (callback) {
      this.realtimeListeners = this.realtimeListeners.filter(listener => listener !== callback);
    } else {
      this.realtimeListeners = [];
    }

    if (this.realtimeListeners.length === 0 && this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
  }

  // Get attendance summary for dashboard
  async getAttendanceSummary(employeeId: string): Promise<{
    todayStatus: 'PRESENT' | 'ABSENT' | 'LATE' | 'EARLY_OUT';
    checkInTime?: string;
    checkOutTime?: string;
    hoursWorked: number;
    breakTime: number;
    overtime: number;
    weeklyHours: number;
    monthlyAttendance: number;
  }> {
    try {
      // Get today's attendance
      const todayAttendance = await this.getTodayAttendance(employeeId);
      
      // Get this week's attendance
      const weekStart = format(new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd');
      const today = format(new Date(), 'yyyy-MM-dd');
      const weeklyAttendance = await this.getAttendanceRange(employeeId, weekStart, today);
      
      // Get this month's attendance
      const monthStart = format(new Date(new Date().getFullYear(), new Date().getMonth(), 1), 'yyyy-MM-dd');
      const monthlyAttendance = await this.getAttendanceRange(employeeId, monthStart, today);

      const weeklyHours = weeklyAttendance.reduce((total, record) => total + (record.total_hours || 0), 0);
      const monthlyDays = monthlyAttendance.filter(record => record.status === 'PRESENT').length;

      return {
        todayStatus: todayAttendance?.status || 'ABSENT',
        checkInTime: todayAttendance?.first_in,
        checkOutTime: todayAttendance?.last_out,
        hoursWorked: todayAttendance?.total_hours || 0,
        breakTime: todayAttendance?.break_time || 0,
        overtime: todayAttendance?.overtime || 0,
        weeklyHours,
        monthlyAttendance: monthlyDays,
      };
    } catch (error) {
      console.error('Error fetching attendance summary:', error);
      return {
        todayStatus: 'ABSENT',
        hoursWorked: 0,
        breakTime: 0,
        overtime: 0,
        weeklyHours: 0,
        monthlyAttendance: 0,
      };
    }
  }

  // Get available devices
  async getDevices(): Promise<BiometricDevice[]> {
    try {
      const response = await biostarApi.getDevices();
      return response.results || [];
    } catch (error) {
      console.error('Error fetching devices:', error);
      return [];
    }
  }

  // Sync employee with BioStar
  async syncEmployee(employee: any): Promise<BiometricUser | null> {
    try {
      const biostarUser: Partial<BiometricUser> = {
        user_id: employee.employee_id,
        name: `${employee.first_name} ${employee.last_name}`,
        email: employee.email,
        phone: employee.phone_number,
        department: employee.department_name,
        position: employee.job_title,
        employee_id: employee.employee_id,
        disabled: !employee.is_active,
      };

      // Try to find existing user first
      try {
        const existingUser = await biostarApi.getUserById(employee.employee_id);
        // Update existing user
        return await biostarApi.updateUser(existingUser.id, biostarUser);
      } catch (error) {
        // User doesn't exist, create new one
        return await biostarApi.createUser(biostarUser);
      }
    } catch (error) {
      console.error('Error syncing employee with BioStar:', error);
      return null;
    }
  }

  // Private helper methods
  private processAttendanceEvents(
    employeeId: string, 
    date: string, 
    events: BiometricEvent[]
  ): AttendanceRecord | null {
    if (!events || events.length === 0) {
      return null;
    }

    // Sort events by time
    const sortedEvents = events.sort((a, b) => 
      new Date(a.datetime).getTime() - new Date(b.datetime).getTime()
    );

    // Find first entry and last exit
    const entryEvents = sortedEvents.filter(e => e.event_type === 'ENTRY');
    const exitEvents = sortedEvents.filter(e => e.event_type === 'EXIT');

    const firstIn = entryEvents.length > 0 ? entryEvents[0].datetime : undefined;
    const lastOut = exitEvents.length > 0 ? exitEvents[exitEvents.length - 1].datetime : undefined;

    // Calculate total hours
    let totalHours = 0;
    if (firstIn && lastOut) {
      totalHours = differenceInHours(parseISO(lastOut), parseISO(firstIn));
    }

    // Determine status
    let status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EARLY_OUT' = 'ABSENT';
    if (firstIn) {
      const checkInTime = parseISO(firstIn);
      const workStartTime = new Date(checkInTime);
      workStartTime.setHours(9, 0, 0, 0); // Assuming 9 AM start time

      if (checkInTime <= workStartTime) {
        status = 'PRESENT';
      } else {
        status = 'LATE';
      }

      // Check for early out
      if (lastOut) {
        const checkOutTime = parseISO(lastOut);
        const workEndTime = new Date(checkOutTime);
        workEndTime.setHours(17, 0, 0, 0); // Assuming 5 PM end time

        if (checkOutTime < workEndTime && totalHours < 8) {
          status = 'EARLY_OUT';
        }
      }
    }

    return {
      id: `${employeeId}-${date}`,
      employee_id: employeeId,
      employee_name: events[0]?.user_name || 'Unknown',
      date,
      first_in: firstIn,
      last_out: lastOut,
      total_hours: totalHours,
      break_time: 0, // Calculate based on gaps between entry/exit
      overtime: Math.max(0, totalHours - 8),
      status,
      events: sortedEvents,
      biostar_synced: true,
    };
  }

  private groupEventsByDate(events: BiometricEvent[]): Record<string, BiometricEvent[]> {
    return events.reduce((groups, event) => {
      const date = format(parseISO(event.datetime), 'yyyy-MM-dd');
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(event);
      return groups;
    }, {} as Record<string, BiometricEvent[]>);
  }

  private mapEventType(biostarEventType: string): 'CHECK_IN' | 'CHECK_OUT' | 'BREAK_START' | 'BREAK_END' {
    switch (biostarEventType) {
      case 'ENTRY':
        return 'CHECK_IN';
      case 'EXIT':
        return 'CHECK_OUT';
      default:
        return 'CHECK_IN';
    }
  }
}

// Export singleton instance
export const attendanceService = AttendanceService.getInstance();
