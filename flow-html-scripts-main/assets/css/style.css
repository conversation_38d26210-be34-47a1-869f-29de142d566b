/*
Author: <PERSON><PERSON><PERSON>
Template Name: <PERSON><PERSON><PERSON> Bootstrap4 admin template
Version: 1.0
*/

/*============================
 [Table of CSS]

1. General
2. Button
3. Select2
4. Header
5. Calendar
6. Mobile Menu
7. <PERSON><PERSON>crump
8. Content
9. Table
10. Header Search
11. Login
12. Settings
13. Responsive

========================================*/

/*-----------------------
	 01. GENERAL
-------------------------*/

@import url('https://fonts.googleapis.com/css?family=Open+Sans:400,400i,600,700,700i,800,800i&display=swap');

body {
	font-family: 'Open Sans', sans-serif;
    position: relative;
    font-weight: 300;
    color: #212529;
    font-size: 15px;
    line-height: 1.8;
	background: #fff;
	overflow: hidden;
  }
ul,
h1, h2, h3, h4, h5, p {
    padding: 0;
    margin: 0;
}
.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 { 
	font-weight: 400; 
}  
li {
    list-style: none;
}
a, button {
    transition: 0.5s all;
    -webkit-transition: 0.5s all;
    -moz-transition: 0.5s all;
    -o-transition: 0.5s all;
    -ms-transition: 0.5s all;
    outline: 0px;
}
a:hover, a:focus {
    text-decoration: none;
}  
.form-control:focus {
    outline: 0;
    box-shadow: none;
    border-color: #f6822d !important;
	color: #333;
}  
button:focus {
    outline: 0;
    box-shadow: none;
    border-color: transparent;
}
img {
    max-width: 100%;
} 
input[type=date],
input[type=datetime-local],
input[type=email],
input[type=number],
input[type=password],
input[type=search],
input[type=tel],
input[type=text],
input[type=time],
input[type=url],
textarea,
select {
    -webkit-box-sizing: border-box;
	box-sizing: border-box;
    -webkit-box-shadow: none;
	box-shadow: none;
    outline: none;
    width: 100%;
    padding: 0 20px;
}
h1 {
	font-size: 3.50rem;
	font-weight: 300;
}
h2 {
	font-size: 2.25rem;
}
h4 {
	font-size: 1.25rem;
}
h5 {
	font-size: 1.125rem;
	line-height: 2rem;
}
::-webkit-input-placeholder {
	color: #898f96;
}
::-moz-placeholder {
	color: #898f96;
}
::-ms-input-placeholder {
	color: #898f96;
}
::-ms-input-placeholder {
	color: #898f96;
}
::placeholder {
	color: #898f96;
}
.display-5 {
	font-size: 1.58rem;
}
.display-6 {
	font-size: 1.22rem;
}
.font-23 {
	font-size: 23px;
}
.no-bg-color {
	background-color: transparent;
}
.text-theme {
	color: #f6822d;
}
.text-grey {
	color: #555555;
}
.text-danger {
	color: #f62d51 !important;
}
.table-responsive {
	white-space: nowrap;
}
.custom-badge {
	padding: 0.8rem 1.25rem; 
	border-radius: 50em; 
	background: #f1f3f6;
	margin-bottom: 5px;
    color: #fff;
    background-color: #6c757d;
}
.ctm-bg-danger {
	background: #ff6a6c;
}
.ctm-bg-warning {
	background: #feb71d;
}
.ctm-bg-info {
	background: #58c9e9;
}
.ctm-bg-success {
	background: #62d03a;
}
.ctm-border-radius {
	border-radius: 10px;
}
.ctm-btn-padding {
	padding: 10px 15px;
}
.ctm-text-sm {
	font-size: 14px;
	color: #888;
}
.ctm-padding {
	padding: 1.25rem;
}
.bg-theme {
    background-color: #f6822d;
}
.l-1 {
    left: 0.5rem;
}
.t-1 {
    top: 0.5rem;
}
.display-6 {
    font-size: 1.22rem;
}
.sidebar-overlay {
	background-color: rgba(0, 0, 0, 0.6);
    z-index: 1048;
    height: 100%;
    width: 100%;
    position: fixed;
    top: 0;
    display: none;
    overflow: hidden;
}

/*-----------------------
	 02.BUTTON
-------------------------*/

.btn-theme {
	border-bottom: 5px solid #d86500;
    border-radius: 10px;
    background: #f6822d;
}
.btn-theme:hover, .btn-theme:focus {
    box-shadow: 0 9px 14px 1px rgba(214, 211, 208, 0.56);
}
.btn-secondary {
	background: #7347c1;
}
.btn-outline { 
	border: 1px solid #ffffff;
}
.text-primary {
    color: #353847!important;
}
.text-pink {
	color: #F00F89 !important;
}
.bg-primary {
    background-color: #f6822d!important;
}
.badge-primary {
    color: #fff;
    background-color: #f6822d;
}
a.text-primary:focus, a.text-primary:hover {
    color: #f6822d !important;
}
a.text-warning:focus, a.text-warning:hover {
    color:  #d86500!important;
}
.nav-tabs .nav-link:focus, .nav-tabs .nav-link:hover {
    color:  #d86500!important;
}
.bootstrap-datetimepicker-widget table td.active, .bootstrap-datetimepicker-widget table td.active:hover {
    background-color: #f6822d;
}
.btn-outline-primary {
    color: #f6822d;
    border-color: #f6822d;
}
.btn-outline-primary:hover {
    color: #fff;
    background-color: #f6822d;
    border-color: #f6822d;
}
.btn-white {
    background-color: #fff;
    border: 1px solid #ccc;
    color: #333;
}
.btn.focus, .btn:focus {
    outline: 0;
    box-shadow: none;
}
.btn-outline-primary:not(:disabled):not(.disabled).active, .btn-outline-primary:not(:disabled):not(.disabled):active, .show > .btn-outline-primary.dropdown-toggle {
    color: #fff;
    background-color: #f6822d;
    border-color: #f6822d;
}
.bg-light {
	background:#f4f4f4;
}
.btn-outline-success:hover {
	color: #28a745;
    border-color: #28a745;
    background: #d4eabb;
}
.btn-outline-danger:hover {
    border-color: #dc3545;
    background: #ffd3d7;
    color: #dc3545;
}
.btn-outline-warning:hover {
    color: #212529;
    background-color: #ffe79e;
    border-color: #ffc107;
}
.bg-success-light {
    background-color: rgba(15, 183, 107,0.12) !important;
    color: #26af48 !important;
}
.bg-danger-light {
    background-color: rgba(242, 17, 54,0.12) !important;
    color: #e63c3c !important;
}
.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
    border-bottom: 5px solid #a22b2b;
}
.btn-danger:hover {
    box-shadow: 0 9px 14px 1px rgba(214, 211, 208, 0.56);
	border-bottom: 5px solid #a22b2b;
}
.badge-light {
    color: #333;
    background-color: #eceff3;
    font-weight: 600;
}
.button-1 {
	color: #fff;
	border-bottom: 5px solid #d86500;
	background-image: -webkit-linear-gradient(30deg, #f6822d 50%, transparent 50%);
	background-image: linear-gradient(30deg, #f6822d 50%, transparent 50%);
	background-size: 900px;
	background-repeat: no-repeat;
	background-position: 0%;
	-webkit-transition: background .8s ease-in-out;
	transition: background .8s ease-in-out;
}
.button-1:hover {
	background-position: -900px;
	color: #fff;
	background-color: #353847;
	border-bottom: 5px solid #d86500;
}
.button-5 {
	background-color: #f6822d;
	background-image: -moz-linear-gradient(top, #d86500 0%, #f6822d 100%); 
	background-image: -webkit-linear-gradient(top, #d86500 0%,#f6822d 100%); 
	background-image: linear-gradient(to bottom, #d86500 0%,#f6822d 100%); 
	background-size: 900px;
	background-repeat: no-repeat;
	background-position: 0%;
	-webkit-transition: background 300ms ease-in-out;
	transition: background 300ms ease-in-out;
}
.button-5:hover {
	background-position: -900px;
	transition: background .5s ease-in-out;
}
.button-6 {
	background-color: #f1f1f1;
	background-image: -moz-linear-gradient(top, #fff 0%, #fff 100%); 
	background-image: -webkit-linear-gradient(top, #fff 0%,#fff 100%); 
	background-image: linear-gradient(to bottom, #fff 0%,#fff 100%); 
	background-size: 900px;
	background-repeat: no-repeat;
	background-position: 0%;
	-webkit-transition: background 300ms ease-in-out;
	transition: background .5s ease-in-out;
}
.button-6:hover {
	background-position: -900px;
	transition: background .5s ease-in-out;
}
.avatar-upload {
	position: relative;
	max-width: 205px;
    margin: 15px auto;
}
.avatar-upload .avatar-edit {
	position: absolute;
	right: 12px;
	z-index: 1;
	top: 10px;
}
.avatar-upload .avatar-edit input {
	display: none;
}
.avatar-upload .avatar-edit input + label {
	display: inline-block;
	width: 34px;
	height: 34px;
	margin-bottom: 0;
	border-radius: 100%;
	background: #FFFFFF;
	border: 1px solid transparent;
	box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.12);
	cursor: pointer;
	font-weight: normal;
	transition: all 0.2s ease-in-out;
}
.avatar-upload .avatar-edit input + label:hover {
	background: #f1f1f1;
	border-color: #d6d6d6;
}
.avatar-upload .avatar-edit input + label:after {
	content: "\f040";
	font-family: 'FontAwesome';
	color: #757575;
	position: absolute;
	top: 3px;
	left: 0;
	right: 0;
	text-align: center;
	margin: auto;
}
.avatar-upload .avatar-preview {
	width: 200px;
	height: 200px;
	position: relative;
	border-radius: 10px;
	border: 6px solid #f3f3f3;
	box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
}
.avatar-upload .avatar-preview > div {
	width: 100%;
	height: 100%;
	border-radius: 100%;
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
}
.avatar-preview div {
	background-image: url(../img/logo2.png);
}

/*-----------------
	Loader
-----------------------*/
.rt_nav_header.horizontal-layout.fixed-on-scroll.nav-bottom {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
    border-bottom: 1px solid #f2f2f2; 
	box-shadow: 0 0px 5px rgba(0, 0, 0, 0.2);
}
#loader-wrapper {
	background-color: #fff;
	bottom: 0;
	height: 100%;
	left: 0;
	position: fixed;
	right: 0;
	top: 0;
	width: 100%;
	z-index: 9999;
}
.loader {
	position: absolute;
	top: 50%;
	left: 40%;
	margin-left: 10%;
	transform: translate3d(-50%, -50%, 0);
}
.dot {
	width: 20px;
	height: 20px;
	background: #3ac;
	border-radius: 100%;
	display: inline-block;
	animation: slide 1s infinite;
}
.dot:nth-child(1) {
	animation-delay: 0.1s;
	background: #f6822d;
}
.dot:nth-child(2) {
	animation-delay: 0.2s;
	background: #f6822d;
}
.dot:nth-child(3) {
	animation-delay: 0.3s;
	background: #007bff;
}
.dot:nth-child(4) {
	animation-delay: 0.4s;
	background: #dc3545;
}
.dot:nth-child(5) {
	animation-delay: 0.5s;
	background: #28a745;
}
@-moz-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-webkit-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@-o-keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}

/*-----------------
	03. Select2
-----------------------*/

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #f5dd91f5;
    border: 1px solid #f6822d;
}
.select2-container--default.select2-container--focus .select2-selection--multiple {
    border: solid #f6822d 1px;
    outline: 0;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #f6822d;
	color: white;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
	height: 42px;
	right: 7px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow b {
	border-color: #e3e3e3 transparent transparent;
	border-style: solid;
	border-width: 6px 6px 0;
	height: 0;
	left: 50%;
	margin-left: -10px;
	margin-top: -2px;
	position: absolute;
	top: 50%;
	width: 0;
}
.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
	border-color: transparent transparent #e3e3e3;
	border-width: 0 6px 6px;
}
.select2-container .select2-selection--single .select2-selection__rendered {
	padding-right: 30px;
	padding-left: 15px;
}
.select2-container--default .select2-selection--multiple {
	border: 1px solid #e3e3e3;
	min-height: 40px;
}
.select2-dropdown {
	border: 1px solid #e3e3e3;
}
.select2-container--default .select2-selection--single {
	border-color: #e3e3e3;
	box-shadow: none;
	font-size: 15px;
	height: 44px;
	outline: none;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
	color: #495057;
	line-height: 42px;
	border-color: transparent;
	outline: 0;
	padding-left: 1.25rem;	
	font-size: 15px;
	font-weight: normal;	
}

/*-----------------------
	 04.HEADER
-------------------------*/

.card:hover {
	box-shadow: 1px 0px 20px rgba(0, 0, 0, 0.08)!important;
	-webkit-box-shadow: 0 10px 6px -6px #777;
	-moz-box-shadow: 0 10px 6px -6px #777;
	box-shadow: 0 10px 6px -6px #777;
}
.header-dropdown:hover>.dropdown-menu {
	display: block;
}
.header {
	background-color: #353847;
	padding: 0 15px 80px ;
	border-bottom-left-radius: 30px;
	border-bottom-right-radius: 30px;
	box-shadow: 0 6px 14px 1px rgba(62, 0, 124, 0.18);
}
.top-header-section {
	padding-bottom: 25px;
    padding-top: 25px;
}
.logo a img {
	width: 170px;
}
.header-menu-list {
	padding: 20px;
    border-radius: 10px;
} 
.user-notification-block ul li a.menu-style {   
    color: #7e8fa1;
    padding: 32px 14px;
}
.user-info a.menu-style {
	color: #7e8fa1; 
	padding: 18px 14px;
	padding-right: 0;
}
.user-notification-block ul li .badge {
    font-family: 'Open Sans', sans-serif;
    width: 23px;
    height: 23px;
    top: 14px;
    right: 5px;
    font-weight: 500;
    font-size: 14px;
}
nav ul li a {
	color: #333333;
}
nav ul li.active a,
nav ul li.focus a,
nav ul li.hover a {
	color: #f6822d;
}
.nav-link:focus, .nav-link:hover {
	background: #e2e2e2;
    border-radius: 10px;
}
.user-block .dropdown-toggle:after { 
	display: none;
}
.user-block .notification-dropdown-menu {
	min-width: 23rem; 
}
.user-block .user-info .notification-dropdown-menu {
	min-width: 5rem; 
	width: 300px;
}
.user-block .notification-dropdown-menu .dropdown-item p {
	width: 230px; 
}
.user-block .notification-dropdown-menu .media a.trash-btn {
	background: #ffe6e6;
	border-radius: 50%;
	width: 50px;  
	height: 50px;
	color: #990000;
}
.user-block .notification-dropdown-menu .media a.position-relative span { 
	font-size: 2rem;
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
}
.dropdown-item:focus, .dropdown-item:hover {
    color: #16181b;
    text-decoration: none;
    background-color: #f4f6f9;
}

/*---------------------
	05. CALENDAR
----------------------*/

.calendar-events {
    border: 1px solid transparent;
    cursor: move;
    padding: 10px 15px;
}
.calendar-events:hover {
	border-color: #e9e9e9;
    background-color: #fff;
}
.calendar-events i {
    margin-right: 8px;
}
.calendar {
	float: left;
	margin-bottom: 0;
}
.fc-toolbar.fc-header-toolbar {
    margin-bottom: 1.5rem;
}
.none-border .modal-footer {
	border-top: none;
}
.fc-toolbar h2 {
	font-size: 18px;
	font-weight: 600;
	font-family: 'Open Sans', sans-serif;
	line-height: 30px;
	text-transform: uppercase;
}
.fc-day-grid-event .fc-time {
	font-family: 'Open Sans', sans-serif;
}
.fc-day {
	background: #fff;
}
.fc-toolbar .fc-state-active,
.fc-toolbar .ui-state-active,
.fc-toolbar button:focus,
.fc-toolbar button:hover,
.fc-toolbar .ui-state-hover {
	z-index: 0;
}
.fc th.fc-widget-header {
	background: #f6822d;
    font-size: 14px;
    line-height: 20px;
    padding: 10px 0;
    text-transform: uppercase;
    color: #fff;
}
.fc-axis.fc-widget-header {
    width: 70px !important;
}
.fc-unthemed th,
.fc-unthemed td,
.fc-unthemed thead,
.fc-unthemed tbody,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-row,
.fc-unthemed .fc-popover {
	border-color: #f6822d;
}
.fc-basic-view .fc-day-number, 
.fc-basic-view .fc-week-number {
    padding: 2px 5px;
}
.fc-button {
	background: #f1f1f1;
	border: none;
	color: #797979;
	text-transform: capitalize;
	box-shadow: none !important;
	border-radius: 3px !important;
	margin: 0 3px !important;
	padding: 6px 12px !important;
	height: auto !important;
}
.fc-text-arrow {
	font-family: inherit;
	font-size: 16px;
}
.fc-state-hover {
	background: #f3f3f3;
}
.fc-state-highlight {
	background: #f0f0f0;
}
.fc-state-down,
.fc-state-active,
.fc-state-disabled {
	background-color: #f6822d !important;
	color: #fff !important;
	text-shadow: none !important;
}
.fc-cell-overlay {
	background: #f0f0f0;
}
.fc-unthemed .fc-today {
	background: #fff;
}
.fc-event {
	border-radius: 2px;
	border: none;
	color: #fff !important;
	cursor: move;
	font-size: 13px;
	margin: 1px 7px;
	padding: 5px 5px;
	text-align: center;
}
.fc-basic-view td.fc-week-number span {
	padding-right: 8px;
	font-weight: 700;
	font-family: 'Open Sans', sans-serif;
}
.fc-basic-view td.fc-day-number {
	padding-right: 8px;
	font-weight: 700;
	font-family: 'Open Sans', sans-serif;
}
.event-form .input-group .form-control {
	height: 40px;
}

/*-----------------------
	06. MOBILE MENU 
-------------------------*/

.offcanvas-menu {
    position: fixed;
    height: 100%;
    width: 0px;
    top: 0;
    right: 0;
    background: #fff;
    z-index: 989999;
    overflow-y: scroll;
    -webkit-transition: 0.3s ease;
    -o-transition: 0.3s ease;
    transition: 0.3s ease;
    -webkit-box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.3);
    box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.3);
    padding-bottom: 25px;
}

/*-----------------
	07. BREADCRUMP
------------------------*/

.custom-search .input-group-append { 
	border-radius: 0rem 5rem 5rem 0rem;
}
.custom-search button { 
	border-radius: 5rem !important;
}
.header-wrapper ul li.active {
    z-index: 2;
    color: #fff;
	background-color: #f6822d;
    border-color: #f6822d;
	border-radius: 10px;
	border-bottom: 5px solid #d86500;
	transition-duration: .5s;
}
.header-wrapper ul li:hover {
	background-color: #e8e8e8;
    border-color: #e8e8e8;
	border-radius: 10px;
	border-bottom: 5px solid #dedede;
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075);
}
.header-wrapper ul li.active:hover {
	background-color: #f6822d;
    border-color: #f6822d;
	border-radius: 10px;
	transform: translateY(1px);
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075);
	border-bottom: 5px solid #d86500;
	text-shadow: 0px -2px #d86500;
	transition-duration: .3s;
}
.header-wrapper div.append select {
	display: none;
	height: 45px;
	border: 1px solid #ececec;
	padding: 0 40px 0 20px;
	-webkit-border-radius: 4px;
	border-radius: 4px;
	-moz-appearance: none;
	-webkit-appearance: none;
	cursor: pointer;
}
.header-wrapper .nav-arrow {
	right: 14px;
}
img:hover {
	box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
}
a .avatar:hover > img {
	-webkit-transform: scale(1.5);
	transform: scale(1.5);
	-webkit-transition: .3s ease-in-out;
	transition: .3s ease-in-out;	
}

/*-----------------------
   08. CONTENT
-------------------------*/

.page-wrapper {
	padding: 0px 15px 0 15px;
    margin-top: -40px;
}
.list-group-item.active {
    z-index: 2;
    color: #fff;
    background-color: #f6822d;
    border-color: #f6822d;
}
.list-group-item:first-child {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}
.list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
}
.page-title {
	border-left: 3px solid #f6822d;
    border-right: 3px solid #f6822d;
}
.card {
    border: 1px solid #ededed;
    margin-bottom: 30px;
    border-radius: 10px !important;
}
.card-header {
    background: none;
    padding: 1.25rem;
	border-bottom: 1px solid #ededed;
}
.card-title {
    color: #333;  
    margin-bottom: 20px;
}
hr {
	border-top: 1px solid #ededed;
}
.list-group-item {
	border: 1px solid #ededed;
}
.team-lead .media {
    align-items: center;
}
.e-avatar:first-of-type {
    margin-left: 0 !important;
}
.e-avatar {
    background-color: #fff;
    border: 3px solid #e7e7e7;
    border-radius: 50%;
    height: 40px;
    position: relative;
    width: 40px;
    margin-left: -20px;
}
.e-avatar > img {
    border-radius: 50%;
    width: 100%;
}
.dash-card-container {
    display: flex;
    flex-direction: row;
    flex-grow: 1;
    min-height: 45px;
}
.dash-card-icon {
    align-items: center;
    display: flex;
    flex-direction: row;
    padding-right: 15px;
}
.dash-card-icon i {
    width: 20px;
}
.today-list .card-body .dash-card-content {
    color: #333;
}
.dash-card-content p {
    margin-bottom: 0;
}
.dash-card-avatars {
    align-items: center;
    display: flex;
    flex-grow: 1;
    justify-content: flex-end;
    padding: 0 15px;
    padding-right: 0;
}
.dash-card-content {
    align-items: center;
    display: flex;
    flex-direction: row;
}
.notice-board {
    display: flex;
    align-items: center;
}
.time-list {
    display: flex;
    flex-grow: 1;
}
.dash-stats-list {
    align-items: center;
    display: flex;
    flex-flow: column wrap;
    flex-grow: 1;
    padding-left: 15px;
    padding-right: 15px;
}
.dash-stats-list span {
	min-width: 105px;
}
.dash-stats-list h4 {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 15px;
}
.dash-stats-list + .dash-stats-list {
    border-left: 1px solid #e7e7e7;
}
.dash-stats-list {
    align-items: center;
    display: flex;
    flex-flow: column wrap;
    flex-grow: 1;
    padding-left: 5px;
    padding-right: 5px;
}
.dash-widget .card-body {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.card-icon {
    float: left;
    width: 60px;
    height: 60px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    vertical-align: middle;
    background: #26a69a;
}
.card-icon .fa {
    font-size: 24px;
    margin: auto;
    color: #fff;
}
.card-right {
    padding-left: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.card-right .card-title {
    margin-bottom: 5px;
    font-size: 16px;
}
.card-right p {
    font-size: 20px;
    font-weight: 700;
}
.drop-active a {
	min-width: 90px;
}
.avatar {
	background-color: #f6822d;
	border-radius: 50%;
	color: #fff;
	display: inline-block;
	font-weight: 500;
	height: 38px;
	line-height: 34px;
	margin: 0 -17px 0 0;
	text-align: center;
	text-decoration: none;
	text-transform: uppercase;
	vertical-align: middle;
	width: 38px;
	position: relative;
	white-space: nowrap;
	border: 3px solid #fff;
	box-shadow: 0px 1px 5px 0px rgba(53, 53, 53, 0.3);
}
.avatar:hover {
	color: #fff;
}
.avatar.avatar-xs {
	width: 24px;
	height: 24px;
}
.avatar > img {
	border-radius: 50%;
	display: block;
	overflow: hidden;
	width: 100%;
}
.nav-pills .nav-link.active, .nav-pills .show > .nav-link {
    color: #fff;
    background-color: #f6822d;
	border-radius: 10px;
}
.nav-pills .nav-link {
    color: #f6822d;
}
.quicklink-sidebar-menu ul li a {
	display: block;
}
.header-menu-list div ul li a {
	display: block;
}
.yalign {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}
.admin-activ .recent-comment {
	max-height: 390px;
	overflow-y: auto;
	position: relative;
}
.btn-add {
	margin-bottom: 30px;
}
.last-card-row, .last-card-row1 {
	margin-bottom: 0;
}

/*-----------------
	09. TABLE
-----------------------*/

.custom-table tr {
	background-color: #fff;
}
.table.custom-table > tbody > tr > td,
.table.custom-table > tbody > tr > th,
.table.custom-table > tfoot > tr > td,
.table.custom-table > tfoot > tr > th,
.table.custom-table > thead > tr > td,
.table.custom-table > thead > tr > th {
	padding: 10px 8px;
	vertical-align: middle;
}
.table.custom-table > tbody > tr > td:first-child,
.table.custom-table > thead > tr > th:first-child {
	padding-left: 20px;
}
.table.custom-table > tbody > tr > td:last-child,
.table.custom-table > thead > tr > th:last-child {
	padding-right: 20px;
}
.table.custom-table > thead > tr > th {
	font-weight: bold;
	border-top: 0;
	text-transform: capitalize;
}
.table td, .table th {
	padding-left: 20px;
}
.admin-table.table td, .admin-table.table th {
	padding-left: 0;
	vertical-align: middle;
}
.table {
	color: #333;
	margin-bottom: 0;
}
.table.table-white {
	background-color: #fff;
}
.table thead th {
	letter-spacing: 0.05em;
	border-bottom: 0px solid #dee2e6;
}
.table-striped > tbody > tr:nth-of-type(2n+1) {
	background-color: #f6f6f6;
}
table.table td .avatar {
	margin: 0 5px 0 0;
}
table.table td h2 {
	display: inline-block;
	font-size: inherit;
	font-weight: 400;
	margin: 0;
	padding: 0;
	vertical-align: middle;
}
table.table td h2.table-avatar {
	align-items: center;
	display: inline-flex;
	font-size: inherit;
	font-weight: 400;
	margin: 0;
	padding: 0;
	vertical-align: middle;
	white-space: nowrap;
}
table.table td h2.table-avatar.blue-link a {
	color: #007bff;
}
table.table td h2 a {
	color: #333;
}
table.table td h2 a:hover {
    color: #f6822d;
}
table.table td h2 span {
	color: #888;
	display: block;
	font-size: 12px;
	margin-top: 3px;
}
table.dataTable {
	margin-bottom: 15px !important;
	margin-top: 15px !important;
}
.table-hover tbody tr:hover {
	background-color: #f7f7f7;
	color: #212529;
}
table.dataTable thead > tr > th.sorting_asc,
table.dataTable thead > tr > th.sorting_desc,
table.dataTable thead > tr > th.sorting,
table.dataTable thead > tr > td.sorting_asc,
table.dataTable thead > tr > td.sorting_desc,
table.dataTable thead > tr > td.sorting {
	padding-right: 30px !important;
}
.form-check-input {
	position: absolute;
	margin-top: -0.7rem;
	height: auto;
	width: auto;
}
.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
    color: #fff;
}
.custom-control-input:checked~.custom-control-label::before {
    color: #fff;
    border-color: #f6822d;
    background-color: #f6822d;
}
.form-control {
	border-color: #e3e3e3;
	box-shadow: none;
	font-size: 15px;
	height: 44px;
	color: #333;
}
.company-logo .form-control {
	border-color: #e3e3e3;
	box-shadow: none;
	font-size: 15px;
	height: 40px;
}
.form-control:focus {
	border-color: #ccc;
	box-shadow: none;
	outline: 0 none;
}
.form-control[disabled],
fieldset[disabled] .form-control {
	cursor: not-allowed;
}
.form-group.row {
	align-items: center;
}
.form-group {
    margin-bottom: 1.25rem;
}
.accordion > .card:first-of-type {
    border-bottom: 0;
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
}
.table td, .table th {
	border-top: 0px solid #dee2e6;
}
.accordion > .card:not(:first-of-type):not(:last-of-type) {
    border-bottom: 0;
    border-radius: 10px;
}
.employee-office-table .table.custom-table > tbody > tr > td:first-child, .employee-office-table .table.custom-table > thead > tr > th:first-child {
    padding-left: 0;
}
.employee-office-table .table.custom-table > tbody > tr > td:last-child, .employee-office-table .table.custom-table > thead > tr > th:last-child {
    padding-right: 0;
}

/*-----------------
	10. HEADER SEARCH
---------------------*/

.btn-ctm-space {
	padding: 10px 20px;
}
.top-nav-search {
    margin-right: 15px;
	margin-left: 15px;
	transition: all 0.4s ease 0s;
}
.top-nav-search:hover {
	margin-right: 20px;
	transition: all 0.4s ease 0s;
}
.top-nav-search form {
	margin-top: 5px;
	position: relative;
    width: auto;
}
.top-nav-search .form-control {
	background-color: rgb(53, 56, 71);
	border-color: rgba(150, 148, 148, 0.9);
    border-radius: 50px;
    color: #fff;
    height: 42px;
    padding: 10px 50px 10px 15px;
}
.offcanvas-menu .top-nav-search form {
	margin-top: 20px;
}
.top-nav-search .btn {
    background-color: transparent;
    border-color: transparent;
    color: #fff;
    min-height: 40px;
    padding: 7px 15px;
    position: absolute;
    right: 0;
    top: 0;
}
.top-nav-search .form-control::-webkit-input-placeholder {
	color: #fff;
}
.top-nav-search .form-control::-moz-placeholder {
	color: #fff;
}
.top-nav-search .form-control:-ms-input-placeholder {
	color: #fff;
}
.top-nav-search .form-control::-ms-input-placeholder {
	color: #fff;
}
.top-nav-search .form-control::placeholder {
	color: #fff;
}
.top-nav-search.active form {
    display: block;
    left: 0;
    position: absolute;
}
.document-up a {
    padding: 15px;
    background: #fff;
    margin-bottom: 0;
    border-radius: 5px;
    display: block;
    color: #333;
    border: 1px solid #e5e5e5;
}
.leave-img {
	width: 20px;
}
.dropdown-item.active, .dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #f6822d;
}
.dropdown-menu {
    font-size: 15px;
}
.widget-profile .profile-info-widget {
	display: block;
	text-align: center;
}
.widget-profile .profile-info-widget .booking-doc-img {
    display: inline-block;
    margin: 0 0 10px;
    width: auto;
    padding: 5px;
    background-color: #f7f7f7;
    border-radius: 50%;
}
.widget-profile .profile-info-widget .booking-doc-img img {
    border-radius: 50%;
	height: 60px;
    width: 60px;
}
.profile-info-widget {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
    text-align: left;
}
.profile-info-widget .booking-doc-img {
    margin-right: 15px;
}
.profile-info-widget .booking-doc-img img {
    border-radius: 4px;
    height: 90px;
    width: 90px;
    object-fit: cover;
}
.profile-det-info {
	overflow: hidden;
}
.profile-det-info h3 {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
.user-info a .user-avatar img {
	margin-bottom: 3px;
}

/*-----------------
	11. LOGIN
-----------------------*/

.login-body {
    display: table;
    height: 100vh;
    min-height: 100vh;
}
.login-wrapper {
	width: 100%;
	height: 100%;
	display: table-cell;
	vertical-align: middle;
}
.login-wrapper .loginbox {
    background-color: #fff;
    border-radius: 10px;  
	display: flex;
    margin: 1.875rem auto;
    max-width: 800px;
    min-height: 500px;
    width: 100%;
}
.login-wrapper .loginbox .login-left {
    align-items: center;
    background: #f6822d;
    border-radius: 6px 0 0 6px;
    flex-direction: column;
    justify-content: center;
    padding: 80px;
    width: 400px;
    display: flex;
}
.login-wrapper .loginbox .login-right {
    align-items: center;
    display: flex;
    justify-content: center;
    padding: 40px;
    width: 400px;
}
.login-wrapper .loginbox .login-right .login-right-wrap {
    max-width: 100%;
    flex: 0 0 100%;
}
.login-wrapper .loginbox .login-right h1 {
    font-size: 26px;
    font-weight: 500;
    margin-bottom: 5px;
    text-align: center;
}
.account-subtitle {
    color: #4c4c4c;
    font-size: 17px;
    margin-bottom: 1.875rem;
    text-align: center;
}
.login-wrapper .loginbox .login-right .forgotpass a {
	color: #a0a0a0;
}
.login-wrapper .loginbox .login-right .forgotpass a:hover {
	color: #333;
	text-decoration: underline;
}
.login-wrapper .loginbox .login-right .dont-have {
	color: #a0a0a0;
	margin-top: 1.875rem;
}
.login-wrapper .loginbox .login-right .dont-have a {
	color: #333;
}
.login-wrapper .loginbox .login-right .dont-have a:hover {
	text-decoration: underline;
}
.social-login {
	text-align: center;
}
.social-login > span {
	color: #a0a0a0;
	margin-right: 8px;
}
.social-login > a {
	background-color: #ccc;
	border-radius: 4px;
	color: #fff;
	display: inline-block;
	font-size: 18px;
	height: 32px;
	line-height: 32px;
	margin-right: 6px;
	text-align: center;
	width: 32px;
}
.social-login > a:last-child {
	margin-right: 0;
}
.social-login > a.facebook {
	background-color: #4b75bd;
}
.social-login > a.google {
	background-color: #fe5240;
}
.login-or {
	color: #a0a0a0;
	margin-bottom: 20px;
	margin-top: 20px;
	padding-bottom: 10px;
	padding-top: 10px;
	position: relative;
}
.or-line {
	background-color: #e5e5e5;
	height: 1px;
	margin-bottom: 0;
	margin-top: 0;
	display: block;
}
.span-or {
	background-color: #fff;
	display: block;
	left: 50%;
	margin-left: -20px;
	position: absolute;
	text-align: center;
	text-transform: uppercase;
	top: 0;
	width: 42px;
}
.inner-wrapper {
	width: 100%;
	height: 100vh;
	min-height: 100vh;
}
a.list-group-item {
	color: #333;
}
.tab-list-emp li a {
	padding: 0 15px;
}
.nav-tabs .nav-item.show .list-view, .nav-tabs .list-view.active {
    color: #f6822d;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}
.nav-tabs .nav-item.show .grid-view, .nav-tabs .grid-view.active {
    color: #f6822d;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}
.nav-tabs .grid-view, .nav-tabs .list-view {
	color: #f6822d;
}
.item-animated a span:hover {
	font-size: 25px;
    transition: all 0.4s ease 0s;
}

/*-----------------------
   12. SETTINGS
-------------------------*/

textarea.form-control {
    color: #898f96;
}
.coll-arrow:after {
	font-family: FontAwesome;
    font-weight: 900;
	float: right;
	position: relative;
}
.coll-arrow:not(.collapsed):after {
    content: "\f106";
}
.coll-arrow.collapsed:after {
	content: "\f107";
}

/*--------------------------
	14. RESPONSIVE
---------------------------*/

/*-----------------------
    Large devices (desktops, less than 1200px)
-------------------------*/

@media only screen and (max-width: 1199.98px) {
	.header-menu-list {
		padding: 15px 10px;
	}
	.header-menu-list ul li a { 
		font-size: 13px; 
	}
	h1 {
		font-size: 2.75rem;
	}
	h2 {
		font-size: 2.03rem;
	}
	.h3, h3 {
		font-size: 1.40rem;
	}
	h5 {
		font-size: 1rem;
	} 
	.last-card-row {
		margin-bottom: 30px;
	}
	.last-card-row1 {
		margin-bottom: 0;
	}
	.btn-ctm-space {
		padding: 10px 12px;
	}
}

/*-----------------------
    Medium devices (tablets, less than 992px)
-------------------------*/

@media only screen and (max-width: 991.98px) {

	h4 {
		font-size: 1.20rem;
	}
	.header-menu-list ul li a { 
		font-size: 18px;
	}
	.user-menu-items a {
		color: #212529; 
	}
	.header {
		padding: 0 15px 45px;
	}

}
/*-----------------------
    Small devices (landscape phones, 768px)
-------------------------*/

@media only screen and (max-width: 767.98px) {

	.user-block .dropdown-menu { 
		display: block; 
		opacity: 0;
		visibility: hidden;
		top: calc(100% + 20px);
	}
	.dropdown:hover > .notification-dropdown-menu {
		opacity: 1;
		visibility: visible;
		top: 100%;
	}
	h1 {
		font-size: 2.75rem;
	}
	h2 {
		font-size: 2.03rem;
	}
	.h3, h3 {
		font-size: 1.30rem;
	}
	h5 {
		font-size: 1rem;
	}  
	.header-wrapper div.append { 
		width: 100%;
	}
	.header-wrapper div.append ul {
		display: none; 
	}
	.header-wrapper div.append select { 
		display: block; 
		position: relative; 
		background: #fff;
		font-size: 16px;
	}
	.header-menu-list {
		padding: 10px;
	}
	.header-wrapper div.append select:after { 
		position: absolute;
		content: "\e874"; 
		font-family: 'Linearicons-Free';
	}
	.offcanvas-menu .user-notification-block ul li a.menu-style {
		padding: 27px 14px 14px;
	}
	.user-notification-block ul li .badge {
		right: 23px;
	}
	.login-wrapper .loginbox .login-left {
		display: none;
	}
	.login-wrapper .loginbox {
		max-width: 450px;
		min-height: unset;
	}
	.login-wrapper .loginbox .login-right {
		float: none;
		padding: 1.875rem;
		width: 100%;
	}
	.top-nav-search form {
		width: 212px;
	}
}

/*-----------------------
    Extra small devices (portrait phones, less than 576px)
-------------------------*/

@media only screen and (max-width: 575.98px) {

	h1 {
		font-size: 1.22rem;
	}
	h2 {
		font-size: 1.67rem;
	}
	.h3, h3 {
		font-size: 1.20rem;
	}
	h4 {
		font-size: 1rem;
	}
	p {
		font-size: .9rem;
	}
	.top-header-section {
		padding-bottom: 5px;
		padding-top: 5px;
	}
	.page-wrapper {
		padding: 0;
		margin-top: -45px;
	}
	.header {
		padding: 0 0px 45px;
	}
	.card {
		margin-bottom: 15px;
	}
	.fc-toolbar .fc-right {
		display: inline-block;
		float: none;
		margin: 10px auto 0;
		width: 200px;
		clear: both;
	}
	.fc-toolbar .fc-left {
		float: none;
		margin: 0 auto;
		width: 200px;
	}
	.fc-toolbar .fc-center {
		display: inline-block;
		width: 100%;
		text-align: center;
	}
	.fc-toolbar .fc-center h2 {
		width: 100%;
	}
	.fc button {
		font-size: 14px;
	}
	.nav-pills .nav-link {
		padding: .5rem .5rem;
	}
	.tab-list-emp li a {
		padding: 0 5px;
	}
	.btn-add {
		margin-bottom: 15px;
	}
	.last-card-row1 {
		margin-bottom: 0;
	}
}