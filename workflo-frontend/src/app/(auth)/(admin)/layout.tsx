'use client';

import React from 'react';
import { AdminLayout } from '@/components/layout/AdminLayout';
import RouteTransition from '@/components/RouteTransition';
import RoleGuard from '@/components/auth/RoleGuard';

interface AdminLayoutWrapperProps {
  children: React.ReactNode;
}

const AdminLayoutWrapper: React.FC<AdminLayoutWrapperProps> = ({ children }) => {
  return (
    <RoleGuard allowedRoles={['admin', 'hr']}>
      <AdminLayout>
        <RouteTransition>
          {children}
        </RouteTransition>
      </AdminLayout>
    </RoleGuard>
  );
};

export default AdminLayoutWrapper;
