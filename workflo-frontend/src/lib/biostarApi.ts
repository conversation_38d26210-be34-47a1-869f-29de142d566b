import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// BioStar 2 API Configuration
const BIOSTAR_API_URL = process.env.NEXT_PUBLIC_BIOSTAR_API_URL || 'https://bs2api.biostar2.com';
const BIOSTAR_USERNAME = process.env.NEXT_PUBLIC_BIOSTAR_USERNAME || '';
const BIOSTAR_PASSWORD = process.env.NEXT_PUBLIC_BIOSTAR_PASSWORD || '';

// Mock mode configuration
const MOCK_MODE = !BIOSTAR_USERNAME || !BIOSTAR_PASSWORD || process.env.NEXT_PUBLIC_BIOSTAR_MOCK_MODE === 'true';

// BioStar 2 API Types
export interface BiostarUser {
  id: string;
  user_id: string;
  name: string;
  email?: string;
  phone?: string;
  department?: string;
  position?: string;
  start_datetime?: string;
  expiry_datetime?: string;
  disabled: boolean;
  created: string;
  updated: string;
}

export interface BiostarEvent {
  id: string;
  user_id: string;
  device_id: string;
  event_type: 'ENTRY' | 'EXIT' | 'DENIED';
  datetime: string;
  user_name?: string;
  device_name?: string;
}

export interface BiostarAttendance {
  user_id: string;
  user_name: string;
  date: string;
  first_in?: string;
  last_out?: string;
  total_hours?: number;
  break_time?: number;
  overtime?: number;
  events: BiostarEvent[];
}

export interface BiostarDevice {
  id: string;
  name: string;
  ip: string;
  port: number;
  status: 'ONLINE' | 'OFFLINE';
  type: string;
  location?: string;
}

export interface BiostarAuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

// Create axios instance for BioStar 2 API
const biostarClient: AxiosInstance = axios.create({
  baseURL: BIOSTAR_API_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// BioStar Token Management
class BiostarTokenManager {
  private static readonly BIOSTAR_TOKEN_KEY = 'biostar_access_token';
  private static readonly BIOSTAR_TOKEN_EXPIRY_KEY = 'biostar_token_expiry';

  static getToken(): string | null {
    if (typeof window === 'undefined') return null;
    const token = localStorage.getItem(this.BIOSTAR_TOKEN_KEY);
    const expiry = localStorage.getItem(this.BIOSTAR_TOKEN_EXPIRY_KEY);

    if (token && expiry) {
      const expiryTime = parseInt(expiry);
      if (Date.now() < expiryTime) {
        return token;
      } else {
        this.clearToken();
      }
    }
    return null;
  }

  static setToken(token: string, expiresIn: number): void {
    if (typeof window === 'undefined') return;
    const expiryTime = Date.now() + (expiresIn * 1000);
    localStorage.setItem(this.BIOSTAR_TOKEN_KEY, token);
    localStorage.setItem(this.BIOSTAR_TOKEN_EXPIRY_KEY, expiryTime.toString());
  }

  static clearToken(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.BIOSTAR_TOKEN_KEY);
    localStorage.removeItem(this.BIOSTAR_TOKEN_EXPIRY_KEY);
  }
}

// Request interceptor to add auth token
biostarClient.interceptors.request.use(
  async (config) => {
    let token = BiostarTokenManager.getToken();

    // If no valid token, authenticate first
    if (!token) {
      try {
        const authResponse = await biostarClient.post<BiostarAuthResponse>('/login', {
          username: BIOSTAR_USERNAME,
          password: BIOSTAR_PASSWORD,
        });

        token = authResponse.data.access_token;
        BiostarTokenManager.setToken(token, authResponse.data.expires_in);
      } catch (error) {
        console.error('BioStar authentication failed:', error);
        throw error;
      }
    }

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
biostarClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      BiostarTokenManager.clearToken();

      try {
        const authResponse = await biostarClient.post<BiostarAuthResponse>('/login', {
          username: BIOSTAR_USERNAME,
          password: BIOSTAR_PASSWORD,
        });

        const token = authResponse.data.access_token;
        BiostarTokenManager.setToken(token, authResponse.data.expires_in);

        // Retry original request with new token
        originalRequest.headers.Authorization = `Bearer ${token}`;
        return biostarClient(originalRequest);
      } catch (authError) {
        console.error('BioStar re-authentication failed:', authError);
        return Promise.reject(authError);
      }
    }

    return Promise.reject(error);
  }
);

// Mock Data
const mockUsers: BiostarUser[] = [
  {
    id: 'user-001',
    user_id: 'EMP001',
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    department: 'Engineering',
    position: 'Software Engineer',
    disabled: false,
    created: '2024-01-01T00:00:00Z',
    updated: '2024-12-21T00:00:00Z'
  },
  {
    id: 'user-002',
    user_id: 'EMP002',
    name: 'Jane Smith',
    email: '<EMAIL>',
    department: 'HR',
    position: 'HR Manager',
    disabled: false,
    created: '2024-01-01T00:00:00Z',
    updated: '2024-12-21T00:00:00Z'
  }
];

const mockDevices: BiostarDevice[] = [
  {
    id: 'dev-001',
    name: 'Main Entrance',
    ip: '*************',
    port: 8080,
    status: 'ONLINE',
    type: 'Fingerprint Scanner',
    location: 'Main Building'
  },
  {
    id: 'dev-002',
    name: 'Office Floor',
    ip: '*************',
    port: 8080,
    status: 'ONLINE',
    type: 'Face Recognition',
    location: 'Second Floor'
  },
  {
    id: 'dev-003',
    name: 'Back Entrance',
    ip: '*************',
    port: 8080,
    status: 'OFFLINE',
    type: 'Fingerprint Scanner',
    location: 'Back Building'
  }
];

// BioStar API Service (with mock mode support)
class BiostarApiService {
  // Generic request method
  private async request<T>(config: AxiosRequestConfig): Promise<T> {
    if (MOCK_MODE) {
      // Return mock data instead of making real API calls
      return this.handleMockRequest<T>(config);
    }

    try {
      const response: AxiosResponse<T> = await biostarClient(config);
      return response.data;
    } catch (error) {
      console.error('BioStar API Request Error:', error);
      throw error;
    }
  }

  // Handle mock requests
  private async handleMockRequest<T>(config: AxiosRequestConfig): Promise<T> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));

    const url = config.url || '';
    const method = config.method?.toUpperCase() || 'GET';

    console.log(`[MOCK] ${method} ${url}`);

    // Mock different endpoints
    if (url.includes('/users/')) {
      const userId = url.split('/users/')[1];
      const user = mockUsers.find(u => u.user_id === userId || u.id === userId);
      if (user) {
        return user as T;
      }
      throw new Error('User not found');
    }

    if (url.includes('/users')) {
      return { results: mockUsers, count: mockUsers.length } as T;
    }

    if (url.includes('/devices')) {
      return { results: mockDevices, count: mockDevices.length } as T;
    }

    if (url.includes('/events')) {
      const mockEvents: BiostarEvent[] = Array.from({ length: 5 }, (_, i) => ({
        id: `event-${i}`,
        user_id: 'EMP001',
        device_id: 'dev-001',
        event_type: i % 2 === 0 ? 'ENTRY' : 'EXIT',
        datetime: new Date(Date.now() - i * 60 * 60 * 1000).toISOString(),
        user_name: 'John Doe',
        device_name: 'Main Entrance'
      }));
      return { results: mockEvents, count: mockEvents.length } as T;
    }

    if (url.includes('/login')) {
      return {
        access_token: 'mock_token_' + Date.now(),
        token_type: 'Bearer',
        expires_in: 3600
      } as T;
    }

    if (url.includes('/health')) {
      return {
        status: 'ok',
        timestamp: new Date().toISOString()
      } as T;
    }

    // Default mock response
    return {} as T;
  }

  // GET request
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'GET', url });
  }

  // POST request
  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'POST', url, data });
  }

  // PUT request
  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PUT', url, data });
  }

  // DELETE request
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'DELETE', url });
  }

  // Authentication
  async authenticate(): Promise<BiostarAuthResponse> {
    return this.post<BiostarAuthResponse>('/login', {
      username: BIOSTAR_USERNAME,
      password: BIOSTAR_PASSWORD,
    });
  }

  // Users
  async getUsers(limit = 100, offset = 0): Promise<{ results: BiostarUser[]; count: number }> {
    return this.get(`/users?limit=${limit}&offset=${offset}`);
  }

  async getUserById(userId: string): Promise<BiostarUser> {
    return this.get(`/users/${userId}`);
  }

  async createUser(userData: Partial<BiostarUser>): Promise<BiostarUser> {
    return this.post('/users', userData);
  }

  async updateUser(userId: string, userData: Partial<BiostarUser>): Promise<BiostarUser> {
    return this.put(`/users/${userId}`, userData);
  }

  async deleteUser(userId: string): Promise<void> {
    return this.delete(`/users/${userId}`);
  }

  // Events (Access logs)
  async getEvents(
    startDate?: string,
    endDate?: string,
    userId?: string,
    deviceId?: string,
    limit = 100,
    offset = 0
  ): Promise<{ results: BiostarEvent[]; count: number }> {
    const params = new URLSearchParams();
    if (startDate) params.append('start_datetime', startDate);
    if (endDate) params.append('end_datetime', endDate);
    if (userId) params.append('user_id', userId);
    if (deviceId) params.append('device_id', deviceId);
    params.append('limit', limit.toString());
    params.append('offset', offset.toString());

    return this.get(`/events?${params.toString()}`);
  }

  // Devices
  async getDevices(): Promise<{ results: BiostarDevice[]; count: number }> {
    return this.get('/devices');
  }

  async getDeviceById(deviceId: string): Promise<BiostarDevice> {
    return this.get(`/devices/${deviceId}`);
  }

  // Attendance Reports
  async getAttendanceReport(
    startDate: string,
    endDate: string,
    userId?: string
  ): Promise<BiostarAttendance[]> {
    const params = new URLSearchParams();
    params.append('start_date', startDate);
    params.append('end_date', endDate);
    if (userId) params.append('user_id', userId);

    return this.get(`/attendance/report?${params.toString()}`);
  }

  // Real-time monitoring
  async getRealtimeEvents(limit = 50): Promise<BiostarEvent[]> {
    return this.get(`/events/realtime?limit=${limit}`);
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.get('/health');
  }
}

// Create and export BioStar API service instance
export const biostarApi = new BiostarApiService();

// Export token manager for external use
export { BiostarTokenManager };

// Export the configured axios instance for custom requests
export { biostarClient };
