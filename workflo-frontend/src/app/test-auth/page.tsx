'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/providers/AuthProvider';
import { useRouter } from 'next/navigation';
import Button from '@/components/ui/Button';

const TestAuthPage: React.FC = () => {
  const { user, isAuthenticated, isLoading, login, logout, getCurrentUser } = useAuth();
  const router = useRouter();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunningTest, setIsRunningTest] = useState(false);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
    console.log(`Test: ${message}`);
  };

  const runAuthTest = async () => {
    setIsRunningTest(true);
    setTestResults([]);

    try {
      addTestResult('Starting authentication test...');

      // Test 1: Check initial state
      addTestResult(`Initial state - isAuthenticated: ${isAuthenticated}, user: ${user?.email || 'null'}`);

      // Test 2: Try to get current user
      addTestResult('Testing getCurrentUser...');
      try {
        await getCurrentUser();
        addTestResult(`getCurrentUser success - isAuthenticated: ${isAuthenticated}, user: ${user?.email || 'null'}`);
      } catch (error) {
        addTestResult(`getCurrentUser failed: ${error}`);
      }

      // Test 3: Try login
      addTestResult('Testing login with admin credentials...');
      try {
        await login({ email: '<EMAIL>', password: 'admin123' });
        addTestResult(`Login success - isAuthenticated: ${isAuthenticated}, user: ${user?.email || 'null'}`);

        // Test 4: Try to navigate to dashboard
        addTestResult('Attempting to navigate to dashboard...');
        router.push('/dashboard');
        addTestResult('Navigation to dashboard initiated');

      } catch (error) {
        addTestResult(`Login failed: ${error}`);
      }

    } catch (error) {
      addTestResult(`Test failed: ${error}`);
    } finally {
      setIsRunningTest(false);
    }
  };

  const testLogout = async () => {
    addTestResult('Testing logout...');
    try {
      logout();
      addTestResult(`Logout success - isAuthenticated: ${isAuthenticated}, user: ${user?.email || 'null'}`);
    } catch (error) {
      addTestResult(`Logout failed: ${error}`);
    }
  };

  const checkLocalStorage = () => {
    addTestResult('Checking localStorage...');
    const accessToken = localStorage.getItem('access_token');
    const refreshToken = localStorage.getItem('refresh_token');
    const mockUserEmail = localStorage.getItem('mock_user_email');
    const authStorage = localStorage.getItem('auth-storage');

    addTestResult(`Access Token: ${accessToken ? 'Present' : 'Not found'}`);
    addTestResult(`Refresh Token: ${refreshToken ? 'Present' : 'Not found'}`);
    addTestResult(`Mock User Email: ${mockUserEmail || 'Not found'}`);
    addTestResult(`Auth Storage: ${authStorage ? 'Present' : 'Not found'}`);
  };

  useEffect(() => {
    addTestResult('Component mounted');
    addTestResult(`Initial state - isAuthenticated: ${isAuthenticated}, isLoading: ${isLoading}, user: ${user?.email || 'null'}`);
  }, []);

  useEffect(() => {
    addTestResult(`Auth state changed - isAuthenticated: ${isAuthenticated}, user: ${user?.email || 'null'}`);
  }, [isAuthenticated, user]);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Authentication Test Page</h1>

          {/* Current State */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h2 className="text-lg font-semibold mb-2">Current State</h2>
            <div className="space-y-1 text-sm">
              <p><strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
              <p><strong>Is Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
              <p><strong>User:</strong> {user ? `${user.first_name} ${user.last_name} (${user.email})` : 'None'}</p>
              <p><strong>User Role:</strong> {user?.role || 'None'}</p>
            </div>
          </div>

          {/* Test Controls */}
          <div className="mb-6 space-y-4">
            <div className="flex flex-wrap gap-4">
              <Button
                onClick={runAuthTest}
                disabled={isRunningTest}
                loading={isRunningTest}
              >
                Run Full Auth Test
              </Button>

              <Button
                variant="secondary"
                onClick={checkLocalStorage}
              >
                Check LocalStorage
              </Button>

              <Button
                variant="secondary"
                onClick={async () => {
                  addTestResult('Testing getCurrentUser...');
                  try {
                    await getCurrentUser();
                    addTestResult('getCurrentUser completed');
                  } catch (error) {
                    addTestResult(`getCurrentUser error: ${error}`);
                  }
                }}
              >
                Test Get Current User
              </Button>

              {isAuthenticated && (
                <Button
                  variant="danger"
                  onClick={testLogout}
                >
                  Test Logout
                </Button>
              )}

              <Button
                variant="secondary"
                onClick={() => router.push('/dashboard')}
              >
                Go to Dashboard
              </Button>

              <Button
                variant="secondary"
                onClick={() => router.push('/login')}
              >
                Go to Login
              </Button>
            </div>
          </div>

          {/* Test Results */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-2">Test Results</h2>
            <div className="bg-black text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
              {testResults.length === 0 ? (
                <p>No test results yet. Click "Run Full Auth Test" to start.</p>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="mb-1">
                    {result}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Clear Results */}
          <Button
            variant="secondary"
            onClick={() => setTestResults([])}
          >
            Clear Results
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TestAuthPage;
