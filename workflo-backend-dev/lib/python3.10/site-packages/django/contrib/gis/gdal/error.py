"""
This module houses the GDAL & SRS Exception objects, and the
check_err() routine which checks the status code returned by
GDAL/OGR methods.
"""


# #### GDAL & SRS Exceptions ####
class GDALException(Exception):
    pass


class SRSException(Exception):
    pass


# #### GDAL/OGR error checking codes and routine ####

# OGR Error Codes
OGRERR_DICT = {
    1: (G<PERSON>LEx<PERSON>, "Not enough data."),
    2: (GDALException, "Not enough memory."),
    3: (<PERSON><PERSON><PERSON>xception, "Unsupported geometry type."),
    4: (G<PERSON><PERSON>xception, "Unsupported operation."),
    5: (G<PERSON><PERSON>xception, "Corrupt data."),
    6: (<PERSON><PERSON><PERSON>x<PERSON>, "OGR failure."),
    7: (<PERSON>SEx<PERSON>, "Unsupported SRS."),
    8: (G<PERSON>LException, "Invalid handle."),
}

# CPL Error Codes
# https://gdal.org/api/cpl.html#cpl-error-h
CPLERR_DICT = {
    1: (<PERSON><PERSON><PERSON><PERSON><PERSON>, "AppDefined"),
    2: (<PERSON><PERSON><PERSON><PERSON><PERSON>, "OutOfMemory"),
    3: (<PERSON><PERSON><PERSON><PERSON><PERSON>, "FileIO"),
    4: (<PERSON><PERSON><PERSON><PERSON><PERSON>, "OpenFailed"),
    5: (GDALException, "IllegalArg"),
    6: (GDALException, "NotSupported"),
    7: (GDALException, "AssertionFailed"),
    8: (GDALException, "NoWriteAccess"),
    9: (GDALException, "UserInterrupt"),
    10: (GDALException, "ObjectNull"),
}

ERR_NONE = 0


def check_err(code, cpl=False):
    """
    Check the given CPL/OGRERR and raise an exception where appropriate.
    """
    err_dict = CPLERR_DICT if cpl else OGRERR_DICT

    if code == ERR_NONE:
        return
    elif code in err_dict:
        e, msg = err_dict[code]
        raise e(msg)
    else:
        raise GDALException('Unknown error code: "%s"' % code)
