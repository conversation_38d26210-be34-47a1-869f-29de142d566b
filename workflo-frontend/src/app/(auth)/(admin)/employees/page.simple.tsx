'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import EmployeeList from '@/components/employees/EmployeeList';
import { Employee } from '@/lib/employeeService';

const SimpleEmployeesPage = () => {
  const router = useRouter();

  const handleEmployeeSelect = (employee: Employee) => {
    router.push(`/employees/${employee.id}`);
  };

  return (
    <div className="space-y-6">
      <EmployeeList onEmployeeSelect={handleEmployeeSelect} />
    </div>
  );
};

export default SimpleEmployeesPage;
