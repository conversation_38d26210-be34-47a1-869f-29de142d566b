{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/__tests__/employee-biostar-view.test.tsx"], "sourcesContent": ["import React from 'react';\nimport { render, screen, waitFor, fireEvent } from '@testing-library/react';\nimport { AuthProvider } from '@/providers/AuthProvider';\nimport StaffBiostarPage from '@/app/(auth)/(staff)/staff/info/biostar/page';\nimport EmployeeBiostarWidget from '@/components/biostar/EmployeeBiostarWidget';\nimport { useBiostarAttendance } from '@/hooks/useBiostarAttendance';\n\n// Mock the BioStar attendance hook\njest.mock('@/hooks/useBiostarAttendance');\nconst mockUseBiostarAttendance = useBiostarAttendance as jest.MockedFunction<typeof useBiostarAttendance>;\n\n// Mock the BioStar API\njest.mock('@/lib/biostarApi', () => ({\n  biostarApi: {\n    getUserById: jest.fn(),\n    getEvents: jest.fn(),\n  },\n}));\n\n// Mock Next.js router\njest.mock('next/navigation', () => ({\n  useRouter: () => ({\n    push: jest.fn(),\n    back: jest.fn(),\n  }),\n  useParams: () => ({}),\n}));\n\nconst mockUser = {\n  id: 1,\n  employee_id: 'EMP001',\n  first_name: 'John',\n  last_name: 'Doe',\n  email: '<EMAIL>',\n  role: 'employee',\n};\n\nconst mockBiostarData = {\n  todayAttendance: {\n    id: 'att-001',\n    employee_id: 'EMP001',\n    employee_name: 'John Doe',\n    date: '2024-12-21',\n    first_in: '2024-12-21T08:30:00Z',\n    last_out: null,\n    total_hours: 0,\n    break_time: 0,\n    overtime: 0,\n    status: 'PRESENT' as const,\n    events: [],\n    biostar_synced: true,\n  },\n  devices: [\n    {\n      id: 'dev-001',\n      name: 'Main Entrance',\n      ip: '*************',\n      port: 8080,\n      status: 'ONLINE' as const,\n      type: 'Fingerprint Scanner',\n      location: 'Main Building',\n    },\n    {\n      id: 'dev-002',\n      name: 'Back Entrance',\n      ip: '*************',\n      port: 8080,\n      status: 'OFFLINE' as const,\n      type: 'Face Recognition',\n      location: 'Back Building',\n    },\n  ],\n  connected: true,\n  loading: false,\n  error: null,\n  summary: {\n    todayStatus: 'PRESENT' as const,\n    checkInTime: '2024-12-21T08:30:00Z',\n    hoursWorked: 0,\n    breakTime: 0,\n    overtime: 0,\n    weeklyHours: 32,\n    monthlyAttendance: 20,\n  },\n};\n\nconst TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (\n  <AuthProvider>\n    {children}\n  </AuthProvider>\n);\n\ndescribe('Employee BioStar View', () => {\n  beforeEach(() => {\n    mockUseBiostarAttendance.mockReturnValue({\n      ...mockBiostarData,\n      attendanceRecords: [],\n      realtimeUpdates: [],\n      refresh: jest.fn(),\n      getAttendanceRange: jest.fn(),\n    });\n  });\n\n  afterEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('StaffBiostarPage', () => {\n    it('should render the BioStar profile page', async () => {\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('BioStar Profile')).toBeInTheDocument();\n        expect(screen.getByText('Manage your biometric data and view access history')).toBeInTheDocument();\n      });\n    });\n\n    it('should show connection status', async () => {\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('BioStar Connected')).toBeInTheDocument();\n      });\n    });\n\n    it('should display profile status cards', async () => {\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Profile Status')).toBeInTheDocument();\n        expect(screen.getByText('Fingerprints')).toBeInTheDocument();\n        expect(screen.getByText('Face Templates')).toBeInTheDocument();\n        expect(screen.getByText('Devices')).toBeInTheDocument();\n      });\n    });\n\n    it('should show tab navigation', async () => {\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Biometric Profile')).toBeInTheDocument();\n        expect(screen.getByText('Devices')).toBeInTheDocument();\n        expect(screen.getByText('Access History')).toBeInTheDocument();\n        expect(screen.getByText('Security')).toBeInTheDocument();\n      });\n    });\n\n    it('should switch between tabs', async () => {\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        const devicesTab = screen.getByText('Devices');\n        fireEvent.click(devicesTab);\n        expect(screen.getByText('System Status')).toBeInTheDocument();\n      });\n    });\n\n    it('should show device information in devices tab', async () => {\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        const devicesTab = screen.getByText('Devices');\n        fireEvent.click(devicesTab);\n        \n        expect(screen.getByText('Available Devices')).toBeInTheDocument();\n        expect(screen.getByText('Main Entrance')).toBeInTheDocument();\n        expect(screen.getByText('Back Entrance')).toBeInTheDocument();\n      });\n    });\n\n    it('should handle connection errors', async () => {\n      mockUseBiostarAttendance.mockReturnValue({\n        ...mockBiostarData,\n        connected: false,\n        error: 'Connection failed',\n        attendanceRecords: [],\n        realtimeUpdates: [],\n        refresh: jest.fn(),\n        getAttendanceRange: jest.fn(),\n      });\n\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Connection Error')).toBeInTheDocument();\n        expect(screen.getByText('Connection failed')).toBeInTheDocument();\n      });\n    });\n\n    it('should show refresh button and handle refresh', async () => {\n      const mockRefresh = jest.fn();\n      mockUseBiostarAttendance.mockReturnValue({\n        ...mockBiostarData,\n        attendanceRecords: [],\n        realtimeUpdates: [],\n        refresh: mockRefresh,\n        getAttendanceRange: jest.fn(),\n      });\n\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        const refreshButton = screen.getByText('Refresh');\n        fireEvent.click(refreshButton);\n        expect(mockRefresh).toHaveBeenCalled();\n      });\n    });\n  });\n\n  describe('EmployeeBiostarWidget', () => {\n    it('should render the BioStar widget', async () => {\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('BioStar Profile')).toBeInTheDocument();\n      });\n    });\n\n    it('should show connection status in widget', async () => {\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Connected')).toBeInTheDocument();\n      });\n    });\n\n    it('should display biometric stats', async () => {\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Fingerprints')).toBeInTheDocument();\n        expect(screen.getByText('Face Template')).toBeInTheDocument();\n      });\n    });\n\n    it('should show today\\'s attendance status', async () => {\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Today\\'s Status')).toBeInTheDocument();\n        expect(screen.getByText('PRESENT')).toBeInTheDocument();\n      });\n    });\n\n    it('should display recent access events', async () => {\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" showFullDetails={true} />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Recent Access')).toBeInTheDocument();\n      });\n    });\n\n    it('should show device status summary', async () => {\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Devices Online')).toBeInTheDocument();\n        expect(screen.getByText('1/2')).toBeInTheDocument(); // 1 online out of 2 total\n      });\n    });\n\n    it('should have link to full profile', async () => {\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('View Full BioStar Profile →')).toBeInTheDocument();\n      });\n    });\n\n    it('should handle disconnected state', async () => {\n      mockUseBiostarAttendance.mockReturnValue({\n        ...mockBiostarData,\n        connected: false,\n        attendanceRecords: [],\n        realtimeUpdates: [],\n        refresh: jest.fn(),\n        getAttendanceRange: jest.fn(),\n      });\n\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Disconnected')).toBeInTheDocument();\n      });\n    });\n\n    it('should show loading state', () => {\n      mockUseBiostarAttendance.mockReturnValue({\n        ...mockBiostarData,\n        loading: true,\n        attendanceRecords: [],\n        realtimeUpdates: [],\n        refresh: jest.fn(),\n        getAttendanceRange: jest.fn(),\n      });\n\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      expect(screen.getByText('Loading BioStar data...')).toBeInTheDocument();\n    });\n  });\n\n  describe('Integration Tests', () => {\n    it('should integrate with useBiostarAttendance hook correctly', async () => {\n      const mockGetAttendanceRange = jest.fn();\n      mockUseBiostarAttendance.mockReturnValue({\n        ...mockBiostarData,\n        attendanceRecords: [],\n        realtimeUpdates: [],\n        refresh: jest.fn(),\n        getAttendanceRange: mockGetAttendanceRange,\n      });\n\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(mockUseBiostarAttendance).toHaveBeenCalledWith({\n          employeeId: undefined, // No user context in test\n          autoRefresh: true,\n          enableRealTime: false,\n        });\n      });\n    });\n\n    it('should handle empty device list', async () => {\n      mockUseBiostarAttendance.mockReturnValue({\n        ...mockBiostarData,\n        devices: [],\n        attendanceRecords: [],\n        realtimeUpdates: [],\n        refresh: jest.fn(),\n        getAttendanceRange: jest.fn(),\n      });\n\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        const devicesTab = screen.getByText('Devices');\n        fireEvent.click(devicesTab);\n        expect(screen.getByText('No Devices Found')).toBeInTheDocument();\n      });\n    });\n  });\n});\n"], "names": ["jest", "mock", "biostarApi", "getUserById", "fn", "getEvents", "useRouter", "push", "back", "useParams", "mockUseBiostarAttendance", "useBiostarAttendance", "mockUser", "id", "employee_id", "first_name", "last_name", "email", "role", "mockBiostarData", "todayAttendance", "employee_name", "date", "first_in", "last_out", "total_hours", "break_time", "overtime", "status", "events", "biostar_synced", "devices", "name", "ip", "port", "type", "location", "connected", "loading", "error", "summary", "todayStatus", "checkInTime", "hoursWorked", "breakTime", "weeklyHours", "monthlyAttendance", "TestWrapper", "children", "<PERSON>th<PERSON><PERSON><PERSON>", "describe", "beforeEach", "mockReturnValue", "attendanceRecords", "realtimeUpdates", "refresh", "getAttendanceRange", "after<PERSON>ach", "clearAllMocks", "it", "render", "StaffBiostarPage", "waitFor", "expect", "screen", "getByText", "toBeInTheDocument", "devicesTab", "fireEvent", "click", "mockRefresh", "refreshButton", "toHaveBeenCalled", "EmployeeBiostarWidget", "employeeId", "showFullDetails", "mockGetAttendanceRange", "toHaveBeenCalledWith", "undefined", "autoRefresh", "enableRealTime"], "mappings": ";AAOA,mCAAmC;AACnCA,KAAKC,IAAI,CAAC;AAGV,uBAAuB;AACvBD,KAAKC,IAAI,CAAC,oBAAoB,IAAO,CAAA;QACnCC,YAAY;YACVC,aAAaH,KAAKI,EAAE;YACpBC,WAAWL,KAAKI,EAAE;QACpB;IACF,CAAA;AAEA,sBAAsB;AACtBJ,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCK,WAAW,IAAO,CAAA;gBAChBC,MAAMP,KAAKI,EAAE;gBACbI,MAAMR,KAAKI,EAAE;YACf,CAAA;QACAK,WAAW,IAAO,CAAA,CAAC,CAAA;IACrB,CAAA;;;;;8DA1BkB;wBACiC;8BACtB;6DACA;8EACK;sCACG;;;;;;AAIrC,MAAMC,2BAA2BC,0CAAoB;AAmBrD,MAAMC,WAAW;IACfC,IAAI;IACJC,aAAa;IACbC,YAAY;IACZC,WAAW;IACXC,OAAO;IACPC,MAAM;AACR;AAEA,MAAMC,kBAAkB;IACtBC,iBAAiB;QACfP,IAAI;QACJC,aAAa;QACbO,eAAe;QACfC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,aAAa;QACbC,YAAY;QACZC,UAAU;QACVC,QAAQ;QACRC,QAAQ,EAAE;QACVC,gBAAgB;IAClB;IACAC,SAAS;QACP;YACElB,IAAI;YACJmB,MAAM;YACNC,IAAI;YACJC,MAAM;YACNN,QAAQ;YACRO,MAAM;YACNC,UAAU;QACZ;QACA;YACEvB,IAAI;YACJmB,MAAM;YACNC,IAAI;YACJC,MAAM;YACNN,QAAQ;YACRO,MAAM;YACNC,UAAU;QACZ;KACD;IACDC,WAAW;IACXC,SAAS;IACTC,OAAO;IACPC,SAAS;QACPC,aAAa;QACbC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXjB,UAAU;QACVkB,aAAa;QACbC,mBAAmB;IACrB;AACF;AAEA,MAAMC,cAAuD,CAAC,EAAEC,QAAQ,EAAE,iBACxE,qBAACC,0BAAY;kBACVD;;AAILE,SAAS,yBAAyB;IAChCC,WAAW;QACTzC,yBAAyB0C,eAAe,CAAC;YACvC,GAAGjC,eAAe;YAClBkC,mBAAmB,EAAE;YACrBC,iBAAiB,EAAE;YACnBC,SAASvD,KAAKI,EAAE;YAChBoD,oBAAoBxD,KAAKI,EAAE;QAC7B;IACF;IAEAqD,UAAU;QACRzD,KAAK0D,aAAa;IACpB;IAEAR,SAAS,oBAAoB;QAC3BS,GAAG,0CAA0C;YAC3CC,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAACc,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;gBAC7DH,OAAOC,cAAM,CAACC,SAAS,CAAC,uDAAuDC,iBAAiB;YAClG;QACF;QAEAP,GAAG,iCAAiC;YAClCC,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAACc,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,sBAAsBC,iBAAiB;YACjE;QACF;QAEAP,GAAG,uCAAuC;YACxCC,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAACc,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,mBAAmBC,iBAAiB;gBAC5DH,OAAOC,cAAM,CAACC,SAAS,CAAC,iBAAiBC,iBAAiB;gBAC1DH,OAAOC,cAAM,CAACC,SAAS,CAAC,mBAAmBC,iBAAiB;gBAC5DH,OAAOC,cAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;YACvD;QACF;QAEAP,GAAG,8BAA8B;YAC/BC,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAACc,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,sBAAsBC,iBAAiB;gBAC/DH,OAAOC,cAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;gBACrDH,OAAOC,cAAM,CAACC,SAAS,CAAC,mBAAmBC,iBAAiB;gBAC5DH,OAAOC,cAAM,CAACC,SAAS,CAAC,aAAaC,iBAAiB;YACxD;QACF;QAEAP,GAAG,8BAA8B;YAC/BC,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAACc,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZ,MAAMK,aAAaH,cAAM,CAACC,SAAS,CAAC;gBACpCG,iBAAS,CAACC,KAAK,CAACF;gBAChBJ,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC7D;QACF;QAEAP,GAAG,iDAAiD;YAClDC,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAACc,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZ,MAAMK,aAAaH,cAAM,CAACC,SAAS,CAAC;gBACpCG,iBAAS,CAACC,KAAK,CAACF;gBAEhBJ,OAAOC,cAAM,CAACC,SAAS,CAAC,sBAAsBC,iBAAiB;gBAC/DH,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;gBAC3DH,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC7D;QACF;QAEAP,GAAG,mCAAmC;YACpCjD,yBAAyB0C,eAAe,CAAC;gBACvC,GAAGjC,eAAe;gBAClBkB,WAAW;gBACXE,OAAO;gBACPc,mBAAmB,EAAE;gBACrBC,iBAAiB,EAAE;gBACnBC,SAASvD,KAAKI,EAAE;gBAChBoD,oBAAoBxD,KAAKI,EAAE;YAC7B;YAEAwD,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAACc,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;gBAC9DH,OAAOC,cAAM,CAACC,SAAS,CAAC,sBAAsBC,iBAAiB;YACjE;QACF;QAEAP,GAAG,iDAAiD;YAClD,MAAMW,cAActE,KAAKI,EAAE;YAC3BM,yBAAyB0C,eAAe,CAAC;gBACvC,GAAGjC,eAAe;gBAClBkC,mBAAmB,EAAE;gBACrBC,iBAAiB,EAAE;gBACnBC,SAASe;gBACTd,oBAAoBxD,KAAKI,EAAE;YAC7B;YAEAwD,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAACc,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZ,MAAMS,gBAAgBP,cAAM,CAACC,SAAS,CAAC;gBACvCG,iBAAS,CAACC,KAAK,CAACE;gBAChBR,OAAOO,aAAaE,gBAAgB;YACtC;QACF;IACF;IAEAtB,SAAS,yBAAyB;QAChCS,GAAG,oCAAoC;YACrCC,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAAC0B,8BAAqB;oBAACC,YAAW;;;YAItC,MAAMZ,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;YAC/D;QACF;QAEAP,GAAG,2CAA2C;YAC5CC,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAAC0B,8BAAqB;oBAACC,YAAW;;;YAItC,MAAMZ,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,cAAcC,iBAAiB;YACzD;QACF;QAEAP,GAAG,kCAAkC;YACnCC,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAAC0B,8BAAqB;oBAACC,YAAW;;;YAItC,MAAMZ,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,iBAAiBC,iBAAiB;gBAC1DH,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC7D;QACF;QAEAP,GAAG,0CAA0C;YAC3CC,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAAC0B,8BAAqB;oBAACC,YAAW;;;YAItC,MAAMZ,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;gBAC7DH,OAAOC,cAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;YACvD;QACF;QAEAP,GAAG,uCAAuC;YACxCC,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAAC0B,8BAAqB;oBAACC,YAAW;oBAASC,iBAAiB;;;YAIhE,MAAMb,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC7D;QACF;QAEAP,GAAG,qCAAqC;YACtCC,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAAC0B,8BAAqB;oBAACC,YAAW;;;YAItC,MAAMZ,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,mBAAmBC,iBAAiB;gBAC5DH,OAAOC,cAAM,CAACC,SAAS,CAAC,QAAQC,iBAAiB,IAAI,0BAA0B;YACjF;QACF;QAEAP,GAAG,oCAAoC;YACrCC,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAAC0B,8BAAqB;oBAACC,YAAW;;;YAItC,MAAMZ,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,gCAAgCC,iBAAiB;YAC3E;QACF;QAEAP,GAAG,oCAAoC;YACrCjD,yBAAyB0C,eAAe,CAAC;gBACvC,GAAGjC,eAAe;gBAClBkB,WAAW;gBACXgB,mBAAmB,EAAE;gBACrBC,iBAAiB,EAAE;gBACnBC,SAASvD,KAAKI,EAAE;gBAChBoD,oBAAoBxD,KAAKI,EAAE;YAC7B;YAEAwD,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAAC0B,8BAAqB;oBAACC,YAAW;;;YAItC,MAAMZ,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,iBAAiBC,iBAAiB;YAC5D;QACF;QAEAP,GAAG,6BAA6B;YAC9BjD,yBAAyB0C,eAAe,CAAC;gBACvC,GAAGjC,eAAe;gBAClBmB,SAAS;gBACTe,mBAAmB,EAAE;gBACrBC,iBAAiB,EAAE;gBACnBC,SAASvD,KAAKI,EAAE;gBAChBoD,oBAAoBxD,KAAKI,EAAE;YAC7B;YAEAwD,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAAC0B,8BAAqB;oBAACC,YAAW;;;YAItCX,OAAOC,cAAM,CAACC,SAAS,CAAC,4BAA4BC,iBAAiB;QACvE;IACF;IAEAhB,SAAS,qBAAqB;QAC5BS,GAAG,6DAA6D;YAC9D,MAAMiB,yBAAyB5E,KAAKI,EAAE;YACtCM,yBAAyB0C,eAAe,CAAC;gBACvC,GAAGjC,eAAe;gBAClBkC,mBAAmB,EAAE;gBACrBC,iBAAiB,EAAE;gBACnBC,SAASvD,KAAKI,EAAE;gBAChBoD,oBAAoBoB;YACtB;YAEAhB,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAACc,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZC,OAAOrD,0BAA0BmE,oBAAoB,CAAC;oBACpDH,YAAYI;oBACZC,aAAa;oBACbC,gBAAgB;gBAClB;YACF;QACF;QAEArB,GAAG,mCAAmC;YACpCjD,yBAAyB0C,eAAe,CAAC;gBACvC,GAAGjC,eAAe;gBAClBY,SAAS,EAAE;gBACXsB,mBAAmB,EAAE;gBACrBC,iBAAiB,EAAE;gBACnBC,SAASvD,KAAKI,EAAE;gBAChBoD,oBAAoBxD,KAAKI,EAAE;YAC7B;YAEAwD,IAAAA,cAAM,gBACJ,qBAACb;0BACC,cAAA,qBAACc,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZ,MAAMK,aAAaH,cAAM,CAACC,SAAS,CAAC;gBACpCG,iBAAS,CAACC,KAAK,CAACF;gBAChBJ,OAAOC,cAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;YAChE;QACF;IACF;AACF"}