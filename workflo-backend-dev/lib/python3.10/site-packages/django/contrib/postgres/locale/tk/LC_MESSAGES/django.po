# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-11 20:56+0200\n"
"PO-Revision-Date: 2020-07-15 16:27+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Turkmen (http://www.transifex.com/django/django/language/"
"tk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tk\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "PostgreSQL extensions"
msgstr "PostgreSQL giňeltmeleri"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr ""

msgid "Nested arrays must have the same length."
msgstr "Iç-içe massiwleriň deň uzynlygy bolmaly."

msgid "Map of strings to strings/nulls"
msgstr "Setirleriň setirler/boşluklara kartasy"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "\"%(key)s\" netijesi setir ýa-da boş däl"

msgid "Could not load JSON data."
msgstr "JSON maglumatlary ýükläp bolmady."

msgid "Input must be a JSON dictionary."
msgstr "Giriş JSON sözlügi bolmaly."

msgid "Enter two valid values."
msgstr "Iki sany dogry baha giriziň."

msgid "The start of the range must not exceed the end of the range."
msgstr "Aralygyň başlangyjy soňundan ýokary bolmaly däldir."

msgid "Enter two whole numbers."
msgstr "iki sany esasy san giriziň."

msgid "Enter two numbers."
msgstr "Iki sany san giriz"

msgid "Enter two valid date/times."
msgstr "Iki sany dogry senäni/wagty giriziň."

msgid "Enter two valid dates."
msgstr "Iki sany dogry sene giriziň."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"Bu listda %(show_value)d element bar, asyl %(limit_value)ddan köp element "
"bolmaly däldir."
msgstr[1] ""
"Bu listda %(show_value)d element bar, asyl %(limit_value)ddan köp element "
"bolmaly däldir."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"Bu listda %(show_value)d element bar, asyl %(limit_value)ddan az element "
"bolmaly däldir."
msgstr[1] ""
"Bu listda %(show_value)d element bar, asyl %(limit_value)ddan az element "
"bolmaly däldir."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Käbir açarlar ýok: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Käbir bilinmeýän açarlar girizilen: %(keys)s"

#, python-format
msgid ""
"Ensure that this range is completely less than or equal to %(limit_value)s."
msgstr "Şu iki aralygyň %(limit_value)s'a deň ýa-da azdygyna göz ýetiriň."

#, python-format
msgid ""
"Ensure that this range is completely greater than or equal to "
"%(limit_value)s."
msgstr "Şu iki aralygyň %(limit_value)s'a deň ýa-da köpdügine göz ýetiriň."
