'use client';

import React from 'react';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { useToast, useToastWithUtils } from '@/hooks/useToast';

const TestToastPage: React.FC = () => {
  const toast = useToastWithUtils();

  const testToasts = () => {
    // Test different toast types
    toast.success('This is a success message!');

    setTimeout(() => {
      toast.error('This is an error message!', {
        title: 'Error Occurred',
        persistent: true
      });
    }, 1000);

    setTimeout(() => {
      toast.warning('This is a warning message!', {
        title: 'Warning',
        duration: 7000
      });
    }, 2000);

    setTimeout(() => {
      toast.info('This is an info message!');
    }, 3000);

    setTimeout(() => {
      toast.showToast({
        type: 'success',
        title: 'Custom Toast',
        message: 'This is a custom toast with an action!',
        action: {
          label: 'Undo',
          onClick: () => alert('Action clicked!')
        }
      });
    }, 4000);
  };

  // Simulate accountant-specific operations
  const testPayrollProcessing = async () => {
    const payrollPromise = new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.3) {
          resolve({ processed: 156, total: 2450000 });
        } else {
          reject(new Error('Payroll processing failed due to validation errors'));
        }
      }, 3000);
    });

    try {
      const result = await toast.promiseToast(payrollPromise, {
        loading: 'Processing payroll for 156 employees...',
        success: (data: any) => `Payroll processed successfully! ${data.processed} employees, KSH ${data.total.toLocaleString()}`,
        error: 'Payroll processing failed'
      });
      console.log('Payroll result:', result);
    } catch (error) {
      console.error('Payroll error:', error);
    }
  };

  const testApiError = () => {
    // Simulate API error
    const apiError = {
      response: {
        data: {
          message: 'Insufficient permissions to access payroll data'
        }
      }
    };
    toast.handleApiError(apiError);
  };

  const testValidationErrors = () => {
    const validationErrors = {
      'Employee ID': ['Employee ID is required', 'Employee ID must be unique'],
      'Salary': ['Salary must be a positive number'],
      'Department': ['Department is required']
    };
    toast.handleValidationErrors(validationErrors);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Toast System Test</h1>
        <p className="text-gray-600">Test the toast notification system</p>
      </div>

      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Toast Tests</h3>

        <div className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button
              variant="primary"
              onClick={() => toast.success('Success toast!')}
            >
              Success Toast
            </Button>

            <Button
              variant="secondary"
              onClick={() => toast.error('Error toast!', { persistent: true })}
            >
              Error Toast
            </Button>

            <Button
              variant="outline"
              onClick={() => toast.warning('Warning toast!')}
            >
              Warning Toast
            </Button>

            <Button
              variant="ghost"
              onClick={() => toast.info('Info toast!')}
            >
              Info Toast
            </Button>
          </div>

          <Button
            variant="primary"
            onClick={testToasts}
            className="w-full"
          >
            Test All Toasts (Sequential)
          </Button>
        </div>
      </Card>

      {/* Accountant-Specific Tests */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Accountant Feature Tests</h3>

        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              variant="primary"
              onClick={testPayrollProcessing}
              className="bg-green-600 hover:bg-green-700"
            >
              💰 Test Payroll Processing
            </Button>

            <Button
              variant="secondary"
              onClick={testApiError}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              🚫 Test API Error
            </Button>

            <Button
              variant="outline"
              onClick={testValidationErrors}
              className="border-yellow-500 text-yellow-700 hover:bg-yellow-50"
            >
              ⚠️ Test Validation Errors
            </Button>

            <Button
              variant="ghost"
              onClick={() => toast.handleNetworkError()}
              className="text-purple-700 hover:bg-purple-50"
            >
              🌐 Test Network Error
            </Button>
          </div>

          <div className="pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-3">
              These tests simulate real accountant workflows with appropriate toast notifications:
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• <strong>Payroll Processing:</strong> Shows loading, success/error states</li>
              <li>• <strong>API Error:</strong> Handles server-side errors gracefully</li>
              <li>• <strong>Validation Errors:</strong> Displays form validation issues</li>
              <li>• <strong>Network Error:</strong> Handles connectivity issues with retry option</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default TestToastPage;
