dea103f7a2ad5678d82d2d6aabc60e10
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    CardBody: function() {
        return CardBody;
    },
    CardFooter: function() {
        return CardFooter;
    },
    CardHeader: function() {
        return CardHeader;
    },
    EmployeeCard: function() {
        return EmployeeCard;
    },
    StatsCard: function() {
        return StatsCard;
    },
    default: function() {
        return _default;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _utils = require("../../lib/utils");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const Card = ({ title, children, className, actions, ...props })=>{
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: (0, _utils.cn)('bg-white rounded-lg shadow-sm border border-gray-200 min-w-0 overflow-hidden', className),
        ...props,
        children: [
            (title || actions) && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "px-4 sm:px-6 py-4 border-b border-gray-200 flex items-center justify-between min-w-0",
                children: [
                    title && /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                        className: "text-base sm:text-lg font-medium text-gray-900 truncate",
                        children: title
                    }),
                    actions && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "flex items-center space-x-2 flex-shrink-0",
                        children: actions
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "p-4 sm:p-6 min-w-0",
                children: children
            })
        ]
    });
};
const CardHeader = ({ children, className })=>{
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
        className: (0, _utils.cn)('px-6 py-4 border-b border-gray-200', className),
        children: children
    });
};
const CardBody = ({ children, className })=>{
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
        className: (0, _utils.cn)('p-6', className),
        children: children
    });
};
const CardFooter = ({ children, className })=>{
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
        className: (0, _utils.cn)('px-6 py-4 border-t border-gray-200 bg-gray-50', className),
        children: children
    });
};
const StatsCard = ({ title, value, icon, color = 'primary', trend, className })=>{
    const colorClasses = {
        primary: 'bg-orange-500',
        success: 'bg-green-500',
        warning: 'bg-yellow-500',
        danger: 'bg-red-500'
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(Card, {
        className: (0, _utils.cn)('', className),
        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            className: "flex items-center min-w-0",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: (0, _utils.cn)('p-3 rounded-lg flex-shrink-0', colorClasses[color]),
                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "text-white text-xl",
                        children: icon
                    })
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "ml-4 flex-1 min-w-0",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                            className: "text-sm font-medium text-gray-600 truncate",
                            children: title
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                            className: "text-xl sm:text-2xl font-bold text-gray-900 truncate",
                            children: value
                        }),
                        trend && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "flex items-center mt-1",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                                    className: (0, _utils.cn)('text-sm font-medium', trend.isPositive ? 'text-green-600' : 'text-red-600'),
                                    children: [
                                        trend.isPositive ? '+' : '-',
                                        Math.abs(trend.value),
                                        "%"
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                    className: "text-sm text-gray-500 ml-1 truncate",
                                    children: "vs last month"
                                })
                            ]
                        })
                    ]
                })
            ]
        })
    });
};
const EmployeeCard = ({ employee, onClick, className })=>{
    const initials = `${employee.first_name.charAt(0)}${employee.last_name.charAt(0)}`;
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(Card, {
        className: (0, _utils.cn)('cursor-pointer hover:shadow-md transition-shadow', className),
        onClick: onClick,
        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            className: "text-center min-w-0",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "mb-4",
                    children: employee.profile_picture ? /*#__PURE__*/ (0, _jsxruntime.jsx)("img", {
                        src: employee.profile_picture,
                        alt: `${employee.first_name} ${employee.last_name}`,
                        className: "w-16 h-16 rounded-full mx-auto object-cover"
                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "w-16 h-16 rounded-full mx-auto bg-orange-500 flex items-center justify-center text-white font-medium text-lg",
                        children: initials
                    })
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsxs)("h4", {
                    className: "text-base sm:text-lg font-medium text-gray-900 truncate",
                    children: [
                        employee.first_name,
                        " ",
                        employee.last_name
                    ]
                }),
                employee.job_title && /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                    className: "text-sm font-medium text-gray-600 mt-1 truncate",
                    title: employee.job_title,
                    children: employee.job_title
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                    className: "text-sm text-gray-500 mt-1 truncate",
                    title: employee.email,
                    children: employee.email
                }),
                employee.department && /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                    className: "text-xs text-gray-400 mt-1 truncate",
                    title: employee.department.name,
                    children: employee.department.name
                })
            ]
        })
    });
};
const _default = Card;

//# sourceMappingURL=data:application/json;base64,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