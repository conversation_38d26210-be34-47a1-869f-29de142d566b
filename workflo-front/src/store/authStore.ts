import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { User, AuthTokens, LoginCredentials } from '@/types';
import { authApi, TokenManager } from '@/lib/api';
import { AuthPersistence } from '@/lib/authPersistence';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean;
  isHydrated: boolean;
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  getCurrentUser: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  initialize: () => Promise<void>;
  setHydrated: () => void;
}

type AuthStore = AuthState & AuthActions;

// Create a simple auth store without persist to avoid hydration issues
export const useAuthStore = create<AuthStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
    isInitialized: false,
    isHydrated: false,

    // Actions
    login: async (credentials: LoginCredentials) => {
      try {
        set({ isLoading: true, error: null });
        const tokens = await authApi.login(credentials);
        TokenManager.setTokens(tokens);
        const user = await authApi.getCurrentUser() as User;

        set({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        // Save to persistence
        AuthPersistence.save(user, true);
      } catch (error: any) {
        const errorMessage = error.response?.data?.message ||
                            error.response?.data?.detail ||
                            error.message ||
                            'Login failed. Please check your credentials.';

        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: errorMessage,
        });

        // Clear persistence
        AuthPersistence.clear();
        throw error;
      }
    },

    logout: () => {
      TokenManager.clearTokens();
      AuthPersistence.clear();

      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        isInitialized: false,
      });

      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    },

    getCurrentUser: async () => {
      try {
        set({ isLoading: true, error: null });
        const token = TokenManager.getAccessToken();

        if (!token) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
          AuthPersistence.clear();
          return;
        }

        const user = await authApi.getCurrentUser() as User;
        set({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        // Save to persistence
        AuthPersistence.save(user, true);
      } catch (error: any) {
        TokenManager.clearTokens();
        AuthPersistence.clear();

        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      }
    },

    clearError: () => {
      set({ error: null });
    },

    setLoading: (loading: boolean) => {
      set({ isLoading: loading });
    },

    initialize: async () => {
      if (get().isInitialized) return;

      try {
        // First, try to load from persistence
        const persisted = AuthPersistence.load();
        if (persisted && persisted.isAuthenticated && persisted.user) {
          set({
            user: persisted.user,
            isAuthenticated: persisted.isAuthenticated,
          });
        }

        // Then validate with server
        await get().getCurrentUser();
      } catch (error) {
        // If validation fails, clear everything
        AuthPersistence.clear();
        set({
          user: null,
          isAuthenticated: false,
          error: null,
        });
      } finally {
        set({ isInitialized: true });
      }
    },

    setHydrated: () => {
      set({ isHydrated: true });
    },
  }))
);

// SSR-safe auth hook
export const useAuth = () => {
  const user = useAuthStore((state) => state.user);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const isLoading = useAuthStore((state) => state.isLoading);
  const error = useAuthStore((state) => state.error);
  const isInitialized = useAuthStore((state) => state.isInitialized);
  const isHydrated = useAuthStore((state) => state.isHydrated);
  const login = useAuthStore((state) => state.login);
  const logout = useAuthStore((state) => state.logout);
  const getCurrentUser = useAuthStore((state) => state.getCurrentUser);
  const clearError = useAuthStore((state) => state.clearError);
  const initialize = useAuthStore((state) => state.initialize);
  const setHydrated = useAuthStore((state) => state.setHydrated);

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    isInitialized,
    isHydrated,
    login,
    logout,
    getCurrentUser,
    clearError,
    initialize,
    setHydrated,
  };
};

export const useUser = () => {
  const user = useAuthStore((state) => state.user);
  return user;
};

export const useIsAuthenticated = () => {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  return isAuthenticated;
};

// Role-based access helpers
export const useHasRole = (role: string | string[]) => {
  const user = useUser();

  if (!user) return false;

  if (Array.isArray(role)) {
    return role.includes(user.role);
  }

  return user.role === role;
};

export const useIsAdmin = () => {
  return useHasRole(['hr', 'supervisor']);
};

export const useIsHR = () => {
  return useHasRole('hr');
};

export const useIsSupervisor = () => {
  return useHasRole('supervisor');
};

export const useIsAccountant = () => {
  return useHasRole('accountant');
};

// Permission helpers
export const useCanManageEmployees = () => {
  return useHasRole(['hr', 'supervisor']);
};

export const useCanManagePayroll = () => {
  return useHasRole(['hr', 'accountant']);
};

export const useCanApproveLeaves = () => {
  return useHasRole(['hr', 'supervisor']);
};

export const useCanViewReports = () => {
  return useHasRole(['hr', 'supervisor', 'accountant']);
};

export const useCanManageSettings = () => {
  return useHasRole('hr');
};
