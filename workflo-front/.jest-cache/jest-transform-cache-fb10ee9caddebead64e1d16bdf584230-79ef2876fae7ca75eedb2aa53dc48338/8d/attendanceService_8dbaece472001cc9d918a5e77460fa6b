f024239ad56114bda9c0485f1a573e90
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AttendanceService: function() {
        return AttendanceService;
    },
    attendanceService: function() {
        return attendanceService;
    }
});
const _biostarApi = require("./biostarApi");
const _datefns = require("date-fns");
class AttendanceService {
    static getInstance() {
        if (!AttendanceService.instance) {
            AttendanceService.instance = new AttendanceService();
        }
        return AttendanceService.instance;
    }
    // Get today's attendance for current user
    async getTodayAttendance(employeeId) {
        try {
            const today = (0, _datefns.format)(new Date(), 'yyyy-MM-dd');
            const startDate = (0, _datefns.format)((0, _datefns.startOfDay)(new Date()), "yyyy-MM-dd'T'HH:mm:ss");
            const endDate = (0, _datefns.format)((0, _datefns.endOfDay)(new Date()), "yyyy-MM-dd'T'HH:mm:ss");
            // Get events for today
            const eventsResponse = await _biostarApi.biostarApi.getEvents(startDate, endDate, employeeId, undefined, 100, 0);
            if (!eventsResponse.results || eventsResponse.results.length === 0) {
                return null;
            }
            return this.processAttendanceEvents(employeeId, today, eventsResponse.results);
        } catch (error) {
            console.error('Error fetching today\'s attendance:', error);
            return null;
        }
    }
    // Get attendance for a date range
    async getAttendanceRange(employeeId, startDate, endDate) {
        try {
            const eventsResponse = await _biostarApi.biostarApi.getEvents(`${startDate}T00:00:00`, `${endDate}T23:59:59`, employeeId, undefined, 1000, 0);
            if (!eventsResponse.results) {
                return [];
            }
            // Group events by date
            const eventsByDate = this.groupEventsByDate(eventsResponse.results);
            // Process each date
            const attendanceRecords = [];
            for (const [date, events] of Object.entries(eventsByDate)){
                const record = this.processAttendanceEvents(employeeId, date, events);
                if (record) {
                    attendanceRecords.push(record);
                }
            }
            return attendanceRecords.sort((a, b)=>b.date.localeCompare(a.date));
        } catch (error) {
            console.error('Error fetching attendance range:', error);
            return [];
        }
    }
    // Get real-time attendance updates
    async getRealTimeUpdates() {
        try {
            const events = await _biostarApi.biostarApi.getRealtimeEvents(20);
            return events.map((event)=>({
                    employee_id: event.user_id,
                    employee_name: event.user_name || 'Unknown',
                    event_type: this.mapEventType(event.event_type),
                    timestamp: event.datetime,
                    device_name: event.device_name || 'Unknown Device',
                    location: 'Main Office' // Default location
                }));
        } catch (error) {
            console.error('Error fetching real-time updates:', error);
            return [];
        }
    }
    // Start real-time monitoring
    startRealTimeMonitoring(callback) {
        this.realtimeListeners.push(callback);
        if (!this.pollingInterval) {
            this.pollingInterval = setInterval(async ()=>{
                try {
                    const updates = await this.getRealTimeUpdates();
                    updates.forEach((update)=>{
                        this.realtimeListeners.forEach((listener)=>listener(update));
                    });
                } catch (error) {
                    console.error('Real-time monitoring error:', error);
                }
            }, 30000); // Poll every 30 seconds
        }
    }
    // Stop real-time monitoring
    stopRealTimeMonitoring(callback) {
        if (callback) {
            this.realtimeListeners = this.realtimeListeners.filter((listener)=>listener !== callback);
        } else {
            this.realtimeListeners = [];
        }
        if (this.realtimeListeners.length === 0 && this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
    }
    // Get attendance summary for dashboard
    async getAttendanceSummary(employeeId) {
        try {
            // Get today's attendance
            const todayAttendance = await this.getTodayAttendance(employeeId);
            // Get this week's attendance
            const weekStart = (0, _datefns.format)(new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd');
            const today = (0, _datefns.format)(new Date(), 'yyyy-MM-dd');
            const weeklyAttendance = await this.getAttendanceRange(employeeId, weekStart, today);
            // Get this month's attendance
            const monthStart = (0, _datefns.format)(new Date(new Date().getFullYear(), new Date().getMonth(), 1), 'yyyy-MM-dd');
            const monthlyAttendance = await this.getAttendanceRange(employeeId, monthStart, today);
            const weeklyHours = weeklyAttendance.reduce((total, record)=>total + (record.total_hours || 0), 0);
            const monthlyDays = monthlyAttendance.filter((record)=>record.status === 'PRESENT').length;
            return {
                todayStatus: todayAttendance?.status || 'ABSENT',
                checkInTime: todayAttendance?.first_in,
                checkOutTime: todayAttendance?.last_out,
                hoursWorked: todayAttendance?.total_hours || 0,
                breakTime: todayAttendance?.break_time || 0,
                overtime: todayAttendance?.overtime || 0,
                weeklyHours,
                monthlyAttendance: monthlyDays
            };
        } catch (error) {
            console.error('Error fetching attendance summary:', error);
            return {
                todayStatus: 'ABSENT',
                hoursWorked: 0,
                breakTime: 0,
                overtime: 0,
                weeklyHours: 0,
                monthlyAttendance: 0
            };
        }
    }
    // Get available devices
    async getDevices() {
        try {
            const response = await _biostarApi.biostarApi.getDevices();
            return response.results || [];
        } catch (error) {
            console.error('Error fetching devices:', error);
            return [];
        }
    }
    // Sync employee with BioStar
    async syncEmployee(employee) {
        try {
            const biostarUser = {
                user_id: employee.employee_id,
                name: `${employee.first_name} ${employee.last_name}`,
                email: employee.email,
                phone: employee.phone_number,
                department: employee.department_name,
                position: employee.job_title,
                employee_id: employee.employee_id,
                disabled: !employee.is_active
            };
            // Try to find existing user first
            try {
                const existingUser = await _biostarApi.biostarApi.getUserById(employee.employee_id);
                // Update existing user
                return await _biostarApi.biostarApi.updateUser(existingUser.id, biostarUser);
            } catch (error) {
                // User doesn't exist, create new one
                return await _biostarApi.biostarApi.createUser(biostarUser);
            }
        } catch (error) {
            console.error('Error syncing employee with BioStar:', error);
            return null;
        }
    }
    // Private helper methods
    processAttendanceEvents(employeeId, date, events) {
        if (!events || events.length === 0) {
            return null;
        }
        // Sort events by time
        const sortedEvents = events.sort((a, b)=>new Date(a.datetime).getTime() - new Date(b.datetime).getTime());
        // Find first entry and last exit
        const entryEvents = sortedEvents.filter((e)=>e.event_type === 'ENTRY');
        const exitEvents = sortedEvents.filter((e)=>e.event_type === 'EXIT');
        const firstIn = entryEvents.length > 0 ? entryEvents[0].datetime : undefined;
        const lastOut = exitEvents.length > 0 ? exitEvents[exitEvents.length - 1].datetime : undefined;
        // Calculate total hours
        let totalHours = 0;
        if (firstIn && lastOut) {
            totalHours = (0, _datefns.differenceInHours)((0, _datefns.parseISO)(lastOut), (0, _datefns.parseISO)(firstIn));
        }
        // Determine status
        let status = 'ABSENT';
        if (firstIn) {
            const checkInTime = (0, _datefns.parseISO)(firstIn);
            const workStartTime = new Date(checkInTime);
            workStartTime.setHours(9, 0, 0, 0); // Assuming 9 AM start time
            if (checkInTime <= workStartTime) {
                status = 'PRESENT';
            } else {
                status = 'LATE';
            }
            // Check for early out
            if (lastOut) {
                const checkOutTime = (0, _datefns.parseISO)(lastOut);
                const workEndTime = new Date(checkOutTime);
                workEndTime.setHours(17, 0, 0, 0); // Assuming 5 PM end time
                if (checkOutTime < workEndTime && totalHours < 8) {
                    status = 'EARLY_OUT';
                }
            }
        }
        return {
            id: `${employeeId}-${date}`,
            employee_id: employeeId,
            employee_name: events[0]?.user_name || 'Unknown',
            date,
            first_in: firstIn,
            last_out: lastOut,
            total_hours: totalHours,
            break_time: 0,
            overtime: Math.max(0, totalHours - 8),
            status,
            events: sortedEvents,
            biostar_synced: true
        };
    }
    groupEventsByDate(events) {
        return events.reduce((groups, event)=>{
            const date = (0, _datefns.format)((0, _datefns.parseISO)(event.datetime), 'yyyy-MM-dd');
            if (!groups[date]) {
                groups[date] = [];
            }
            groups[date].push(event);
            return groups;
        }, {});
    }
    mapEventType(biostarEventType) {
        switch(biostarEventType){
            case 'ENTRY':
                return 'CHECK_IN';
            case 'EXIT':
                return 'CHECK_OUT';
            default:
                return 'CHECK_IN';
        }
    }
    constructor(){
        this.realtimeListeners = [];
        this.pollingInterval = null;
    }
}
const attendanceService = AttendanceService.getInstance();

//# sourceMappingURL=data:application/json;base64,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