'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import {
  Search,
  Bell,
  User,
  Settings,
  LogOut,
  Menu,
  Briefcase,
  ChevronDown,
  Info
} from 'lucide-react';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials } from '@/lib/utils';

interface HeaderProps {
  onMobileMenuToggle: () => void;
}

const Header: React.FC<HeaderProps> = ({ onMobileMenuToggle }) => {
  const { user, logout } = useAuth();
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleLogout = () => {
    logout();
  };

  const userInitials = user ? getInitials(user.first_name, user.last_name) : 'U';

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      {/* Top Header Section */}
      <div className="bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center">
                <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">W</span>
                </div>
                <span className="ml-2 text-white font-semibold text-lg hidden sm:block">
                  WorkFlow
                </span>
              </Link>
            </div>

            {/* Search Bar */}
            <div className="flex-1 max-w-lg mx-8 hidden md:block">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search here..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>
            </div>

            {/* Right Side */}
            <div className="flex items-center space-x-4">
              {/* Leave Application Link */}
              <Link
                href="/leave"
                className="text-gray-300 hover:text-white p-2 rounded-md transition-colors"
                title="Apply Leave"
              >
                <Briefcase className="h-5 w-5" />
              </Link>

              {/* Notifications */}
              <button className="text-gray-300 hover:text-white p-2 rounded-md transition-colors relative">
                <Bell className="h-5 w-5" />
                <span className="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400"></span>
              </button>

              {/* User Menu */}
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 text-gray-300 hover:text-white p-2 rounded-md transition-colors"
                >
                  {user?.profile_picture ? (
                    <Image
                      src={user.profile_picture}
                      alt="Profile"
                      width={32}
                      height={32}
                      className="rounded-full"
                    />
                  ) : (
                    <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">
                        {userInitials}
                      </span>
                    </div>
                  )}
                  <ChevronDown className="h-4 w-4" />
                </button>

                {/* User Dropdown */}
                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                    <Link
                      href="/profile"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <User className="h-4 w-4 mr-2" />
                      Profile
                    </Link>
                    <Link
                      href="/settings"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      Settings
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      Logout
                    </button>
                  </div>
                )}
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={onMobileMenuToggle}
                className="md:hidden text-gray-300 hover:text-white p-2 rounded-md transition-colors"
              >
                <Menu className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Bar */}
      <div className="bg-white hidden md:block">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8 h-12">
            <NavLink href="/dashboard" icon="🏠">Home</NavLink>
            <NavLink href="/employees" icon="👥">Admin</NavLink>
            <NavLink href="/info" icon="ℹ️">Info</NavLink>
            <NavLink href="/company" icon="🏢">Company</NavLink>
            <NavLink href="/manage" icon="⚙️">Manage</NavLink>
            <NavLink href="/settings" icon="🔧">Settings</NavLink>
          </nav>
        </div>
      </div>

      {/* Mobile Search */}
      <div className="md:hidden bg-white border-t border-gray-200 px-4 py-3">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search here..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
          />
        </div>
      </div>
    </header>
  );
};

// Navigation Link Component
const NavLink: React.FC<{
  href: string;
  icon: string;
  children: React.ReactNode;
}> = ({ href, icon, children }) => {
  const pathname = usePathname();
  const isActive = pathname === href || (href !== '/dashboard' && pathname.startsWith(href));

  return (
    <Link
      href={href}
      className={cn(
        'flex items-center space-x-2 px-3 py-3 text-sm font-medium rounded-md transition-colors',
        isActive
          ? 'text-orange-600 bg-orange-50 border-b-2 border-orange-500'
          : 'text-gray-600 hover:text-orange-600 hover:bg-orange-50 border-b-2 border-transparent hover:border-orange-500'
      )}
    >
      <span>{icon}</span>
      <span className="hidden lg:block">{children}</span>
    </Link>
  );
};

export default Header;
