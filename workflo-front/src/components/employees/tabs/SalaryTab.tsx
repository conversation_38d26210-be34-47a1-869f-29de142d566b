'use client';

import React, { useState, useEffect } from 'react';
import {
  DollarSign,
  Plus,
  Search,
  Filter,
  TrendingUp,
  TrendingDown,
  Calendar,
  Download,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  CreditCard,
  PiggyBank,
  Receipt,
  Calculator,
  Settings,
  Clock,
  AlertTriangle,
  Award,
  Zap,
  Heart
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { cn, formatDate, formatCurrency } from '@/lib/utils';
import SalaryConfigModal from '../SalaryConfigModal';
import { SalaryCalculator, SalaryConfig, DynamicSalaryCalculation } from '@/lib/salaryCalculator';
import BenefitsSection from '../salary/BenefitsSection';
import TaxSection from '../salary/TaxSection';
import BonusesSection from '../salary/BonusesSection';
import OvertimeSection from '../salary/OvertimeSection';

interface SalaryRecord {
  id: number;
  employee_id: number;
  effective_date: string;
  basic_salary: number;
  allowances: {
    housing: number;
    transport: number;
    medical: number;
    other: number;
  };
  deductions: {
    tax: number;
    insurance: number;
    pension: number;
    other: number;
  };
  gross_salary: number;
  net_salary: number;
  currency: string;
  status: 'active' | 'inactive' | 'pending';
  created_by: string;
  created_date: string;
  notes?: string;
}

interface PayslipRecord {
  id: number;
  employee_id: number;
  pay_period: string;
  pay_date: string;
  basic_salary: number;
  overtime_pay: number;
  allowances: number;
  gross_pay: number;
  deductions: number;
  net_pay: number;
  status: 'paid' | 'pending' | 'processing';
  payslip_url?: string;
}

interface SalaryTabProps {
  employeeId: number;
  employeeName: string;
  isCurrentUser?: boolean;
}

const SalaryTab: React.FC<SalaryTabProps> = ({
  employeeId,
  employeeName,
  isCurrentUser = false
}) => {
  const [activeSubTab, setActiveSubTab] = useState<'current' | 'history' | 'payslips' | 'analytics' | 'dynamic' | 'benefits' | 'taxes' | 'bonuses' | 'overtime'>('current');
  const [salaryRecords, setSalaryRecords] = useState<SalaryRecord[]>([]);
  const [payslipRecords, setPayslipRecords] = useState<PayslipRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());
  const [showConfigModal, setShowConfigModal] = useState(false);
  const [salaryConfig, setSalaryConfig] = useState<SalaryConfig>({
    isDynamic: false,
    hourlyRate: 25,
    overtimeMultiplier: 1.5,
    workingHoursPerDay: 8,
    workingDaysPerMonth: 22,
    leaveDeductions: {
      unpaidLeavePerDay: 100,
      lateArrivalPerHour: 15,
      earlyDeparturePerHour: 15,
      absenteeismPerDay: 150
    },
    bonuses: {
      perfectAttendanceBonus: 200,
      overtimeBonus: 50,
      performanceBonus: 300
    },
    penalties: {
      excessiveAbsenceThreshold: 3,
      excessiveAbsencePenalty: 5,
      lateArrivalThreshold: 5,
      lateArrivalPenalty: 25
    }
  });
  const [dynamicCalculation, setDynamicCalculation] = useState<DynamicSalaryCalculation | null>(null);
  const [salaryCalculator] = useState(() => new SalaryCalculator(salaryConfig));

  // Mock data
  const mockSalaryRecords: SalaryRecord[] = [
    {
      id: 1,
      employee_id: employeeId,
      effective_date: '2024-01-01',
      basic_salary: 10500000,
      allowances: {
        housing: 1300000,
        transport: 400000,
        medical: 260000,
        other: 0
      },
      deductions: {
        tax: 1950000,
        insurance: 260000,
        pension: 520000,
        other: 0
      },
      gross_salary: 12460000,
      net_salary: 9730000,
      currency: 'KSH',
      status: 'active',
      created_by: 'HR Department',
      created_date: '2023-12-15T00:00:00Z',
      notes: 'Annual salary review increase'
    },
    {
      id: 2,
      employee_id: employeeId,
      effective_date: '2023-01-01',
      basic_salary: 9800000,
      allowances: {
        housing: 1170000,
        transport: 400000,
        medical: 260000,
        other: 0
      },
      deductions: {
        tax: 1755000,
        insurance: 234000,
        pension: 487500,
        other: 0
      },
      gross_salary: 11630000,
      net_salary: 9153500,
      currency: 'KSH',
      status: 'inactive',
      created_by: 'HR Department',
      created_date: '2022-12-20T00:00:00Z',
      notes: 'Initial salary package'
    }
  ];

  const mockPayslipRecords: PayslipRecord[] = [
    {
      id: 1,
      employee_id: employeeId,
      pay_period: '2024-01',
      pay_date: '2024-01-31',
      basic_salary: 875000,
      overtime_pay: 65000,
      allowances: 163333,
      gross_pay: 1103333,
      deductions: 227500,
      net_pay: 875833,
      status: 'paid',
      payslip_url: '/payslips/2024-01.pdf'
    },
    {
      id: 2,
      employee_id: employeeId,
      pay_period: '2023-12',
      pay_date: '2023-12-31',
      basic_salary: 875000,
      overtime_pay: 97500,
      allowances: 163333,
      gross_pay: 1135833,
      deductions: 234000,
      net_pay: 901833,
      status: 'paid',
      payslip_url: '/payslips/2023-12.pdf'
    },
    {
      id: 3,
      employee_id: employeeId,
      pay_period: '2023-11',
      pay_date: '2023-11-30',
      basic_salary: 816667,
      overtime_pay: 39000,
      allowances: 152500,
      gross_pay: 1008167,
      deductions: 214500,
      net_pay: 793667,
      status: 'paid',
      payslip_url: '/payslips/2023-11.pdf'
    }
  ];

  // Mock attendance data for dynamic calculation
  const mockAttendanceRecords = [
    {
      id: 1, employee_id: employeeId, date: '2024-01-15', check_in_time: '09:00:00', check_out_time: '18:00:00',
      total_hours: 8, regular_hours: 8, overtime_hours: 0, status: 'present' as const
    },
    {
      id: 2, employee_id: employeeId, date: '2024-01-16', check_in_time: '09:15:00', check_out_time: '19:30:00',
      total_hours: 9.25, regular_hours: 8, overtime_hours: 1.25, status: 'late' as const, late_minutes: 15
    },
    {
      id: 3, employee_id: employeeId, date: '2024-01-17', check_in_time: '08:45:00', check_out_time: '13:00:00',
      total_hours: 4.25, regular_hours: 4.25, overtime_hours: 0, status: 'half_day' as const
    },
    {
      id: 4, employee_id: employeeId, date: '2024-01-18', total_hours: 0, regular_hours: 0, overtime_hours: 0, status: 'absent' as const
    }
  ];

  const mockLeaveRecords = [
    {
      id: 1, employee_id: employeeId, start_date: '2024-01-18', end_date: '2024-01-18',
      business_days: 1, leave_type: 'unpaid' as const, status: 'approved' as const
    }
  ];

  const mockPerformanceData = {
    overall_rating: 4.2,
    goals_completed: 3,
    total_goals: 4
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setTimeout(() => {
        setSalaryRecords(mockSalaryRecords);
        setPayslipRecords(mockPayslipRecords);

        // Calculate dynamic salary if enabled
        if (salaryConfig.isDynamic) {
          const currentSalary = mockSalaryRecords.find(r => r.status === 'active');
          if (currentSalary) {
            salaryCalculator.updateConfig(salaryConfig);
            const calculation = salaryCalculator.calculateDynamicSalary(
              employeeId,
              currentSalary.basic_salary,
              mockAttendanceRecords,
              mockLeaveRecords,
              mockPerformanceData,
              selectedYear + '-01'
            );
            setDynamicCalculation(calculation);
          }
        }

        setLoading(false);
      }, 1000);
    };

    loadData();
  }, [employeeId, salaryConfig, selectedYear]);

  const handleConfigSave = (newConfig: SalaryConfig) => {
    setSalaryConfig(newConfig);

    // Recalculate if dynamic mode is enabled
    if (newConfig.isDynamic) {
      const currentSalary = salaryRecords.find(r => r.status === 'active');
      if (currentSalary) {
        salaryCalculator.updateConfig(newConfig);
        const calculation = salaryCalculator.calculateDynamicSalary(
          employeeId,
          currentSalary.basic_salary,
          mockAttendanceRecords,
          mockLeaveRecords,
          mockPerformanceData,
          selectedYear + '-01'
        );
        setDynamicCalculation(calculation);
      }
    } else {
      setDynamicCalculation(null);
    }
  };

  const currentSalary = salaryRecords.find(record => record.status === 'active');

  const filteredPayslips = payslipRecords.filter(payslip => {
    const matchesSearch = payslip.pay_period.includes(searchQuery);
    const matchesYear = payslip.pay_period.startsWith(selectedYear);
    return matchesSearch && matchesYear;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': case 'paid': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'pending': case 'processing': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const calculateYearlyStats = () => {
    const yearPayslips = payslipRecords.filter(p =>
      p.pay_period.startsWith(selectedYear)
    );

    const totalGrossPay = yearPayslips.reduce((sum, p) => sum + p.gross_pay, 0);
    const totalNetPay = yearPayslips.reduce((sum, p) => sum + p.net_pay, 0);
    const totalDeductions = yearPayslips.reduce((sum, p) => sum + p.deductions, 0);
    const totalOvertimePay = yearPayslips.reduce((sum, p) => sum + p.overtime_pay, 0);

    return {
      totalGrossPay,
      totalNetPay,
      totalDeductions,
      totalOvertimePay,
      averageMonthlyPay: yearPayslips.length > 0 ? totalNetPay / yearPayslips.length : 0
    };
  };

  const yearlyStats = calculateYearlyStats();

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Loading Header */}
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>

        {/* Loading Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="h-8 bg-gray-200 rounded w-8 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Loading Tabs */}
        <div className="animate-pulse">
          <div className="border-b border-gray-200">
            <div className="flex space-x-8">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="h-10 bg-gray-200 rounded w-24 mb-2"></div>
              ))}
            </div>
          </div>
        </div>

        {/* Loading Content */}
        <div className="animate-pulse space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-medium text-gray-900">Salary Management</h3>
            {salaryConfig.isDynamic && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                <Zap className="h-3 w-3 mr-1" />
                Dynamic Mode
              </span>
            )}
          </div>
          <p className="text-sm text-gray-600 mt-1">
            {salaryConfig.isDynamic
              ? `Dynamic salary calculation based on attendance, time tracking, and performance for ${employeeName}`
              : `Manage salary information and payroll for ${employeeName}`
            }
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <Button variant="secondary" onClick={() => setShowConfigModal(true)}>
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </Button>
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          {!isCurrentUser && (
            <Button variant="primary">
              <Plus className="h-4 w-4 mr-2" />
              Update Salary
            </Button>
          )}
        </div>
      </div>

      {/* Current Salary Overview */}
      {currentSalary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">
                    {salaryConfig.isDynamic ? 'Base Salary' : 'Basic Salary'}
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {formatCurrency(dynamicCalculation?.baseSalary || currentSalary.basic_salary)}
                  </p>
                </div>
              </div>
            </div>
          </Card>

          <Card>
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Gross Pay</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {formatCurrency(dynamicCalculation?.grossPay || currentSalary.gross_salary)}
                  </p>
                  {salaryConfig.isDynamic && dynamicCalculation && (
                    <p className="text-xs text-gray-500 mt-1">
                      Includes bonuses: {formatCurrency(dynamicCalculation.bonuses.total)}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </Card>

          <Card>
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <PiggyBank className="h-8 w-8 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Net Pay</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {formatCurrency(dynamicCalculation?.netPay || currentSalary.net_salary)}
                  </p>
                  {salaryConfig.isDynamic && dynamicCalculation && (
                    <p className="text-xs text-gray-500 mt-1">
                      After deductions: {formatCurrency(dynamicCalculation.deductions.total)}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </Card>

          <Card>
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  {salaryConfig.isDynamic ? (
                    <Clock className="h-8 w-8 text-purple-600" />
                  ) : (
                    <TrendingDown className="h-8 w-8 text-red-600" />
                  )}
                </div>
                <div className="ml-4">
                  {salaryConfig.isDynamic && dynamicCalculation ? (
                    <>
                      <p className="text-sm font-medium text-gray-500">Attendance Rate</p>
                      <p className="text-2xl font-semibold text-gray-900">
                        {dynamicCalculation.attendanceRate.toFixed(1)}%
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {dynamicCalculation.breakdown.presentDays}/{dynamicCalculation.breakdown.workingDays} days
                      </p>
                    </>
                  ) : (
                    <>
                      <p className="text-sm font-medium text-gray-500">Total Deductions</p>
                      <p className="text-2xl font-semibold text-gray-900">
                        {formatCurrency(
                          currentSalary.deductions.tax +
                          currentSalary.deductions.insurance +
                          currentSalary.deductions.pension +
                          currentSalary.deductions.other
                        )}
                      </p>
                    </>
                  )}
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Sub Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {[
            { key: 'current', label: 'Current Salary', icon: DollarSign },
            { key: 'benefits', label: 'Benefits', icon: Heart },
            { key: 'taxes', label: 'Taxes', icon: Receipt },
            { key: 'bonuses', label: 'Bonuses', icon: Award },
            { key: 'overtime', label: 'Overtime', icon: Clock },
            { key: 'history', label: 'Salary History', icon: Calendar },
            { key: 'payslips', label: 'Payslips', icon: Receipt },
            { key: 'analytics', label: 'Analytics', icon: Calculator },
            ...(salaryConfig.isDynamic ? [{ key: 'dynamic', label: 'Dynamic Breakdown', icon: Zap }] : []),
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.key}
                onClick={() => setActiveSubTab(tab.key as any)}
                className={cn(
                  'py-2 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2',
                  activeSubTab === tab.key
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeSubTab === 'current' && currentSalary && (
        <CurrentSalaryView salary={currentSalary} />
      )}

      {activeSubTab === 'history' && (
        <SalaryHistoryView records={salaryRecords} loading={loading} />
      )}

      {activeSubTab === 'payslips' && (
        <PayslipsView
          payslips={filteredPayslips}
          loading={loading}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          selectedYear={selectedYear}
          setSelectedYear={setSelectedYear}
        />
      )}

      {activeSubTab === 'analytics' && (
        <SalaryAnalyticsView stats={yearlyStats} selectedYear={selectedYear} />
      )}

      {activeSubTab === 'dynamic' && salaryConfig.isDynamic && dynamicCalculation && (
        <DynamicSalaryBreakdownView calculation={dynamicCalculation} config={salaryConfig} />
      )}

      {activeSubTab === 'benefits' && (
        <BenefitsSection
          employeeId={employeeId}
          employeeName={employeeName}
          isCurrentUser={isCurrentUser}
        />
      )}

      {activeSubTab === 'taxes' && (
        <TaxSection
          employeeId={employeeId}
          employeeName={employeeName}
          isCurrentUser={isCurrentUser}
        />
      )}

      {activeSubTab === 'bonuses' && (
        <BonusesSection
          employeeId={employeeId}
          employeeName={employeeName}
          isCurrentUser={isCurrentUser}
        />
      )}

      {activeSubTab === 'overtime' && (
        <OvertimeSection
          employeeId={employeeId}
          employeeName={employeeName}
          isCurrentUser={isCurrentUser}
        />
      )}

      {/* Configuration Modal */}
      <SalaryConfigModal
        isOpen={showConfigModal}
        onClose={() => setShowConfigModal(false)}
        onSave={handleConfigSave}
        currentConfig={salaryConfig}
      />
    </div>
  );
};

// Current Salary View Component
interface CurrentSalaryViewProps {
  salary: SalaryRecord;
}

const CurrentSalaryView: React.FC<CurrentSalaryViewProps> = ({ salary }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Salary Breakdown</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Basic Salary:</span>
              <span className="text-sm font-medium text-gray-900">{formatCurrency(salary.basic_salary)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Housing Allowance:</span>
              <span className="text-sm font-medium text-gray-900">{formatCurrency(salary.allowances.housing)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Transport Allowance:</span>
              <span className="text-sm font-medium text-gray-900">{formatCurrency(salary.allowances.transport)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Medical Allowance:</span>
              <span className="text-sm font-medium text-gray-900">{formatCurrency(salary.allowances.medical)}</span>
            </div>
            <div className="border-t border-gray-200 pt-3">
              <div className="flex justify-between">
                <span className="text-base font-medium text-gray-900">Gross Salary:</span>
                <span className="text-base font-bold text-gray-900">{formatCurrency(salary.gross_salary)}</span>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Deductions</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Income Tax:</span>
              <span className="text-sm font-medium text-red-600">{formatCurrency(salary.deductions.tax)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Insurance:</span>
              <span className="text-sm font-medium text-red-600">{formatCurrency(salary.deductions.insurance)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Pension:</span>
              <span className="text-sm font-medium text-red-600">{formatCurrency(salary.deductions.pension)}</span>
            </div>
            <div className="border-t border-gray-200 pt-3">
              <div className="flex justify-between">
                <span className="text-base font-medium text-gray-900">Net Salary:</span>
                <span className="text-base font-bold text-green-600">{formatCurrency(salary.net_salary)}</span>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

// Salary History View Component
interface SalaryHistoryViewProps {
  records: SalaryRecord[];
  loading: boolean;
}

const SalaryHistoryView: React.FC<SalaryHistoryViewProps> = ({ records, loading }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {records.map((record) => (
        <Card key={record.id}>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h4 className="text-lg font-medium text-gray-900">
                  Effective from {formatDate(record.effective_date)}
                </h4>
                <p className="text-sm text-gray-600">Created by {record.created_by}</p>
              </div>
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                getStatusColor(record.status)
              )}>
                {record.status.toUpperCase()}
              </span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h5 className="text-sm font-medium text-gray-900 mb-2">Basic Information</h5>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Basic Salary:</span>
                    <span className="font-medium">{formatCurrency(record.basic_salary)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Currency:</span>
                    <span className="font-medium">{record.currency}</span>
                  </div>
                </div>
              </div>

              <div>
                <h5 className="text-sm font-medium text-gray-900 mb-2">Compensation</h5>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Gross Salary:</span>
                    <span className="font-medium">{formatCurrency(record.gross_salary)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Net Salary:</span>
                    <span className="font-medium text-green-600">{formatCurrency(record.net_salary)}</span>
                  </div>
                </div>
              </div>

              <div>
                <h5 className="text-sm font-medium text-gray-900 mb-2">Notes</h5>
                <p className="text-sm text-gray-600">{record.notes || 'No notes available'}</p>
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

// Payslips View Component
interface PayslipsViewProps {
  payslips: PayslipRecord[];
  loading: boolean;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  selectedYear: string;
  setSelectedYear: (year: string) => void;
}

const PayslipsView: React.FC<PayslipsViewProps> = ({
  payslips,
  loading,
  searchQuery,
  setSearchQuery,
  selectedYear,
  setSelectedYear
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <div className="flex-1">
          <Input
            placeholder="Search payslips..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="h-4 w-4" />}
          />
        </div>
        <select
          value={selectedYear}
          onChange={(e) => setSelectedYear(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="2024">2024</option>
          <option value="2023">2023</option>
          <option value="2022">2022</option>
        </select>
      </div>

      {/* Payslips List */}
      {loading ? (
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      ) : payslips.length === 0 ? (
        <div className="text-center py-12">
          <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No payslips found
          </h3>
          <p className="text-gray-600">
            No payslips found for the selected criteria.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {payslips.map((payslip) => (
            <Card key={payslip.id}>
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="text-lg font-medium text-gray-900">
                      Payslip for {payslip.pay_period}
                    </h4>
                    <p className="text-sm text-gray-600">
                      Pay Date: {formatDate(payslip.pay_date)}
                    </p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={cn(
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      getStatusColor(payslip.status)
                    )}>
                      {payslip.status.toUpperCase()}
                    </span>
                    {payslip.payslip_url && (
                      <Button variant="secondary" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Basic Pay:</span>
                    <div className="font-medium">{formatCurrency(payslip.basic_salary)}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Overtime:</span>
                    <div className="font-medium">{formatCurrency(payslip.overtime_pay)}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Allowances:</span>
                    <div className="font-medium">{formatCurrency(payslip.allowances)}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Deductions:</span>
                    <div className="font-medium text-red-600">{formatCurrency(payslip.deductions)}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Net Pay:</span>
                    <div className="font-bold text-green-600">{formatCurrency(payslip.net_pay)}</div>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

// Salary Analytics View Component
interface SalaryAnalyticsViewProps {
  stats: any;
  selectedYear: string;
}

const SalaryAnalyticsView: React.FC<SalaryAnalyticsViewProps> = ({ stats, selectedYear }) => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Gross Pay</p>
                <p className="text-2xl font-semibold text-gray-900">{formatCurrency(stats.totalGrossPay)}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <PiggyBank className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Net Pay</p>
                <p className="text-2xl font-semibold text-gray-900">{formatCurrency(stats.totalNetPay)}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingDown className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Deductions</p>
                <p className="text-2xl font-semibold text-gray-900">{formatCurrency(stats.totalDeductions)}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calculator className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Average Monthly</p>
                <p className="text-2xl font-semibold text-gray-900">{formatCurrency(stats.averageMonthlyPay)}</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Yearly Summary for {selectedYear}
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">{formatCurrency(stats.totalGrossPay)}</div>
              <div className="text-sm text-gray-600">Total Gross</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">{formatCurrency(stats.totalNetPay)}</div>
              <div className="text-sm text-gray-600">Total Net</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">{formatCurrency(stats.totalDeductions)}</div>
              <div className="text-sm text-gray-600">Total Deductions</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-orange-600">{formatCurrency(stats.totalOvertimePay)}</div>
              <div className="text-sm text-gray-600">Overtime Pay</div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

// Dynamic Salary Breakdown View Component
interface DynamicSalaryBreakdownViewProps {
  calculation: DynamicSalaryCalculation;
  config: SalaryConfig;
}

const DynamicSalaryBreakdownView: React.FC<DynamicSalaryBreakdownViewProps> = ({ calculation, config }) => {
  return (
    <div className="space-y-6">
      {/* Attendance & Performance Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900">Attendance Summary</h4>
              <Clock className="h-5 w-5 text-blue-600" />
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Working Days:</span>
                <span className="font-medium">{calculation.breakdown.workingDays}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Present Days:</span>
                <span className="font-medium text-green-600">{calculation.breakdown.presentDays}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Absent Days:</span>
                <span className="font-medium text-red-600">{calculation.breakdown.absentDays}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Late Days:</span>
                <span className="font-medium text-yellow-600">{calculation.breakdown.lateDays}</span>
              </div>
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-900">Attendance Rate:</span>
                  <span className="font-bold text-blue-600">{calculation.attendanceRate.toFixed(1)}%</span>
                </div>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900">Hours Breakdown</h4>
              <Clock className="h-5 w-5 text-orange-600" />
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Regular Hours:</span>
                <span className="font-medium">{calculation.regularHours}h</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Overtime Hours:</span>
                <span className="font-medium text-orange-600">{calculation.overtimeHours}h</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Hourly Rate:</span>
                <span className="font-medium">{formatCurrency(config.hourlyRate)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">OT Multiplier:</span>
                <span className="font-medium">{config.overtimeMultiplier}x</span>
              </div>
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-900">Total Hours:</span>
                  <span className="font-bold text-orange-600">{calculation.regularHours + calculation.overtimeHours}h</span>
                </div>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900">Performance Impact</h4>
              <Award className="h-5 w-5 text-purple-600" />
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Punctuality Rate:</span>
                <span className="font-medium">{calculation.punctualityRate.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Performance Bonus:</span>
                <span className="font-medium text-green-600">{formatCurrency(calculation.bonuses.performance)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Attendance Bonus:</span>
                <span className="font-medium text-green-600">{formatCurrency(calculation.bonuses.perfectAttendance)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Overtime Bonus:</span>
                <span className="font-medium text-green-600">{formatCurrency(calculation.bonuses.overtime)}</span>
              </div>
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-900">Total Bonuses:</span>
                  <span className="font-bold text-green-600">{formatCurrency(calculation.bonuses.total)}</span>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Detailed Calculation Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900">Earnings Breakdown</h4>
              <TrendingUp className="h-5 w-5 text-green-600" />
            </div>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Regular Pay:</span>
                <span className="font-medium">{formatCurrency(calculation.regularPay)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Overtime Pay:</span>
                <span className="font-medium">{formatCurrency(calculation.overtimePay)}</span>
              </div>
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Base Earnings:</span>
                  <span className="font-medium">{formatCurrency(calculation.regularPay + calculation.overtimePay)}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">Perfect Attendance Bonus:</span>
                  <span className="text-sm text-green-600">+{formatCurrency(calculation.bonuses.perfectAttendance)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">Overtime Bonus:</span>
                  <span className="text-sm text-green-600">+{formatCurrency(calculation.bonuses.overtime)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">Performance Bonus:</span>
                  <span className="text-sm text-green-600">+{formatCurrency(calculation.bonuses.performance)}</span>
                </div>
              </div>
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between items-center">
                  <span className="text-base font-medium text-gray-900">Gross Pay:</span>
                  <span className="text-lg font-bold text-green-600">{formatCurrency(calculation.grossPay)}</span>
                </div>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900">Deductions Breakdown</h4>
              <TrendingDown className="h-5 w-5 text-red-600" />
            </div>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">Unpaid Leave:</span>
                  <span className="text-sm text-red-600">-{formatCurrency(calculation.deductions.unpaidLeave)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">Late Arrival:</span>
                  <span className="text-sm text-red-600">-{formatCurrency(calculation.deductions.lateArrival)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">Early Departure:</span>
                  <span className="text-sm text-red-600">-{formatCurrency(calculation.deductions.earlyDeparture)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">Absenteeism:</span>
                  <span className="text-sm text-red-600">-{formatCurrency(calculation.deductions.absenteeism)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">Excessive Absence Penalty:</span>
                  <span className="text-sm text-red-600">-{formatCurrency(calculation.deductions.excessiveAbsence)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">Late Arrival Penalty:</span>
                  <span className="text-sm text-red-600">-{formatCurrency(calculation.deductions.lateArrivalPenalty)}</span>
                </div>
              </div>
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Deductions:</span>
                  <span className="font-medium text-red-600">{formatCurrency(calculation.deductions.total)}</span>
                </div>
              </div>
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between items-center">
                  <span className="text-base font-medium text-gray-900">Net Pay:</span>
                  <span className="text-lg font-bold text-orange-600">{formatCurrency(calculation.netPay)}</span>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Configuration Summary */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-gray-900">Current Configuration</h4>
            <Settings className="h-5 w-5 text-gray-600" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
            <div>
              <h5 className="font-medium text-gray-900 mb-2">Rate Settings</h5>
              <div className="space-y-1 text-gray-600">
                <div>Hourly Rate: {formatCurrency(config.hourlyRate)}</div>
                <div>Overtime Multiplier: {config.overtimeMultiplier}x</div>
                <div>Working Hours/Day: {config.workingHoursPerDay}h</div>
                <div>Working Days/Month: {config.workingDaysPerMonth}</div>
              </div>
            </div>
            <div>
              <h5 className="font-medium text-gray-900 mb-2">Deduction Rates</h5>
              <div className="space-y-1 text-gray-600">
                <div>Unpaid Leave: {formatCurrency(config.leaveDeductions.unpaidLeavePerDay)}/day</div>
                <div>Late Arrival: {formatCurrency(config.leaveDeductions.lateArrivalPerHour)}/hour</div>
                <div>Early Departure: {formatCurrency(config.leaveDeductions.earlyDeparturePerHour)}/hour</div>
                <div>Absenteeism: {formatCurrency(config.leaveDeductions.absenteeismPerDay)}/day</div>
              </div>
            </div>
            <div>
              <h5 className="font-medium text-gray-900 mb-2">Bonus Amounts</h5>
              <div className="space-y-1 text-gray-600">
                <div>Perfect Attendance: {formatCurrency(config.bonuses.perfectAttendanceBonus)}</div>
                <div>Overtime Bonus: {formatCurrency(config.bonuses.overtimeBonus)}</div>
                <div>Performance Bonus: {formatCurrency(config.bonuses.performanceBonus)}</div>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SalaryTab;
