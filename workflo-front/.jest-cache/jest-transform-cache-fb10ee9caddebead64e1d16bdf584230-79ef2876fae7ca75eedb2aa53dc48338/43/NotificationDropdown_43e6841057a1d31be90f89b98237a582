5405ba2329f9f7687581bee4def1bcd7
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _lucidereact = require("lucide-react");
const _NotificationProvider = require("../../providers/NotificationProvider");
const _utils = require("../../lib/utils");
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const NotificationDropdown = ({ className })=>{
    const [isOpen, setIsOpen] = (0, _react.useState)(false);
    const [showSettings, setShowSettings] = (0, _react.useState)(false);
    const dropdownRef = (0, _react.useRef)(null);
    const { notifications, unreadCount, isConnected, connectionState, markAsRead, markAllAsRead, clearNotifications, requestPermission } = (0, _NotificationProvider.useNotifications)();
    // Close dropdown when clicking outside
    (0, _react.useEffect)(()=>{
        const handleClickOutside = (event)=>{
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
                setShowSettings(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return ()=>document.removeEventListener('mousedown', handleClickOutside);
    }, []);
    const getNotificationIcon = (type)=>{
        switch(type){
            case 'leave_update':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Calendar, {
                    className: "h-4 w-4 text-blue-500"
                });
            case 'payroll_update':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.DollarSign, {
                    className: "h-4 w-4 text-green-500"
                });
            case 'performance_update':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Star, {
                    className: "h-4 w-4 text-yellow-500"
                });
            case 'company_announcement':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Building, {
                    className: "h-4 w-4 text-purple-500"
                });
            case 'system_maintenance':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Wrench, {
                    className: "h-4 w-4 text-orange-500"
                });
            case 'ticket_update':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Info, {
                    className: "h-4 w-4 text-indigo-500"
                });
            default:
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Bell, {
                    className: "h-4 w-4 text-gray-500"
                });
        }
    };
    const getPriorityColor = (priority)=>{
        switch(priority){
            case 'high':
                return 'border-l-red-500 bg-red-50';
            case 'medium':
                return 'border-l-yellow-500 bg-yellow-50';
            case 'low':
                return 'border-l-green-500 bg-green-50';
            default:
                return 'border-l-gray-500 bg-gray-50';
        }
    };
    const handleNotificationClick = (notification)=>{
        if (!notification.read) {
            markAsRead(notification.id);
        }
    // Handle navigation based on notification type
    // You can implement routing logic here
    };
    const handleRequestPermission = async ()=>{
        const permission = await requestPermission();
        if (permission === 'granted') {
            console.log('Notification permission granted');
        }
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: (0, _utils.cn)('relative', className),
        ref: dropdownRef,
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                onClick: ()=>setIsOpen(!isOpen),
                className: "relative p-2 text-gray-400 hover:text-gray-600 transition-colors",
                children: [
                    unreadCount > 0 ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.BellRing, {
                        className: "h-5 w-5"
                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Bell, {
                        className: "h-5 w-5"
                    }),
                    unreadCount > 0 && /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                        className: "absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",
                        children: unreadCount > 99 ? '99+' : unreadCount
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: (0, _utils.cn)('absolute -bottom-1 -right-1 h-3 w-3 rounded-full border-2 border-white', isConnected ? 'bg-green-500' : 'bg-red-500')
                    })
                ]
            }),
            isOpen && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "p-4 border-b border-gray-200",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center justify-between",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "flex items-center space-x-2",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                                className: "text-lg font-medium text-gray-900",
                                                children: "Notifications"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex items-center space-x-1",
                                                children: [
                                                    isConnected ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Wifi, {
                                                        className: "h-4 w-4 text-green-500"
                                                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.WifiOff, {
                                                        className: "h-4 w-4 text-red-500"
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                        className: "text-xs text-gray-500 capitalize",
                                                        children: connectionState
                                                    })
                                                ]
                                            })
                                        ]
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "flex items-center space-x-2",
                                        children: [
                                            unreadCount > 0 && /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                                onClick: markAllAsRead,
                                                className: "text-xs text-orange-600 hover:text-orange-700",
                                                title: "Mark all as read",
                                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCheck, {
                                                    className: "h-4 w-4"
                                                })
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                                onClick: ()=>setShowSettings(!showSettings),
                                                className: "text-gray-400 hover:text-gray-600",
                                                title: "Settings",
                                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Settings, {
                                                    className: "h-4 w-4"
                                                })
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                                onClick: ()=>setIsOpen(false),
                                                className: "text-gray-400 hover:text-gray-600",
                                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.X, {
                                                    className: "h-4 w-4"
                                                })
                                            })
                                        ]
                                    })
                                ]
                            }),
                            unreadCount > 0 && /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                className: "text-sm text-gray-600 mt-1",
                                children: [
                                    unreadCount,
                                    " unread notification",
                                    unreadCount !== 1 ? 's' : ''
                                ]
                            })
                        ]
                    }),
                    showSettings && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "p-4 border-b border-gray-200 bg-gray-50",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                className: "text-sm font-medium text-gray-900 mb-3",
                                children: "Notification Settings"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "space-y-2",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                        onClick: handleRequestPermission,
                                        className: "w-full text-left text-sm text-gray-700 hover:text-gray-900 py-1",
                                        children: "Enable Browser Notifications"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                        onClick: clearNotifications,
                                        className: "w-full text-left text-sm text-red-600 hover:text-red-700 py-1",
                                        children: "Clear All Notifications"
                                    })
                                ]
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "max-h-64 overflow-y-auto",
                        children: notifications.length === 0 ? /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "p-8 text-center",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Bell, {
                                    className: "h-12 w-12 text-gray-300 mx-auto mb-4"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                    className: "text-gray-500 text-sm",
                                    children: "No notifications yet"
                                })
                            ]
                        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "divide-y divide-gray-100",
                            children: notifications.map((notification)=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    onClick: ()=>handleNotificationClick(notification),
                                    className: (0, _utils.cn)('p-4 cursor-pointer hover:bg-gray-50 transition-colors border-l-4', getPriorityColor(notification.priority), !notification.read && 'bg-blue-50'),
                                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "flex items-start space-x-3",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                className: "flex-shrink-0 mt-1",
                                                children: getNotificationIcon(notification.type)
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex-1 min-w-0",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                        className: "flex items-center justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                                className: (0, _utils.cn)('text-sm font-medium truncate', notification.read ? 'text-gray-700' : 'text-gray-900'),
                                                                children: notification.title
                                                            }),
                                                            !notification.read && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                                className: "h-2 w-2 bg-blue-500 rounded-full flex-shrink-0 ml-2"
                                                            })
                                                        ]
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                        className: (0, _utils.cn)('text-sm mt-1', notification.read ? 'text-gray-500' : 'text-gray-700'),
                                                        children: notification.message
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                        className: "flex items-center justify-between mt-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                                className: "text-xs text-gray-400",
                                                                children: (0, _utils.formatDate)(notification.timestamp, 'MMM dd, HH:mm')
                                                            }),
                                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                                className: "flex items-center space-x-1",
                                                                children: [
                                                                    notification.priority === 'high' && /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertCircle, {
                                                                        className: "h-3 w-3 text-red-500"
                                                                    }),
                                                                    !notification.read && /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                                                                        onClick: (e)=>{
                                                                            e.stopPropagation();
                                                                            markAsRead(notification.id);
                                                                        },
                                                                        className: "text-xs text-orange-600 hover:text-orange-700",
                                                                        title: "Mark as read",
                                                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Check, {
                                                                            className: "h-3 w-3"
                                                                        })
                                                                    })
                                                                ]
                                                            })
                                                        ]
                                                    })
                                                ]
                                            })
                                        ]
                                    })
                                }, notification.id))
                        })
                    }),
                    notifications.length > 0 && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "p-3 border-t border-gray-200 bg-gray-50",
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("button", {
                            onClick: ()=>{
                                // Navigate to notifications page
                                setIsOpen(false);
                            },
                            className: "w-full text-center text-sm text-orange-600 hover:text-orange-700 font-medium",
                            children: "View All Notifications"
                        })
                    })
                ]
            })
        ]
    });
};
const _default = NotificationDropdown;

//# sourceMappingURL=data:application/json;base64,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