'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  DollarSign,
  FileText,
  Calculator,
  TrendingUp,
  Users,
  CreditCard,
  PieChart,
  BarChart3,
  Receipt,
  Banknote,
  Building,
  User,
  Settings,
  Shield,
  Clock,
  AlertCircle,
  CheckCircle,
  Calendar,
  Download,
  Upload,
  Search,
  Filter
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SidebarSection {
  title: string;
  items: SidebarItem[];
}

interface SidebarItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
  badge?: string;
  isNew?: boolean;
}

const AccountantDynamicSidebar: React.FC = () => {
  const pathname = usePathname();

  const getSidebarSections = (): SidebarSection[] => {
    // Default sections that appear on all pages
    const defaultSections: SidebarSection[] = [
      {
        title: 'Quick Actions',
        items: [
          {
            name: 'Process Payroll',
            href: '/accountant/manage/payroll/process',
            icon: DollarSign,
            description: 'Run monthly payroll',
            badge: 'Action'
          },
          {
            name: 'Generate Reports',
            href: '/accountant/manage/reports/generate',
            icon: TrendingUp,
            description: 'Create financial reports'
          },
          {
            name: 'Review Expenses',
            href: '/accountant/manage/expenses/review',
            icon: Receipt,
            description: 'Approve pending expenses',
            badge: '3'
          }
        ]
      }
    ];

    // Page-specific sections
    if (pathname.startsWith('/accountant/manage/payroll')) {
      return [
        ...defaultSections,
        {
          title: 'Payroll Management',
          items: [
            {
              name: 'Employee Salaries',
              href: '/accountant/manage/payroll/salaries',
              icon: Users,
              description: 'Manage employee compensation'
            },
            {
              name: 'Pay Cycles',
              href: '/accountant/manage/payroll/cycles',
              icon: Calendar,
              description: 'Configure pay periods'
            },
            {
              name: 'Deductions',
              href: '/accountant/manage/payroll/deductions',
              icon: Calculator,
              description: 'Tax and benefit deductions'
            },
            {
              name: 'Bonuses & Overtime',
              href: '/accountant/manage/payroll/bonuses',
              icon: Banknote,
              description: 'Additional compensation'
            },
            {
              name: 'Payroll History',
              href: '/accountant/manage/payroll/history',
              icon: Clock,
              description: 'Previous payroll runs'
            }
          ]
        },
        {
          title: 'Compliance',
          items: [
            {
              name: 'Tax Filings',
              href: '/accountant/manage/payroll/tax-filings',
              icon: FileText,
              description: 'Government submissions'
            },
            {
              name: 'Audit Trail',
              href: '/accountant/manage/payroll/audit',
              icon: Shield,
              description: 'Payroll change history'
            }
          ]
        }
      ];
    }

    if (pathname.startsWith('/accountant/manage/payslips')) {
      return [
        ...defaultSections,
        {
          title: 'Payslip Management',
          items: [
            {
              name: 'Generate Payslips',
              href: '/accountant/manage/payslips/generate',
              icon: FileText,
              description: 'Create employee payslips'
            },
            {
              name: 'Bulk Operations',
              href: '/accountant/manage/payslips/bulk',
              icon: Upload,
              description: 'Mass payslip actions'
            },
            {
              name: 'Templates',
              href: '/accountant/manage/payslips/templates',
              icon: Settings,
              description: 'Payslip design templates'
            },
            {
              name: 'Distribution',
              href: '/accountant/manage/payslips/distribution',
              icon: Download,
              description: 'Send payslips to employees'
            }
          ]
        },
        {
          title: 'Payslip Status',
          items: [
            {
              name: 'Pending',
              href: '/accountant/manage/payslips/pending',
              icon: AlertCircle,
              description: 'Awaiting generation',
              badge: '12'
            },
            {
              name: 'Completed',
              href: '/accountant/manage/payslips/completed',
              icon: CheckCircle,
              description: 'Successfully generated'
            },
            {
              name: 'Failed',
              href: '/accountant/manage/payslips/failed',
              icon: AlertCircle,
              description: 'Generation errors',
              badge: '2'
            }
          ]
        }
      ];
    }

    if (pathname.startsWith('/accountant/manage/reports')) {
      return [
        ...defaultSections,
        {
          title: 'Financial Reports',
          items: [
            {
              name: 'Payroll Summary',
              href: '/accountant/manage/reports/payroll-summary',
              icon: BarChart3,
              description: 'Monthly payroll overview'
            },
            {
              name: 'Tax Reports',
              href: '/accountant/manage/reports/tax',
              icon: Calculator,
              description: 'Tax liability and payments'
            },
            {
              name: 'Cost Analysis',
              href: '/accountant/manage/reports/cost-analysis',
              icon: PieChart,
              description: 'Labor cost breakdown'
            },
            {
              name: 'Compliance Reports',
              href: '/accountant/manage/reports/compliance',
              icon: Shield,
              description: 'Regulatory submissions'
            }
          ]
        },
        {
          title: 'Report Tools',
          items: [
            {
              name: 'Custom Reports',
              href: '/accountant/manage/reports/custom',
              icon: Settings,
              description: 'Build custom reports'
            },
            {
              name: 'Scheduled Reports',
              href: '/accountant/manage/reports/scheduled',
              icon: Calendar,
              description: 'Automated report generation'
            },
            {
              name: 'Export Center',
              href: '/accountant/manage/reports/export',
              icon: Download,
              description: 'Download reports'
            }
          ]
        }
      ];
    }

    if (pathname.startsWith('/accountant/info')) {
      return [
        ...defaultSections,
        {
          title: 'Personal Information',
          items: [
            {
              name: 'Profile',
              href: '/accountant/info/profile',
              icon: User,
              description: 'Personal details'
            },
            {
              name: 'Employment',
              href: '/accountant/info/employment',
              icon: Building,
              description: 'Job information'
            },
            {
              name: 'Settings',
              href: '/accountant/info/settings',
              icon: Settings,
              description: 'Account preferences'
            }
          ]
        }
      ];
    }

    if (pathname.startsWith('/accountant/company')) {
      return [
        ...defaultSections,
        {
          title: 'Company Information',
          items: [
            {
              name: 'Organization',
              href: '/accountant/company/organization',
              icon: Building,
              description: 'Company structure'
            },
            {
              name: 'Financial Settings',
              href: '/accountant/company/financial-settings',
              icon: Settings,
              description: 'Accounting configurations'
            },
            {
              name: 'Bank Accounts',
              href: '/accountant/company/bank-accounts',
              icon: CreditCard,
              description: 'Company banking details'
            }
          ]
        }
      ];
    }

    return defaultSections;
  };

  const isActiveRoute = (href: string) => {
    return pathname === href || pathname.startsWith(href + '/');
  };

  const sidebarSections = getSidebarSections();

  return (
    <aside className="w-80 bg-white shadow-sm border-r border-gray-200 h-[calc(100vh-4rem)] overflow-y-auto">
      <div className="p-6">
        {sidebarSections.map((section, sectionIndex) => (
          <div key={section.title} className={cn(sectionIndex > 0 && 'mt-8')}>
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
              {section.title}
            </h3>

            <nav className="space-y-1">
              {section.items.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                    isActiveRoute(item.href)
                      ? 'bg-orange-100 text-orange-700 border-r-2 border-orange-500'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  )}
                >
                  <item.icon
                    className={cn(
                      'mr-3 h-4 w-4 flex-shrink-0',
                      isActiveRoute(item.href)
                        ? 'text-orange-500'
                        : 'text-gray-400 group-hover:text-gray-500'
                    )}
                  />

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="truncate">{item.name}</span>
                      {item.badge && (
                        <span className={cn(
                          'ml-2 px-2 py-0.5 text-xs rounded-full',
                          item.badge === 'Action'
                            ? 'bg-green-100 text-green-700'
                            : 'bg-red-100 text-red-700'
                        )}>
                          {item.badge}
                        </span>
                      )}
                      {item.isNew && (
                        <span className="ml-2 px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded-full">
                          New
                        </span>
                      )}
                    </div>
                    {item.description && (
                      <p className="text-xs text-gray-500 mt-0.5 truncate">
                        {item.description}
                      </p>
                    )}
                  </div>
                </Link>
              ))}
            </nav>
          </div>
        ))}
      </div>
    </aside>
  );
};

export default AccountantDynamicSidebar;
