# This file is distributed under the same license as the Django package.
#
# Translators:
# lan<PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2018
# <PERSON>long Sun <<EMAIL>>, 2016
# <PERSON><PERSON><PERSON><PERSON>, 2022
# jack yang, 2023
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <<EMAIL>>, 2011,2015
# <PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2020
# Liping <PERSON> <<EMAIL>>, 2016-2017
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2013-2014
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2013
# <PERSON> <<EMAIL>>, 2013
# <AUTHOR> <EMAIL>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018,2020
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2019
# ced773123cfad7b4e8b79ca80f736af9, 2012
# 千百度, 2024
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2012
# <AUTHOR> <EMAIL>, 2020
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2013
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-05-22 11:46-0300\n"
"PO-Revision-Date: 2024-08-07 07:05+0000\n"
"Last-Translator: 千百度, 2024\n"
"Language-Team: Chinese (China) (http://app.transifex.com/django/django/"
"language/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#, python-format
msgid "Delete selected %(verbose_name_plural)s"
msgstr "删除所选的 %(verbose_name_plural)s"

#, python-format
msgid "Successfully deleted %(count)d %(items)s."
msgstr "成功删除了 %(count)d 个 %(items)s"

#, python-format
msgid "Cannot delete %(name)s"
msgstr "无法删除 %(name)s"

msgid "Are you sure?"
msgstr "你确定吗？"

msgid "Administration"
msgstr "管理"

msgid "All"
msgstr "全部"

msgid "Yes"
msgstr "是"

msgid "No"
msgstr "否"

msgid "Unknown"
msgstr "未知"

msgid "Any date"
msgstr "任意日期"

msgid "Today"
msgstr "今天"

msgid "Past 7 days"
msgstr "过去7天"

msgid "This month"
msgstr "本月"

msgid "This year"
msgstr "今年"

msgid "No date"
msgstr "没有日期"

msgid "Has date"
msgstr "具有日期"

msgid "Empty"
msgstr "空"

msgid "Not empty"
msgstr "非空"

#, python-format
msgid ""
"Please enter the correct %(username)s and password for a staff account. Note "
"that both fields may be case-sensitive."
msgstr ""
"请输入一个正确的工作人员账户 %(username)s 和密码. 注意他们都是区分大小写的."

msgid "Action:"
msgstr "动作"

#, python-format
msgid "Add another %(verbose_name)s"
msgstr "添加另一个 %(verbose_name)s"

msgid "Remove"
msgstr "删除"

msgid "Addition"
msgstr "添加"

msgid "Change"
msgstr "修改"

msgid "Deletion"
msgstr "删除"

msgid "action time"
msgstr "操作时间"

msgid "user"
msgstr "用户"

msgid "content type"
msgstr "内容类型"

msgid "object id"
msgstr "对象id"

#. Translators: 'repr' means representation
#. (https://docs.python.org/library/functions.html#repr)
msgid "object repr"
msgstr "对象表示"

msgid "action flag"
msgstr "动作标志"

msgid "change message"
msgstr "修改消息"

msgid "log entry"
msgstr "日志记录"

msgid "log entries"
msgstr "日志记录"

#, python-format
msgid "Added “%(object)s”."
msgstr "添加了“%(object)s”。"

#, python-format
msgid "Changed “%(object)s” — %(changes)s"
msgstr "修改了“%(object)s”—%(changes)s"

#, python-format
msgid "Deleted “%(object)s.”"
msgstr "删除了“%(object)s”。"

msgid "LogEntry Object"
msgstr "LogEntry对象"

#, python-brace-format
msgid "Added {name} “{object}”."
msgstr "添加了 {name}“{object}”。"

msgid "Added."
msgstr "已添加。"

msgid "and"
msgstr "和"

#, python-brace-format
msgid "Changed {fields} for {name} “{object}”."
msgstr "修改了 {name}“{object}”的 {fields}。"

#, python-brace-format
msgid "Changed {fields}."
msgstr "已修改{fields}。"

#, python-brace-format
msgid "Deleted {name} “{object}”."
msgstr "删除了 {name}“{object}”。"

msgid "No fields changed."
msgstr "没有字段被修改。"

msgid "None"
msgstr "无"

msgid "Hold down “Control”, or “Command” on a Mac, to select more than one."
msgstr "按住 Control 键或 Mac 上的 Command 键来选择多项。"

msgid "Select this object for an action - {}"
msgstr "选择此对象执行操作 - {}"

#, python-brace-format
msgid "The {name} “{obj}” was added successfully."
msgstr "成功添加了 {name}“{obj}”。"

msgid "You may edit it again below."
msgstr "您可以在下面再次编辑它."

#, python-brace-format
msgid ""
"The {name} “{obj}” was added successfully. You may add another {name} below."
msgstr "成功添加了 {name}“{obj}”。你可以在下面添加另一个 {name}。"

#, python-brace-format
msgid ""
"The {name} “{obj}” was changed successfully. You may edit it again below."
msgstr "成功修改了 {name}“{obj}”。你可以在下面再次编辑它。"

#, python-brace-format
msgid ""
"The {name} “{obj}” was changed successfully. You may add another {name} "
"below."
msgstr "成功修改了 {name}“{obj}”。你可以在下面添加另一个 {name}。"

#, python-brace-format
msgid "The {name} “{obj}” was changed successfully."
msgstr "成功修改了 {name}“{obj}”。"

msgid ""
"Items must be selected in order to perform actions on them. No items have "
"been changed."
msgstr "条目必须选中以对其进行操作。没有任何条目被更改。"

msgid "No action selected."
msgstr "未选择动作"

#, python-format
msgid "The %(name)s “%(obj)s” was deleted successfully."
msgstr "成功删除了 %(name)s“%(obj)s”。"

#, python-format
msgid "%(name)s with ID “%(key)s” doesn’t exist. Perhaps it was deleted?"
msgstr "ID 为“%(key)s”的 %(name)s 不存在。可能已经被删除了？"

#, python-format
msgid "Add %s"
msgstr "增加 %s"

#, python-format
msgid "Change %s"
msgstr "修改 %s"

#, python-format
msgid "View %s"
msgstr "查看 %s"

msgid "Database error"
msgstr "数据库错误"

#, python-format
msgid "%(count)s %(name)s was changed successfully."
msgid_plural "%(count)s %(name)s were changed successfully."
msgstr[0] "总共 %(count)s 个 %(name)s 变更成功。"

#, python-format
msgid "%(total_count)s selected"
msgid_plural "All %(total_count)s selected"
msgstr[0] "选中了 %(total_count)s 个"

#, python-format
msgid "0 of %(cnt)s selected"
msgstr "%(cnt)s 个中 0 个被选"

#, python-format
msgid "Change history: %s"
msgstr "变更历史： %s"

#. Translators: Model verbose name and instance
#. representation, suitable to be an item in a
#. list.
#, python-format
msgid "%(class_name)s %(instance)s"
msgstr "%(class_name)s %(instance)s"

#, python-format
msgid ""
"Deleting %(class_name)s %(instance)s would require deleting the following "
"protected related objects: %(related_objects)s"
msgstr ""
"删除 %(class_name)s %(instance)s 将需要删除以下受保护的相关对象: "
"%(related_objects)s"

msgid "Django site admin"
msgstr "Django 站点管理员"

msgid "Django administration"
msgstr "Django 管理"

msgid "Site administration"
msgstr "站点管理"

msgid "Log in"
msgstr "登录"

#, python-format
msgid "%(app)s administration"
msgstr "%(app)s 管理"

msgid "Page not found"
msgstr "页面没有找到"

msgid "We’re sorry, but the requested page could not be found."
msgstr "非常抱歉，请求的页面不存在。"

msgid "Home"
msgstr "首页"

msgid "Server error"
msgstr "服务器错误"

msgid "Server error (500)"
msgstr "服务器错误(500)"

msgid "Server Error <em>(500)</em>"
msgstr "服务器错误 <em>(500)</em>"

msgid ""
"There’s been an error. It’s been reported to the site administrators via "
"email and should be fixed shortly. Thanks for your patience."
msgstr ""
"发生了错误，已经通过电子邮件报告给了网站管理员。我们会尽快修复，感谢您的耐心"
"等待。"

msgid "Run the selected action"
msgstr "运行选中的动作"

msgid "Go"
msgstr "执行"

msgid "Click here to select the objects across all pages"
msgstr "点击此处选择所有页面中包含的对象。"

#, python-format
msgid "Select all %(total_count)s %(module_name)s"
msgstr "选中所有的 %(total_count)s 个 %(module_name)s"

msgid "Clear selection"
msgstr "清除选中"

msgid "Breadcrumbs"
msgstr "条形导航"

#, python-format
msgid "Models in the %(name)s application"
msgstr "在应用程序 %(name)s 中的模型"

msgid "Add"
msgstr "增加"

msgid "View"
msgstr "查看"

msgid "You don’t have permission to view or edit anything."
msgstr "你没有查看或编辑的权限。"

msgid ""
"First, enter a username and password. Then, you’ll be able to edit more user "
"options."
msgstr "输入用户名和密码后，你将能够编辑更多的用户选项。"

msgid "Enter a username and password."
msgstr "输入用户名和密码"

msgid "Change password"
msgstr "修改密码"

msgid "Set password"
msgstr "设置密码"

msgid "Please correct the error below."
msgid_plural "Please correct the errors below."
msgstr[0] "请更正以下错误。"

#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr "为用户 <strong>%(username)s</strong> 输入一个新的密码。"

msgid ""
"This action will <strong>enable</strong> password-based authentication for "
"this user."
msgstr "这将<strong>启用</strong>本用户基于密码的验证"

msgid "Disable password-based authentication"
msgstr "禁用基于密码的验证"

msgid "Enable password-based authentication"
msgstr "启用基于密码的验证"

msgid "Skip to main content"
msgstr "跳到主要内容"

msgid "Welcome,"
msgstr "欢迎，"

msgid "View site"
msgstr "查看站点"

msgid "Documentation"
msgstr "文档"

msgid "Log out"
msgstr "注销"

#, python-format
msgid "Add %(name)s"
msgstr "增加 %(name)s"

msgid "History"
msgstr "历史"

msgid "View on site"
msgstr "在站点上查看"

msgid "Filter"
msgstr "过滤器"

msgid "Hide counts"
msgstr "隐藏计数"

msgid "Show counts"
msgstr "显示计数"

msgid "Clear all filters"
msgstr "清除所有筛选"

msgid "Remove from sorting"
msgstr "删除排序"

#, python-format
msgid "Sorting priority: %(priority_number)s"
msgstr "排序优先级: %(priority_number)s"

msgid "Toggle sorting"
msgstr "正逆序切换"

msgid "Toggle theme (current theme: auto)"
msgstr "切换主题（当前主题：自动）"

msgid "Toggle theme (current theme: light)"
msgstr "切换主题（当前主题：浅色）"

msgid "Toggle theme (current theme: dark)"
msgstr "切换主题（当前主题：深色）"

msgid "Delete"
msgstr "删除"

#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would result in deleting "
"related objects, but your account doesn't have permission to delete the "
"following types of objects:"
msgstr ""
"删除 %(object_name)s  '%(escaped_object)s' 会导致删除相关的对象，但你的帐号无"
"权删除下列类型的对象："

#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would require deleting the "
"following protected related objects:"
msgstr ""
"要删除 %(object_name)s '%(escaped_object)s', 将要求删除以下受保护的相关对象:"

#, python-format
msgid ""
"Are you sure you want to delete the %(object_name)s \"%(escaped_object)s\"? "
"All of the following related items will be deleted:"
msgstr ""
"你确认想要删除 %(object_name)s \"%(escaped_object)s\"？ 下列所有相关的项目都"
"将被删除："

msgid "Objects"
msgstr "对象"

msgid "Yes, I’m sure"
msgstr "是的，我确定"

msgid "No, take me back"
msgstr "不，返回"

msgid "Delete multiple objects"
msgstr "删除多个对象"

#, python-format
msgid ""
"Deleting the selected %(objects_name)s would result in deleting related "
"objects, but your account doesn't have permission to delete the following "
"types of objects:"
msgstr ""
"要删除所选的 %(objects_name)s 结果会删除相关对象, 但你的账户没有权限删除这类"
"对象:"

#, python-format
msgid ""
"Deleting the selected %(objects_name)s would require deleting the following "
"protected related objects:"
msgstr "要删除所选的 %(objects_name)s, 将要求删除以下受保护的相关对象:"

#, python-format
msgid ""
"Are you sure you want to delete the selected %(objects_name)s? All of the "
"following objects and their related items will be deleted:"
msgstr ""
"请确认要删除选中的 %(objects_name)s 吗？以下所有对象和与它们相关的条目将都会"
"被删除："

msgid "Delete?"
msgstr "删除？"

#, python-format
msgid " By %(filter_title)s "
msgstr " 以 %(filter_title)s"

msgid "Summary"
msgstr "概览"

msgid "Recent actions"
msgstr "最近动作"

msgid "My actions"
msgstr "我的动作"

msgid "None available"
msgstr "无可选的"

msgid "Added:"
msgstr "已添加："

msgid "Changed:"
msgstr "已修改："

msgid "Deleted:"
msgstr "已删除："

msgid "Unknown content"
msgstr "未知内容"

msgid ""
"Something’s wrong with your database installation. Make sure the appropriate "
"database tables have been created, and make sure the database is readable by "
"the appropriate user."
msgstr ""
"数据库设置有误。请检查所需的数据库表格是否已经创建，以及数据库用户是否具有正"
"确的权限。"

#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr ""
"您当前以%(username)s登录，但是没有这个页面的访问权限。您想使用另外一个账号登"
"录吗？"

msgid "Forgotten your password or username?"
msgstr "忘记了您的密码或用户名？"

msgid "Toggle navigation"
msgstr "切换导航"

msgid "Sidebar"
msgstr "侧边栏"

msgid "Start typing to filter…"
msgstr "开始输入以筛选..."

msgid "Filter navigation items"
msgstr "筛选导航项目"

msgid "Date/time"
msgstr "日期/时间"

msgid "User"
msgstr "用户"

msgid "Action"
msgstr "动作"

msgid "entry"
msgid_plural "entries"
msgstr[0] "条目"

msgid ""
"This object doesn’t have a change history. It probably wasn’t added via this "
"admin site."
msgstr "此对象没有修改历史。它可能不是通过管理站点添加的。"

msgid "Show all"
msgstr "显示全部"

msgid "Save"
msgstr "保存"

msgid "Popup closing…"
msgstr "弹窗关闭中..."

msgid "Search"
msgstr "搜索"

#, python-format
msgid "%(counter)s result"
msgid_plural "%(counter)s results"
msgstr[0] "%(counter)s 条结果。"

#, python-format
msgid "%(full_result_count)s total"
msgstr "总共 %(full_result_count)s"

msgid "Save as new"
msgstr "保存为新的"

msgid "Save and add another"
msgstr "保存并增加另一个"

msgid "Save and continue editing"
msgstr "保存并继续编辑"

msgid "Save and view"
msgstr "保存并查看"

msgid "Close"
msgstr "关闭"

#, python-format
msgid "Change selected %(model)s"
msgstr "更改选中的%(model)s"

#, python-format
msgid "Add another %(model)s"
msgstr "增加另一个 %(model)s"

#, python-format
msgid "Delete selected %(model)s"
msgstr "取消选中 %(model)s"

#, python-format
msgid "View selected %(model)s"
msgstr "查看已选择的%(model)s"

msgid "Thanks for spending some quality time with the web site today."
msgstr "感谢您今天与本网站共享一段美好时光。"

msgid "Log in again"
msgstr "重新登录"

msgid "Password change"
msgstr "密码修改"

msgid "Your password was changed."
msgstr "你的密码已修改。"

msgid ""
"Please enter your old password, for security’s sake, and then enter your new "
"password twice so we can verify you typed it in correctly."
msgstr "安全起见请输入你的旧密码。然后输入两次你的新密码以确保输入正确。"

msgid "Change my password"
msgstr "修改我的密码"

msgid "Password reset"
msgstr "密码重设"

msgid "Your password has been set.  You may go ahead and log in now."
msgstr "你的密码己经设置完成，现在你可以继续进行登录。"

msgid "Password reset confirmation"
msgstr "密码重设确认"

msgid ""
"Please enter your new password twice so we can verify you typed it in "
"correctly."
msgstr "请输入两遍新密码，以便我们校验你输入的是否正确。"

msgid "New password:"
msgstr "新密码："

msgid "Confirm password:"
msgstr "确认密码："

msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a new password reset."
msgstr "密码重置链接无效，可能是因为它已使用。可以请求一次新的密码重置。"

msgid ""
"We’ve emailed you instructions for setting your password, if an account "
"exists with the email you entered. You should receive them shortly."
msgstr ""
"如果你所输入的电子邮箱存在对应的用户，我们将通过电子邮件向你发送设置密码的操"
"作步骤说明。你应该很快就会收到。"

msgid ""
"If you don’t receive an email, please make sure you’ve entered the address "
"you registered with, and check your spam folder."
msgstr ""
"如果你没有收到电子邮件，请检查输入的是你注册的电子邮箱地址。另外，也请检查你"
"的垃圾邮件文件夹。"

#, python-format
msgid ""
"You're receiving this email because you requested a password reset for your "
"user account at %(site_name)s."
msgstr "你收到这封邮件是因为你请求重置你在网站 %(site_name)s上的用户账户密码。"

msgid "Please go to the following page and choose a new password:"
msgstr "请访问该页面并选择一个新密码："

msgid "Your username, in case you’ve forgotten:"
msgstr "提醒一下，你的用户名是："

msgid "Thanks for using our site!"
msgstr "感谢使用我们的站点！"

#, python-format
msgid "The %(site_name)s team"
msgstr "%(site_name)s 团队"

msgid ""
"Forgotten your password? Enter your email address below, and we’ll email "
"instructions for setting a new one."
msgstr ""
"忘记密码？在下面输入你的电子邮箱地址，我们将会把设置新密码的操作步骤说明通过"
"电子邮件发送给你。"

msgid "Email address:"
msgstr "电子邮件地址："

msgid "Reset my password"
msgstr "重设我的密码"

msgid "Select all objects on this page for an action"
msgstr "选择此页面上的所有对象执行操作"

msgid "All dates"
msgstr "所有日期"

#, python-format
msgid "Select %s"
msgstr "选择 %s"

#, python-format
msgid "Select %s to change"
msgstr "选择 %s 来修改"

#, python-format
msgid "Select %s to view"
msgstr "选择%s查看"

msgid "Date:"
msgstr "日期："

msgid "Time:"
msgstr "时间："

msgid "Lookup"
msgstr "查询"

msgid "Currently:"
msgstr "当前："

msgid "Change:"
msgstr "更改："
