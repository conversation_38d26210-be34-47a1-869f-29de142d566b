'use client';

import React, { useMemo, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Home,
  Users,
  Building,
  Calendar,
  FileText,
  Star,
  BarChart3,
  Settings,
  X,
  User,
  LogOut,
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Edit,
  Trash2,
  Download,
  Eye,
  Shield,
  Database,
  Globe,
  Bell,
  MapPin,
  Phone,
  Mail,
  TrendingUp,
  Briefcase,
  GraduationCap,
  Heart,
  Award,
  MessageSquare,
  Target,
  BookOpen,
  Clipboard,
  UserCog,
  GitBranch,
  Zap,
  Coffee,
  Smile,
  UserPlus,
  UserCheck,
  UserX,
  Layers,
  Crown,
  Megaphone,
  Calendar as CalendarIcon,
  FileCheck,
  Activity,
  Headphones,
  PartyPopper,
  ChevronRight,
  Calculator
} from 'lucide-react';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials, formatDate } from '@/lib/utils';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

// Define page-specific features and actions
const getPageFeatures = (pathname: string) => {
  switch (pathname) {
    case '/dashboard':
      return {
        title: 'Dashboard',
        breadcrumb: 'Admin Dashboard',
        quickActions: [
          { name: 'Add Employee', icon: Users, href: '/employees?action=add' },
          { name: 'Apply Leave', icon: FileText, href: '/leave?action=apply' },
          { name: 'Schedule Meeting', icon: Calendar, href: '/calendar?action=add' },
          { name: 'View Reports', icon: BarChart3, href: '/reports' },
        ],
        features: [
          { name: 'Statistics Overview', icon: TrendingUp },
          { name: 'Recent Activities', icon: Clock },
          { name: 'Quick Actions', icon: Plus },
          { name: 'System Status', icon: CheckCircle },
        ]
      };

    case '/employees':
      return {
        title: 'Employees',
        breadcrumb: 'Employee Management Hub',
        quickActions: [
          { name: 'Add Employee', icon: UserPlus, href: '/employees?action=add' },
          { name: 'Import Employees', icon: Download, href: '/employees?action=import' },
          { name: 'Export Data', icon: Download, href: '/employees?action=export' },
          { name: 'Bulk Actions', icon: Edit, href: '/employees?action=bulk' },
        ],
        features: [
          { name: 'Employee Management', icon: Users },
          { name: 'Department Management', icon: Building },
          { name: 'Team Leads', icon: Crown },
          { name: 'Recruitment Management', icon: Briefcase },
          { name: 'User Management', icon: UserCog },
          { name: 'Training Management', icon: GraduationCap },
          { name: 'Employee Engagement & Wellness', icon: Heart },
        ]
      };

    case '/company':
      return {
        title: 'Company',
        breadcrumb: 'Company Information',
        quickActions: [
          { name: 'Edit Company Info', icon: Edit, href: '/company?action=edit' },
          { name: 'Add Department', icon: Plus, href: '/company?action=add-dept' },
          { name: 'Manage Locations', icon: MapPin, href: '/company?action=locations' },
          { name: 'Contact Settings', icon: Phone, href: '/company?action=contact' },
        ],
        features: [
          { name: 'Company Overview', icon: Building },
          { name: 'Departments', icon: Users },
          { name: 'Locations', icon: MapPin },
          { name: 'Contact Info', icon: Mail },
        ]
      };

    case '/payroll':
      return {
        title: 'Payroll System',
        breadcrumb: 'Payroll Management',
        quickActions: [
          { name: 'Create Pay Cycle', icon: Plus, href: '/payroll/pay-cycles?action=create' },
          { name: 'Generate Payslips', icon: FileText, href: '/payroll/payslips?action=generate' },
          { name: 'Bulk Payroll', icon: Users, href: '/payroll/bulk?action=create' },
          { name: 'Tax Settings', icon: Calculator, href: '/payroll/settings?tab=tax' },
        ],
        features: [
          { name: 'Pay Cycles', icon: Calendar },
          { name: 'Salary Calculation', icon: Calculator },
          { name: 'Tax Management', icon: FileText },
          { name: 'Banking Integration', icon: Building },
          { name: 'Payslip Generation', icon: Download },
          { name: 'Statutory Deductions', icon: Shield },
          { name: 'Bulk Processing', icon: Users },
          { name: 'Reports & Analytics', icon: TrendingUp },
        ]
      };

    case '/calendar':
      return {
        title: 'Calendar',
        breadcrumb: 'Calendar & Events',
        quickActions: [
          { name: 'Add Event', icon: Plus, href: '/calendar?action=add' },
          { name: 'Schedule Meeting', icon: Users, href: '/calendar?action=meeting' },
          { name: 'View Today', icon: Clock, href: '/calendar?view=today' },
          { name: 'Monthly View', icon: Calendar, href: '/calendar?view=month' },
        ],
        features: [
          { name: 'Monthly Calendar', icon: Calendar },
          { name: 'Event Management', icon: Plus },
          { name: 'Meeting Scheduler', icon: Users },
          { name: 'Event Details', icon: Eye },
        ]
      };

    case '/leave':
      return {
        title: 'Leave Management',
        breadcrumb: 'Leave Requests',
        quickActions: [
          { name: 'Apply Leave', icon: Plus, href: '/leave?action=apply' },
          { name: 'Approve Requests', icon: CheckCircle, href: '/leave?action=approve' },
          { name: 'View Calendar', icon: Calendar, href: '/leave?view=calendar' },
          { name: 'Leave Reports', icon: BarChart3, href: '/leave?view=reports' },
        ],
        features: [
          { name: 'Leave Applications', icon: FileText },
          { name: 'Approval Workflow', icon: CheckCircle },
          { name: 'Leave Balance', icon: Clock },
          { name: 'Leave Types', icon: Filter },
        ]
      };

    case '/reviews':
      return {
        title: 'Performance Reviews',
        breadcrumb: 'Performance Management',
        quickActions: [
          { name: 'Create Review', icon: Plus, href: '/reviews?action=create' },
          { name: 'Review Templates', icon: FileText, href: '/reviews?view=templates' },
          { name: 'Performance Reports', icon: BarChart3, href: '/reviews?view=reports' },
          { name: 'Goal Setting', icon: Star, href: '/reviews?action=goals' },
        ],
        features: [
          { name: 'Review Cycles', icon: Star },
          { name: 'Performance Metrics', icon: BarChart3 },
          { name: 'Goal Tracking', icon: CheckCircle },
          { name: 'Feedback System', icon: Edit },
        ]
      };

    case '/profile':
      return {
        title: 'Profile',
        breadcrumb: 'User Profile',
        quickActions: [
          { name: 'Edit Profile', icon: Edit, href: '/profile?action=edit' },
          { name: 'Change Password', icon: Shield, href: '/profile?action=password' },
          { name: 'Privacy Settings', icon: Settings, href: '/profile?action=privacy' },
          { name: 'Notification Preferences', icon: Bell, href: '/profile?action=notifications' },
        ],
        features: [
          { name: 'Personal Information', icon: User },
          { name: 'Security Settings', icon: Shield },
          { name: 'Preferences', icon: Settings },
          { name: 'Activity Log', icon: Clock },
        ]
      };

    case '/reports':
      return {
        title: 'Reports & Analytics',
        breadcrumb: 'Business Intelligence',
        quickActions: [
          { name: 'Generate Report', icon: Plus, href: '/reports?action=generate' },
          { name: 'Export Data', icon: Download, href: '/reports?action=export' },
          { name: 'Schedule Reports', icon: Clock, href: '/reports?action=schedule' },
          { name: 'Custom Reports', icon: Edit, href: '/reports?action=custom' },
        ],
        features: [
          { name: 'Employee Reports', icon: Users },
          { name: 'Leave Reports', icon: Calendar },
          { name: 'Performance Reports', icon: TrendingUp },
          { name: 'Financial Reports', icon: BarChart3 },
        ]
      };

    case '/manage':
      return {
        title: 'Management Center',
        breadcrumb: 'Administration',
        quickActions: [
          { name: 'Payroll Management', icon: DollarSign, href: '/payroll' },
          { name: 'Calendar Management', icon: Calendar, href: '/calendar' },
          { name: 'Leave Management', icon: FileText, href: '/leave' },
          { name: 'Performance Reviews', icon: Star, href: '/reviews' },
        ],
        features: [
          { name: 'Payroll Management', icon: DollarSign },
          { name: 'Calendar Management', icon: Calendar },
          { name: 'Leave Management', icon: FileText },
          { name: 'Performance Reviews', icon: Star },
          { name: 'Reports & Analytics', icon: BarChart3 },
        ]
      };

    case '/settings':
      return {
        title: 'Settings',
        breadcrumb: 'System Settings',
        quickActions: [
          { name: 'General Settings', icon: Settings, href: '/settings?tab=general' },
          { name: 'User Preferences', icon: User, href: '/settings?tab=preferences' },
          { name: 'Security Settings', icon: Shield, href: '/settings?tab=security' },
          { name: 'Notifications', icon: Bell, href: '/settings?tab=notifications' },
        ],
        features: [
          { name: 'General Config', icon: Settings },
          { name: 'User Management', icon: User },
          { name: 'Security Policies', icon: Shield },
          { name: 'System Preferences', icon: Globe },
        ]
      };

    default:
      return {
        title: 'Dashboard',
        breadcrumb: 'Admin Dashboard',
        quickActions: [],
        features: []
      };
  }
};

const DynamicSidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const pathname = usePathname();
  const { user, logout } = useAuth();

  // Navigation removed as requested - using only page-specific features

  // Memoize page features to prevent recalculation
  const pageFeatures = useMemo(() => getPageFeatures(pathname), [pathname]);

  // Memoize user-related data
  const userInitials = useMemo(() =>
    user ? getInitials(user.first_name, user.last_name) : 'U',
    [user?.first_name, user?.last_name]
  );

  const currentDate = useMemo(() =>
    formatDate(new Date().toISOString(), 'EEE, MMM dd, yyyy'),
    []
  );

  // Memoize close handler
  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  // Memoize logout handler
  const handleLogout = useCallback(() => {
    logout();
    onClose();
  }, [logout, onClose]);

  // Employee Management Areas - Always available on employee pages
  const employeeManagementAreas = useMemo(() => [
    {
      name: 'Employee Management',
      href: '/employees',
      icon: Users
    },
    {
      name: 'Department Management',
      href: '/employees/departments',
      icon: Building
    },
    {
      name: 'Team Leads',
      href: '/employees/team-leads',
      icon: Crown
    },
    {
      name: 'Recruitment Management',
      href: '/employees/recruitment',
      icon: Briefcase
    },
    {
      name: 'User Management',
      href: '/employees/user-management',
      icon: UserCog
    },
    {
      name: 'Training Management',
      href: '/employees/training',
      icon: GraduationCap
    },
    {
      name: 'Employee Engagement & Wellness',
      href: '/employees/engagement',
      icon: Heart
    }
  ], []);

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={handleClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out md:relative md:translate-x-0',
          isOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        {/* Mobile close button */}
        <div className="md:hidden absolute top-4 right-4">
          <button
            onClick={handleClose}
            className="p-2 rounded-md text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Sidebar content */}
        <div className="flex flex-col h-full">
          {/* Dynamic Breadcrumb and Title */}
          <div className="p-6 border-b border-gray-200">
            <nav className="text-sm text-gray-500 mb-2">
              <Link href="/dashboard" className="hover:text-gray-700">
                Home
              </Link>
              <span className="mx-2">/</span>
              <span className="text-gray-900">{pageFeatures.title}</span>
            </nav>
            <h1 className="text-xl font-semibold text-gray-900">
              {pageFeatures.breadcrumb}
            </h1>
          </div>

          {/* User Card */}
          <div className="p-6 border-b border-gray-200">
            <div className="bg-white rounded-lg border border-gray-200 p-4 text-center">
              <div className="mb-4">
                {user?.profile_picture ? (
                  <img
                    src={user.profile_picture}
                    alt="Profile"
                    className="w-16 h-16 rounded-full mx-auto object-cover"
                  />
                ) : (
                  <div className="w-16 h-16 bg-orange-500 rounded-full mx-auto flex items-center justify-center">
                    <span className="text-white text-xl font-medium">
                      {userInitials}
                    </span>
                  </div>
                )}
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">
                  Welcome {user?.first_name || 'Admin'}
                </h3>
                <p className="text-sm text-gray-500 mt-1">{currentDate}</p>
              </div>
            </div>
          </div>

          {/* Page-Specific Quick Actions */}
          {pageFeatures.quickActions.length > 0 && (
            <div className="p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
              <div className="bg-white rounded-lg border border-gray-200">
                <div className="p-4">
                  <h3 className="font-medium text-gray-900 mb-3">Quick Actions</h3>
                  <div className="space-y-1">
                    {pageFeatures.quickActions.map((action, index) => {
                      const Icon = action.icon;
                      return (
                        <Link
                          key={`${pathname}-${index}`}
                          href={action.href}
                          className="flex items-center w-full text-left py-2 px-3 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 transition-all duration-150 ease-in-out"
                        >
                          <Icon className="h-4 w-4 mr-2" />
                          {action.name}
                        </Link>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Management Areas - Show on manage pages */}
          {pathname.startsWith('/manage') && (
            <div className="p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
              <div className="bg-white rounded-lg border border-gray-200">
                <div className="p-4">
                  <h3 className="font-medium text-gray-900 mb-3">Management Areas</h3>
                  <div className="space-y-2">
                    {[
                      { name: 'Payroll Management', href: '/payroll', icon: DollarSign },
                      { name: 'Calendar Management', href: '/calendar', icon: Calendar },
                      { name: 'Leave Management', href: '/leave', icon: FileText },
                      { name: 'Performance Reviews', href: '/reviews', icon: Star },
                      { name: 'Reports & Analytics', href: '/reports', icon: BarChart3 },
                    ].map((area, index) => {
                      const Icon = area.icon;
                      const isCurrentPage = pathname === area.href;

                      return (
                        <Link
                          key={`manage-area-${index}`}
                          href={area.href}
                          onClick={handleClose}
                          className={cn(
                            "flex items-center py-2 px-3 rounded-md text-sm transition-all duration-150 ease-in-out",
                            isCurrentPage
                              ? "bg-orange-50 text-orange-700 border-l-2 border-orange-500"
                              : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                          )}
                        >
                          <Icon className={cn(
                            "h-4 w-4 mr-2",
                            isCurrentPage ? "text-orange-500" : "text-gray-400"
                          )} />
                          {area.name}
                        </Link>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Employee Management Areas - Show on all employee pages */}
          {pathname.startsWith('/employees') && (
            <div className="p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
              <div className="bg-white rounded-lg border border-gray-200">
                <div className="p-4">
                  <h3 className="font-medium text-gray-900 mb-3">Management Areas</h3>
                  <div className="space-y-2">
                    {employeeManagementAreas.map((area, index) => {
                      const Icon = area.icon;
                      const isCurrentPage = pathname === area.href;

                      return (
                        <Link
                          key={`employee-area-${index}`}
                          href={area.href}
                          onClick={handleClose}
                          className={cn(
                            "flex items-center py-2 px-3 rounded-md text-sm transition-all duration-150 ease-in-out",
                            isCurrentPage
                              ? "bg-orange-50 text-orange-700 border-l-2 border-orange-500"
                              : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                          )}
                        >
                          <Icon className={cn(
                            "h-4 w-4 mr-2",
                            isCurrentPage ? "text-orange-500" : "text-gray-400"
                          )} />
                          {area.name}
                        </Link>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Page Features - Show for non-employee pages */}
          {!pathname.startsWith('/employees') && pageFeatures.features.length > 0 && (
            <div className="p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
              <div className="bg-white rounded-lg border border-gray-200">
                <div className="p-4">
                  <h3 className="font-medium text-gray-900 mb-3">Page Features</h3>
                  <div className="space-y-2">
                    {pageFeatures.features.map((feature, index) => {
                      const Icon = feature.icon;
                      return (
                        <div
                          key={`${pathname}-feature-${index}`}
                          className="flex items-center py-2 px-3 rounded-md text-sm text-gray-600 transition-all duration-150 ease-in-out hover:bg-gray-50"
                        >
                          <Icon className="h-4 w-4 mr-2 text-orange-500" />
                          {feature.name}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Detailed Employee Management Features */}
          {pathname === '/employees' && (
            <div className="p-6 border-b border-gray-200">
              <div className="space-y-4">
                {/* Recruitment Management Details */}
                <div className="bg-white rounded-lg border border-gray-200">
                  <div className="p-4">
                    <Link
                      href="/employees/recruitment"
                      onClick={handleClose}
                      className="flex items-center mb-3 hover:text-orange-600 transition-colors"
                    >
                      <Briefcase className="h-4 w-4 mr-2 text-blue-500" />
                      <h4 className="font-medium text-gray-900">Recruitment Management</h4>
                      <ChevronRight className="h-3 w-3 ml-auto text-gray-400" />
                    </Link>
                    <div className="space-y-1 text-sm text-gray-600">
                      <div className="flex items-center py-1">
                        <FileText className="h-3 w-3 mr-2" />
                        Job Postings & Requirements
                      </div>
                      <div className="flex items-center py-1">
                        <Clipboard className="h-3 w-3 mr-2" />
                        Application Management
                      </div>
                      <div className="flex items-center py-1">
                        <GitBranch className="h-3 w-3 mr-2" />
                        Candidate Status Workflow
                      </div>
                      <div className="flex items-center py-1">
                        <CalendarIcon className="h-3 w-3 mr-2" />
                        Interview Scheduling
                      </div>
                    </div>
                  </div>
                </div>

                {/* Training Management Details */}
                <div className="bg-white rounded-lg border border-gray-200">
                  <div className="p-4">
                    <Link
                      href="/employees/training"
                      onClick={handleClose}
                      className="flex items-center mb-3 hover:text-orange-600 transition-colors"
                    >
                      <GraduationCap className="h-4 w-4 mr-2 text-green-500" />
                      <h4 className="font-medium text-gray-900">Training Management</h4>
                      <ChevronRight className="h-3 w-3 ml-auto text-gray-400" />
                    </Link>
                    <div className="space-y-1 text-sm text-gray-600">
                      <div className="flex items-center py-1">
                        <BookOpen className="h-3 w-3 mr-2" />
                        Training Modules & Courses
                      </div>
                      <div className="flex items-center py-1">
                        <Target className="h-3 w-3 mr-2" />
                        Employee Training Assignment
                      </div>
                      <div className="flex items-center py-1">
                        <Activity className="h-3 w-3 mr-2" />
                        Progress Tracking
                      </div>
                      <div className="flex items-center py-1">
                        <MapPin className="h-3 w-3 mr-2" />
                        Training Venues
                      </div>
                    </div>
                  </div>
                </div>

                {/* Employee Engagement & Wellness Details */}
                <div className="bg-white rounded-lg border border-gray-200">
                  <div className="p-4">
                    <Link
                      href="/employees/engagement"
                      onClick={handleClose}
                      className="flex items-center mb-3 hover:text-orange-600 transition-colors"
                    >
                      <Heart className="h-4 w-4 mr-2 text-red-500" />
                      <h4 className="font-medium text-gray-900">Engagement & Wellness</h4>
                      <ChevronRight className="h-3 w-3 ml-auto text-gray-400" />
                    </Link>
                    <div className="space-y-1 text-sm text-gray-600">
                      <div className="flex items-center py-1">
                        <MessageSquare className="h-3 w-3 mr-2" />
                        Feedback & Surveys
                      </div>
                      <div className="flex items-center py-1">
                        <Award className="h-3 w-3 mr-2" />
                        Recognition & Rewards
                      </div>
                      <div className="flex items-center py-1">
                        <Headphones className="h-3 w-3 mr-2" />
                        Employee Assistance Programs
                      </div>
                      <div className="flex items-center py-1">
                        <PartyPopper className="h-3 w-3 mr-2" />
                        Culture & Events
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Spacer for better layout */}
          <div className="flex-1"></div>

          {/* User Actions */}
          <div className="p-6 border-t border-gray-200 space-y-1">
            <Link
              href="/profile"
              onClick={handleClose}
              className="flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <User className="h-5 w-5 mr-3" />
              Profile
            </Link>
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <LogOut className="h-5 w-5 mr-3" />
              Logout
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default DynamicSidebar;
