import React from 'react';
import { screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, createMockFile } from '@/__tests__/utils/test-utils';
import FileUpload from '@/components/ui/FileUpload';
import fileUploadService from '@/lib/fileUpload';

// Mock the file upload service
jest.mock('@/lib/fileUpload', () => ({
  __esModule: true,
  default: {
    uploadFile: jest.fn(),
    formatFileSize: jest.fn((size: number) => `${size} bytes`),
    isImage: jest.fn((file: File) => file.type.startsWith('image/')),
    isPDF: jest.fn((file: File) => file.type === 'application/pdf'),
    createPreviewUrl: jest.fn(() => 'mock-preview-url'),
    revokePreviewUrl: jest.fn(),
  },
}));

const mockFileUploadService = fileUploadService as jest.Mocked<typeof fileUploadService>;

describe('FileUpload', () => {
  const defaultProps = {
    endpoint: '/api/upload',
    onUpload: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockFileUploadService.uploadFile.mockResolvedValue({
      success: true,
      url: 'https://example.com/file.pdf',
      filename: 'file.pdf',
      size: 1024,
    });
  });

  describe('rendering', () => {
    it('renders upload area with default content', () => {
      render(<FileUpload {...defaultProps} />);

      expect(screen.getByText('Click to upload or drag and drop')).toBeInTheDocument();
      expect(screen.getByText('All file types accepted')).toBeInTheDocument();
    });

    it('renders custom children when provided', () => {
      render(
        <FileUpload {...defaultProps}>
          <div>Custom Upload Area</div>
        </FileUpload>
      );

      expect(screen.getByText('Custom Upload Area')).toBeInTheDocument();
      expect(screen.queryByText('Click to upload or drag and drop')).not.toBeInTheDocument();
    });

    it('shows accepted formats when accept prop is provided', () => {
      render(<FileUpload {...defaultProps} accept=".pdf,.doc" />);

      expect(screen.getByText('Accepted formats: .pdf,.doc')).toBeInTheDocument();
    });

    it('shows max size when maxSize prop is provided', () => {
      render(<FileUpload {...defaultProps} maxSize={5 * 1024 * 1024} />);

      expect(screen.getByText('Max size: 5242880 bytes')).toBeInTheDocument();
    });
  });

  describe('file selection', () => {
    it('handles file selection via input', async () => {
      const user = userEvent.setup();
      const file = createMockFile('test.pdf');

      render(<FileUpload {...defaultProps} />);

      const input = screen.getByRole('button', { name: /click to upload/i });
      await user.click(input);

      // Simulate file selection
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false,
      });
      fireEvent.change(fileInput);

      await waitFor(() => {
        expect(screen.getByText('Selected Files (1)')).toBeInTheDocument();
        expect(screen.getByText('test.pdf')).toBeInTheDocument();
      });
    });

    it('handles multiple file selection when multiple is true', async () => {
      const user = userEvent.setup();
      const files = [createMockFile('test1.pdf'), createMockFile('test2.pdf')];

      render(<FileUpload {...defaultProps} multiple />);

      const input = screen.getByRole('button', { name: /click to upload/i });
      await user.click(input);

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: files,
        writable: false,
      });
      fireEvent.change(fileInput);

      await waitFor(() => {
        expect(screen.getByText('Selected Files (2)')).toBeInTheDocument();
        expect(screen.getByText('test1.pdf')).toBeInTheDocument();
        expect(screen.getByText('test2.pdf')).toBeInTheDocument();
      });
    });

    it('limits files to maxFiles when specified', async () => {
      const user = userEvent.setup();
      const files = [
        createMockFile('test1.pdf'),
        createMockFile('test2.pdf'),
        createMockFile('test3.pdf'),
      ];

      render(<FileUpload {...defaultProps} multiple maxFiles={2} />);

      const input = screen.getByRole('button', { name: /click to upload/i });
      await user.click(input);

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: files,
        writable: false,
      });
      fireEvent.change(fileInput);

      await waitFor(() => {
        expect(screen.getByText('Selected Files (2)')).toBeInTheDocument();
        expect(screen.getByText('test1.pdf')).toBeInTheDocument();
        expect(screen.getByText('test2.pdf')).toBeInTheDocument();
        expect(screen.queryByText('test3.pdf')).not.toBeInTheDocument();
      });
    });
  });

  describe('drag and drop', () => {
    it('handles drag over events', () => {
      render(<FileUpload {...defaultProps} />);

      const dropZone = screen.getByRole('button', { name: /click to upload/i });
      
      fireEvent.dragOver(dropZone);
      
      // Should add drag over styling (tested via class changes)
      expect(dropZone).toHaveClass('border-orange-500', 'bg-orange-50');
    });

    it('handles drag leave events', () => {
      render(<FileUpload {...defaultProps} />);

      const dropZone = screen.getByRole('button', { name: /click to upload/i });
      
      fireEvent.dragOver(dropZone);
      fireEvent.dragLeave(dropZone);
      
      // Should remove drag over styling
      expect(dropZone).not.toHaveClass('border-orange-500', 'bg-orange-50');
    });

    it('handles file drop', async () => {
      const file = createMockFile('dropped.pdf');
      
      render(<FileUpload {...defaultProps} />);

      const dropZone = screen.getByRole('button', { name: /click to upload/i });
      
      fireEvent.drop(dropZone, {
        dataTransfer: {
          files: [file],
        },
      });

      await waitFor(() => {
        expect(screen.getByText('Selected Files (1)')).toBeInTheDocument();
        expect(screen.getByText('dropped.pdf')).toBeInTheDocument();
      });
    });
  });

  describe('file removal', () => {
    it('allows removing selected files', async () => {
      const user = userEvent.setup();
      const file = createMockFile('test.pdf');

      render(<FileUpload {...defaultProps} />);

      // Add file
      const input = screen.getByRole('button', { name: /click to upload/i });
      await user.click(input);

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false,
      });
      fireEvent.change(fileInput);

      await waitFor(() => {
        expect(screen.getByText('test.pdf')).toBeInTheDocument();
      });

      // Remove file
      const removeButton = screen.getByRole('button', { name: '' }); // X button
      await user.click(removeButton);

      await waitFor(() => {
        expect(screen.queryByText('Selected Files (1)')).not.toBeInTheDocument();
        expect(screen.queryByText('test.pdf')).not.toBeInTheDocument();
      });
    });
  });

  describe('file upload', () => {
    it('uploads files when upload button is clicked', async () => {
      const user = userEvent.setup();
      const file = createMockFile('test.pdf');
      const onUpload = jest.fn();

      render(<FileUpload {...defaultProps} onUpload={onUpload} />);

      // Add file
      const input = screen.getByRole('button', { name: /click to upload/i });
      await user.click(input);

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false,
      });
      fireEvent.change(fileInput);

      await waitFor(() => {
        expect(screen.getByText('Upload All')).toBeInTheDocument();
      });

      // Upload files
      const uploadButton = screen.getByText('Upload All');
      await user.click(uploadButton);

      await waitFor(() => {
        expect(mockFileUploadService.uploadFile).toHaveBeenCalledWith(
          file,
          '/api/upload',
          expect.objectContaining({
            folder: undefined,
            maxSize: undefined,
            onProgress: expect.any(Function),
          })
        );
        expect(onUpload).toHaveBeenCalledWith([
          {
            success: true,
            url: 'https://example.com/file.pdf',
            filename: 'file.pdf',
            size: 1024,
          },
        ]);
      });
    });

    it('shows upload progress during upload', async () => {
      const user = userEvent.setup();
      const file = createMockFile('test.pdf');
      let progressCallback: ((progress: any) => void) | undefined;

      mockFileUploadService.uploadFile.mockImplementation((file, endpoint, options) => {
        progressCallback = options?.onProgress;
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              success: true,
              url: 'https://example.com/file.pdf',
              filename: 'file.pdf',
              size: 1024,
            });
          }, 100);
        });
      });

      render(<FileUpload {...defaultProps} />);

      // Add file
      const input = screen.getByRole('button', { name: /click to upload/i });
      await user.click(input);

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false,
      });
      fireEvent.change(fileInput);

      // Upload files
      const uploadButton = screen.getByText('Upload All');
      await user.click(uploadButton);

      // Simulate progress
      if (progressCallback) {
        progressCallback({ loaded: 50, total: 100, percentage: 50 });
      }

      expect(screen.getByText('50%')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.queryByText('50%')).not.toBeInTheDocument();
      });
    });

    it('handles upload errors', async () => {
      const user = userEvent.setup();
      const file = createMockFile('test.pdf');

      mockFileUploadService.uploadFile.mockResolvedValue({
        success: false,
        error: 'Upload failed',
      });

      render(<FileUpload {...defaultProps} />);

      // Add file
      const input = screen.getByRole('button', { name: /click to upload/i });
      await user.click(input);

      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false,
      });
      fireEvent.change(fileInput);

      // Upload files
      const uploadButton = screen.getByText('Upload All');
      await user.click(uploadButton);

      await waitFor(() => {
        expect(screen.getByText('Upload failed')).toBeInTheDocument();
      });
    });
  });

  describe('disabled state', () => {
    it('prevents interaction when disabled', async () => {
      const user = userEvent.setup();

      render(<FileUpload {...defaultProps} disabled />);

      const dropZone = screen.getByRole('button', { name: /click to upload/i });
      
      expect(dropZone).toHaveClass('opacity-50', 'cursor-not-allowed');

      // Should not respond to drag events
      fireEvent.dragOver(dropZone);
      expect(dropZone).not.toHaveClass('border-orange-500');

      // Should not open file dialog
      await user.click(dropZone);
      // File input should be disabled
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      expect(fileInput).toBeDisabled();
    });
  });
});
