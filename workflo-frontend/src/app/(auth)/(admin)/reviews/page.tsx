'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Star, Plus, Calendar, User, TrendingUp, Filter, ArrowLeft } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input, { Select } from '@/components/ui/Input';
import { useAuth } from '@/store/authStore';
import { formatDate } from '@/lib/utils';

const ReviewsPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');

  // Mock reviews data
  const reviews = [
    {
      id: 1,
      employee_name: '<PERSON>',
      employee_id: 'EMP001',
      reviewer_name: '<PERSON>',
      review_period: '2024 Q1',
      status: 'completed',
      overall_rating: 4.5,
      due_date: '2024-03-31',
      completed_date: '2024-03-28',
      department: 'Engineering'
    },
    {
      id: 2,
      employee_name: '<PERSON>',
      employee_id: 'EMP002',
      reviewer_name: '<PERSON>',
      review_period: '2024 Q1',
      status: 'pending',
      overall_rating: null,
      due_date: '2024-03-31',
      completed_date: null,
      department: 'Design'
    },
    {
      id: 3,
      employee_name: 'David Lee',
      employee_id: 'EMP003',
      reviewer_name: 'Lisa Brown',
      review_period: '2024 Q1',
      status: 'in_progress',
      overall_rating: null,
      due_date: '2024-03-31',
      completed_date: null,
      department: 'Marketing'
    },
    {
      id: 4,
      employee_name: 'Emma Davis',
      employee_id: 'EMP004',
      reviewer_name: 'John Smith',
      review_period: '2024 Q1',
      status: 'completed',
      overall_rating: 4.2,
      due_date: '2024-03-31',
      completed_date: '2024-03-30',
      department: 'HR'
    }
  ];

  const getStatusColor = (status: string) => {
    const colors = {
      completed: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      in_progress: 'bg-blue-100 text-blue-800',
      overdue: 'bg-red-100 text-red-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? 'text-yellow-400 fill-current'
            : i < rating
            ? 'text-yellow-400 fill-current opacity-50'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  const filteredReviews = reviews.filter(review => {
    const matchesSearch = review.employee_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         review.employee_id.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = !statusFilter || review.status === statusFilter;
    const matchesTab = activeTab === 'all' || review.status === activeTab;

    return matchesSearch && matchesStatus && matchesTab;
  });

  const stats = {
    total: reviews.length,
    completed: reviews.filter(r => r.status === 'completed').length,
    pending: reviews.filter(r => r.status === 'pending').length,
    in_progress: reviews.filter(r => r.status === 'in_progress').length,
    average_rating: reviews
      .filter(r => r.overall_rating)
      .reduce((sum, r) => sum + (r.overall_rating || 0), 0) /
      reviews.filter(r => r.overall_rating).length || 0
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <div className="flex items-center space-x-4 mb-2">
              <Link href="/manage">
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Management
                </Button>
              </Link>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Performance Reviews</h1>
            <p className="text-gray-600 mt-1">
              Manage employee performance reviews and evaluations
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <Button variant="primary">
              <Plus className="h-4 w-4 mr-2" />
              New Review Cycle
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-blue-100">
              <User className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-sm font-medium text-gray-600">Total Reviews</h3>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-green-100">
              <Star className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-sm font-medium text-gray-600">Completed</h3>
              <p className="text-2xl font-bold text-gray-900">{stats.completed}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-yellow-100">
              <Calendar className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-sm font-medium text-gray-600">Pending</h3>
              <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-purple-100">
              <TrendingUp className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-sm font-medium text-gray-600">In Progress</h3>
              <p className="text-2xl font-bold text-gray-900">{stats.in_progress}</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-orange-100">
              <Star className="h-6 w-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-sm font-medium text-gray-600">Avg Rating</h3>
              <p className="text-2xl font-bold text-gray-900">{stats.average_rating.toFixed(1)}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          {/* Left side - Tabs */}
          <div className="flex space-x-1">
            {[
              { key: 'all', label: 'All Reviews' },
              { key: 'pending', label: 'Pending' },
              { key: 'in_progress', label: 'In Progress' },
              { key: 'completed', label: 'Completed' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.key
                    ? 'bg-orange-100 text-orange-700'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Right side - Search and filters */}
          <div className="flex items-center space-x-4">
            <div className="w-64">
              <Input
                placeholder="Search reviews..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={<Filter className="h-4 w-4" />}
              />
            </div>

            <div className="w-48">
              <Select
                placeholder="All Status"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                options={[
                  { value: '', label: 'All Status' },
                  { value: 'pending', label: 'Pending' },
                  { value: 'in_progress', label: 'In Progress' },
                  { value: 'completed', label: 'Completed' },
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Reviews List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <div className="space-y-4">
            {filteredReviews.map((review) => (
              <div
                key={review.id}
                className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-medium">
                        {review.employee_name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{review.employee_name}</h4>
                      <p className="text-sm text-gray-600">{review.employee_id} • {review.department}</p>
                      <p className="text-sm text-gray-500">Reviewer: {review.reviewer_name}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-6">
                    <div className="text-center">
                      <p className="text-sm font-medium text-gray-900">{review.review_period}</p>
                      <p className="text-xs text-gray-500">Review Period</p>
                    </div>

                    {review.overall_rating && (
                      <div className="text-center">
                        <div className="flex items-center space-x-1">
                          {renderStars(review.overall_rating)}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{review.overall_rating}/5</p>
                      </div>
                    )}

                    <div className="text-center">
                      <p className="text-sm font-medium text-gray-900">
                        {review.completed_date ? formatDate(review.completed_date) : formatDate(review.due_date)}
                      </p>
                      <p className="text-xs text-gray-500">
                        {review.completed_date ? 'Completed' : 'Due Date'}
                      </p>
                    </div>

                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(review.status)}`}
                    >
                      {review.status.replace('_', ' ').charAt(0).toUpperCase() + review.status.replace('_', ' ').slice(1)}
                    </span>

                    <Button variant="secondary" size="sm">
                      View Details
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredReviews.length === 0 && (
            <div className="text-center py-12">
              <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No reviews found
              </h3>
              <p className="text-gray-600">
                Try adjusting your search or filter criteria.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReviewsPage;
