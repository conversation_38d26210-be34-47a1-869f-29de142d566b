# BioStar 2 API Integration

This document outlines the integration of BioStar 2 biometric access control system with the WorkFlo staff management application.

## Overview

The BioStar 2 integration provides real-time employee attendance tracking through biometric devices, enhancing the staff section with accurate time and attendance data.

## Features

### ✅ Implemented Features

1. **Real-time Attendance Tracking**
   - Live employee check-in/check-out monitoring
   - Biometric device status monitoring
   - Automatic attendance record synchronization

2. **Enhanced Staff Dashboard**
   - BioStar connection status indicator
   - Real-time attendance updates
   - Live activity feed from biometric events

3. **Advanced Time & Attendance Page**
   - Integration with BioStar attendance data
   - Real-time device status monitoring
   - Comprehensive attendance statistics
   - Historical attendance records with BioStar sync status

4. **Attendance Analytics**
   - Automatic calculation of work hours
   - Overtime tracking
   - Break time monitoring
   - Late arrival detection

## API Integration

### BioStar 2 API Service (`src/lib/biostarApi.ts`)

The service provides a comprehensive interface to the BioStar 2 API:

```typescript
// Authentication
await biostarApi.authenticate();

// User Management
const users = await biostarApi.getUsers();
const user = await biostarApi.getUserById(userId);

// Event Tracking
const events = await biostarApi.getEvents(startDate, endDate, userId);
const realtimeEvents = await biostarApi.getRealtimeEvents();

// Device Management
const devices = await biostarApi.getDevices();
```

### Attendance Service (`src/lib/attendanceService.ts`)

High-level service for attendance management:

```typescript
// Get today's attendance
const todayAttendance = await attendanceService.getTodayAttendance(employeeId);

// Get attendance summary for dashboard
const summary = await attendanceService.getAttendanceSummary(employeeId);

// Start real-time monitoring
attendanceService.startRealTimeMonitoring(callback);
```

## Configuration

### Environment Variables

Add the following to your `.env.local` file:

```env
# BioStar 2 API Configuration
NEXT_PUBLIC_BIOSTAR_API_URL=https://bs2api.biostar2.com
NEXT_PUBLIC_BIOSTAR_USERNAME=your-biostar-username
NEXT_PUBLIC_BIOSTAR_PASSWORD=your-biostar-password
```

### API Endpoints

The integration uses the following BioStar 2 API endpoints:

- `POST /login` - Authentication
- `GET /users` - User management
- `GET /events` - Access events
- `GET /devices` - Device status
- `GET /attendance/report` - Attendance reports

## Data Flow

1. **Authentication**: Automatic token management with refresh
2. **Real-time Monitoring**: 30-second polling for live updates
3. **Data Synchronization**: Attendance records marked with `biostar_synced: true`
4. **Error Handling**: Graceful fallback when BioStar is unavailable

## UI Components

### Staff Dashboard Enhancements

- **Connection Status**: Visual indicator for BioStar connectivity
- **Live Updates**: Real-time attendance events in activity feed
- **Attendance Status**: Current employee status (Present, Late, Absent)

### Time & Attendance Page

- **BioStar Integration Status**: Connection and device status
- **Real-time Data**: Live attendance tracking
- **Historical Records**: Complete attendance history with sync status
- **Statistics**: Calculated from actual BioStar data

## Data Types

### Core Types (`src/types/index.ts`)

```typescript
interface BiometricEvent {
  id: string;
  user_id: string;
  device_id: string;
  event_type: 'ENTRY' | 'EXIT' | 'DENIED';
  datetime: string;
  user_name?: string;
  device_name?: string;
}

interface AttendanceRecord {
  id: string;
  employee_id: string;
  employee_name: string;
  date: string;
  first_in?: string;
  last_out?: string;
  total_hours?: number;
  break_time?: number;
  overtime?: number;
  status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EARLY_OUT';
  events: BiometricEvent[];
  biostar_synced: boolean;
}
```

## Error Handling

The integration includes comprehensive error handling:

1. **Connection Failures**: Graceful degradation when BioStar is unavailable
2. **Authentication Errors**: Automatic token refresh
3. **Rate Limiting**: Proper request throttling
4. **Data Validation**: Type-safe data processing

## Security

- **Token Management**: Secure storage and automatic refresh
- **Environment Variables**: Sensitive credentials stored securely
- **Request Interceptors**: Automatic authentication header injection
- **Error Logging**: Comprehensive logging without exposing credentials

## Performance Optimizations

1. **Caching**: Token caching with expiry management
2. **Polling Optimization**: Configurable polling intervals
3. **Data Pagination**: Efficient data loading for large datasets
4. **Memory Management**: Proper cleanup of event listeners

## Testing

The integration includes comprehensive testing:

```bash
# Run tests
npm test

# Run specific BioStar tests
npm test -- --testNamePattern="BioStar"
```

## Deployment

### Production Considerations

1. **Environment Variables**: Set production BioStar credentials
2. **Network Configuration**: Ensure API access to BioStar servers
3. **Monitoring**: Set up monitoring for BioStar connectivity
4. **Backup**: Implement fallback attendance tracking

### Monitoring

Monitor the following metrics:

- BioStar API response times
- Connection success rate
- Data synchronization status
- Device online/offline status

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check network connectivity to BioStar API
   - Verify credentials in environment variables
   - Check BioStar server status

2. **Authentication Errors**
   - Verify username/password
   - Check token expiry handling
   - Review API permissions

3. **Missing Data**
   - Check device connectivity
   - Verify user synchronization
   - Review event filtering

### Debug Mode

Enable debug logging:

```typescript
// In development
localStorage.setItem('biostar_debug', 'true');
```

## Future Enhancements

### Planned Features

1. **Advanced Analytics**
   - Attendance patterns analysis
   - Productivity metrics
   - Custom reporting

2. **Mobile Integration**
   - Mobile attendance tracking
   - Push notifications
   - Offline support

3. **Integration Expansion**
   - Payroll system integration
   - HR system synchronization
   - Third-party calendar integration

## Support

For issues related to BioStar integration:

1. Check the troubleshooting section
2. Review API documentation
3. Contact system administrator
4. Submit issue to development team

## API Documentation

For detailed BioStar 2 API documentation, refer to:
- [BioStar 2 API Guide](https://bs2api.biostar2.com/docs)
- [Integration Examples](./examples/biostar-integration.md)
- [Testing Guide](./docs/testing-biostar.md)
