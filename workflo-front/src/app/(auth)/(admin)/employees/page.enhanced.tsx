'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Users, Plus, Search, Filter, Download, Upload, Grid, List, Edit, Trash2, Eye,
  MoreHorizontal, UserCheck, UserX, Building, Mail, Phone, Calendar, DollarSign,
  MapPin, Shield, FileText, Settings, RefreshCw, CheckSquare, Square, AlertCircle,
  X, Check, ArrowUpDown, ChevronDown, Loader2
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useAuth } from '@/providers/AuthProvider';
import { Employee, EmployeeFilters, Department, EmployeeBulkAction } from '@/types';
import { cn, getInitials, formatDate, formatCurrency } from '@/lib/utils';
import { EmployeeApi } from '@/lib/api/employeeApi';

// Enhanced Employee Management Component
const EmployeesPage: React.FC = () => {
  const { user } = useAuth();

  // State Management
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [selectedEmployees, setSelectedEmployees] = useState<Set<number>>(new Set());
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'table'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalCount, setTotalCount] = useState(0);

  // Filters
  const [filters, setFilters] = useState<EmployeeFilters>({
    search: '',
    department: undefined,
    status: '',
    employment_type: '',
    role: '',
  });

  // Sorting
  const [sortField, setSortField] = useState<keyof Employee>('first_name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Modals
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);

  // Load employees data
  const loadEmployees = useCallback(async () => {
    try {
      setLoading(true);
      const response = await EmployeeApi.getEmployees(filters, currentPage, pageSize);
      setEmployees(response.results);
      setTotalCount(response.count);
    } catch (error) {
      console.error('Failed to load employees:', error);
    } finally {
      setLoading(false);
    }
  }, [filters, currentPage, pageSize]);

  // Load departments
  const loadDepartments = useCallback(async () => {
    try {
      const depts = await EmployeeApi.getDepartments();
      setDepartments(depts);
    } catch (error) {
      console.error('Failed to load departments:', error);
    }
  }, []);

  // Initial data load
  useEffect(() => {
    loadEmployees();
    loadDepartments();
  }, [loadEmployees, loadDepartments]);

  // Handle filter changes
  const handleFilterChange = useCallback((key: keyof EmployeeFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page when filtering
  }, []);

  // Handle sorting
  const handleSort = useCallback((field: keyof Employee) => {
    if (sortField === field) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  }, [sortField]);

  // Handle employee selection
  const handleEmployeeSelect = useCallback((employeeId: number, selected: boolean) => {
    setSelectedEmployees(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(employeeId);
      } else {
        newSet.delete(employeeId);
      }
      return newSet;
    });
  }, []);

  // Handle select all
  const handleSelectAll = useCallback((selected: boolean) => {
    if (selected) {
      setSelectedEmployees(new Set(employees.map(emp => emp.id)));
    } else {
      setSelectedEmployees(new Set());
    }
  }, [employees]);

  // Handle bulk actions
  const handleBulkAction = useCallback(async (action: EmployeeBulkAction) => {
    try {
      setActionLoading(true);
      const result = await EmployeeApi.bulkAction(action);
      console.log('Bulk action result:', result);

      // Reload employees after bulk action
      await loadEmployees();
      setSelectedEmployees(new Set());
      setShowBulkActions(false);
    } catch (error) {
      console.error('Bulk action failed:', error);
    } finally {
      setActionLoading(false);
    }
  }, [loadEmployees]);

  // Handle employee actions
  const handleCreateEmployee = useCallback(() => {
    setSelectedEmployee(null);
    setShowCreateModal(true);
  }, []);

  const handleEditEmployee = useCallback((employee: Employee) => {
    setSelectedEmployee(employee);
    setShowEditModal(true);
  }, []);

  const handleDeleteEmployee = useCallback((employee: Employee) => {
    setSelectedEmployee(employee);
    setShowDeleteModal(true);
  }, []);

  const handleViewEmployee = useCallback((employee: Employee) => {
    // Navigate to employee detail page
    window.location.href = `/employees/${employee.id}`;
  }, []);

  // Export employees
  const handleExport = useCallback(async (format: 'csv' | 'excel' | 'pdf') => {
    try {
      setActionLoading(true);
      const result = await EmployeeApi.exportEmployees(format, filters);
      // Download the file
      window.open(result.download_url, '_blank');
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setActionLoading(false);
    }
  }, [filters]);

  // Import employees
  const handleImport = useCallback(() => {
    setShowImportModal(true);
  }, []);

  // Computed values
  const isAllSelected = useMemo(() => {
    return employees.length > 0 && selectedEmployees.size === employees.length;
  }, [employees.length, selectedEmployees.size]);

  const isPartiallySelected = useMemo(() => {
    return selectedEmployees.size > 0 && selectedEmployees.size < employees.length;
  }, [selectedEmployees.size, employees.length]);

  const totalPages = useMemo(() => {
    return Math.ceil(totalCount / pageSize);
  }, [totalCount, pageSize]);

  // Filter options
  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'on_leave', label: 'On Leave' },
    { value: 'terminated', label: 'Terminated' },
  ];

  const employmentTypeOptions = [
    { value: '', label: 'All Types' },
    { value: 'full_time', label: 'Full Time' },
    { value: 'part_time', label: 'Part Time' },
    { value: 'contract', label: 'Contract' },
    { value: 'intern', label: 'Intern' },
  ];

  const roleOptions = [
    { value: '', label: 'All Roles' },
    { value: 'employee', label: 'Employee' },
    { value: 'supervisor', label: 'Supervisor' },
    { value: 'hr', label: 'HR' },
    { value: 'accountant', label: 'Accountant' },
    { value: 'admin', label: 'Admin' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Employee Management</h1>
            <p className="text-gray-600 mt-1">
              Manage your organization's employees with comprehensive CRUD operations
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-3">
            <Button
              variant="secondary"
              onClick={handleImport}
              disabled={actionLoading}
            >
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <div className="relative">
              <Button
                variant="secondary"
                onClick={() => setShowBulkActions(!showBulkActions)}
                disabled={actionLoading}
              >
                <Download className="h-4 w-4 mr-2" />
                Export
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
              {showBulkActions && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                  <div className="py-1">
                    <button
                      onClick={() => handleExport('csv')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Export as CSV
                    </button>
                    <button
                      onClick={() => handleExport('excel')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Export as Excel
                    </button>
                    <button
                      onClick={() => handleExport('pdf')}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Export as PDF
                    </button>
                  </div>
                </div>
              )}
            </div>
            <Button
              variant="primary"
              onClick={handleCreateEmployee}
              disabled={actionLoading}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Employee
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Employees</p>
                <p className="text-2xl font-semibold text-gray-900">{totalCount}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserCheck className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {employees.filter(emp => emp.status === 'active').length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Building className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Departments</p>
                <p className="text-2xl font-semibold text-gray-900">{departments.length}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">New This Month</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {employees.filter(emp => {
                    const hireDate = new Date(emp.hire_date);
                    const now = new Date();
                    return hireDate.getMonth() === now.getMonth() &&
                           hireDate.getFullYear() === now.getFullYear();
                  }).length}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          {/* Left side - Search and filters toggle */}
          <div className="flex items-center space-x-4">
            <div className="w-80">
              <Input
                placeholder="Search employees by name, email, or ID..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                leftIcon={<Search className="h-4 w-4" />}
              />
            </div>
            <Button
              variant="secondary"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {Object.values(filters).some(v => v && v !== '') && (
                <span className="ml-2 bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                  Active
                </span>
              )}
            </Button>
          </div>

          {/* Right side - View mode and actions */}
          <div className="flex items-center space-x-4">
            {/* Selected count and bulk actions */}
            {selectedEmployees.size > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">
                  {selectedEmployees.size} selected
                </span>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => handleBulkAction({
                    action: 'activate',
                    employee_ids: Array.from(selectedEmployees)
                  })}
                  disabled={actionLoading}
                >
                  <UserCheck className="h-4 w-4 mr-1" />
                  Activate
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => handleBulkAction({
                    action: 'deactivate',
                    employee_ids: Array.from(selectedEmployees)
                  })}
                  disabled={actionLoading}
                >
                  <UserX className="h-4 w-4 mr-1" />
                  Deactivate
                </Button>
                <Button
                  variant="danger"
                  size="sm"
                  onClick={() => handleBulkAction({
                    action: 'delete',
                    employee_ids: Array.from(selectedEmployees)
                  })}
                  disabled={actionLoading}
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </Button>
              </div>
            )}

            {/* View Mode Toggle */}
            <div className="flex items-center space-x-1 bg-gray-100 rounded-md p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={cn(
                  'p-2 rounded-md transition-colors',
                  viewMode === 'grid'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                )}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={cn(
                  'p-2 rounded-md transition-colors',
                  viewMode === 'list'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                )}
              >
                <List className="h-4 w-4" />
              </button>
            </div>

            <Button
              variant="secondary"
              onClick={loadEmployees}
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Department
                </label>
                <select
                  value={filters.department || ''}
                  onChange={(e) => handleFilterChange('department', e.target.value ? Number(e.target.value) : undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  <option value="">All Departments</option>
                  {departments.map(dept => (
                    <option key={dept.id} value={dept.id}>
                      {dept.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={filters.status || ''}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  {statusOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Employment Type
                </label>
                <select
                  value={filters.employment_type || ''}
                  onChange={(e) => handleFilterChange('employment_type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  {employmentTypeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Role
                </label>
                <select
                  value={filters.role || ''}
                  onChange={(e) => handleFilterChange('role', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                >
                  {roleOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="mt-4 flex items-center space-x-3">
              <Button
                variant="secondary"
                onClick={() => {
                  setFilters({
                    search: '',
                    department: undefined,
                    status: '',
                    employment_type: '',
                    role: '',
                  });
                }}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Employee List/Grid */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="p-6">
            <div className="animate-pulse space-y-4">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : viewMode === 'grid' ? (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {employees.map((employee) => (
                <EmployeeCard
                  key={employee.id}
                  employee={employee}
                  selected={selectedEmployees.has(employee.id)}
                  onSelect={(selected) => handleEmployeeSelect(employee.id, selected)}
                  onEdit={() => handleEditEmployee(employee)}
                  onDelete={() => handleDeleteEmployee(employee)}
                  onView={() => handleViewEmployee(employee)}
                />
              ))}
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={isAllSelected}
                      ref={(el) => {
                        if (el) el.indeterminate = isPartiallySelected;
                      }}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('first_name')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Employee</span>
                      <ArrowUpDown className="h-4 w-4" />
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('job_title')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Position</span>
                      <ArrowUpDown className="h-4 w-4" />
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('department')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Department</span>
                      <ArrowUpDown className="h-4 w-4" />
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('status')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Status</span>
                      <ArrowUpDown className="h-4 w-4" />
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('hire_date')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Hire Date</span>
                      <ArrowUpDown className="h-4 w-4" />
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {employees.map((employee) => (
                  <EmployeeTableRow
                    key={employee.id}
                    employee={employee}
                    selected={selectedEmployees.has(employee.id)}
                    onSelect={(selected) => handleEmployeeSelect(employee.id, selected)}
                    onEdit={() => handleEditEmployee(employee)}
                    onDelete={() => handleDeleteEmployee(employee)}
                    onView={() => handleViewEmployee(employee)}
                  />
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Empty State */}
        {!loading && employees.length === 0 && (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No employees found
            </h3>
            <p className="text-gray-600 mb-4">
              {Object.values(filters).some(v => v && v !== '')
                ? 'Try adjusting your search or filter criteria.'
                : 'Get started by adding your first employee.'
              }
            </p>
            <Button variant="primary" onClick={handleCreateEmployee}>
              <Plus className="h-4 w-4 mr-2" />
              Add Employee
            </Button>
          </div>
        )}

        {/* Pagination */}
        {!loading && employees.length > 0 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700">
                  Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} results
                </span>
                <select
                  value={pageSize}
                  onChange={(e) => setPageSize(Number(e.target.value))}
                  className="ml-2 px-2 py-1 border border-gray-300 rounded text-sm"
                >
                  <option value={10}>10 per page</option>
                  <option value={20}>20 per page</option>
                  <option value={50}>50 per page</option>
                  <option value={100}>100 per page</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>

                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <button
                        key={page}
                        onClick={() => setCurrentPage(page)}
                        className={cn(
                          'px-3 py-1 text-sm rounded',
                          currentPage === page
                            ? 'bg-orange-600 text-white'
                            : 'text-gray-700 hover:bg-gray-100'
                        )}
                      >
                        {page}
                      </button>
                    );
                  })}
                  {totalPages > 5 && (
                    <>
                      <span className="px-2 text-gray-500">...</span>
                      <button
                        onClick={() => setCurrentPage(totalPages)}
                        className={cn(
                          'px-3 py-1 text-sm rounded',
                          currentPage === totalPages
                            ? 'bg-orange-600 text-white'
                            : 'text-gray-700 hover:bg-gray-100'
                        )}
                      >
                        {totalPages}
                      </button>
                    </>
                  )}
                </div>

                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals will be added here */}
      {showCreateModal && (
        <EmployeeCreateModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSuccess={loadEmployees}
          departments={departments}
        />
      )}

      {showEditModal && selectedEmployee && (
        <EmployeeEditModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          onSuccess={loadEmployees}
          employee={selectedEmployee}
          departments={departments}
        />
      )}

      {showDeleteModal && selectedEmployee && (
        <EmployeeDeleteModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onSuccess={loadEmployees}
          employee={selectedEmployee}
        />
      )}

      {showImportModal && (
        <EmployeeImportModal
          isOpen={showImportModal}
          onClose={() => setShowImportModal(false)}
          onSuccess={loadEmployees}
        />
      )}
    </div>
  );
};

// Employee Card Component
interface EmployeeCardProps {
  employee: Employee;
  selected: boolean;
  onSelect: (selected: boolean) => void;
  onEdit: () => void;
  onDelete: () => void;
  onView: () => void;
}

const EmployeeCard: React.FC<EmployeeCardProps> = ({
  employee,
  selected,
  onSelect,
  onEdit,
  onDelete,
  onView,
}) => {
  const [showActions, setShowActions] = useState(false);

  return (
    <Card className={cn(
      'relative transition-all duration-200 hover:shadow-md cursor-pointer',
      selected && 'ring-2 ring-orange-500 ring-opacity-50'
    )}>
      <div className="p-6">
        {/* Selection checkbox */}
        <div className="absolute top-4 right-4">
          <input
            type="checkbox"
            checked={selected}
            onChange={(e) => onSelect(e.target.checked)}
            className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
            onClick={(e) => e.stopPropagation()}
          />
        </div>

        {/* Employee Info */}
        <div className="flex items-center space-x-4 mb-4" onClick={onView}>
          <div className="flex-shrink-0">
            {employee.profile_picture ? (
              <img
                src={employee.profile_picture}
                alt={`${employee.first_name} ${employee.last_name}`}
                className="w-12 h-12 rounded-full object-cover"
              />
            ) : (
              <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
                <span className="text-white font-medium">
                  {getInitials(employee.first_name, employee.last_name)}
                </span>
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-medium text-gray-900 truncate">
              {employee.first_name} {employee.last_name}
            </h3>
            <p className="text-sm text-gray-600 truncate">{employee.job_title}</p>
            <p className="text-sm text-gray-500 truncate">{employee.employee_id}</p>
          </div>
        </div>

        {/* Employee Details */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-600">
            <Mail className="h-4 w-4 mr-2 flex-shrink-0" />
            <span className="truncate">{employee.email}</span>
          </div>
          {employee.phone_number && (
            <div className="flex items-center text-sm text-gray-600">
              <Phone className="h-4 w-4 mr-2 flex-shrink-0" />
              <span>{employee.phone_number}</span>
            </div>
          )}
          <div className="flex items-center text-sm text-gray-600">
            <Building className="h-4 w-4 mr-2 flex-shrink-0" />
            <span>{employee.department?.name || 'No Department'}</span>
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <Calendar className="h-4 w-4 mr-2 flex-shrink-0" />
            <span>Hired {formatDate(employee.hire_date)}</span>
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <DollarSign className="h-4 w-4 mr-2 flex-shrink-0" />
            <span>{formatCurrency(employee.salary, employee.currency || 'USD')}</span>
          </div>
        </div>

        {/* Status Badge */}
        <div className="flex items-center justify-between">
          <span className={cn(
            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
            employee.status === 'active' && 'bg-green-100 text-green-800',
            employee.status === 'inactive' && 'bg-gray-100 text-gray-800',
            employee.status === 'on_leave' && 'bg-yellow-100 text-yellow-800',
            employee.status === 'terminated' && 'bg-red-100 text-red-800'
          )}>
            {employee.status.replace('_', ' ').toUpperCase()}
          </span>

          {/* Actions */}
          <div className="relative">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowActions(!showActions);
              }}
              className="p-1 rounded-full hover:bg-gray-100"
            >
              <MoreHorizontal className="h-4 w-4 text-gray-500" />
            </button>

            {showActions && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onView();
                      setShowActions(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit();
                      setShowActions(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete();
                      setShowActions(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

// Employee Table Row Component
interface EmployeeTableRowProps {
  employee: Employee;
  selected: boolean;
  onSelect: (selected: boolean) => void;
  onEdit: () => void;
  onDelete: () => void;
  onView: () => void;
}

const EmployeeTableRow: React.FC<EmployeeTableRowProps> = ({
  employee,
  selected,
  onSelect,
  onEdit,
  onDelete,
  onView,
}) => {
  const [showActions, setShowActions] = useState(false);

  return (
    <tr className={cn(
      'hover:bg-gray-50 transition-colors',
      selected && 'bg-orange-50'
    )}>
      <td className="px-6 py-4 whitespace-nowrap">
        <input
          type="checkbox"
          checked={selected}
          onChange={(e) => onSelect(e.target.checked)}
          className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
        />
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            {employee.profile_picture ? (
              <img
                src={employee.profile_picture}
                alt={`${employee.first_name} ${employee.last_name}`}
                className="h-10 w-10 rounded-full object-cover"
              />
            ) : (
              <div className="h-10 w-10 bg-orange-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {getInitials(employee.first_name, employee.last_name)}
                </span>
              </div>
            )}
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">
              {employee.first_name} {employee.last_name}
            </div>
            <div className="text-sm text-gray-500">{employee.email}</div>
            <div className="text-sm text-gray-500">{employee.employee_id}</div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">{employee.job_title}</div>
        <div className="text-sm text-gray-500">{employee.employment_type?.replace('_', ' ')}</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">{employee.department?.name || 'No Department'}</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={cn(
          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
          employee.status === 'active' && 'bg-green-100 text-green-800',
          employee.status === 'inactive' && 'bg-gray-100 text-gray-800',
          employee.status === 'on_leave' && 'bg-yellow-100 text-yellow-800',
          employee.status === 'terminated' && 'bg-red-100 text-red-800'
        )}>
          {employee.status.replace('_', ' ').toUpperCase()}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {formatDate(employee.hire_date)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="relative">
          <button
            onClick={() => setShowActions(!showActions)}
            className="p-1 rounded-full hover:bg-gray-100"
          >
            <MoreHorizontal className="h-4 w-4 text-gray-500" />
          </button>

          {showActions && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
              <div className="py-1">
                <button
                  onClick={() => {
                    onView();
                    setShowActions(false);
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </button>
                <button
                  onClick={() => {
                    onEdit();
                    setShowActions(false);
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </button>
                <button
                  onClick={() => {
                    onDelete();
                    setShowActions(false);
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </button>
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  );
};

// Placeholder Modal Components (to be implemented)
const EmployeeCreateModal: React.FC<any> = () => <div>Create Modal</div>;
const EmployeeEditModal: React.FC<any> = () => <div>Edit Modal</div>;
const EmployeeDeleteModal: React.FC<any> = () => <div>Delete Modal</div>;
const EmployeeImportModal: React.FC<any> = () => <div>Import Modal</div>;

export default EmployeesPage;
