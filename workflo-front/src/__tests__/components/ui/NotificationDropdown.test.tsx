import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '@/__tests__/utils/test-utils';
import NotificationDropdown from '@/components/ui/NotificationDropdown';
import { NotificationData } from '@/lib/websocket';

const mockNotifications: NotificationData[] = [
  {
    id: '1',
    type: 'leave_update',
    title: 'Leave Application Approved',
    message: 'Your leave application for Dec 25-29 has been approved.',
    timestamp: '2024-12-20T10:00:00Z',
    read: false,
    priority: 'medium',
    user_id: 1,
  },
  {
    id: '2',
    type: 'payroll_update',
    title: 'Payslip Available',
    message: 'Your December payslip is now available for download.',
    timestamp: '2024-12-19T15:30:00Z',
    read: true,
    priority: 'low',
    user_id: 1,
  },
  {
    id: '3',
    type: 'system_maintenance',
    title: 'System Maintenance',
    message: 'Scheduled maintenance on Dec 22, 2024 from 2:00 AM to 4:00 AM.',
    timestamp: '2024-12-18T09:00:00Z',
    read: false,
    priority: 'high',
    user_id: 1,
  },
];

describe('NotificationDropdown', () => {
  const mockNotificationContext = {
    notifications: mockNotifications,
    unreadCount: 2,
    isConnected: true,
    connectionState: 'connected',
    markAsRead: jest.fn(),
    markAllAsRead: jest.fn(),
    clearNotifications: jest.fn(),
    requestPermission: jest.fn().mockResolvedValue('granted' as NotificationPermission),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('notification bell', () => {
    it('renders notification bell with unread count', () => {
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      expect(screen.getByText('2')).toBeInTheDocument(); // Unread count badge
    });

    it('shows connected status indicator', () => {
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      const bell = screen.getByRole('button');
      const statusIndicator = bell.querySelector('.bg-green-500');
      expect(statusIndicator).toBeInTheDocument();
    });

    it('shows disconnected status when not connected', () => {
      render(<NotificationDropdown />, {
        notificationContextValue: {
          ...mockNotificationContext,
          isConnected: false,
          connectionState: 'disconnected',
        },
      });

      const bell = screen.getByRole('button');
      const statusIndicator = bell.querySelector('.bg-red-500');
      expect(statusIndicator).toBeInTheDocument();
    });

    it('does not show unread count when there are no unread notifications', () => {
      render(<NotificationDropdown />, {
        notificationContextValue: {
          ...mockNotificationContext,
          unreadCount: 0,
        },
      });

      expect(screen.queryByText('2')).not.toBeInTheDocument();
    });
  });

  describe('dropdown functionality', () => {
    it('opens dropdown when bell is clicked', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      const bell = screen.getByRole('button');
      await user.click(bell);

      expect(screen.getByText('Notifications')).toBeInTheDocument();
      expect(screen.getByText('2 unread notifications')).toBeInTheDocument();
    });

    it('closes dropdown when close button is clicked', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      // Open dropdown
      const bell = screen.getByRole('button');
      await user.click(bell);

      // Close dropdown
      const closeButton = screen.getByRole('button', { name: '' }); // X button
      await user.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByText('Notifications')).not.toBeInTheDocument();
      });
    });

    it('closes dropdown when clicking outside', async () => {
      const user = userEvent.setup();
      
      render(
        <div>
          <NotificationDropdown />
          <div data-testid="outside">Outside element</div>
        </div>,
        {
          notificationContextValue: mockNotificationContext,
        }
      );

      // Open dropdown
      const bell = screen.getByRole('button');
      await user.click(bell);

      expect(screen.getByText('Notifications')).toBeInTheDocument();

      // Click outside
      const outsideElement = screen.getByTestId('outside');
      await user.click(outsideElement);

      await waitFor(() => {
        expect(screen.queryByText('Notifications')).not.toBeInTheDocument();
      });
    });
  });

  describe('notification list', () => {
    it('displays all notifications', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      const bell = screen.getByRole('button');
      await user.click(bell);

      expect(screen.getByText('Leave Application Approved')).toBeInTheDocument();
      expect(screen.getByText('Payslip Available')).toBeInTheDocument();
      expect(screen.getByText('System Maintenance')).toBeInTheDocument();
    });

    it('shows empty state when no notifications', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: {
          ...mockNotificationContext,
          notifications: [],
          unreadCount: 0,
        },
      });

      const bell = screen.getByRole('button');
      await user.click(bell);

      expect(screen.getByText('No notifications yet')).toBeInTheDocument();
    });

    it('marks notification as read when clicked', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      const bell = screen.getByRole('button');
      await user.click(bell);

      const notification = screen.getByText('Leave Application Approved');
      await user.click(notification);

      expect(mockNotificationContext.markAsRead).toHaveBeenCalledWith('1');
    });

    it('marks individual notification as read via button', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      const bell = screen.getByRole('button');
      await user.click(bell);

      // Find the mark as read button for the first notification
      const markAsReadButtons = screen.getAllByRole('button', { name: '' });
      const markAsReadButton = markAsReadButtons.find(button => 
        button.querySelector('svg')?.getAttribute('data-testid') === 'check'
      );

      if (markAsReadButton) {
        await user.click(markAsReadButton);
        expect(mockNotificationContext.markAsRead).toHaveBeenCalledWith('1');
      }
    });
  });

  describe('notification actions', () => {
    it('marks all notifications as read', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      const bell = screen.getByRole('button');
      await user.click(bell);

      const markAllButton = screen.getByRole('button', { name: '' }); // CheckCheck icon
      await user.click(markAllButton);

      expect(mockNotificationContext.markAllAsRead).toHaveBeenCalled();
    });

    it('opens settings panel', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      const bell = screen.getByRole('button');
      await user.click(bell);

      const settingsButton = screen.getByRole('button', { name: '' }); // Settings icon
      await user.click(settingsButton);

      expect(screen.getByText('Notification Settings')).toBeInTheDocument();
      expect(screen.getByText('Enable Browser Notifications')).toBeInTheDocument();
      expect(screen.getByText('Clear All Notifications')).toBeInTheDocument();
    });

    it('requests browser notification permission', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      const bell = screen.getByRole('button');
      await user.click(bell);

      // Open settings
      const settingsButton = screen.getByRole('button', { name: '' }); // Settings icon
      await user.click(settingsButton);

      // Click enable notifications
      const enableButton = screen.getByText('Enable Browser Notifications');
      await user.click(enableButton);

      expect(mockNotificationContext.requestPermission).toHaveBeenCalled();
    });

    it('clears all notifications', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      const bell = screen.getByRole('button');
      await user.click(bell);

      // Open settings
      const settingsButton = screen.getByRole('button', { name: '' }); // Settings icon
      await user.click(settingsButton);

      // Click clear all
      const clearButton = screen.getByText('Clear All Notifications');
      await user.click(clearButton);

      expect(mockNotificationContext.clearNotifications).toHaveBeenCalled();
    });
  });

  describe('connection status', () => {
    it('shows connection status in header', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      const bell = screen.getByRole('button');
      await user.click(bell);

      expect(screen.getByText('connected')).toBeInTheDocument();
    });

    it('shows disconnected status when not connected', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: {
          ...mockNotificationContext,
          isConnected: false,
          connectionState: 'disconnected',
        },
      });

      const bell = screen.getByRole('button');
      await user.click(bell);

      expect(screen.getByText('disconnected')).toBeInTheDocument();
    });
  });

  describe('notification priority styling', () => {
    it('applies correct styling for high priority notifications', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      const bell = screen.getByRole('button');
      await user.click(bell);

      const highPriorityNotification = screen.getByText('System Maintenance').closest('div');
      expect(highPriorityNotification).toHaveClass('border-l-red-500', 'bg-red-50');
    });

    it('applies correct styling for medium priority notifications', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      const bell = screen.getByRole('button');
      await user.click(bell);

      const mediumPriorityNotification = screen.getByText('Leave Application Approved').closest('div');
      expect(mediumPriorityNotification).toHaveClass('border-l-yellow-500', 'bg-yellow-50');
    });

    it('applies correct styling for low priority notifications', async () => {
      const user = userEvent.setup();
      
      render(<NotificationDropdown />, {
        notificationContextValue: mockNotificationContext,
      });

      const bell = screen.getByRole('button');
      await user.click(bell);

      const lowPriorityNotification = screen.getByText('Payslip Available').closest('div');
      expect(lowPriorityNotification).toHaveClass('border-l-green-500', 'bg-green-50');
    });
  });
});
