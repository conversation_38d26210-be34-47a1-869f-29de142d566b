'use client';

import React, { useState } from 'react';
import {
  DollarSign,
  Download,
  Eye,
  Calendar,
  TrendingUp,
  PieChart,
  FileText,
  CreditCard,
  Building,
  Calculator,
  Shield,
  Gift,
  Clock,
  BarChart3,
  Receipt,
  History,
  Settings
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { cn, formatCurrency, formatDate } from '@/lib/utils';

const StaffSalaryPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('balance');
  const [selectedPayslip, setSelectedPayslip] = useState<number | null>(null);

  const tabs = [
    { id: 'balance', name: 'Current Balance', icon: DollarSign },
    { id: 'benefits', name: 'Benefits', icon: Gift },
    { id: 'taxes', name: 'Taxes', icon: Calculator },
    { id: 'bank', name: 'Bank Details', icon: CreditCard },
    { id: 'bonuses', name: 'Bonuses', icon: TrendingUp },
    { id: 'overtime', name: 'Overtime', icon: Clock },
    { id: 'payslip', name: 'Payslip', icon: Receipt },
    { id: 'history', name: 'Salary History', icon: History },
    { id: 'analytics', name: 'Analytics', icon: BarChart3 }
  ];

  // Mock salary data - in real app, this would come from API
  const salaryData = {
    basic_salary: 95000,
    currency: 'KSH',
    pay_frequency: 'Monthly',
    bank_name: 'KCB Bank',
    bank_account: '****1234',
    effective_date: '2024-01-01'
  };

  const currentPayslip = {
    id: 1,
    pay_period: 'December 2024',
    pay_date: '2024-12-31',
    gross_salary: 95000,
    basic_salary: 95000,
    allowances: {
      house_allowance: 15000,
      transport_allowance: 8000,
      medical_allowance: 5000
    },
    deductions: {
      nssf: 5700, // 6% of gross
      nhif: 2613, // 2.75% of gross
      housing_levy: 1425, // 1.5% of gross
      paye: 18500, // Progressive tax
      other_deductions: 0
    },
    net_salary: 95262,
    status: 'Paid'
  };

  const payslipHistory = [
    {
      id: 1,
      pay_period: 'December 2024',
      pay_date: '2024-12-31',
      gross_salary: 95000,
      net_salary: 95262,
      status: 'Paid'
    },
    {
      id: 2,
      pay_period: 'November 2024',
      pay_date: '2024-11-30',
      gross_salary: 95000,
      net_salary: 95262,
      status: 'Paid'
    },
    {
      id: 3,
      pay_period: 'October 2024',
      pay_date: '2024-10-31',
      gross_salary: 95000,
      net_salary: 95262,
      status: 'Paid'
    },
    {
      id: 4,
      pay_period: 'September 2024',
      pay_date: '2024-09-30',
      gross_salary: 95000,
      net_salary: 95262,
      status: 'Paid'
    }
  ];

  // Mock data for new tabs
  const benefits = [
    { name: 'House Allowance', amount: 15000, description: 'Monthly housing allowance', taxable: true },
    { name: 'Transport Allowance', amount: 8000, description: 'Monthly transport allowance', taxable: true },
    { name: 'Medical Allowance', amount: 5000, description: 'Monthly medical allowance', taxable: false },
    { name: 'Life Insurance', amount: 2000, description: 'Company-paid life insurance', taxable: false },
    { name: 'Pension Contribution', amount: 4750, description: 'Company pension contribution (5%)', taxable: false }
  ];

  const bonuses = [
    { id: 1, type: 'Performance Bonus', amount: 25000, date: '2024-12-15', period: 'Q4 2024', status: 'Paid' },
    { id: 2, type: 'Annual Bonus', amount: 50000, date: '2024-12-31', period: '2024', status: 'Pending' },
    { id: 3, type: 'Project Completion', amount: 15000, date: '2024-11-30', period: 'Project Alpha', status: 'Paid' },
    { id: 4, type: 'Performance Bonus', amount: 20000, date: '2024-09-15', period: 'Q3 2024', status: 'Paid' }
  ];

  const overtimeRecords = [
    { id: 1, date: '2024-12-20', hours: 3, rate: 1500, amount: 4500, approved: true },
    { id: 2, date: '2024-12-18', hours: 2, rate: 1500, amount: 3000, approved: true },
    { id: 3, date: '2024-12-15', hours: 4, rate: 1500, amount: 6000, approved: true },
    { id: 4, date: '2024-12-10', hours: 2.5, rate: 1500, amount: 3750, approved: false }
  ];

  const salaryHistory = [
    { period: 'Dec 2024', basic: 95000, gross: 123000, net: 95262, increment: 0 },
    { period: 'Nov 2024', basic: 95000, gross: 123000, net: 95262, increment: 0 },
    { period: 'Oct 2024', basic: 95000, gross: 123000, net: 95262, increment: 0 },
    { period: 'Sep 2024', basic: 90000, gross: 118000, net: 91262, increment: 5000 },
    { period: 'Aug 2024', basic: 90000, gross: 118000, net: 91262, increment: 0 },
    { period: 'Jul 2024', basic: 90000, gross: 118000, net: 91262, increment: 0 }
  ];

  const totalAllowances = Object.values(currentPayslip.allowances).reduce((sum, amount) => sum + amount, 0);
  const totalDeductions = Object.values(currentPayslip.deductions).reduce((sum, amount) => sum + amount, 0);

  const handleDownloadPayslip = (payslipId: number) => {
    // TODO: Implement payslip download
    console.log('Downloading payslip:', payslipId);
  };

  const handleViewPayslip = (payslipId: number) => {
    setSelectedPayslip(payslipId);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Salary Information</h1>
          <p className="text-gray-600 mt-1">
            View your salary details, payslips, and payment history
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Download Latest Payslip
          </Button>
          <Button variant="primary">
            <Eye className="h-4 w-4 mr-2" />
            View Tax Certificate
          </Button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  'flex items-center py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap',
                  activeTab === tab.id
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'balance' && (
        <div className="space-y-6">
          {/* Salary Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Basic Salary</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(salaryData.basic_salary, salaryData.currency)}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Gross Salary</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(currentPayslip.gross_salary, salaryData.currency)}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Calculator className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Net Salary</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(currentPayslip.net_salary, salaryData.currency)}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Calendar className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pay Frequency</p>
                <p className="text-2xl font-bold text-gray-900">{salaryData.pay_frequency}</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Payslip Breakdown */}
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Current Payslip - {currentPayslip.pay_period}
            </h3>

            <div className="space-y-4">
              {/* Earnings */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Earnings</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Basic Salary</span>
                    <span className="font-medium">{formatCurrency(currentPayslip.basic_salary, salaryData.currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">House Allowance</span>
                    <span className="font-medium">{formatCurrency(currentPayslip.allowances.house_allowance, salaryData.currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Transport Allowance</span>
                    <span className="font-medium">{formatCurrency(currentPayslip.allowances.transport_allowance, salaryData.currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Medical Allowance</span>
                    <span className="font-medium">{formatCurrency(currentPayslip.allowances.medical_allowance, salaryData.currency)}</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between font-medium">
                    <span>Gross Salary</span>
                    <span>{formatCurrency(currentPayslip.gross_salary + totalAllowances, salaryData.currency)}</span>
                  </div>
                </div>
              </div>

              {/* Deductions */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Deductions</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">NSSF (6%)</span>
                    <span className="font-medium text-red-600">-{formatCurrency(currentPayslip.deductions.nssf, salaryData.currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">NHIF/SHA (2.75%)</span>
                    <span className="font-medium text-red-600">-{formatCurrency(currentPayslip.deductions.nhif, salaryData.currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Housing Levy (1.5%)</span>
                    <span className="font-medium text-red-600">-{formatCurrency(currentPayslip.deductions.housing_levy, salaryData.currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">PAYE Tax</span>
                    <span className="font-medium text-red-600">-{formatCurrency(currentPayslip.deductions.paye, salaryData.currency)}</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between font-medium">
                    <span>Total Deductions</span>
                    <span className="text-red-600">-{formatCurrency(totalDeductions, salaryData.currency)}</span>
                  </div>
                </div>
              </div>

              {/* Net Salary */}
              <div className="bg-green-50 p-3 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-green-800">Net Salary</span>
                  <span className="text-lg font-bold text-green-800">
                    {formatCurrency(currentPayslip.net_salary, salaryData.currency)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Payment Information */}
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Information</h3>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Building className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Bank Name</p>
                  <p className="font-medium text-gray-900">{salaryData.bank_name}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CreditCard className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Account Number</p>
                  <p className="font-medium text-gray-900">{salaryData.bank_account}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Calendar className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Next Pay Date</p>
                  <p className="font-medium text-gray-900">January 31, 2025</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Shield className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-500">Tax Status</p>
                  <p className="font-medium text-gray-900">PAYE Compliant</p>
                </div>
              </div>
            </div>

            {/* Tax Breakdown */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Tax Information</h4>
              <div className="space-y-1 text-xs text-gray-600">
                <p>• Tax Rate: Progressive (10%, 25%, 30%, 32.5%, 35%)</p>
                <p>• Personal Relief: KSH 2,400/month</p>
                <p>• Insurance Relief: Available</p>
                <p>• NSSF Relief: KSH 200/month</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Payslip History */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Payslip History</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pay Period
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pay Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Gross Salary
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Net Salary
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {payslipHistory.map((payslip) => (
                  <tr key={payslip.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {payslip.pay_period}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(payslip.pay_date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(payslip.gross_salary, salaryData.currency)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatCurrency(payslip.net_salary, salaryData.currency)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {payslip.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleViewPayslip(payslip.id)}
                          className="text-orange-600 hover:text-orange-900"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDownloadPayslip(payslip.id)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Download className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </Card>
        </div>
      )}

      {/* Benefits Tab */}
      {activeTab === 'benefits' && (
        <div className="space-y-6">
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Employee Benefits</h3>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{benefit.name}</h4>
                      <p className="text-sm text-gray-600">{benefit.description}</p>
                      <div className="flex items-center mt-2">
                        <span className={cn(
                          'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                          benefit.taxable ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                        )}>
                          {benefit.taxable ? 'Taxable' : 'Non-taxable'}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-gray-900">
                        {formatCurrency(benefit.amount, salaryData.currency)}
                      </p>
                      <p className="text-sm text-gray-500">per month</p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-900">Total Monthly Benefits</span>
                  <span className="text-xl font-bold text-gray-900">
                    {formatCurrency(benefits.reduce((sum, b) => sum + b.amount, 0), salaryData.currency)}
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Taxes Tab */}
      {activeTab === 'taxes' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Tax Breakdown</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">PAYE Tax</span>
                    <span className="font-medium">{formatCurrency(currentPayslip.deductions.paye, salaryData.currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">NSSF (6%)</span>
                    <span className="font-medium">{formatCurrency(currentPayslip.deductions.nssf, salaryData.currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">NHIF/SHA (2.75%)</span>
                    <span className="font-medium">{formatCurrency(currentPayslip.deductions.nhif, salaryData.currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Housing Levy (1.5%)</span>
                    <span className="font-medium">{formatCurrency(currentPayslip.deductions.housing_levy, salaryData.currency)}</span>
                  </div>
                  <div className="border-t pt-3 flex justify-between font-medium">
                    <span>Total Tax & Deductions</span>
                    <span>{formatCurrency(totalDeductions, salaryData.currency)}</span>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Tax Information</h3>
                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">Kenya Tax Brackets</h4>
                    <div className="space-y-1 text-sm text-blue-800">
                      <p>• 0 - 288,000: 10%</p>
                      <p>• 288,001 - 388,000: 25%</p>
                      <p>• 388,001 - 6,000,000: 30%</p>
                      <p>• 6,000,001 - 9,600,000: 32.5%</p>
                      <p>• Above 9,600,000: 35%</p>
                    </div>
                  </div>
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-900 mb-2">Tax Relief</h4>
                    <div className="space-y-1 text-sm text-green-800">
                      <p>• Personal Relief: KSH 2,400/month</p>
                      <p>• Insurance Relief: Available</p>
                      <p>• NSSF Relief: KSH 200/month</p>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      )}

      {/* Bank Details Tab */}
      {activeTab === 'bank' && (
        <div className="space-y-6">
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Bank Account Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Building className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Bank Name</p>
                      <p className="font-medium text-gray-900">{salaryData.bank_name}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <CreditCard className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Account Number</p>
                      <p className="font-medium text-gray-900">{salaryData.bank_account}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Calendar className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Next Pay Date</p>
                      <p className="font-medium text-gray-900">January 31, 2025</p>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-3">Payment Schedule</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Frequency:</span>
                      <span className="font-medium">Monthly</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Pay Day:</span>
                      <span className="font-medium">Last working day</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Currency:</span>
                      <span className="font-medium">{salaryData.currency}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Bonuses Tab */}
      {activeTab === 'bonuses' && (
        <div className="space-y-6">
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Bonus History</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Period
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {bonuses.map((bonus) => (
                      <tr key={bonus.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {bonus.type}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(bonus.amount, salaryData.currency)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {bonus.period}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(bonus.date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={cn(
                            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                            bonus.status === 'Paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                          )}>
                            {bonus.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <div className="mt-4 p-4 bg-green-50 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-green-900">Total Bonuses (2024)</span>
                  <span className="text-xl font-bold text-green-900">
                    {formatCurrency(bonuses.filter(b => b.status === 'Paid').reduce((sum, b) => sum + b.amount, 0), salaryData.currency)}
                  </span>
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Overtime Tab */}
      {activeTab === 'overtime' && (
        <div className="space-y-6">
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Overtime Records</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Hours
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Rate
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {overtimeRecords.map((record) => (
                      <tr key={record.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {formatDate(record.date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.hours}h
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(record.rate, salaryData.currency)}/hr
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(record.amount, salaryData.currency)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={cn(
                            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                            record.approved ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                          )}>
                            {record.approved ? 'Approved' : 'Pending'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Payslip Tab */}
      {activeTab === 'payslip' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Current Payslip Breakdown */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Current Payslip - {currentPayslip.pay_period}
                </h3>

                <div className="space-y-4">
                  {/* Earnings */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Earnings</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Basic Salary</span>
                        <span className="font-medium">{formatCurrency(currentPayslip.basic_salary, salaryData.currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">House Allowance</span>
                        <span className="font-medium">{formatCurrency(currentPayslip.allowances.house_allowance, salaryData.currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Transport Allowance</span>
                        <span className="font-medium">{formatCurrency(currentPayslip.allowances.transport_allowance, salaryData.currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Medical Allowance</span>
                        <span className="font-medium">{formatCurrency(currentPayslip.allowances.medical_allowance, salaryData.currency)}</span>
                      </div>
                      <div className="border-t pt-2 flex justify-between font-medium">
                        <span>Gross Salary</span>
                        <span>{formatCurrency(currentPayslip.gross_salary + totalAllowances, salaryData.currency)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Deductions */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Deductions</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">NSSF (6%)</span>
                        <span className="font-medium text-red-600">-{formatCurrency(currentPayslip.deductions.nssf, salaryData.currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">NHIF/SHA (2.75%)</span>
                        <span className="font-medium text-red-600">-{formatCurrency(currentPayslip.deductions.nhif, salaryData.currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Housing Levy (1.5%)</span>
                        <span className="font-medium text-red-600">-{formatCurrency(currentPayslip.deductions.housing_levy, salaryData.currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">PAYE Tax</span>
                        <span className="font-medium text-red-600">-{formatCurrency(currentPayslip.deductions.paye, salaryData.currency)}</span>
                      </div>
                      <div className="border-t pt-2 flex justify-between font-medium">
                        <span>Total Deductions</span>
                        <span className="text-red-600">-{formatCurrency(totalDeductions, salaryData.currency)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Net Salary */}
                  <div className="bg-green-50 p-3 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="font-medium text-green-800">Net Salary</span>
                      <span className="text-lg font-bold text-green-800">
                        {formatCurrency(currentPayslip.net_salary, salaryData.currency)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Payslip Actions */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Payslip Actions</h3>
                <div className="space-y-4">
                  <Button variant="primary" className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    Download Current Payslip
                  </Button>
                  <Button variant="secondary" className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Payslip Details
                  </Button>
                  <Button variant="secondary" className="w-full">
                    <FileText className="h-4 w-4 mr-2" />
                    Email Payslip
                  </Button>
                </div>

                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Payslip Information</h4>
                  <div className="space-y-1 text-sm text-blue-800">
                    <p>• Payslips are generated monthly</p>
                    <p>• Available for download 24/7</p>
                    <p>• Secure PDF format</p>
                    <p>• Email notifications sent</p>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      )}

      {/* Salary History Tab */}
      {activeTab === 'history' && (
        <div className="space-y-6">
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Salary History</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Period
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Basic Salary
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Gross Salary
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Net Salary
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Increment
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {salaryHistory.map((record, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {record.period}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(record.basic, salaryData.currency)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(record.gross, salaryData.currency)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(record.net, salaryData.currency)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          {record.increment > 0 ? (
                            <span className="text-green-600 font-medium">
                              +{formatCurrency(record.increment, salaryData.currency)}
                            </span>
                          ) : (
                            <span className="text-gray-500">-</span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <div className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">YTD Earnings</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(salaryHistory.reduce((sum, s) => sum + s.net, 0), salaryData.currency)}
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <BarChart3 className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Average Monthly</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(salaryHistory.reduce((sum, s) => sum + s.net, 0) / salaryHistory.length, salaryData.currency)}
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Gift className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Bonuses</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(bonuses.filter(b => b.status === 'Paid').reduce((sum, b) => sum + b.amount, 0), salaryData.currency)}
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          <Card>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Salary Trends</h3>
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">Salary Analytics</h4>
                <p className="text-gray-600 mb-4">
                  Detailed salary trends and analytics will be displayed here.
                </p>
                <Button variant="primary">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Detailed Analytics
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default StaffSalaryPage;
