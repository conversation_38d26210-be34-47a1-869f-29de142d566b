8a3f9ee950da03656bae4a92f4fc9955
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _utils = require("../../lib/utils");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const Button = ({ variant = 'primary', size = 'md', disabled = false, loading = false, onClick, children, className, type = 'button', title, ...props })=>{
    const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
    const variantClasses = {
        primary: 'bg-orange-500 hover:bg-orange-600 text-white focus:ring-orange-500 border-b-4 border-orange-600',
        secondary: 'bg-gray-100 hover:bg-gray-200 text-gray-900 focus:ring-gray-500 border border-gray-300',
        outline: 'border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 focus:ring-gray-500',
        ghost: 'hover:bg-gray-100 text-gray-700 focus:ring-gray-500',
        danger: 'bg-red-500 hover:bg-red-600 text-white focus:ring-red-500 border-b-4 border-red-600',
        success: 'bg-green-500 hover:bg-green-600 text-white focus:ring-green-500 border-b-4 border-green-600',
        warning: 'bg-yellow-500 hover:bg-yellow-600 text-white focus:ring-yellow-500 border-b-4 border-yellow-600'
    };
    const sizeClasses = {
        sm: 'px-3 py-1.5 text-sm',
        md: 'px-4 py-2 text-sm',
        lg: 'px-6 py-3 text-base'
    };
    const classes = (0, _utils.cn)(baseClasses, variantClasses[variant], sizeClasses[size], className);
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
        type: type,
        className: classes,
        disabled: disabled || loading,
        onClick: onClick,
        title: title,
        ...props,
        children: [
            loading && /*#__PURE__*/ (0, _jsxruntime.jsxs)("svg", {
                className: "animate-spin -ml-1 mr-2 h-4 w-4",
                xmlns: "http://www.w3.org/2000/svg",
                fill: "none",
                viewBox: "0 0 24 24",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("circle", {
                        className: "opacity-25",
                        cx: "12",
                        cy: "12",
                        r: "10",
                        stroke: "currentColor",
                        strokeWidth: "4"
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("path", {
                        className: "opacity-75",
                        fill: "currentColor",
                        d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    })
                ]
            }),
            children
        ]
    });
};
const _default = Button;

//# sourceMappingURL=data:application/json;base64,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