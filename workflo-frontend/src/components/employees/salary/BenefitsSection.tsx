'use client';

import React, { useState, useEffect } from 'react';
import {
  Heart,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  MoreHorizontal,
  Shield,
  Car,
  Home,
  GraduationCap,
  Plane,
  Baby,
  Calendar,
  DollarSign,
  Percent,
  X,
  Save
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { cn, formatDate, formatCurrency } from '@/lib/utils';

interface Benefit {
  id: number;
  employee_id: number;
  benefit_type: 'health_insurance' | 'dental_insurance' | 'vision_insurance' | 'life_insurance' |
                'retirement_401k' | 'car_allowance' | 'housing_allowance' | 'education_allowance' |
                'vacation_days' | 'sick_days' | 'maternity_leave' | 'gym_membership' | 'other';
  benefit_name: string;
  description: string;
  value_type: 'fixed_amount' | 'percentage' | 'days' | 'coverage';
  value: number;
  currency?: string;
  provider?: string;
  start_date: string;
  end_date?: string;
  status: 'active' | 'inactive' | 'pending' | 'expired';
  employee_contribution: number;
  employer_contribution: number;
  coverage_details?: string;
  dependents_covered?: number;
  created_date: string;
  updated_date: string;
}

interface BenefitsSectionProps {
  employeeId: number;
  employeeName: string;
  isCurrentUser?: boolean;
}

const BenefitsSection: React.FC<BenefitsSectionProps> = ({
  employeeId,
  employeeName,
  isCurrentUser = false
}) => {
  const [benefits, setBenefits] = useState<Benefit[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedBenefit, setSelectedBenefit] = useState<Benefit | null>(null);

  // Mock benefits data
  const mockBenefits: Benefit[] = [
    {
      id: 1,
      employee_id: employeeId,
      benefit_type: 'health_insurance',
      benefit_name: 'Premium Health Insurance',
      description: 'Comprehensive health coverage including medical, dental, and vision',
      value_type: 'fixed_amount',
      value: 58500,
      currency: 'KSH',
      provider: 'Blue Cross Blue Shield',
      start_date: '2024-01-01',
      status: 'active',
      employee_contribution: 19500,
      employer_contribution: 39000,
      coverage_details: 'Individual + Family coverage',
      dependents_covered: 3,
      created_date: '2023-12-15T00:00:00Z',
      updated_date: '2024-01-01T00:00:00Z'
    },
    {
      id: 2,
      employee_id: employeeId,
      benefit_type: 'retirement_401k',
      benefit_name: '401(k) Retirement Plan',
      description: 'Company-matched retirement savings plan',
      value_type: 'percentage',
      value: 6,
      currency: 'USD',
      provider: 'Fidelity Investments',
      start_date: '2024-01-01',
      status: 'active',
      employee_contribution: 6,
      employer_contribution: 3,
      coverage_details: '50% company match up to 6% of salary',
      created_date: '2023-12-15T00:00:00Z',
      updated_date: '2024-01-01T00:00:00Z'
    },
    {
      id: 3,
      employee_id: employeeId,
      benefit_type: 'car_allowance',
      benefit_name: 'Monthly Car Allowance',
      description: 'Transportation allowance for work-related travel',
      value_type: 'fixed_amount',
      value: 500,
      currency: 'USD',
      start_date: '2024-01-01',
      status: 'active',
      employee_contribution: 0,
      employer_contribution: 500,
      coverage_details: 'Monthly transportation allowance',
      created_date: '2023-12-15T00:00:00Z',
      updated_date: '2024-01-01T00:00:00Z'
    },
    {
      id: 4,
      employee_id: employeeId,
      benefit_type: 'vacation_days',
      benefit_name: 'Annual Vacation Days',
      description: 'Paid time off for vacation and personal use',
      value_type: 'days',
      value: 25,
      start_date: '2024-01-01',
      end_date: '2024-12-31',
      status: 'active',
      employee_contribution: 0,
      employer_contribution: 0,
      coverage_details: '25 days annual vacation allowance',
      created_date: '2023-12-15T00:00:00Z',
      updated_date: '2024-01-01T00:00:00Z'
    }
  ];

  useEffect(() => {
    const loadBenefits = async () => {
      setLoading(true);
      setTimeout(() => {
        setBenefits(mockBenefits);
        setLoading(false);
      }, 1000);
    };

    loadBenefits();
  }, [employeeId]);

  const filteredBenefits = benefits.filter(benefit => {
    const matchesSearch = benefit.benefit_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         benefit.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = !filterType || benefit.benefit_type === filterType;
    const matchesStatus = !filterStatus || benefit.status === filterStatus;
    return matchesSearch && matchesType && matchesStatus;
  });

  const getBenefitIcon = (type: string) => {
    switch (type) {
      case 'health_insurance':
      case 'dental_insurance':
      case 'vision_insurance':
      case 'life_insurance': return <Shield className="h-5 w-5 text-blue-600" />;
      case 'retirement_401k': return <DollarSign className="h-5 w-5 text-green-600" />;
      case 'car_allowance': return <Car className="h-5 w-5 text-purple-600" />;
      case 'housing_allowance': return <Home className="h-5 w-5 text-orange-600" />;
      case 'education_allowance': return <GraduationCap className="h-5 w-5 text-indigo-600" />;
      case 'vacation_days':
      case 'sick_days': return <Calendar className="h-5 w-5 text-teal-600" />;
      case 'maternity_leave': return <Baby className="h-5 w-5 text-pink-600" />;
      case 'gym_membership': return <Heart className="h-5 w-5 text-red-600" />;
      default: return <Heart className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'expired': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatBenefitValue = (benefit: Benefit) => {
    switch (benefit.value_type) {
      case 'fixed_amount':
        return formatCurrency(benefit.value);
      case 'percentage':
        return `${benefit.value}%`;
      case 'days':
        return `${benefit.value} days`;
      case 'coverage':
        return benefit.coverage_details || 'Coverage provided';
      default:
        return benefit.value.toString();
    }
  };

  const calculateTotalBenefitValue = () => {
    return benefits
      .filter(b => b.status === 'active' && b.value_type === 'fixed_amount')
      .reduce((total, b) => total + b.employer_contribution, 0);
  };

  const handleCreateBenefit = () => {
    setSelectedBenefit(null);
    setShowCreateModal(true);
  };

  const handleEditBenefit = (benefit: Benefit) => {
    setSelectedBenefit(benefit);
    setShowEditModal(true);
  };

  const handleDeleteBenefit = (benefit: Benefit) => {
    if (confirm(`Are you sure you want to remove "${benefit.benefit_name}"?`)) {
      setBenefits(prev => prev.filter(b => b.id !== benefit.id));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Employee Benefits</h3>
          <p className="text-sm text-gray-600 mt-1">
            Manage benefits and perks for {employeeName}
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <div className="text-right">
            <div className="text-sm text-gray-600">Total Monthly Value</div>
            <div className="text-lg font-bold text-green-600">
              {formatCurrency(calculateTotalBenefitValue())}
            </div>
          </div>
          {!isCurrentUser && (
            <Button variant="primary" onClick={handleCreateBenefit}>
              <Plus className="h-4 w-4 mr-2" />
              Add Benefit
            </Button>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <div className="flex-1">
          <Input
            placeholder="Search benefits..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="h-4 w-4" />}
          />
        </div>
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="">All Types</option>
          <option value="health_insurance">Health Insurance</option>
          <option value="retirement_401k">401(k) Plan</option>
          <option value="car_allowance">Car Allowance</option>
          <option value="housing_allowance">Housing Allowance</option>
          <option value="vacation_days">Vacation Days</option>
          <option value="other">Other</option>
        </select>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
          <option value="pending">Pending</option>
          <option value="expired">Expired</option>
        </select>
      </div>

      {/* Benefits Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      ) : filteredBenefits.length === 0 ? (
        <div className="text-center py-12">
          <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No benefits found
          </h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || filterType || filterStatus
              ? 'Try adjusting your search criteria.'
              : 'No benefits have been assigned yet.'
            }
          </p>
          {!isCurrentUser && (
            <Button variant="primary" onClick={handleCreateBenefit}>
              <Plus className="h-4 w-4 mr-2" />
              Add Benefit
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredBenefits.map((benefit) => (
            <BenefitCard
              key={benefit.id}
              benefit={benefit}
              onEdit={() => handleEditBenefit(benefit)}
              onDelete={() => handleDeleteBenefit(benefit)}
              isCurrentUser={isCurrentUser}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Benefit Card Component
interface BenefitCardProps {
  benefit: Benefit;
  onEdit: () => void;
  onDelete: () => void;
  isCurrentUser: boolean;
}

const BenefitCard: React.FC<BenefitCardProps> = ({ benefit, onEdit, onDelete, isCurrentUser }) => {
  const [showActions, setShowActions] = useState(false);

  const getBenefitIcon = (type: string) => {
    switch (type) {
      case 'health_insurance':
      case 'dental_insurance':
      case 'vision_insurance':
      case 'life_insurance': return <Shield className="h-5 w-5 text-blue-600" />;
      case 'retirement_401k': return <DollarSign className="h-5 w-5 text-green-600" />;
      case 'car_allowance': return <Car className="h-5 w-5 text-purple-600" />;
      case 'housing_allowance': return <Home className="h-5 w-5 text-orange-600" />;
      case 'education_allowance': return <GraduationCap className="h-5 w-5 text-indigo-600" />;
      case 'vacation_days':
      case 'sick_days': return <Calendar className="h-5 w-5 text-teal-600" />;
      case 'maternity_leave': return <Baby className="h-5 w-5 text-pink-600" />;
      case 'gym_membership': return <Heart className="h-5 w-5 text-red-600" />;
      default: return <Heart className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'expired': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatBenefitValue = (benefit: Benefit) => {
    switch (benefit.value_type) {
      case 'fixed_amount':
        return formatCurrency(benefit.value);
      case 'percentage':
        return `${benefit.value}%`;
      case 'days':
        return `${benefit.value} days`;
      case 'coverage':
        return benefit.coverage_details || 'Coverage provided';
      default:
        return benefit.value.toString();
    }
  };

  return (
    <Card className="relative hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        {/* Actions Menu */}
        {!isCurrentUser && (
          <div className="absolute top-4 right-4">
            <div className="relative">
              <button
                onClick={() => setShowActions(!showActions)}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <MoreHorizontal className="h-4 w-4 text-gray-500" />
              </button>

              {showActions && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                  <div className="py-1">
                    <button
                      onClick={() => {
                        onEdit();
                        setShowActions(false);
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Benefit
                    </button>
                    <button
                      onClick={() => {
                        onDelete();
                        setShowActions(false);
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Remove Benefit
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Benefit Info */}
        <div className="mb-4">
          <div className="flex items-center mb-3">
            {getBenefitIcon(benefit.benefit_type)}
            <h4 className="text-lg font-medium text-gray-900 ml-3">
              {benefit.benefit_name}
            </h4>
          </div>

          <div className="flex items-center space-x-2 mb-3">
            <span className={cn(
              'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
              getStatusColor(benefit.status)
            )}>
              {benefit.status.toUpperCase()}
            </span>
            {benefit.provider && (
              <span className="text-xs text-gray-500">
                via {benefit.provider}
              </span>
            )}
          </div>

          <p className="text-sm text-gray-600 mb-4">
            {benefit.description}
          </p>
        </div>

        {/* Benefit Details */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Value:</span>
            <span className="text-lg font-bold text-green-600">
              {formatBenefitValue(benefit)}
            </span>
          </div>

          {benefit.value_type === 'fixed_amount' && (
            <>
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">Employee Contribution:</span>
                <span className="text-sm text-red-600">
                  {formatCurrency(benefit.employee_contribution)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">Employer Contribution:</span>
                <span className="text-sm text-green-600">
                  {formatCurrency(benefit.employer_contribution)}
                </span>
              </div>
            </>
          )}

          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-500">Start Date:</span>
            <span className="text-sm text-gray-900">
              {formatDate(benefit.start_date)}
            </span>
          </div>

          {benefit.end_date && (
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-500">End Date:</span>
              <span className="text-sm text-gray-900">
                {formatDate(benefit.end_date)}
              </span>
            </div>
          )}

          {benefit.coverage_details && (
            <div className="mt-3 p-3 bg-gray-50 rounded-lg">
              <p className="text-xs text-gray-600">
                {benefit.coverage_details}
              </p>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default BenefitsSection;
