f446cc844c112500996278f9735eb585
/**
 * Simple auth persistence without Zustand middleware
 * This prevents hydration issues while maintaining auth state
 */ "use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AuthPersistence", {
    enumerable: true,
    get: function() {
        return AuthPersistence;
    }
});
const AUTH_STORAGE_KEY = 'workflo-auth';
class AuthPersistence {
    static save(user, isAuthenticated) {
        if (typeof window === 'undefined') return;
        try {
            const state = {
                user,
                isAuthenticated
            };
            localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(state));
        } catch (error) {
            console.warn('Failed to save auth state:', error);
        }
    }
    static load() {
        if (typeof window === 'undefined') return null;
        try {
            const stored = localStorage.getItem(AUTH_STORAGE_KEY);
            if (!stored) return null;
            return JSON.parse(stored);
        } catch (error) {
            console.warn('Failed to load auth state:', error);
            return null;
        }
    }
    static clear() {
        if (typeof window === 'undefined') return;
        try {
            localStorage.removeItem(AUTH_STORAGE_KEY);
        } catch (error) {
            console.warn('Failed to clear auth state:', error);
        }
    }
}

//# sourceMappingURL=data:application/json;base64,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