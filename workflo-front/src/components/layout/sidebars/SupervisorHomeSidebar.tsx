'use client';

import React, { useMemo, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Home,
  Users,
  Calendar,
  Clock,
  CheckCircle,
  TrendingUp,
  BarChart3,
  Bell,
  AlertCircle,
  Activity,
  Target,
  Award,
  Zap,
  CalendarCheck,
  UserCheck,
  FileText,
  Settings,
  X,
  LogOut,
  ChevronRight
} from 'lucide-react';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials, formatDate } from '@/lib/utils';

interface SupervisorHomeSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const SupervisorHomeSidebar: React.FC<SupervisorHomeSidebarProps> = ({ isOpen, onClose }) => {
  const { user, logout } = useAuth();
  const pathname = usePathname();

  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  const handleLogout = useCallback(() => {
    logout();
    handleClose();
  }, [logout, handleClose]);

  // Dashboard quick actions
  const dashboardActions = useMemo(() => [
    {
      name: 'Approve Leave Requests',
      href: '/supervisor/manage/leave-management?action=approve',
      icon: CalendarCheck,
      description: 'Review and approve pending leave requests',
      badge: '3'
    },
    {
      name: 'Manage Overtime',
      href: '/supervisor/manage/overtime?action=apply',
      icon: Clock,
      description: 'Handle overtime applications and approvals',
      badge: '2'
    },
    {
      name: 'View Team Performance',
      href: '/supervisor/manage?view=performance',
      icon: BarChart3,
      description: 'Check team metrics and performance data'
    },
    {
      name: 'Team Directory',
      href: '/supervisor/manage?view=team',
      icon: Users,
      description: 'Access team member information'
    },
    {
      name: 'Check Notifications',
      href: '/supervisor/notifications',
      icon: Bell,
      description: 'View important notifications and alerts',
      badge: '5'
    },
    {
      name: 'Schedule Team Meeting',
      href: '/supervisor/manage?action=meeting',
      icon: Calendar,
      description: 'Organize team meetings and events'
    }
  ], []);

  // Team overview sections
  const teamSections = useMemo(() => [
    {
      name: 'Team Statistics',
      icon: BarChart3,
      stats: [
        { label: 'Total Members', value: '12', trend: 'stable' },
        { label: 'Present Today', value: '10', trend: 'up' },
        { label: 'On Leave', value: '2', trend: 'down' }
      ]
    },
    {
      name: 'Pending Approvals',
      icon: Clock,
      stats: [
        { label: 'Leave Requests', value: '3', trend: 'up' },
        { label: 'Overtime Apps', value: '2', trend: 'stable' },
        { label: 'Time Off', value: '1', trend: 'down' }
      ]
    },
    {
      name: 'Performance Metrics',
      icon: TrendingUp,
      stats: [
        { label: 'Team Score', value: '87%', trend: 'up' },
        { label: 'Completed Tasks', value: '45', trend: 'up' },
        { label: 'Deadlines Met', value: '92%', trend: 'stable' }
      ]
    }
  ], []);

  // Recent activities
  const recentActivities = useMemo(() => [
    {
      id: 1,
      type: 'leave_request',
      message: 'John Doe submitted leave request',
      time: '2 hours ago',
      icon: Calendar,
      color: 'text-blue-600'
    },
    {
      id: 2,
      type: 'overtime',
      message: 'Sarah Wilson applied for overtime',
      time: '4 hours ago',
      icon: Clock,
      color: 'text-orange-600'
    },
    {
      id: 3,
      type: 'performance',
      message: 'Team completed Q4 reviews',
      time: '1 day ago',
      icon: Award,
      color: 'text-green-600'
    },
    {
      id: 4,
      type: 'attendance',
      message: 'Mike Johnson checked in late',
      time: '2 days ago',
      icon: UserCheck,
      color: 'text-red-600'
    }
  ], []);

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
        onClick={handleClose}
      />

      {/* Sidebar */}
      <div
        className={cn(
          "fixed left-0 top-0 h-full bg-white shadow-xl z-30 transform transition-transform duration-300 ease-in-out overflow-y-auto",
          "md:relative md:translate-x-0 md:shadow-lg md:top-0 md:h-full",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
        style={{ width: '368px' }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-orange-500 to-orange-600">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
              <Home className="h-6 w-6 text-orange-500" />
            </div>
            <div>
              <h2 className="text-white font-semibold">Supervisor Dashboard</h2>
              <p className="text-orange-100 text-sm">Team Management Hub</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 rounded-lg text-white hover:bg-white hover:bg-opacity-20 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* User Info */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
              <span className="text-white font-medium">
                {getInitials(user?.first_name || '', user?.last_name || '')}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="font-medium text-gray-900 truncate">
                {user?.first_name} {user?.last_name}
              </p>
              <p className="text-sm text-gray-500 capitalize">{user?.role}</p>
              <p className="text-xs text-gray-400">{formatDate(new Date().toISOString(), 'EEE, MMM dd')}</p>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-2">
            {dashboardActions.map((action, index) => {
              const Icon = action.icon;
              return (
                <Link
                  key={index}
                  href={action.href}
                  onClick={handleClose}
                  className="flex items-center justify-between p-3 rounded-lg hover:bg-orange-50 transition-colors group"
                >
                  <div className="flex items-center">
                    <Icon className="h-5 w-5 mr-3 text-orange-600" />
                    <div>
                      <div className="font-medium text-sm text-gray-900">{action.name}</div>
                      <div className="text-xs text-gray-500">{action.description}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {action.badge && (
                      <span className="bg-orange-100 text-orange-800 text-xs font-medium px-2 py-1 rounded-full">
                        {action.badge}
                      </span>
                    )}
                    <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-orange-600" />
                  </div>
                </Link>
              );
            })}
          </div>
        </div>

        {/* Team Overview */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-4">Team Overview</h3>
          <div className="space-y-4">
            {teamSections.map((section, index) => {
              const Icon = section.icon;
              return (
                <div key={index} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <Icon className="h-5 w-5 mr-2 text-orange-600" />
                    <h4 className="font-medium text-gray-900">{section.name}</h4>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    {section.stats.map((stat, statIndex) => (
                      <div key={statIndex} className="text-center">
                        <div className="text-lg font-semibold text-gray-900">{stat.value}</div>
                        <div className="text-xs text-gray-500">{stat.label}</div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Recent Activities */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-4">Recent Activities</h3>
          <div className="space-y-3">
            {recentActivities.map((activity) => {
              const Icon = activity.icon;
              return (
                <div key={activity.id} className="flex items-start space-x-3">
                  <Icon className={cn("h-4 w-4 mt-1", activity.color)} />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900">{activity.message}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 mt-auto">
          <div className="space-y-2">
            <Link
              href="/supervisor/settings"
              onClick={handleClose}
              className="flex items-center p-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Link>
            <button
              onClick={handleLogout}
              className="flex items-center w-full p-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default SupervisorHomeSidebar;
