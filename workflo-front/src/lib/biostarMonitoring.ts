import { biostarConfig, testBiostarConnectivity } from './biostarConfig';

// Metrics interface
export interface BiostarMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  lastSuccessfulConnection: string | null;
  lastFailedConnection: string | null;
  uptime: number;
  downtimeEvents: DowntimeEvent[];
  deviceStatus: DeviceStatusMetrics;
}

export interface DowntimeEvent {
  startTime: string;
  endTime?: string;
  duration?: number;
  reason: string;
}

export interface DeviceStatusMetrics {
  totalDevices: number;
  onlineDevices: number;
  offlineDevices: number;
  lastUpdated: string;
}

// Monitoring service
class BiostarMonitoringService {
  private metrics: BiostarMetrics;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isMonitoring = false;
  private listeners: ((metrics: BiostarMetrics) => void)[] = [];

  constructor() {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      lastSuccessfulConnection: null,
      lastFailedConnection: null,
      uptime: 0,
      downtimeEvents: [],
      deviceStatus: {
        totalDevices: 0,
        onlineDevices: 0,
        offlineDevices: 0,
        lastUpdated: new Date().toISOString()
      }
    };
  }

  // Start monitoring
  startMonitoring(interval: number = biostarConfig.pollingInterval): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, interval);

    console.log(`BioStar monitoring started with ${interval}ms interval`);
  }

  // Stop monitoring
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('BioStar monitoring stopped');
  }

  // Perform health check
  private async performHealthCheck(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const result = await testBiostarConnectivity();
      this.recordMetrics(result.success, result.responseTime, result.error);
      
      if (biostarConfig.enableLogging) {
        console.log(`BioStar health check: ${result.success ? 'SUCCESS' : 'FAILED'} (${result.responseTime}ms)`);
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.recordMetrics(false, responseTime, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  // Record metrics
  private recordMetrics(success: boolean, responseTime: number, error?: string): void {
    this.metrics.totalRequests++;
    
    if (success) {
      this.metrics.successfulRequests++;
      this.metrics.lastSuccessfulConnection = new Date().toISOString();
      
      // End any ongoing downtime
      const ongoingDowntime = this.metrics.downtimeEvents.find(event => !event.endTime);
      if (ongoingDowntime) {
        ongoingDowntime.endTime = new Date().toISOString();
        ongoingDowntime.duration = new Date(ongoingDowntime.endTime).getTime() - 
                                   new Date(ongoingDowntime.startTime).getTime();
      }
    } else {
      this.metrics.failedRequests++;
      this.metrics.lastFailedConnection = new Date().toISOString();
      
      // Start new downtime event if not already ongoing
      const ongoingDowntime = this.metrics.downtimeEvents.find(event => !event.endTime);
      if (!ongoingDowntime) {
        this.metrics.downtimeEvents.push({
          startTime: new Date().toISOString(),
          reason: error || 'Connection failed'
        });
      }
    }

    // Update average response time
    this.metrics.averageResponseTime = this.calculateAverageResponseTime(responseTime);
    
    // Calculate uptime percentage
    this.metrics.uptime = (this.metrics.successfulRequests / this.metrics.totalRequests) * 100;

    // Notify listeners
    this.notifyListeners();
  }

  // Calculate average response time
  private calculateAverageResponseTime(newResponseTime: number): number {
    const totalRequests = this.metrics.totalRequests;
    const currentAverage = this.metrics.averageResponseTime;
    
    return ((currentAverage * (totalRequests - 1)) + newResponseTime) / totalRequests;
  }

  // Update device status
  updateDeviceStatus(totalDevices: number, onlineDevices: number): void {
    this.metrics.deviceStatus = {
      totalDevices,
      onlineDevices,
      offlineDevices: totalDevices - onlineDevices,
      lastUpdated: new Date().toISOString()
    };
    this.notifyListeners();
  }

  // Get current metrics
  getMetrics(): BiostarMetrics {
    return { ...this.metrics };
  }

  // Add listener for metrics updates
  addListener(callback: (metrics: BiostarMetrics) => void): void {
    this.listeners.push(callback);
  }

  // Remove listener
  removeListener(callback: (metrics: BiostarMetrics) => void): void {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  // Notify all listeners
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.getMetrics());
      } catch (error) {
        console.error('Error notifying metrics listener:', error);
      }
    });
  }

  // Reset metrics
  resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      lastSuccessfulConnection: null,
      lastFailedConnection: null,
      uptime: 0,
      downtimeEvents: [],
      deviceStatus: {
        totalDevices: 0,
        onlineDevices: 0,
        offlineDevices: 0,
        lastUpdated: new Date().toISOString()
      }
    };
    this.notifyListeners();
  }

  // Export metrics for external monitoring
  exportMetrics(): string {
    return JSON.stringify({
      ...this.metrics,
      exportedAt: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version
    }, null, 2);
  }

  // Get health status
  getHealthStatus(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    uptime: number;
    lastCheck: string | null;
    issues: string[];
  } {
    const issues: string[] = [];
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    // Check uptime
    if (this.metrics.uptime < 95) {
      status = 'unhealthy';
      issues.push(`Low uptime: ${this.metrics.uptime.toFixed(2)}%`);
    } else if (this.metrics.uptime < 99) {
      status = 'degraded';
      issues.push(`Degraded uptime: ${this.metrics.uptime.toFixed(2)}%`);
    }

    // Check response time
    if (this.metrics.averageResponseTime > 5000) {
      status = 'unhealthy';
      issues.push(`High response time: ${this.metrics.averageResponseTime}ms`);
    } else if (this.metrics.averageResponseTime > 2000) {
      if (status === 'healthy') status = 'degraded';
      issues.push(`Elevated response time: ${this.metrics.averageResponseTime}ms`);
    }

    // Check recent failures
    const recentFailures = this.metrics.downtimeEvents.filter(event => {
      const eventTime = new Date(event.startTime).getTime();
      const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
      return eventTime > fiveMinutesAgo;
    });

    if (recentFailures.length > 0) {
      if (status === 'healthy') status = 'degraded';
      issues.push(`${recentFailures.length} recent failure(s)`);
    }

    return {
      status,
      uptime: this.metrics.uptime,
      lastCheck: this.metrics.lastSuccessfulConnection || this.metrics.lastFailedConnection,
      issues
    };
  }
}

// Export singleton instance
export const biostarMonitoring = new BiostarMonitoringService();

// Auto-start monitoring in production
if (typeof window !== 'undefined' && biostarConfig.enableMetrics) {
  biostarMonitoring.startMonitoring();
}
