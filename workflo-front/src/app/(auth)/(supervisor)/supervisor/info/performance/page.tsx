'use client';

import React, { useState } from 'react';
import {
  Star,
  TrendingUp,
  Target,
  Award,
  Calendar,
  Eye,
  Plus,
  BarChart3,
  Users,
  CheckCircle
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { formatDate } from '@/lib/utils';

const SupervisorPerformancePage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  // Mock performance data
  const performanceOverview = {
    overall_rating: 4.5,
    last_review_date: '2024-01-15',
    next_review_date: '2025-01-15',
    goals_completed: 8,
    goals_total: 10,
    team_performance: 87,
    leadership_score: 4.3
  };

  const performanceHistory = [
    {
      id: 1,
      period: '2024',
      overall_rating: 4.5,
      leadership: 4.3,
      communication: 4.6,
      problem_solving: 4.4,
      team_management: 4.7,
      reviewer: '<PERSON>',
      review_date: '2024-01-15',
      status: 'Completed'
    },
    {
      id: 2,
      period: '2023',
      overall_rating: 4.2,
      leadership: 4.0,
      communication: 4.3,
      problem_solving: 4.1,
      team_management: 4.4,
      reviewer: '<PERSON>',
      review_date: '2023-01-15',
      status: 'Completed'
    }
  ];

  const currentGoals = [
    {
      id: 1,
      title: 'Improve Team Productivity',
      description: 'Increase team productivity by 15% through process optimization',
      target_date: '2024-12-31',
      progress: 75,
      status: 'In Progress',
      category: 'Team Management'
    },
    {
      id: 2,
      title: 'Complete Leadership Training',
      description: 'Finish advanced leadership certification program',
      target_date: '2024-06-30',
      progress: 100,
      status: 'Completed',
      category: 'Professional Development'
    },
    {
      id: 3,
      title: 'Reduce Employee Turnover',
      description: 'Decrease department turnover rate to below 10%',
      target_date: '2024-12-31',
      progress: 60,
      status: 'In Progress',
      category: 'Team Management'
    }
  ];

  const achievements = [
    {
      id: 1,
      title: 'Leadership Excellence Award',
      description: 'Recognized for outstanding leadership and team development',
      date: '2024-03-15',
      type: 'Award'
    },
    {
      id: 2,
      title: 'Team Performance Milestone',
      description: 'Led team to achieve 120% of quarterly targets',
      date: '2024-01-30',
      type: 'Milestone'
    },
    {
      id: 3,
      title: 'Process Improvement Initiative',
      description: 'Implemented new workflow that increased efficiency by 25%',
      date: '2023-11-20',
      type: 'Initiative'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in progress':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Star },
    { id: 'goals', label: 'Goals & Objectives', icon: Target },
    { id: 'reviews', label: 'Performance Reviews', icon: BarChart3 },
    { id: 'achievements', label: 'Achievements', icon: Award }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Performance Management</h1>
          <p className="text-gray-600 mt-1">
            Track your performance, goals, and professional development
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Eye className="h-4 w-4 mr-2" />
            View Report
          </Button>
          <Button variant="primary">
            <Plus className="h-4 w-4 mr-2" />
            Set New Goal
          </Button>
        </div>
      </div>

      {/* Performance Overview Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="h-5 w-5 text-yellow-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Overall Rating</p>
                <p className="text-lg font-semibold text-gray-900">
                  {performanceOverview.overall_rating}/5.0
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Target className="h-5 w-5 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Goals Completed</p>
                <p className="text-lg font-semibold text-gray-900">
                  {performanceOverview.goals_completed}/{performanceOverview.goals_total}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Team Performance</p>
                <p className="text-lg font-semibold text-gray-900">
                  {performanceOverview.team_performance}%
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-5 w-5 text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Leadership Score</p>
                <p className="text-lg font-semibold text-gray-900">
                  {performanceOverview.leadership_score}/5.0
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Current Performance</h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm text-gray-600">Overall Rating</span>
                      <span className="text-sm font-medium">{performanceOverview.overall_rating}/5.0</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-yellow-500 h-2 rounded-full"
                        style={{ width: `${(performanceOverview.overall_rating / 5) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm text-gray-600">Leadership Score</span>
                      <span className="text-sm font-medium">{performanceOverview.leadership_score}/5.0</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ width: `${(performanceOverview.leadership_score / 5) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm text-gray-600">Team Performance</span>
                      <span className="text-sm font-medium">{performanceOverview.team_performance}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: `${performanceOverview.team_performance}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Review Schedule</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">Last Review</p>
                      <p className="text-sm text-gray-600">
                        {formatDate(performanceOverview.last_review_date, 'MMM dd, yyyy')}
                      </p>
                    </div>
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  </div>
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">Next Review</p>
                      <p className="text-sm text-gray-600">
                        {formatDate(performanceOverview.next_review_date, 'MMM dd, yyyy')}
                      </p>
                    </div>
                    <Calendar className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {activeTab === 'goals' && (
          <div className="space-y-6">
            {currentGoals.map((goal) => (
              <Card key={goal.id}>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{goal.title}</h3>
                      <p className="text-gray-600">{goal.description}</p>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(goal.status)}`}>
                      {goal.status}
                    </span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                      <p className="text-gray-900">{goal.category}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Target Date</label>
                      <p className="text-gray-900">{formatDate(goal.target_date, 'MMM dd, yyyy')}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Progress</label>
                      <div className="flex items-center">
                        <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                          <div
                            className="bg-orange-500 h-2 rounded-full"
                            style={{ width: `${goal.progress}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium text-gray-900">{goal.progress}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {activeTab === 'reviews' && (
          <div className="space-y-6">
            {performanceHistory.map((review) => (
              <Card key={review.id}>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      Performance Review - {review.period}
                    </h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(review.status)}`}>
                      {review.status}
                    </span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Overall</p>
                      <p className="text-2xl font-bold text-gray-900">{review.overall_rating}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Leadership</p>
                      <p className="text-2xl font-bold text-blue-600">{review.leadership}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Communication</p>
                      <p className="text-2xl font-bold text-green-600">{review.communication}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Problem Solving</p>
                      <p className="text-2xl font-bold text-purple-600">{review.problem_solving}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Team Management</p>
                      <p className="text-2xl font-bold text-orange-600">{review.team_management}</p>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Reviewed by: {review.reviewer}</span>
                    <span>Date: {formatDate(review.review_date, 'MMM dd, yyyy')}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {activeTab === 'achievements' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {achievements.map((achievement) => (
              <Card key={achievement.id}>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <Award className="h-6 w-6 text-yellow-500 mr-3" />
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      {achievement.type}
                    </span>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{achievement.title}</h3>
                  <p className="text-gray-600 mb-4">{achievement.description}</p>
                  <p className="text-sm text-gray-500">
                    {formatDate(achievement.date, 'MMM dd, yyyy')}
                  </p>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SupervisorPerformancePage;
