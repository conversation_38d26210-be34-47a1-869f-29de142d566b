'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  Calendar,
  Users,
  DollarSign,
  ArrowLeft,
  Edit,
  Trash2,
  Download,
  CheckCircle,
  Clock,
  AlertTriangle,
  FileText,
  TrendingUp,
  Building,
  Eye,
  Send,
  MoreHorizontal
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { cn, formatDate, formatCurrency } from '@/lib/utils';
import PayCycleModal from '@/components/payroll/PayCycleModal';
import DeleteConfirmationModal from '@/components/payroll/DeleteConfirmationModal';
import { PayCycle } from '@/types';

interface PayslipSummary {
  id: number;
  employee_name: string;
  employee_code: string;
  gross_salary: number;
  net_salary: number;
  total_deductions: number;
  status: 'generated' | 'sent' | 'viewed';
}

const PayCycleDetailPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const payCycleId = parseInt(params.id as string);

  const [payCycle, setPayCycle] = useState<PayCycle | null>(null);
  const [payslips, setPayslips] = useState<PayslipSummary[]>([]);
  const [allPayCycles, setAllPayCycles] = useState<PayCycle[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  useEffect(() => {
    fetchPayCycleDetails();
    fetchAllPayCycles();
  }, [payCycleId]);

  const fetchAllPayCycles = async () => {
    try {
      // Mock data for validation
      const mockPayCycles: PayCycle[] = [
        {
          id: 1,
          pay_period: 'January 2024',
          start_date: '2024-01-01',
          end_date: '2024-01-31',
          pay_date: '2024-01-31',
          paid: true,
          total_employees: 156,
          total_amount: 12500000,
          created_at: '2024-01-01'
        },
        {
          id: 2,
          pay_period: 'February 2024',
          start_date: '2024-02-01',
          end_date: '2024-02-29',
          pay_date: '2024-02-29',
          paid: false,
          total_employees: 158,
          total_amount: 12800000,
          created_at: '2024-02-01'
        },
        {
          id: 3,
          pay_period: 'March 2024',
          start_date: '2024-03-01',
          end_date: '2024-03-31',
          pay_date: '2024-03-31',
          paid: false,
          total_employees: 160,
          total_amount: 13000000,
          created_at: '2024-03-01'
        }
      ];
      setAllPayCycles(mockPayCycles);
    } catch (error) {
      console.error('Error fetching pay cycles:', error);
    }
  };

  const fetchPayCycleDetails = async () => {
    try {
      // Mock pay cycle data
      const mockPayCycle: PayCycle = {
        id: payCycleId,
        pay_period: 'January 2024',
        start_date: '2024-01-01',
        end_date: '2024-01-31',
        pay_date: '2024-01-31',
        paid: true,
        total_employees: 156,
        total_amount: 12500000,
        created_at: '2024-01-01'
      };

      // Mock payslips for this pay cycle
      const mockPayslips: PayslipSummary[] = [
        {
          id: 1,
          employee_name: 'John Doe',
          employee_code: 'EMP001',
          gross_salary: 135000,
          net_salary: 95000,
          total_deductions: 40000,
          status: 'sent'
        },
        {
          id: 2,
          employee_name: 'Jane Smith',
          employee_code: 'EMP002',
          gross_salary: 165000,
          net_salary: 118000,
          total_deductions: 47000,
          status: 'viewed'
        },
        {
          id: 3,
          employee_name: 'Michael Johnson',
          employee_code: 'EMP003',
          gross_salary: 125000,
          net_salary: 89000,
          total_deductions: 36000,
          status: 'generated'
        },
        {
          id: 4,
          employee_name: 'Sarah Wilson',
          employee_code: 'EMP004',
          gross_salary: 105000,
          net_salary: 77000,
          total_deductions: 28000,
          status: 'sent'
        },
        {
          id: 5,
          employee_name: 'David Brown',
          employee_code: 'EMP005',
          gross_salary: 145000,
          net_salary: 103000,
          total_deductions: 42000,
          status: 'viewed'
        }
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      setPayCycle(mockPayCycle);
      setPayslips(mockPayslips);
    } catch (error) {
      console.error('Error fetching pay cycle details:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    setIsEditModalOpen(true);
  };

  const handleDelete = () => {
    setIsDeleteModalOpen(true);
  };

  const handleSavePayCycle = async (payCycleData: PayCycle) => {
    try {
      // Update the pay cycle
      setPayCycle(prev => prev ? { ...payCycleData, id: prev.id, created_at: prev.created_at } : null);
    } catch (error) {
      console.error('Error updating pay cycle:', error);
      throw error;
    }
  };

  const handleConfirmDelete = async () => {
    try {
      // Navigate back to pay cycles list after deletion
      router.push('/payroll/pay-cycles');
    } catch (error) {
      console.error('Error deleting pay cycle:', error);
      throw error;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'generated': return 'bg-blue-100 text-blue-800';
      case 'sent': return 'bg-yellow-100 text-yellow-800';
      case 'viewed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'generated': return FileText;
      case 'sent': return Send;
      case 'viewed': return CheckCircle;
      default: return AlertTriangle;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading pay cycle details...</p>
        </div>
      </div>
    );
  }

  if (!payCycle) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Pay Cycle Not Found</h2>
          <p className="text-gray-600 mb-6">The requested pay cycle could not be found.</p>
          <Button variant="primary" onClick={() => router.push('/payroll/pay-cycles')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Pay Cycles
          </Button>
        </div>
      </div>
    );
  }

  const StatusIcon = payCycle.paid ? CheckCircle : Clock;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => router.push('/payroll/pay-cycles')}
            className="p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{payCycle.pay_period}</h1>
            <p className="text-sm text-gray-600 mt-1">
              Pay cycle details and employee payslips
            </p>
          </div>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button variant="outline" onClick={handleEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="danger" onClick={handleDelete}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Pay Cycle Overview */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900">Pay Cycle Overview</h2>
            <span className={cn(
              'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
              payCycle.paid ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
            )}>
              <StatusIcon className="h-4 w-4 mr-1" />
              {payCycle.paid ? 'Completed' : 'Pending'}
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Calendar className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Period</p>
                <p className="text-lg font-semibold text-gray-900">
                  {formatDate(payCycle.start_date)} - {formatDate(payCycle.end_date)}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Pay Date</p>
                <p className="text-lg font-semibold text-gray-900">
                  {formatDate(payCycle.pay_date)}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <Users className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Employees</p>
                <p className="text-lg font-semibold text-gray-900">
                  {payCycle.total_employees}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Total Amount</p>
                <p className="text-lg font-semibold text-gray-900">
                  {formatCurrency(payCycle.total_amount, 'KSH')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Payslips Summary */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900">Employee Payslips</h2>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">
                {payslips.length} payslips generated
              </span>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Employee
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Gross Salary
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Deductions
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Net Salary
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {payslips.map((payslip) => {
                  const StatusIcon = getStatusIcon(payslip.status);

                  return (
                    <tr key={payslip.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {payslip.employee_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {payslip.employee_code}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {formatCurrency(payslip.gross_salary, 'KSH')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-red-600">
                          -{formatCurrency(payslip.total_deductions, 'KSH')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-bold text-green-600">
                          {formatCurrency(payslip.net_salary, 'KSH')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={cn(
                          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                          getStatusColor(payslip.status)
                        )}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {payslip.status.charAt(0).toUpperCase() + payslip.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <Button variant="ghost" size="sm" title="View Payslip">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" title="Download">
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" title="More Options">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </Card>

      {/* Modals */}
      <PayCycleModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSave={handleSavePayCycle}
        payCycle={payCycle}
        existingPayCycles={allPayCycles.filter(cycle => cycle.id !== payCycle.id)}
        mode="edit"
      />

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Pay Cycle"
        message="Are you sure you want to delete this pay cycle? This action cannot be undone."
        itemName={`${payCycle.pay_period} (${formatDate(payCycle.start_date)} - ${formatDate(payCycle.end_date)})`}
        warningMessage={payCycle.paid ? "This pay cycle has already been paid. Deleting it may affect payroll records and employee payment history." : "This will also delete all associated payslips for this pay cycle."}
      />
    </div>
  );
};

export default PayCycleDetailPage;
