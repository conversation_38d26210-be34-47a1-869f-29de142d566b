'use client';

import React, { useState } from 'react';
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Users,
  Plus,
  Eye,
  Filter,
  Download,
  BarChart3,
  Calendar,
  DollarSign,
  TrendingUp
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { formatDate } from '@/lib/utils';

const SupervisorOvertimeManagementPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('pending');

  // Mock overtime requests data
  const overtimeRequests = [
    {
      id: 1,
      employee: {
        name: '<PERSON>',
        id: 'EMP002',
        department: 'Operations',
        hourly_rate: 1500
      },
      type: 'Weekend Work',
      date: '2024-12-21',
      start_time: '09:00',
      end_time: '17:00',
      hours: 8,
      reason: 'Complete urgent project deliverables',
      status: 'Pending',
      applied_date: '2024-12-18',
      priority: 'high',
      project: 'Q4 System Upgrade'
    },
    {
      id: 2,
      employee: {
        name: '<PERSON>',
        id: 'EMP003',
        department: 'Operations',
        hourly_rate: 1200
      },
      type: 'Extended Hours',
      date: '2024-12-20',
      start_time: '17:00',
      end_time: '21:00',
      hours: 4,
      reason: 'Support weekend deployment',
      status: 'Pending',
      applied_date: '2024-12-19',
      priority: 'medium',
      project: 'System Deployment'
    },
    {
      id: 3,
      employee: {
        name: 'John Doe',
        id: 'EMP001',
        department: 'Operations',
        hourly_rate: 1800
      },
      type: 'Holiday Work',
      date: '2024-12-25',
      start_time: '10:00',
      end_time: '14:00',
      hours: 4,
      reason: 'Emergency system maintenance',
      status: 'Approved',
      applied_date: '2024-12-15',
      approved_date: '2024-12-16',
      priority: 'urgent',
      project: 'Emergency Maintenance'
    }
  ];

  const overtimeStats = {
    pending_requests: overtimeRequests.filter(r => r.status === 'Pending').length,
    approved_this_month: overtimeRequests.filter(r => r.status === 'Approved').length,
    total_hours_this_month: 45,
    estimated_cost: 67500
  };

  const teamOvertimeHistory = [
    {
      employee: 'Sarah Wilson',
      hours_this_month: 16,
      hours_last_month: 12,
      total_cost: 24000
    },
    {
      employee: 'Mike Johnson',
      hours_this_month: 12,
      hours_last_month: 8,
      total_cost: 14400
    },
    {
      employee: 'John Doe',
      hours_this_month: 8,
      hours_last_month: 15,
      total_cost: 14400
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return <CheckCircle className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-4 border-red-500';
      case 'high':
        return 'border-l-4 border-orange-500';
      case 'medium':
        return 'border-l-4 border-yellow-500';
      default:
        return 'border-l-4 border-blue-500';
    }
  };

  const filteredRequests = overtimeRequests.filter(request => {
    switch (activeTab) {
      case 'pending':
        return request.status === 'Pending';
      case 'approved':
        return request.status === 'Approved';
      case 'rejected':
        return request.status === 'Rejected';
      default:
        return true;
    }
  });

  const handleApprove = (requestId: number) => {
    console.log('Approving overtime request:', requestId);
    // TODO: Implement approval logic
  };

  const handleReject = (requestId: number) => {
    console.log('Rejecting overtime request:', requestId);
    // TODO: Implement rejection logic
  };

  const tabs = [
    { id: 'pending', label: 'Pending Requests', count: overtimeRequests.filter(r => r.status === 'Pending').length },
    { id: 'approved', label: 'Approved', count: overtimeRequests.filter(r => r.status === 'Approved').length },
    { id: 'all', label: 'All Requests', count: overtimeRequests.length },
    { id: 'analytics', label: 'Analytics', count: 0 }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Overtime Management</h1>
          <p className="text-gray-600 mt-1">
            Manage overtime applications, approvals, and track overtime hours
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button variant="primary">
            <Plus className="h-4 w-4 mr-2" />
            Apply for Team
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="h-5 w-5 text-yellow-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Pending Requests</p>
                <p className="text-lg font-semibold text-gray-900">{overtimeStats.pending_requests}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Approved This Month</p>
                <p className="text-lg font-semibold text-gray-900">{overtimeStats.approved_this_month}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <BarChart3 className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Total Hours</p>
                <p className="text-lg font-semibold text-gray-900">{overtimeStats.total_hours_this_month}h</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <DollarSign className="h-5 w-5 text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Estimated Cost</p>
                <p className="text-lg font-semibold text-gray-900">
                  KSH {overtimeStats.estimated_cost.toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                activeTab === tab.id
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
              {tab.count > 0 && (
                <span className="ml-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab !== 'analytics' ? (
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">
                  {activeTab === 'pending' ? 'Pending Overtime Requests' : 
                   activeTab === 'approved' ? 'Approved Overtime Requests' : 'All Overtime Requests'}
                </h3>
                <div className="flex items-center space-x-3">
                  <Button variant="secondary" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                {filteredRequests.map((request) => (
                  <div key={request.id} className={`bg-white border rounded-lg p-4 ${getPriorityColor(request.priority)}`}>
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-sm font-medium">
                            {request.employee.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-medium text-gray-900">{request.employee.name}</h4>
                            <span className="text-sm text-gray-500">({request.employee.id})</span>
                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                              request.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                              request.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                              request.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {request.priority}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{request.type}</p>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                            <div>
                              <span className="text-gray-500">Date:</span>
                              <span className="ml-1 font-medium">{formatDate(request.date, 'MMM dd, yyyy')}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Time:</span>
                              <span className="ml-1 font-medium">{request.start_time} - {request.end_time}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Hours:</span>
                              <span className="ml-1 font-medium">{request.hours}h</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Project:</span>
                              <span className="ml-1 font-medium">{request.project}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Rate:</span>
                              <span className="ml-1 font-medium">KSH {request.employee.hourly_rate}/hr</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Cost:</span>
                              <span className="ml-1 font-medium text-green-600">
                                KSH {(request.hours * request.employee.hourly_rate).toLocaleString()}
                              </span>
                            </div>
                          </div>
                          <div className="mt-2">
                            <span className="text-gray-500 text-sm">Reason:</span>
                            <p className="text-sm text-gray-900 mt-1">{request.reason}</p>
                          </div>
                          <div className="mt-2">
                            <span className="text-gray-500 text-sm">Applied:</span>
                            <span className="ml-1 text-sm text-gray-900">{formatDate(request.applied_date, 'MMM dd, yyyy')}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                          {getStatusIcon(request.status)}
                          <span className="ml-1">{request.status}</span>
                        </span>
                        {request.status === 'Pending' && (
                          <div className="flex items-center space-x-2">
                            <Button 
                              variant="primary" 
                              size="sm"
                              onClick={() => handleApprove(request.id)}
                            >
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Approve
                            </Button>
                            <Button 
                              variant="secondary" 
                              size="sm"
                              onClick={() => handleReject(request.id)}
                            >
                              <XCircle className="h-3 w-3 mr-1" />
                              Reject
                            </Button>
                          </div>
                        )}
                        <Button variant="secondary" size="sm">
                          <Eye className="h-3 w-3 mr-1" />
                          Details
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {filteredRequests.length === 0 && (
                <div className="text-center py-12">
                  <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No overtime requests found</h3>
                  <p className="text-gray-600">
                    {activeTab === 'pending' 
                      ? "No pending overtime requests at the moment."
                      : `No ${activeTab} overtime requests found.`
                    }
                  </p>
                </div>
              )}
            </div>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Team Overtime Summary */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-blue-500" />
                  Team Overtime Summary
                </h3>
                <div className="space-y-4">
                  {teamOvertimeHistory.map((member, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">{member.employee}</h4>
                        <span className="text-sm font-medium text-green-600">
                          KSH {member.total_cost.toLocaleString()}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">This Month:</span>
                          <span className="ml-1 font-medium">{member.hours_this_month}h</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Last Month:</span>
                          <span className="ml-1 font-medium">{member.hours_last_month}h</span>
                        </div>
                      </div>
                      <div className="mt-2">
                        <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                          <span>Monthly Progress</span>
                          <span>{member.hours_this_month}/40h</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-orange-500 h-2 rounded-full"
                            style={{ width: `${Math.min((member.hours_this_month / 40) * 100, 100)}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            {/* Monthly Trends */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-green-500" />
                  Monthly Trends
                </h3>
                <div className="space-y-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Total Hours This Month</p>
                        <p className="text-2xl font-bold text-blue-600">{overtimeStats.total_hours_this_month}h</p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-blue-600" />
                    </div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Total Cost This Month</p>
                        <p className="text-2xl font-bold text-green-600">
                          KSH {overtimeStats.estimated_cost.toLocaleString()}
                        </p>
                      </div>
                      <DollarSign className="h-8 w-8 text-green-600" />
                    </div>
                  </div>
                  <div className="bg-orange-50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Average Hours per Employee</p>
                        <p className="text-2xl font-bold text-orange-600">
                          {Math.round(overtimeStats.total_hours_this_month / teamOvertimeHistory.length)}h
                        </p>
                      </div>
                      <Users className="h-8 w-8 text-orange-600" />
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default SupervisorOvertimeManagementPage;
