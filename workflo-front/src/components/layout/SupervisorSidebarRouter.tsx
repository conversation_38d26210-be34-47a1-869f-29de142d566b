'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import SupervisorEnhancedSidebar from './SupervisorEnhancedSidebar';
// Keep imports for backward compatibility if needed
import SupervisorHomeSidebar from './sidebars/SupervisorHomeSidebar';
import SupervisorInfoSidebar from './sidebars/SupervisorInfoSidebar';
import SupervisorManageSidebar from './sidebars/SupervisorManageSidebar';
import SupervisorNotificationsSidebar from './sidebars/SupervisorNotificationsSidebar';
import SupervisorDynamicSidebar from './SupervisorDynamicSidebar';

interface SupervisorSidebarRouterProps {
  isOpen: boolean;
  onClose: () => void;
}

const SupervisorSidebarRouter: React.FC<SupervisorSidebarRouterProps> = ({ isOpen, onClose }) => {
  const pathname = usePathname();

  // Use the new enhanced sidebar for all supervisor pages
  // This provides a unified, comprehensive navigation experience
  const getSidebarComponent = () => {
    // The enhanced sidebar handles all routes dynamically
    return <SupervisorEnhancedSidebar isOpen={isOpen} onClose={onClose} />;

    // Legacy route-specific sidebars (commented out but kept for reference)
    /*
    // Home/Dashboard page
    if (pathname === '/supervisor') {
      return <SupervisorHomeSidebar isOpen={isOpen} onClose={onClose} />;
    }

    // Info pages
    if (pathname.startsWith('/supervisor/info')) {
      return <SupervisorInfoSidebar isOpen={isOpen} onClose={onClose} />;
    }

    // Management pages
    if (pathname.startsWith('/supervisor/manage')) {
      return <SupervisorManageSidebar isOpen={isOpen} onClose={onClose} />;
    }

    // Notification pages
    if (pathname.startsWith('/supervisor/notifications')) {
      return <SupervisorNotificationsSidebar isOpen={isOpen} onClose={onClose} />;
    }

    // Company pages and other pages use the dynamic sidebar
    return <SupervisorDynamicSidebar isOpen={isOpen} onClose={onClose} />;
    */
  };

  return getSidebarComponent();
};

export default SupervisorSidebarRouter;
