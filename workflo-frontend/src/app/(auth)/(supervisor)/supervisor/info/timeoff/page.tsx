'use client';

import React, { useState } from 'react';
import {
  Clock,
  Plus,
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Filter,
  Download
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { formatDate } from '@/lib/utils';

const SupervisorTimeOffPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('requests');

  // Mock time off data
  const timeOffRequests = [
    {
      id: 1,
      type: 'Flexible Hours',
      date: '2024-12-20',
      start_time: '10:00 AM',
      end_time: '6:00 PM',
      reason: 'Medical appointment in the morning',
      status: 'Approved',
      requested_date: '2024-12-15',
      approved_by: '<PERSON>'
    },
    {
      id: 2,
      type: 'Half Day',
      date: '2024-11-28',
      start_time: '12:00 PM',
      end_time: '5:00 PM',
      reason: 'Personal errands',
      status: 'Approved',
      requested_date: '2024-11-20',
      approved_by: '<PERSON>'
    },
    {
      id: 3,
      type: 'Comp Time',
      date: '2024-11-15',
      start_time: '9:00 AM',
      end_time: '5:00 PM',
      reason: 'Compensation for weekend work',
      status: 'Approved',
      requested_date: '2024-11-10',
      approved_by: 'Sarah Johnson'
    }
  ];

  const flexibleArrangements = [
    {
      id: 1,
      type: 'Remote Work',
      frequency: 'Twice a week',
      days: 'Monday, Wednesday',
      start_date: '2024-01-01',
      end_date: '2024-12-31',
      status: 'Active',
      description: 'Work from home arrangement for better work-life balance'
    },
    {
      id: 2,
      type: 'Flexible Hours',
      frequency: 'Daily',
      schedule: '7:00 AM - 3:00 PM',
      start_date: '2024-06-01',
      end_date: '2024-12-31',
      status: 'Active',
      description: 'Early start to accommodate family commitments'
    }
  ];

  const compTimeBalance = {
    earned: 16,
    used: 8,
    remaining: 8,
    expiring_soon: 2
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'active':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
      case 'active':
        return <CheckCircle className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const tabs = [
    { id: 'requests', label: 'Time Off Requests', icon: Clock },
    { id: 'flexible', label: 'Flexible Arrangements', icon: Calendar },
    { id: 'comp-time', label: 'Comp Time', icon: CheckCircle }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Time Off Management</h1>
          <p className="text-gray-600 mt-1">
            Manage your time off requests, flexible work arrangements, and comp time
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button variant="primary">
            <Plus className="h-4 w-4 mr-2" />
            New Request
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Clock className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Total Requests</p>
                <p className="text-lg font-semibold text-gray-900">{timeOffRequests.length}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Approved</p>
                <p className="text-lg font-semibold text-gray-900">
                  {timeOffRequests.filter(r => r.status === 'Approved').length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Calendar className="h-5 w-5 text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Flexible Arrangements</p>
                <p className="text-lg font-semibold text-gray-900">{flexibleArrangements.length}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <AlertCircle className="h-5 w-5 text-orange-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Comp Time</p>
                <p className="text-lg font-semibold text-gray-900">{compTimeBalance.remaining} hours</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'requests' && (
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">Time Off Requests</h3>
                <div className="flex items-center space-x-3">
                  <Button variant="secondary" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button variant="primary" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    New Request
                  </Button>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date & Time
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reason
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {timeOffRequests.map((request) => (
                      <tr key={request.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {request.type}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div>
                            <p>{formatDate(request.date, 'MMM dd, yyyy')}</p>
                            <p className="text-xs text-gray-500">
                              {request.start_time} - {request.end_time}
                            </p>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                          {request.reason}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                            {getStatusIcon(request.status)}
                            <span className="ml-1">{request.status}</span>
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <Button variant="secondary" size="sm">
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </Card>
        )}

        {activeTab === 'flexible' && (
          <div className="space-y-6">
            {flexibleArrangements.map((arrangement) => (
              <Card key={arrangement.id}>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{arrangement.type}</h3>
                      <p className="text-gray-600">{arrangement.description}</p>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(arrangement.status)}`}>
                      {getStatusIcon(arrangement.status)}
                      <span className="ml-1">{arrangement.status}</span>
                    </span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Frequency</label>
                      <p className="text-gray-900">{arrangement.frequency}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {arrangement.days ? 'Days' : 'Schedule'}
                      </label>
                      <p className="text-gray-900">{arrangement.days || arrangement.schedule}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                      <p className="text-gray-900">{formatDate(arrangement.start_date, 'MMM dd, yyyy')}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                      <p className="text-gray-900">{formatDate(arrangement.end_date, 'MMM dd, yyyy')}</p>
                    </div>
                  </div>
                </div>
              </Card>
            ))}

            <Card>
              <div className="p-6 text-center">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Request New Arrangement</h3>
                <p className="text-gray-600 mb-4">
                  Need a flexible work arrangement? Submit a request for approval.
                </p>
                <Button variant="primary">
                  <Plus className="h-4 w-4 mr-2" />
                  Request Flexible Arrangement
                </Button>
              </div>
            </Card>
          </div>
        )}

        {activeTab === 'comp-time' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Comp Time Balance</h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Earned</span>
                    <span className="font-medium">{compTimeBalance.earned} hours</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Used</span>
                    <span className="font-medium text-red-600">{compTimeBalance.used} hours</span>
                  </div>
                  <div className="flex justify-between border-t pt-3">
                    <span className="text-gray-900 font-medium">Remaining</span>
                    <span className="font-semibold text-green-600">{compTimeBalance.remaining} hours</span>
                  </div>
                  {compTimeBalance.expiring_soon > 0 && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <div className="flex items-center">
                        <AlertCircle className="h-4 w-4 text-yellow-600 mr-2" />
                        <span className="text-sm text-yellow-800">
                          {compTimeBalance.expiring_soon} hours expiring soon
                        </span>
                      </div>
                    </div>
                  )}
                </div>
                <div className="mt-6">
                  <Button variant="primary" className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Use Comp Time
                  </Button>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Comp Time Usage</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">Weekend Project Work</p>
                      <p className="text-sm text-gray-600">Nov 15, 2024</p>
                    </div>
                    <span className="text-sm font-medium text-green-600">+8 hours</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">Emergency Response</p>
                      <p className="text-sm text-gray-600">Oct 28, 2024</p>
                    </div>
                    <span className="text-sm font-medium text-green-600">+4 hours</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">Used Comp Time</p>
                      <p className="text-sm text-gray-600">Nov 15, 2024</p>
                    </div>
                    <span className="text-sm font-medium text-red-600">-8 hours</span>
                  </div>
                </div>
                <div className="mt-4">
                  <Button variant="secondary" className="w-full">
                    <Eye className="h-4 w-4 mr-2" />
                    View Full History
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default SupervisorTimeOffPage;
