'use client';

import React, { useState } from 'react';
import {
  FileText,
  Upload,
  Download,
  Eye,
  Trash2,
  Search,
  Filter,
  Plus,
  Calendar,
  User,
  Shield
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { formatDate } from '@/lib/utils';

const SupervisorDocumentsPage: React.FC = () => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Mock documents data
  const documents = [
    {
      id: 1,
      name: 'Employment Contract',
      category: 'employment',
      type: 'PDF',
      size: '2.4 MB',
      uploaded_date: '2021-03-15',
      uploaded_by: 'HR Department',
      description: 'Original employment contract and terms'
    },
    {
      id: 2,
      name: 'Performance Review 2024',
      category: 'performance',
      type: 'PDF',
      size: '1.8 MB',
      uploaded_date: '2024-01-15',
      uploaded_by: '<PERSON>',
      description: 'Annual performance review and goals'
    },
    {
      id: 3,
      name: 'Leadership Certificate',
      category: 'certification',
      type: 'PDF',
      size: '1.2 MB',
      uploaded_date: '2022-08-15',
      uploaded_by: 'John Doe',
      description: 'Leadership Excellence Certificate from KMI'
    },
    {
      id: 4,
      name: 'ID Copy',
      category: 'personal',
      type: 'PDF',
      size: '0.8 MB',
      uploaded_date: '2021-03-10',
      uploaded_by: 'John Doe',
      description: 'National ID card copy'
    },
    {
      id: 5,
      name: 'Bank Details',
      category: 'financial',
      type: 'PDF',
      size: '0.5 MB',
      uploaded_date: '2021-03-12',
      uploaded_by: 'John Doe',
      description: 'Bank account details for salary'
    },
    {
      id: 6,
      name: 'Medical Certificate',
      category: 'medical',
      type: 'PDF',
      size: '1.1 MB',
      uploaded_date: '2024-11-15',
      uploaded_by: 'John Doe',
      description: 'Annual medical checkup certificate'
    }
  ];

  const categories = [
    { id: 'all', label: 'All Documents', count: documents.length },
    { id: 'employment', label: 'Employment', count: documents.filter(d => d.category === 'employment').length },
    { id: 'performance', label: 'Performance', count: documents.filter(d => d.category === 'performance').length },
    { id: 'certification', label: 'Certifications', count: documents.filter(d => d.category === 'certification').length },
    { id: 'personal', label: 'Personal', count: documents.filter(d => d.category === 'personal').length },
    { id: 'financial', label: 'Financial', count: documents.filter(d => d.category === 'financial').length },
    { id: 'medical', label: 'Medical', count: documents.filter(d => d.category === 'medical').length }
  ];

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'employment':
        return <User className="h-4 w-4" />;
      case 'performance':
        return <Shield className="h-4 w-4" />;
      case 'certification':
        return <Shield className="h-4 w-4" />;
      case 'personal':
        return <User className="h-4 w-4" />;
      case 'financial':
        return <FileText className="h-4 w-4" />;
      case 'medical':
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'employment':
        return 'bg-blue-100 text-blue-800';
      case 'performance':
        return 'bg-green-100 text-green-800';
      case 'certification':
        return 'bg-purple-100 text-purple-800';
      case 'personal':
        return 'bg-orange-100 text-orange-800';
      case 'financial':
        return 'bg-yellow-100 text-yellow-800';
      case 'medical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Document Center</h1>
          <p className="text-gray-600 mt-1">
            Manage your personal and employment documents
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Download All
          </Button>
          <Button variant="primary">
            <Upload className="h-4 w-4 mr-2" />
            Upload Document
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Total Documents</p>
                <p className="text-lg font-semibold text-gray-900">{documents.length}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Shield className="h-5 w-5 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Certifications</p>
                <p className="text-lg font-semibold text-gray-900">
                  {documents.filter(d => d.category === 'certification').length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <User className="h-5 w-5 text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Personal Docs</p>
                <p className="text-lg font-semibold text-gray-900">
                  {documents.filter(d => d.category === 'personal').length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Calendar className="h-5 w-5 text-orange-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Recent Uploads</p>
                <p className="text-lg font-semibold text-gray-900">2</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Categories Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Categories</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full flex items-center justify-between p-3 rounded-lg text-left transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-orange-50 text-orange-700 border border-orange-200'
                        : 'hover:bg-gray-50 text-gray-700'
                    }`}
                  >
                    <div className="flex items-center">
                      {getCategoryIcon(category.id)}
                      <span className="ml-2 text-sm font-medium">{category.label}</span>
                    </div>
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                      {category.count}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          </Card>
        </div>

        {/* Documents List */}
        <div className="lg:col-span-3">
          <Card>
            <div className="p-6">
              {/* Search and Filter */}
              <div className="flex items-center space-x-4 mb-6">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search documents..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>
                <Button variant="secondary" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </div>

              {/* Documents Grid */}
              <div className="space-y-4">
                {filteredDocuments.map((document) => (
                  <div key={document.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 bg-gray-100 rounded-lg">
                          <FileText className="h-6 w-6 text-gray-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{document.name}</h4>
                          <p className="text-sm text-gray-600">{document.description}</p>
                          <div className="flex items-center space-x-4 mt-2">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(document.category)}`}>
                              {getCategoryIcon(document.category)}
                              <span className="ml-1 capitalize">{document.category}</span>
                            </span>
                            <span className="text-xs text-gray-500">{document.type} • {document.size}</span>
                            <span className="text-xs text-gray-500">
                              Uploaded {formatDate(document.uploaded_date, 'MMM dd, yyyy')}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="secondary" size="sm">
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                        <Button variant="secondary" size="sm">
                          <Download className="h-3 w-3 mr-1" />
                          Download
                        </Button>
                        <Button variant="secondary" size="sm">
                          <Trash2 className="h-3 w-3 text-red-600" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {filteredDocuments.length === 0 && (
                <div className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm ? 'Try adjusting your search terms' : 'Upload your first document to get started'}
                  </p>
                  <Button variant="primary">
                    <Plus className="h-4 w-4 mr-2" />
                    Upload Document
                  </Button>
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SupervisorDocumentsPage;
