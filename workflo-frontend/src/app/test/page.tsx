'use client';

import React, { useState } from 'react';
import Input, { Textarea, Select } from '@/components/ui/Input';
import Button from '@/components/ui/Button';

const TestPage: React.FC = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    message: '',
    category: '',
  });

  const categories = [
    { value: 'general', label: 'General' },
    { value: 'support', label: 'Support' },
    { value: 'feedback', label: 'Feedback' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          Hydration Test Form
        </h1>
        
        <div className="space-y-4">
          <Input
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            placeholder="Enter your email"
          />

          <Input
            label="Password"
            type="password"
            value={formData.password}
            onChange={(e) => setFormData({ ...formData, password: e.target.value })}
            placeholder="Enter your password"
          />

          <Select
            label="Category"
            value={formData.category}
            onChange={(e) => setFormData({ ...formData, category: e.target.value })}
            options={categories}
            placeholder="Select a category"
          />

          <Textarea
            label="Message"
            value={formData.message}
            onChange={(e) => setFormData({ ...formData, message: e.target.value })}
            placeholder="Enter your message"
            rows={4}
          />

          <Button variant="primary" className="w-full">
            Submit Test Form
          </Button>
        </div>

        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-medium text-gray-900 mb-2">Form Data:</h3>
          <pre className="text-sm text-gray-600 whitespace-pre-wrap">
            {JSON.stringify(formData, null, 2)}
          </pre>
        </div>

        <div className="mt-4 text-center">
          <a
            href="/login"
            className="text-orange-600 hover:text-orange-700 text-sm"
          >
            ← Back to Login
          </a>
        </div>
      </div>
    </div>
  );
};

export default TestPage;
