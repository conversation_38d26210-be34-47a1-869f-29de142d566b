# Generated by Django 4.2.11 on 2024-03-20 18:56

from django.conf import settings
from django.db import migrations, models
import django.db.migrations.operations.special
import django.db.models.deletion


# Functions from the following migrations need manual copying.
# Move them and any dependencies into this file, then update the
# RunPython operations to refer to the local versions:
# easyaudit.migrations.0013_auto_20190723_0126

class Migration(migrations.Migration):

    replaces = [('easyaudit', '0004_auto_20170620_1354'), ('easyaudit', '0005_auto_20170713_1155'), ('easyaudit', '0006_auto_20171018_1242'), ('easyaudit', '0007_auto_20180105_0838'), ('easyaudit', '0008_auto_20180220_1908'), ('easyaudit', '0009_auto_20180314_2225'), ('easyaudit', '0010_repr_text'), ('easyaudit', '0011_auto_20181101_1339'), ('easyaudit', '0012_auto_20181018_0012'), ('easyaudit', '0013_auto_20190723_0126'), ('easyaudit', '0014_auto_20200513_0008'), ('easyaudit', '0015_auto_20201019_1217'), ('easyaudit', '0016_alter_crudevent_event_type'), ('easyaudit', '0017_alter_requestevent_datetime'), ('easyaudit', '0018_rename_crudevent_object_id_content_type_index'), ('easyaudit', '0019_alter_crudevent_changed_fields_and_more')]

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('easyaudit', '0003_auto_20170228_1505'),
    ]

    operations = [
        # replaces index_together on object_id and content_type
        migrations.AddIndex(
            model_name='crudevent',
            index=models.Index(fields=['object_id', 'content_type'], name='easyaudit_c_object__82020b_idx'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='event_type',
            field=models.SmallIntegerField(choices=[(1, b'Create'), (2, b'Update'), (3, b'Delete'), (4, b'Many-to-Many Change'), (5, b'Reverse Many-to-Many Change')]),
        ),
        migrations.AddField(
            model_name='loginevent',
            name='remote_ip',
            field=models.CharField(db_index=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='event_type',
            field=models.SmallIntegerField(choices=[(1, 'Create'), (2, 'Update'), (3, 'Delete'), (4, 'Many-to-Many Change'), (5, 'Reverse Many-to-Many Change')]),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='user_pk_as_string',
            field=models.CharField(blank=True, help_text='String version of the user pk', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='loginevent',
            name='login_type',
            field=models.SmallIntegerField(choices=[(0, 'Login'), (1, 'Logout'), (2, 'Failed login')]),
        ),
        migrations.AddField(
            model_name='crudevent',
            name='changed_fields',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='user_pk_as_string',
            field=models.CharField(blank=True, help_text='String version of the user pk', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='object_repr',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='user',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='content_type',
            field=models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AlterField(
            model_name='loginevent',
            name='user',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='RequestEvent',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.TextField()),
                ('method', models.CharField(db_index=True, max_length=20)),
                ('query_string', models.TextField(null=True)),
                ('remote_ip', models.CharField(db_index=True, max_length=50, null=True)),
                ('datetime', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-datetime'],
                'verbose_name': 'request event',
                'verbose_name_plural': 'request events',
            },
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='url',
            field=models.CharField(db_index=True, max_length=254),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='object_id',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='changed_fields',
            field=models.TextField(blank=True, null=True, verbose_name='Changed fields'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='content_type',
            field=models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype', verbose_name='Content type'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='datetime',
            field=models.DateTimeField(auto_now_add=True, verbose_name='Date time'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='event_type',
            field=models.SmallIntegerField(choices=[(1, 'Create'), (2, 'Update'), (3, 'Delete'), (4, 'Many-to-Many Change'), (5, 'Reverse Many-to-Many Change'), (6, 'Many-to-Many Add'), (7, 'Reverse Many-to-Many Add'), (8, 'Many-to-Many Remove'), (9, 'Reverse Many-to-Many Remove')], verbose_name='Event type'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='object_id',
            field=models.CharField(max_length=255, verbose_name='Object ID'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='object_json_repr',
            field=models.TextField(blank=True, null=True, verbose_name='Object JSON representation'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='object_repr',
            field=models.TextField(blank=True, null=True, verbose_name='Object representation'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='user',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='User'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='user_pk_as_string',
            field=models.CharField(blank=True, help_text='String version of the user pk', max_length=255, null=True, verbose_name='User PK as string'),
        ),
        migrations.AlterField(
            model_name='loginevent',
            name='datetime',
            field=models.DateTimeField(auto_now_add=True, verbose_name='Date time'),
        ),
        migrations.AlterField(
            model_name='loginevent',
            name='login_type',
            field=models.SmallIntegerField(choices=[(0, 'Login'), (1, 'Logout'), (2, 'Failed login')], verbose_name='Event type'),
        ),
        migrations.AlterField(
            model_name='loginevent',
            name='remote_ip',
            field=models.CharField(db_index=True, max_length=50, null=True, verbose_name='Remote IP'),
        ),
        migrations.AlterField(
            model_name='loginevent',
            name='user',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='User'),
        ),
        migrations.AlterField(
            model_name='loginevent',
            name='username',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Username'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='datetime',
            field=models.DateTimeField(auto_now_add=True, verbose_name='Date time'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='method',
            field=models.CharField(db_index=True, max_length=20, verbose_name='Method'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='query_string',
            field=models.TextField(null=True, verbose_name='Query string'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='remote_ip',
            field=models.CharField(db_index=True, max_length=50, null=True, verbose_name='Remote IP'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='url',
            field=models.CharField(db_index=True, max_length=254, verbose_name='URL'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='user',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='User'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='event_type',
            field=models.SmallIntegerField(choices=[(1, 'Create'), (2, 'Update'), (3, 'Delete'), (4, 'Many-to-Many Change'), (5, 'Reverse Many-to-Many Change'), (6, 'Many-to-Many Add'), (7, 'Reverse Many-to-Many Add'), (8, 'Many-to-Many Remove'), (9, 'Reverse Many-to-Many Remove'), (10, 'Many-to-Many Clear'), (11, 'Reverse Many-to-Many Clear')], verbose_name='Event type'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='datetime',
            field=models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Date time'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='changed_fields',
            field=models.TextField(blank=True, default='', verbose_name='Changed fields'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='object_json_repr',
            field=models.TextField(blank=True, default='', verbose_name='Object JSON representation'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='object_repr',
            field=models.TextField(blank=True, default='', verbose_name='Object representation'),
        ),
        migrations.AlterField(
            model_name='crudevent',
            name='user_pk_as_string',
            field=models.CharField(blank=True, default='', help_text='String version of the user pk', max_length=255, verbose_name='User PK as string'),
        ),
        migrations.AlterField(
            model_name='loginevent',
            name='remote_ip',
            field=models.CharField(db_index=True, default='', max_length=50, verbose_name='Remote IP'),
        ),
        migrations.AlterField(
            model_name='loginevent',
            name='username',
            field=models.CharField(blank=True, default='', max_length=255, verbose_name='Username'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='query_string',
            field=models.TextField(default='', verbose_name='Query string'),
        ),
        migrations.AlterField(
            model_name='requestevent',
            name='remote_ip',
            field=models.CharField(db_index=True, default='', max_length=50, verbose_name='Remote IP'),
        ),
    ]
