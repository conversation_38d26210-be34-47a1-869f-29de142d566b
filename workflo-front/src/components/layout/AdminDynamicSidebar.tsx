'use client';

import React, { useMemo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { X, Calendar, DollarSign, FileText, Star, BarChart3, Users, Building, Settings, Clock, TrendingUp } from 'lucide-react';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials } from '@/lib/utils';

interface AdminDynamicSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const AdminDynamicSidebar: React.FC<AdminDynamicSidebarProps> = ({ isOpen, onClose }) => {
  const pathname = usePathname();
  const { user } = useAuth();

  const userInitials = user ? getInitials(user.first_name, user.last_name) : 'A';
  const currentDate = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  // Management areas for admin manage section
  const managementAreas = useMemo(() => [
    {
      name: 'Payroll Management',
      href: '/payroll',
      icon: DollarSign,
      description: 'Manage employee payroll, salaries, and benefits'
    },
    {
      name: 'Calendar Management',
      href: '/calendar',
      icon: Calendar,
      description: 'Schedule events, meetings, and company activities'
    },
    {
      name: 'Leave Management',
      href: '/leave',
      icon: FileText,
      description: 'Approve leave requests and manage holidays'
    },
    {
      name: 'Performance Reviews',
      href: '/reviews',
      icon: Star,
      description: 'Conduct and manage employee performance reviews'
    },
    {
      name: 'Reports & Analytics',
      href: '/reports',
      icon: BarChart3,
      description: 'Generate reports and view analytics'
    }
  ], []);

  // Employee management areas - Show on admin/employees pages
  const employeeManagementAreas = useMemo(() => [
    {
      name: 'Employee Management',
      href: '/employees',
      icon: Users,
      description: 'Manage employee records and information'
    },
    {
      name: 'Department Management',
      href: '/employees/departments',
      icon: Building,
      description: 'Organize and manage company departments'
    },
    {
      name: 'Team Leads',
      href: '/employees/team-leads',
      icon: Users,
      description: 'Manage team leaders and supervisors'
    },
    {
      name: 'Recruitment Management',
      href: '/employees/recruitment',
      icon: Users,
      description: 'Handle job postings and recruitment'
    },
    {
      name: 'User Management',
      href: '/employees/user-management',
      icon: Settings,
      description: 'Manage user accounts and permissions'
    },
    {
      name: 'Training Management',
      href: '/employees/training',
      icon: TrendingUp,
      description: 'Organize training programs and development'
    },
    {
      name: 'Employee Engagement & Wellness',
      href: '/employees/engagement',
      icon: Users,
      description: 'Manage employee wellness and engagement programs'
    }
  ], []);

  const handleClose = () => {
    onClose();
  };

  return (
    <>
      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={handleClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'fixed top-0 left-0 h-full w-80 bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-50 md:relative md:translate-x-0 md:shadow-none md:border-r md:border-gray-200',
          isOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        {/* Sidebar content */}
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 md:hidden">
            <h2 className="text-lg font-semibold text-gray-900">Admin Panel</h2>
            <button
              onClick={handleClose}
              className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* User Card */}
          <div className="p-6 border-b border-gray-200">
            <div className="bg-white rounded-lg border border-gray-200 p-4 text-center">
              <div className="mb-4">
                {user?.profile_picture ? (
                  <img
                    src={user.profile_picture}
                    alt="Profile"
                    className="w-16 h-16 rounded-full mx-auto object-cover"
                  />
                ) : (
                  <div className="w-16 h-16 bg-orange-500 rounded-full mx-auto flex items-center justify-center">
                    <span className="text-white text-xl font-medium">
                      {userInitials}
                    </span>
                  </div>
                )}
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">
                  Welcome {user?.first_name || 'Admin'}
                </h3>
                <p className="text-sm text-gray-500 mt-1">{currentDate}</p>
              </div>
            </div>
          </div>

          {/* Management areas - Show on manage pages */}
          {pathname.startsWith('/manage') && (
            <div className="p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
              <div className="bg-white rounded-lg border border-gray-200">
                <div className="p-4">
                  <h3 className="font-medium text-gray-900 mb-3">Management Areas</h3>
                  <div className="space-y-2">
                    {managementAreas.map((area, index) => {
                      const Icon = area.icon;
                      const isCurrentPage = pathname === area.href;
                      return (
                        <Link
                          key={index}
                          href={area.href}
                          onClick={handleClose}
                          className={cn(
                            "flex items-center justify-between p-3 rounded-lg transition-colors",
                            isCurrentPage
                              ? "bg-orange-50 text-orange-700 border border-orange-200"
                              : "hover:bg-gray-50 text-gray-700"
                          )}
                        >
                          <div className="flex items-center space-x-3">
                            <Icon className="h-5 w-5" />
                            <div>
                              <p className="font-medium text-sm">{area.name}</p>
                              <p className="text-xs text-gray-500">{area.description}</p>
                            </div>
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Employee Management areas - Show on admin/employees pages */}
          {pathname.startsWith('/employees') && (
            <div className="p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
              <div className="bg-white rounded-lg border border-gray-200">
                <div className="p-4">
                  <h3 className="font-medium text-gray-900 mb-3">Employee Management</h3>
                  <div className="space-y-2">
                    {employeeManagementAreas.map((area, index) => {
                      const Icon = area.icon;
                      const isCurrentPage = pathname === area.href;
                      return (
                        <Link
                          key={index}
                          href={area.href}
                          onClick={handleClose}
                          className={cn(
                            "flex items-center justify-between p-3 rounded-lg transition-colors",
                            isCurrentPage
                              ? "bg-orange-50 text-orange-700 border border-orange-200"
                              : "hover:bg-gray-50 text-gray-700"
                          )}
                        >
                          <div className="flex items-center space-x-3">
                            <Icon className="h-5 w-5" />
                            <div>
                              <p className="font-medium text-sm">{area.name}</p>
                              <p className="text-xs text-gray-500">{area.description}</p>
                            </div>
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="p-6 border-b border-gray-200">
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="p-4">
                <h3 className="font-medium text-gray-900 mb-3">Quick Actions</h3>
                <div className="space-y-1">
                  <Link
                    href="/dashboard"
                    className={cn(
                      'block w-full text-center py-2 px-3 rounded-md text-sm font-medium transition-colors',
                      pathname === '/dashboard'
                        ? 'bg-orange-500 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    )}
                  >
                    Admin Dashboard
                  </Link>
                  <Link
                    href="/info"
                    className={cn(
                      'block w-full text-center py-2 px-3 rounded-md text-sm font-medium transition-colors',
                      pathname === '/info'
                        ? 'bg-orange-500 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    )}
                  >
                    Admin Info
                  </Link>
                  <Link
                    href="/employees"
                    className={cn(
                      'block w-full text-center py-2 px-3 rounded-md text-sm font-medium transition-colors',
                      pathname.startsWith('/employees')
                        ? 'bg-orange-500 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    )}
                  >
                    Employee Management
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* System Status */}
          <div className="p-6 flex-1">
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="p-4">
                <h3 className="font-medium text-gray-900 mb-3">System Status</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Server Status</span>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Online
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Last Backup</span>
                    <span className="text-xs text-gray-500">2 hours ago</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Active Users</span>
                    <span className="text-xs text-gray-500">24</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminDynamicSidebar;
