// User and Authentication Types
export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  employee_id: string;
  phone_number?: string;
  profile_picture?: string;
  role: 'hr' | 'supervisor' | 'accountant' | 'admin' | 'employee';
  department?: Department;
  is_active: boolean;
  date_joined: string;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

// Department Types
export interface Department {
  id: number;
  name: string;
  description?: string;
  supervisor?: User;
  created_at: string;
  updated_at: string;
}

// Enhanced Employee Types
export interface Employee extends User {
  // Job Information
  job_title: string;
  position: string; // Alias for job_title for compatibility
  department_id: number; // Department ID for forms and API
  department_name: string; // Department name for display
  hire_date: string;
  employment_type: 'full_time' | 'part_time' | 'contract' | 'intern';
  work_location: 'office' | 'remote' | 'hybrid';
  supervisor_id?: number;
  supervisor_name?: string;

  // Financial Information
  salary: number;
  hourly_rate?: number;
  currency: string;
  pay_frequency: 'monthly' | 'bi_weekly' | 'weekly';
  bank_name?: string;
  bank_account?: string;

  // Government IDs (Kenya specific)
  nssf_number?: string;
  nhif_number?: string;
  kra_pin?: string;
  national_id?: string;

  // Personal Information
  date_of_birth?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  marital_status?: 'single' | 'married' | 'divorced' | 'widowed';
  nationality?: string;

  // Contact Information
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;

  // Emergency Contact
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;

  // System Fields
  status: 'active' | 'inactive' | 'terminated' | 'on_leave';
  is_deleted: boolean;
  deleted_at?: string;
  deleted_by?: number;
  created_by?: number;
  updated_by?: number;
}

export interface EmployeeFormData {
  // Personal Information
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  date_of_birth: string;
  gender: string;
  nationality: string;
  marital_status: string;
  national_id: string;

  // Address Information
  address: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;

  // Job Information
  employee_id: string;
  department: number;
  job_title: string;
  hire_date: string;
  employment_type: string;
  work_location: string;
  role: string;
  supervisor_id?: number;

  // Financial Information
  salary: number;
  hourly_rate: number;
  currency: string;
  pay_frequency: string;
  bank_name: string;
  bank_account: string;

  // Government IDs
  nssf_number: string;
  nhif_number: string;
  kra_pin: string;

  // Emergency Contact
  emergency_contact_name: string;
  emergency_contact_phone: string;
  emergency_contact_relationship: string;

  // System
  status: string;
}

export interface EmployeeFilters {
  search?: string;
  department?: number;
  status?: string;
  employment_type?: string;
  role?: string;
  supervisor?: number;
  hire_date_from?: string;
  hire_date_to?: string;
  salary_min?: number;
  salary_max?: number;
}

export interface EmployeeBulkAction {
  action: 'activate' | 'deactivate' | 'delete' | 'transfer' | 'update_role';
  employee_ids: number[];
  data?: {
    department?: number;
    role?: string;
    status?: string;
    [key: string]: any;
  };
}

// Attendance Types
export interface Attendance {
  id: number;
  employee: number;
  check_in: string;
  check_out?: string;
  date: string;
  hours_worked?: number;
  overtime_hours?: number;
  created_at: string;
}

// BioStar Integration Types
export interface BiometricEvent {
  id: string;
  user_id: string;
  device_id: string;
  event_type: 'ENTRY' | 'EXIT' | 'DENIED';
  datetime: string;
  user_name?: string;
  device_name?: string;
  location?: string;
}

export interface BiometricUser {
  id: string;
  user_id: string;
  name: string;
  email?: string;
  phone?: string;
  department?: string;
  position?: string;
  employee_id?: string;
  start_datetime?: string;
  expiry_datetime?: string;
  disabled: boolean;
  created: string;
  updated: string;
}

export interface BiometricDevice {
  id: string;
  name: string;
  ip: string;
  port: number;
  status: 'ONLINE' | 'OFFLINE';
  type: string;
  location?: string;
  last_seen?: string;
}

export interface AttendanceRecord {
  id: string;
  employee_id: string;
  employee_name: string;
  date: string;
  first_in?: string;
  last_out?: string;
  total_hours?: number;
  break_time?: number;
  overtime?: number;
  status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EARLY_OUT';
  events: BiometricEvent[];
  biostar_synced: boolean;
}

export interface RealTimeAttendance {
  employee_id: string;
  employee_name: string;
  event_type: 'CHECK_IN' | 'CHECK_OUT' | 'BREAK_START' | 'BREAK_END';
  timestamp: string;
  device_name: string;
  location: string;
}

// Leave Types
export interface LeaveApplication {
  id: number;
  employee: number;
  employee_name?: string;
  leave_type: 'sick' | 'annual' | 'maternity' | 'paternity';
  start_date: string;
  end_date: string;
  days_requested: number;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  approved_by?: number;
  approved_by_name?: string;
  reviewer_comments?: string;
  applied_date: string;
}

// Payroll Types
export interface PayCycle {
  id?: number;
  pay_period: string;
  start_date: string;
  end_date: string;
  pay_date: string;
  paid: boolean;
  total_employees: number;
  total_amount: number;
  created_at?: string;
}

export interface Payroll {
  id: number;
  employee: number;
  employee_name?: string;
  pay_cycle: number;
  gross_salary: number;
  nssf_deduction: number;
  nhif_deduction: number;
  housing_levy: number;
  tax_deduction: number;
  other_deductions: number;
  refunds: number;
  net_salary: number;
  created_at: string;
}

// Performance Types
export interface PerformanceReview {
  id: number;
  employee: number;
  employee_name?: string;
  reviewer: number;
  reviewer_name?: string;
  review_period_start: string;
  review_period_end: string;
  overall_rating: number;
  goals_achievement: number;
  communication_skills: number;
  teamwork: number;
  leadership: number;
  comments?: string;
  review_document?: string;
  created_at: string;
}

// Training Types
export interface TrainingModule {
  id: number;
  title: string;
  description: string;
  duration_hours: number;
  venue?: string;
  instructor?: string;
  start_date?: string;
  end_date?: string;
  created_at: string;
}

export interface EmployeeTraining {
  id: number;
  employee: number;
  training_module: number;
  status: 'not_started' | 'in_progress' | 'completed';
  assigned_date: string;
  completion_date?: string;
}

// Job and Recruitment Types
export interface JobPosting {
  id: number;
  title: string;
  department: number;
  description: string;
  requirements: string;
  salary_range?: string;
  location?: string;
  employment_type: 'full_time' | 'part_time' | 'contract' | 'internship';
  is_active: boolean;
  posted_date: string;
  closing_date?: string;
}

export interface Candidate {
  id: number;
  job_posting: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  resume?: string;
  cover_letter?: string;
  status: 'applied' | 'interview' | 'rejected' | 'hired';
  applied_date: string;
}

// Document Types
export interface EmployeeDocument {
  id: number;
  employee: number;
  document_type: string;
  document_file: string;
  uploaded_date: string;
}

export interface CompanyDocument {
  id: number;
  title: string;
  document_file: string;
  uploaded_date: string;
}

// Benefit Types
export interface Benefit {
  id: number;
  name: string;
  description: string;
  amount?: number;
  is_active: boolean;
}

// Dashboard Types
export interface DashboardStats {
  total_employees: number;
  total_departments: number;
  pending_leaves: number;
  total_salary: number;
  recent_activities: Activity[];
}

export interface Activity {
  id: number;
  type: 'leave' | 'attendance' | 'performance' | 'training';
  description: string;
  user: string;
  timestamp: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

export interface PaginatedResponse<T> {
  count: number;
  next?: string;
  previous?: string;
  results: T[];
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea';
  required?: boolean;
  options?: { value: string; label: string }[];
  placeholder?: string;
}

// Navigation Types
export interface NavItem {
  name: string;
  href: string;
  icon: string;
  current?: boolean;
  children?: NavItem[];
}

// Component Props Types
export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  children: React.ReactNode;
  className?: string;
  actions?: React.ReactNode;
}

export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success' | 'warning';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  title?: string;
}
