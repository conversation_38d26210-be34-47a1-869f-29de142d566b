# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: django-extensions\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2011-02-02 11:42+0100\n"
"PO-Revision-Date: 2011-02-02 10:38+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1)\n"

#: admin/__init__.py:121
msgid "and"
msgstr "και"

#: admin/__init__.py:123
#, python-format
msgid ""
"Use the left field to do %(model_name)s lookups in the fields %(field_list)s."
msgstr ""
"Χρησιμοποίησε το αριστερό πεδίο για να κάνεις αναζήτηση του %(model_name)s "
"με βάσει τα πεδία %(field_list)s."

#: db/models.py:15
msgid "created"
msgstr "δημιουργήθηκε"

#: db/models.py:16
msgid "modified"
msgstr "τροποποιήθηκε"

#: db/models.py:26
msgid "title"
msgstr "τίτλος"

#: db/models.py:27
msgid "slug"
msgstr "μίνι-όνομα"

#: db/models.py:28
msgid "description"
msgstr "περιγραφή"

#: db/models.py:50
msgid "Inactive"
msgstr "ανενεργό"

#: db/models.py:51
msgid "Active"
msgstr "Ενεργό"

#: db/models.py:53
msgid "status"
msgstr "κατάσταση"

#: db/models.py:56
msgid "keep empty for an immediate activation"
msgstr "αφήστε άδειο για άμεση ενεργοποίηση"

#: db/models.py:58
msgid "keep empty for indefinite activation"
msgstr "αφήστε άδειο για αόριστη ενεργοποίηση"

#: management/commands/show_urls.py:34
#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s δεν φαίνεται να είναι ένα αντικείμενο urlpattern"

#: templates/django_extensions/widgets/foreignkey_searchinput.html:4
msgid "Lookup"
msgstr "Αναζήτηση"
