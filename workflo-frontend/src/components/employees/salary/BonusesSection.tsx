'use client';

import React, { useState, useEffect } from 'react';
import {
  Award,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  MoreHorizontal,
  Star,
  Target,
  TrendingUp,
  Calendar,
  DollarSign,
  Percent,
  Gift,
  Trophy,
  Zap,
  Users,
  X,
  Save
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { cn, formatDate, formatCurrency } from '@/lib/utils';

interface Bonus {
  id: number;
  employee_id: number;
  bonus_type: 'performance' | 'annual' | 'quarterly' | 'project_completion' | 'sales_commission' |
              'retention' | 'referral' | 'holiday' | 'spot_bonus' | 'signing' | 'other';
  bonus_name: string;
  description: string;
  amount_type: 'fixed_amount' | 'percentage_salary' | 'percentage_target' | 'commission_rate';
  amount: number;
  currency: string;
  target_amount?: number;
  actual_amount: number;
  calculation_base?: number;
  performance_period_start?: string;
  performance_period_end?: string;
  award_date: string;
  payment_date?: string;
  status: 'pending' | 'approved' | 'paid' | 'cancelled' | 'on_hold';
  approval_level: 'manager' | 'hr' | 'executive' | 'board';
  approved_by?: string;
  approved_date?: string;
  criteria_met: boolean;
  performance_score?: number;
  notes?: string;
  created_date: string;
  updated_date: string;
}

interface BonusesSectionProps {
  employeeId: number;
  employeeName: string;
  isCurrentUser?: boolean;
}

const BonusesSection: React.FC<BonusesSectionProps> = ({
  employeeId,
  employeeName,
  isCurrentUser = false
}) => {
  const [bonuses, setBonuses] = useState<Bonus[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedBonus, setSelectedBonus] = useState<Bonus | null>(null);

  // Mock bonuses data
  const mockBonuses: Bonus[] = [
    {
      id: 1,
      employee_id: employeeId,
      bonus_type: 'performance',
      bonus_name: 'Annual Performance Bonus',
      description: 'Performance-based bonus for exceeding annual targets',
      amount_type: 'percentage_salary',
      amount: 15,
      currency: 'KSH',
      target_amount: 1560000,
      actual_amount: 1560000,
      calculation_base: 10400000,
      performance_period_start: '2024-01-01',
      performance_period_end: '2024-12-31',
      award_date: '2024-12-31',
      payment_date: '2025-01-15',
      status: 'approved',
      approval_level: 'manager',
      approved_by: 'John Smith',
      approved_date: '2024-12-20T00:00:00Z',
      criteria_met: true,
      performance_score: 4.5,
      notes: 'Exceeded all performance targets for 2024',
      created_date: '2024-12-15T00:00:00Z',
      updated_date: '2024-12-20T00:00:00Z'
    },
    {
      id: 2,
      employee_id: employeeId,
      bonus_type: 'quarterly',
      bonus_name: 'Q4 2024 Quarterly Bonus',
      description: 'Quarterly performance bonus for Q4 achievements',
      amount_type: 'fixed_amount',
      amount: 325000,
      currency: 'KSH',
      actual_amount: 325000,
      performance_period_start: '2024-10-01',
      performance_period_end: '2024-12-31',
      award_date: '2024-12-31',
      payment_date: '2025-01-05',
      status: 'paid',
      approval_level: 'manager',
      approved_by: 'John Smith',
      approved_date: '2024-12-28T00:00:00Z',
      criteria_met: true,
      performance_score: 4.2,
      notes: 'Strong performance in Q4 project deliveries',
      created_date: '2024-12-20T00:00:00Z',
      updated_date: '2024-12-28T00:00:00Z'
    },
    {
      id: 3,
      employee_id: employeeId,
      bonus_type: 'project_completion',
      bonus_name: 'Customer Portal Project Bonus',
      description: 'Completion bonus for successful delivery of customer portal',
      amount_type: 'fixed_amount',
      amount: 390000,
      currency: 'KSH',
      actual_amount: 390000,
      award_date: '2024-11-15',
      payment_date: '2024-11-30',
      status: 'paid',
      approval_level: 'manager',
      approved_by: 'John Smith',
      approved_date: '2024-11-16T00:00:00Z',
      criteria_met: true,
      notes: 'Project delivered on time and under budget',
      created_date: '2024-11-10T00:00:00Z',
      updated_date: '2024-11-16T00:00:00Z'
    },
    {
      id: 4,
      employee_id: employeeId,
      bonus_type: 'spot_bonus',
      bonus_name: 'Innovation Spot Bonus',
      description: 'Recognition for innovative solution implementation',
      amount_type: 'fixed_amount',
      amount: 1000,
      currency: 'USD',
      actual_amount: 1000,
      award_date: '2024-09-15',
      payment_date: '2024-09-30',
      status: 'paid',
      approval_level: 'manager',
      approved_by: 'John Smith',
      approved_date: '2024-09-16T00:00:00Z',
      criteria_met: true,
      notes: 'Implemented automated testing framework',
      created_date: '2024-09-10T00:00:00Z',
      updated_date: '2024-09-16T00:00:00Z'
    }
  ];

  useEffect(() => {
    const loadBonuses = async () => {
      setLoading(true);
      setTimeout(() => {
        setBonuses(mockBonuses);
        setLoading(false);
      }, 1000);
    };

    loadBonuses();
  }, [employeeId]);

  const filteredBonuses = bonuses.filter(bonus => {
    const matchesSearch = bonus.bonus_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         bonus.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = !filterType || bonus.bonus_type === filterType;
    const matchesStatus = !filterStatus || bonus.status === filterStatus;
    const matchesYear = bonus.award_date.startsWith(selectedYear);
    return matchesSearch && matchesType && matchesStatus && matchesYear;
  });

  const getBonusIcon = (type: string) => {
    switch (type) {
      case 'performance': return <Star className="h-5 w-5 text-yellow-600" />;
      case 'annual': return <Trophy className="h-5 w-5 text-gold-600" />;
      case 'quarterly': return <Calendar className="h-5 w-5 text-blue-600" />;
      case 'project_completion': return <Target className="h-5 w-5 text-green-600" />;
      case 'sales_commission': return <TrendingUp className="h-5 w-5 text-purple-600" />;
      case 'retention': return <Users className="h-5 w-5 text-indigo-600" />;
      case 'referral': return <Users className="h-5 w-5 text-teal-600" />;
      case 'holiday': return <Gift className="h-5 w-5 text-red-600" />;
      case 'spot_bonus': return <Zap className="h-5 w-5 text-orange-600" />;
      case 'signing': return <Award className="h-5 w-5 text-pink-600" />;
      default: return <Award className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-blue-100 text-blue-800';
      case 'paid': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'on_hold': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatBonusAmount = (bonus: Bonus) => {
    switch (bonus.amount_type) {
      case 'fixed_amount':
        return formatCurrency(bonus.actual_amount);
      case 'percentage_salary':
      case 'percentage_target':
        return `${bonus.amount}% (${formatCurrency(bonus.actual_amount)})`;
      case 'commission_rate':
        return `${bonus.amount}% commission (${formatCurrency(bonus.actual_amount)})`;
      default:
        return formatCurrency(bonus.actual_amount);
    }
  };

  const calculateTotalBonuses = () => {
    return filteredBonuses
      .filter(b => b.status === 'paid' || b.status === 'approved')
      .reduce((total, b) => total + b.actual_amount, 0);
  };

  const calculatePendingBonuses = () => {
    return filteredBonuses
      .filter(b => b.status === 'pending' || b.status === 'approved')
      .reduce((total, b) => total + b.actual_amount, 0);
  };

  const getAveragePerformanceScore = () => {
    const bonusesWithScores = filteredBonuses.filter(b => b.performance_score);
    if (bonusesWithScores.length === 0) return 0;
    const totalScore = bonusesWithScores.reduce((sum, b) => sum + (b.performance_score || 0), 0);
    return totalScore / bonusesWithScores.length;
  };

  const handleCreateBonus = () => {
    setSelectedBonus(null);
    setShowCreateModal(true);
  };

  const handleEditBonus = (bonus: Bonus) => {
    setSelectedBonus(bonus);
    setShowCreateModal(true);
  };

  const handleDeleteBonus = (bonus: Bonus) => {
    if (confirm(`Are you sure you want to delete "${bonus.bonus_name}"?`)) {
      setBonuses(prev => prev.filter(b => b.id !== bonus.id));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Bonuses & Incentives</h3>
          <p className="text-sm text-gray-600 mt-1">
            Manage bonus payments and incentives for {employeeName}
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <div className="text-right">
            <div className="text-sm text-gray-600">Total {selectedYear} Bonuses</div>
            <div className="text-lg font-bold text-green-600">
              {formatCurrency(calculateTotalBonuses())}
            </div>
          </div>
          {!isCurrentUser && (
            <Button variant="primary" onClick={handleCreateBonus}>
              <Plus className="h-4 w-4 mr-2" />
              Add Bonus
            </Button>
          )}
        </div>
      </div>

      {/* Bonus Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Paid</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(calculateTotalBonuses())}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Award className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Pending</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(calculatePendingBonuses())}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Trophy className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Bonuses</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {filteredBonuses.length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Star className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Avg Performance</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {getAveragePerformanceScore().toFixed(1)}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <div className="flex-1">
          <Input
            placeholder="Search bonuses..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="h-4 w-4" />}
          />
        </div>
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="">All Types</option>
          <option value="performance">Performance</option>
          <option value="annual">Annual</option>
          <option value="quarterly">Quarterly</option>
          <option value="project_completion">Project Completion</option>
          <option value="spot_bonus">Spot Bonus</option>
          <option value="other">Other</option>
        </select>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="">All Status</option>
          <option value="pending">Pending</option>
          <option value="approved">Approved</option>
          <option value="paid">Paid</option>
          <option value="cancelled">Cancelled</option>
        </select>
        <select
          value={selectedYear}
          onChange={(e) => setSelectedYear(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="2024">2024</option>
          <option value="2023">2023</option>
          <option value="2022">2022</option>
        </select>
      </div>

      {/* Bonuses List */}
      {loading ? (
        <div className="space-y-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      ) : filteredBonuses.length === 0 ? (
        <div className="text-center py-12">
          <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No bonuses found
          </h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || filterType || filterStatus
              ? 'Try adjusting your search criteria.'
              : 'No bonuses have been awarded yet.'
            }
          </p>
          {!isCurrentUser && (
            <Button variant="primary" onClick={handleCreateBonus}>
              <Plus className="h-4 w-4 mr-2" />
              Add Bonus
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredBonuses.map((bonus) => (
            <BonusCard
              key={bonus.id}
              bonus={bonus}
              onEdit={() => handleEditBonus(bonus)}
              onDelete={() => handleDeleteBonus(bonus)}
              isCurrentUser={isCurrentUser}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Bonus Card Component
interface BonusCardProps {
  bonus: Bonus;
  onEdit: () => void;
  onDelete: () => void;
  isCurrentUser: boolean;
}

const BonusCard: React.FC<BonusCardProps> = ({ bonus, onEdit, onDelete, isCurrentUser }) => {
  const [showActions, setShowActions] = useState(false);

  const getBonusIcon = (type: string) => {
    switch (type) {
      case 'performance': return <Star className="h-5 w-5 text-yellow-600" />;
      case 'annual': return <Trophy className="h-5 w-5 text-gold-600" />;
      case 'quarterly': return <Calendar className="h-5 w-5 text-blue-600" />;
      case 'project_completion': return <Target className="h-5 w-5 text-green-600" />;
      case 'sales_commission': return <TrendingUp className="h-5 w-5 text-purple-600" />;
      case 'retention': return <Users className="h-5 w-5 text-indigo-600" />;
      case 'referral': return <Users className="h-5 w-5 text-teal-600" />;
      case 'holiday': return <Gift className="h-5 w-5 text-red-600" />;
      case 'spot_bonus': return <Zap className="h-5 w-5 text-orange-600" />;
      case 'signing': return <Award className="h-5 w-5 text-pink-600" />;
      default: return <Award className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-blue-100 text-blue-800';
      case 'paid': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'on_hold': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatBonusAmount = (bonus: Bonus) => {
    switch (bonus.amount_type) {
      case 'fixed_amount':
        return formatCurrency(bonus.actual_amount);
      case 'percentage_salary':
      case 'percentage_target':
        return `${bonus.amount}% (${formatCurrency(bonus.actual_amount)})`;
      case 'commission_rate':
        return `${bonus.amount}% commission (${formatCurrency(bonus.actual_amount)})`;
      default:
        return formatCurrency(bonus.actual_amount);
    }
  };

  return (
    <Card className="relative hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        {/* Actions Menu */}
        {!isCurrentUser && (
          <div className="absolute top-4 right-4">
            <div className="relative">
              <button
                onClick={() => setShowActions(!showActions)}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <MoreHorizontal className="h-4 w-4 text-gray-500" />
              </button>

              {showActions && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                  <div className="py-1">
                    <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </button>
                    <button
                      onClick={() => {
                        onEdit();
                        setShowActions(false);
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Bonus
                    </button>
                    <button
                      onClick={() => {
                        onDelete();
                        setShowActions(false);
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Bonus
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Bonus Info */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div>
            <div className="flex items-center mb-3">
              {getBonusIcon(bonus.bonus_type)}
              <h4 className="text-lg font-medium text-gray-900 ml-3">
                {bonus.bonus_name}
              </h4>
            </div>
            <div className="flex items-center space-x-2 mb-3">
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                getStatusColor(bonus.status)
              )}>
                {bonus.status.replace('_', ' ').toUpperCase()}
              </span>
              <span className="text-xs text-gray-500 capitalize">
                {bonus.bonus_type.replace('_', ' ')}
              </span>
            </div>
            <p className="text-sm text-gray-600">{bonus.description}</p>
          </div>

          <div>
            <h5 className="text-sm font-medium text-gray-900 mb-2">Amount & Type</h5>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Amount:</span>
                <span className="text-lg font-bold text-green-600">
                  {formatBonusAmount(bonus)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Type:</span>
                <span className="font-medium capitalize">
                  {bonus.amount_type.replace('_', ' ')}
                </span>
              </div>
              {bonus.target_amount && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Target:</span>
                  <span className="font-medium">{formatCurrency(bonus.target_amount)}</span>
                </div>
              )}
            </div>
          </div>

          <div>
            <h5 className="text-sm font-medium text-gray-900 mb-2">Performance & Dates</h5>
            <div className="space-y-1 text-sm">
              {bonus.performance_score && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Score:</span>
                  <div className="flex items-center">
                    <Star className="h-3 w-3 text-yellow-500 mr-1" />
                    <span className="font-medium">{bonus.performance_score}</span>
                  </div>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600">Award Date:</span>
                <span className="font-medium">{formatDate(bonus.award_date)}</span>
              </div>
              {bonus.payment_date && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Paid Date:</span>
                  <span className="font-medium">{formatDate(bonus.payment_date)}</span>
                </div>
              )}
              {bonus.performance_period_start && bonus.performance_period_end && (
                <div className="text-xs text-gray-500 mt-2">
                  Period: {formatDate(bonus.performance_period_start)} - {formatDate(bonus.performance_period_end)}
                </div>
              )}
            </div>
          </div>

          <div>
            <h5 className="text-sm font-medium text-gray-900 mb-2">Approval</h5>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Level:</span>
                <span className="font-medium capitalize">{bonus.approval_level}</span>
              </div>
              {bonus.approved_by && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Approved by:</span>
                  <span className="font-medium">{bonus.approved_by}</span>
                </div>
              )}
              {bonus.approved_date && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Approved:</span>
                  <span className="font-medium">{formatDate(bonus.approved_date)}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600">Criteria Met:</span>
                <span className={cn(
                  "font-medium",
                  bonus.criteria_met ? "text-green-600" : "text-red-600"
                )}>
                  {bonus.criteria_met ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {bonus.notes && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-700">{bonus.notes}</p>
          </div>
        )}
      </div>
    </Card>
  );
};

export default BonusesSection;
