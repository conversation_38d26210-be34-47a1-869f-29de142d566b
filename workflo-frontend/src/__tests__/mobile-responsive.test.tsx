import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import StaffDashboard from '@/app/(auth)/(staff)/staff/page';

// Mock the hooks and dependencies
jest.mock('@/hooks/useBiostarAttendance', () => ({
  useBiostarAttendance: () => ({
    summary: {
      checkInTime: '08:30 AM',
      hoursWorked: 8.5,
      todayStatus: 'PRESENT',
      weeklyHours: 40.0,
      monthlyAttendance: 22
    },
    realtimeUpdates: [],
    connected: true,
    loading: false
  })
}));

jest.mock('@/providers/AuthProvider', () => ({
  useAuth: () => ({
    user: {
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      employee_id: 'EMP001'
    },
    isAuthenticated: true,
    isLoading: false
  })
}));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  usePathname: () => '/staff',
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn()
  })
}));

describe('Mobile Responsive Staff Dashboard', () => {
  beforeEach(() => {
    // Reset viewport to mobile size
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 667,
    });
  });

  it('renders staff dashboard with mobile-friendly layout', () => {
    render(<StaffDashboard />);

    // Check if welcome header is present
    expect(screen.getByText(/Good/)).toBeInTheDocument();
    expect(screen.getByText(/John/)).toBeInTheDocument();

    // Check if stats cards are present
    expect(screen.getByText('Check-in Time')).toBeInTheDocument();
    expect(screen.getByText('Hours Worked')).toBeInTheDocument();
    expect(screen.getByText('Tasks Completed')).toBeInTheDocument();
    expect(screen.getByText('Weekly Hours')).toBeInTheDocument();

    // Check if activities section is present
    expect(screen.getByText("Today's Activities")).toBeInTheDocument();

    // Check if upcoming events section is present
    expect(screen.getByText('Upcoming Events')).toBeInTheDocument();

    // Check if quick actions section is present
    expect(screen.getByText('Quick Actions')).toBeInTheDocument();
  });

  it('has proper responsive classes for mobile', () => {
    const { container } = render(<StaffDashboard />);

    // Check for responsive spacing classes
    const mainContainer = container.querySelector('.space-y-4');
    expect(mainContainer).toBeInTheDocument();

    // Check for responsive grid classes
    const statsGrid = container.querySelector('.grid-cols-1');
    expect(statsGrid).toBeInTheDocument();
  });

  it('displays BioStar connection status', () => {
    render(<StaffDashboard />);

    expect(screen.getByText('BioStar Connected')).toBeInTheDocument();
  });

  it('shows current time', () => {
    render(<StaffDashboard />);

    expect(screen.getByText('Current Time')).toBeInTheDocument();
  });

  it('has proper responsive layout structure', () => {
    const { container } = render(<StaffDashboard />);

    // Check that the main container has proper responsive structure
    const mainContainer = container.querySelector('.space-y-4');
    expect(mainContainer).toBeInTheDocument();

    // Check for responsive grid structure
    const gridContainer = container.querySelector('.grid');
    expect(gridContainer).toBeInTheDocument();
  });
});
