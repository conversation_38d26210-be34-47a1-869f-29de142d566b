{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/components/auth/RoleGuard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/providers/AuthProvider';\nimport { User } from '@/types';\n\ninterface RoleGuardProps {\n  children: React.ReactNode;\n  allowedRoles: string[];\n  fallback?: React.ReactNode;\n  redirectTo?: string;\n}\n\n// Helper function to check if user has required role\nconst hasRequiredRole = (user: User | null, allowedRoles: string[]): boolean => {\n  if (!user) return false;\n  \n  // Admin roles that can access admin routes\n  const adminRoles = ['hr', 'supervisor', 'admin'];\n  \n  // If allowed roles include 'admin', check if user has any admin role\n  if (allowedRoles.includes('admin')) {\n    return adminRoles.includes(user.role);\n  }\n  \n  // If allowed roles include 'staff', check if user is employee\n  if (allowedRoles.includes('staff')) {\n    return user.role === 'employee';\n  }\n  \n  // Direct role check\n  return allowedRoles.includes(user.role);\n};\n\nconst RoleGuard: React.FC<RoleGuardProps> = ({ \n  children, \n  allowedRoles, \n  fallback,\n  redirectTo \n}) => {\n  const { user, isAuthenticated, isLoading } = useAuth();\n  const router = useRouter();\n\n  // Show loading while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600 text-sm\">Checking permissions...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    if (redirectTo) {\n      router.replace(redirectTo);\n      return null;\n    }\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Authentication Required\n          </h2>\n          <p className=\"text-gray-600 mb-4\">\n            Please log in to access this page.\n          </p>\n          <button\n            onClick={() => router.push('/login')}\n            className=\"px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors\"\n          >\n            Go to Login\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Check if user has required role\n  if (!hasRequiredRole(user, allowedRoles)) {\n    if (fallback) {\n      return <>{fallback}</>;\n    }\n\n    // Default unauthorized access component\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center max-w-md mx-auto p-6\">\n          <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <svg className=\"w-8 h-8 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n            </svg>\n          </div>\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n            Access Denied\n          </h2>\n          <p className=\"text-gray-600 mb-4\">\n            You don't have permission to access this page. Your current role ({user?.role}) is not authorized for this section.\n          </p>\n          <div className=\"space-y-2\">\n            <button\n              onClick={() => router.back()}\n              className=\"w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors\"\n            >\n              Go Back\n            </button>\n            <button\n              onClick={() => {\n                // Redirect based on user role\n                if (hasRequiredRole(user, ['admin'])) {\n                  router.push('/dashboard');\n                } else if (hasRequiredRole(user, ['staff'])) {\n                  router.push('/staff');\n                } else {\n                  router.push('/login');\n                }\n              }}\n              className=\"w-full px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors\"\n            >\n              Go to Dashboard\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // User has required role, render children\n  return <>{children}</>;\n};\n\nexport default RoleGuard;\n\n// Convenience components for specific roles\nexport const AdminGuard: React.FC<Omit<RoleGuardProps, 'allowedRoles'>> = (props) => (\n  <RoleGuard {...props} allowedRoles={['admin']} />\n);\n\nexport const StaffGuard: React.FC<Omit<RoleGuardProps, 'allowedRoles'>> = (props) => (\n  <RoleGuard {...props} allowedRoles={['staff']} />\n);\n\nexport const HRGuard: React.FC<Omit<RoleGuardProps, 'allowedRoles'>> = (props) => (\n  <RoleGuard {...props} allowedRoles={['hr']} />\n);\n\nexport const SupervisorGuard: React.FC<Omit<RoleGuardProps, 'allowedRoles'>> = (props) => (\n  <RoleGuard {...props} allowedRoles={['supervisor']} />\n);\n\n// Hook for checking roles in components\nexport const useRoleCheck = () => {\n  const { user } = useAuth();\n  \n  return {\n    hasRole: (roles: string[]) => hasRequiredRole(user, roles),\n    isAdmin: () => hasRequiredRole(user, ['admin']),\n    isStaff: () => hasRequiredRole(user, ['staff']),\n    isHR: () => hasRequiredRole(user, ['hr']),\n    isSupervisor: () => hasRequiredRole(user, ['supervisor']),\n    userRole: user?.role || null\n  };\n};\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "SupervisorGuard", "useRoleCheck", "hasRequiredRole", "user", "allowedRoles", "adminRoles", "includes", "role", "<PERSON><PERSON><PERSON>", "children", "fallback", "redirectTo", "isAuthenticated", "isLoading", "useAuth", "router", "useRouter", "div", "className", "p", "replace", "h2", "button", "onClick", "push", "svg", "fill", "stroke", "viewBox", "path", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "back", "props", "hasRole", "roles", "isAdmin", "isStaff", "isHR", "isSupervisor", "userRole"], "mappings": "AAAA;;;;;;;;;;;;IA0IaA,UAAU;eAAVA;;IAQAC,OAAO;eAAPA;;IAJAC,UAAU;eAAVA;;IAQAC,eAAe;eAAfA;;IAfb,OAAyB;eAAzB;;IAoBaC,YAAY;eAAZA;;;;8DAzJK;4BACQ;8BACF;;;;;;AAUxB,qDAAqD;AACrD,MAAMC,kBAAkB,CAACC,MAAmBC;IAC1C,IAAI,CAACD,MAAM,OAAO;IAElB,2CAA2C;IAC3C,MAAME,aAAa;QAAC;QAAM;QAAc;KAAQ;IAEhD,qEAAqE;IACrE,IAAID,aAAaE,QAAQ,CAAC,UAAU;QAClC,OAAOD,WAAWC,QAAQ,CAACH,KAAKI,IAAI;IACtC;IAEA,8DAA8D;IAC9D,IAAIH,aAAaE,QAAQ,CAAC,UAAU;QAClC,OAAOH,KAAKI,IAAI,KAAK;IACvB;IAEA,oBAAoB;IACpB,OAAOH,aAAaE,QAAQ,CAACH,KAAKI,IAAI;AACxC;AAEA,MAAMC,YAAsC,CAAC,EAC3CC,QAAQ,EACRL,YAAY,EACZM,QAAQ,EACRC,UAAU,EACX;IACC,MAAM,EAAER,IAAI,EAAES,eAAe,EAAEC,SAAS,EAAE,GAAGC,IAAAA,qBAAO;IACpD,MAAMC,SAASC,IAAAA,qBAAS;IAExB,6CAA6C;IAC7C,IAAIH,WAAW;QACb,qBACE,qBAACI;YAAIC,WAAU;sBACb,cAAA,sBAACD;gBAAIC,WAAU;;kCACb,qBAACD;wBAAIC,WAAU;;kCACf,qBAACC;wBAAED,WAAU;kCAA6B;;;;;IAIlD;IAEA,yCAAyC;IACzC,IAAI,CAACN,iBAAiB;QACpB,IAAID,YAAY;YACdI,OAAOK,OAAO,CAACT;YACf,OAAO;QACT;QACA,qBACE,qBAACM;YAAIC,WAAU;sBACb,cAAA,sBAACD;gBAAIC,WAAU;;kCACb,qBAACG;wBAAGH,WAAU;kCAA2C;;kCAGzD,qBAACC;wBAAED,WAAU;kCAAqB;;kCAGlC,qBAACI;wBACCC,SAAS,IAAMR,OAAOS,IAAI,CAAC;wBAC3BN,WAAU;kCACX;;;;;IAMT;IAEA,kCAAkC;IAClC,IAAI,CAAChB,gBAAgBC,MAAMC,eAAe;QACxC,IAAIM,UAAU;YACZ,qBAAO;0BAAGA;;QACZ;QAEA,wCAAwC;QACxC,qBACE,qBAACO;YAAIC,WAAU;sBACb,cAAA,sBAACD;gBAAIC,WAAU;;kCACb,qBAACD;wBAAIC,WAAU;kCACb,cAAA,qBAACO;4BAAIP,WAAU;4BAAuBQ,MAAK;4BAAOC,QAAO;4BAAeC,SAAQ;sCAC9E,cAAA,qBAACC;gCAAKC,eAAc;gCAAQC,gBAAe;gCAAQC,aAAa;gCAAGC,GAAE;;;;kCAGzE,qBAACZ;wBAAGH,WAAU;kCAA2C;;kCAGzD,sBAACC;wBAAED,WAAU;;4BAAqB;4BACmCf,MAAMI;4BAAK;;;kCAEhF,sBAACU;wBAAIC,WAAU;;0CACb,qBAACI;gCACCC,SAAS,IAAMR,OAAOmB,IAAI;gCAC1BhB,WAAU;0CACX;;0CAGD,qBAACI;gCACCC,SAAS;oCACP,8BAA8B;oCAC9B,IAAIrB,gBAAgBC,MAAM;wCAAC;qCAAQ,GAAG;wCACpCY,OAAOS,IAAI,CAAC;oCACd,OAAO,IAAItB,gBAAgBC,MAAM;wCAAC;qCAAQ,GAAG;wCAC3CY,OAAOS,IAAI,CAAC;oCACd,OAAO;wCACLT,OAAOS,IAAI,CAAC;oCACd;gCACF;gCACAN,WAAU;0CACX;;;;;;;IAOX;IAEA,0CAA0C;IAC1C,qBAAO;kBAAGT;;AACZ;MAEA,WAAeD;AAGR,MAAMX,aAA6D,CAACsC,sBACzE,qBAAC3B;QAAW,GAAG2B,KAAK;QAAE/B,cAAc;YAAC;SAAQ;;AAGxC,MAAML,aAA6D,CAACoC,sBACzE,qBAAC3B;QAAW,GAAG2B,KAAK;QAAE/B,cAAc;YAAC;SAAQ;;AAGxC,MAAMN,UAA0D,CAACqC,sBACtE,qBAAC3B;QAAW,GAAG2B,KAAK;QAAE/B,cAAc;YAAC;SAAK;;AAGrC,MAAMJ,kBAAkE,CAACmC,sBAC9E,qBAAC3B;QAAW,GAAG2B,KAAK;QAAE/B,cAAc;YAAC;SAAa;;AAI7C,MAAMH,eAAe;IAC1B,MAAM,EAAEE,IAAI,EAAE,GAAGW,IAAAA,qBAAO;IAExB,OAAO;QACLsB,SAAS,CAACC,QAAoBnC,gBAAgBC,MAAMkC;QACpDC,SAAS,IAAMpC,gBAAgBC,MAAM;gBAAC;aAAQ;QAC9CoC,SAAS,IAAMrC,gBAAgBC,MAAM;gBAAC;aAAQ;QAC9CqC,MAAM,IAAMtC,gBAAgBC,MAAM;gBAAC;aAAK;QACxCsC,cAAc,IAAMvC,gBAAgBC,MAAM;gBAAC;aAAa;QACxDuC,UAAUvC,MAAMI,QAAQ;IAC1B;AACF"}