ae53b21703255e9895dfc292410ee8e2
"use strict";
jest.mock('next/navigation', ()=>({
        useRouter: ()=>({
                push: mockPush,
                replace: mockReplace,
                back: mockBack
            })
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _react1 = require("@testing-library/react");
const _userevent = /*#__PURE__*/ _interop_require_default(require("@testing-library/user-event"));
const _testutils = require("../../utils/test-utils");
const _RoleGuard = /*#__PURE__*/ _interop_require_wildcard(require("../../../components/auth/RoleGuard"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
// Mock useRouter
const mockPush = jest.fn();
const mockReplace = jest.fn();
const mockBack = jest.fn();
describe('RoleGuard', ()=>{
    beforeEach(()=>{
        jest.clearAllMocks();
    });
    describe('when user is authenticated and has required role', ()=>{
        it('renders children for staff role', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_RoleGuard.default, {
                allowedRoles: [
                    'staff'
                ],
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Staff Content"
                })
            }));
            expect(_react1.screen.getByText('Staff Content')).toBeInTheDocument();
        });
        it('renders children for admin role when user is HR', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_RoleGuard.default, {
                allowedRoles: [
                    'admin'
                ],
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Admin Content"
                })
            }), {
                authContextValue: {
                    ..._testutils.mockUnauthenticatedAuthContextValue,
                    user: _testutils.mockAdminUser,
                    isAuthenticated: true,
                    isLoading: false
                }
            });
            expect(_react1.screen.getByText('Admin Content')).toBeInTheDocument();
        });
    });
    describe('when user is not authenticated', ()=>{
        it('shows authentication required message', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_RoleGuard.default, {
                allowedRoles: [
                    'staff'
                ],
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Protected Content"
                })
            }), {
                authContextValue: _testutils.mockUnauthenticatedAuthContextValue
            });
            expect(_react1.screen.getByText('Authentication Required')).toBeInTheDocument();
            expect(_react1.screen.getByText('Please log in to access this page.')).toBeInTheDocument();
        });
        it('redirects to login when redirectTo is provided', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_RoleGuard.default, {
                allowedRoles: [
                    'staff'
                ],
                redirectTo: "/login",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Protected Content"
                })
            }), {
                authContextValue: _testutils.mockUnauthenticatedAuthContextValue
            });
            expect(mockReplace).toHaveBeenCalledWith('/login');
        });
        it('navigates to login when login button is clicked', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_RoleGuard.default, {
                allowedRoles: [
                    'staff'
                ],
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Protected Content"
                })
            }), {
                authContextValue: _testutils.mockUnauthenticatedAuthContextValue
            });
            const loginButton = _react1.screen.getByText('Go to Login');
            await user.click(loginButton);
            expect(mockPush).toHaveBeenCalledWith('/login');
        });
    });
    describe('when user lacks required role', ()=>{
        it('shows access denied message', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_RoleGuard.default, {
                allowedRoles: [
                    'admin'
                ],
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Admin Content"
                })
            }));
            expect(_react1.screen.getByText('Access Denied')).toBeInTheDocument();
            expect(_react1.screen.getByText(/You don't have permission to access this page/)).toBeInTheDocument();
            expect(_react1.screen.getByText(/Your current role \(employee\) is not authorized/)).toBeInTheDocument();
        });
        it('shows custom fallback when provided', ()=>{
            const customFallback = /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Custom Access Denied"
            });
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_RoleGuard.default, {
                allowedRoles: [
                    'admin'
                ],
                fallback: customFallback,
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Admin Content"
                })
            }));
            expect(_react1.screen.getByText('Custom Access Denied')).toBeInTheDocument();
            expect(_react1.screen.queryByText('Access Denied')).not.toBeInTheDocument();
        });
        it('navigates back when go back button is clicked', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_RoleGuard.default, {
                allowedRoles: [
                    'admin'
                ],
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Admin Content"
                })
            }));
            const backButton = _react1.screen.getByText('Go Back');
            await user.click(backButton);
            expect(mockBack).toHaveBeenCalled();
        });
        it('navigates to staff dashboard for employee role', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_RoleGuard.default, {
                allowedRoles: [
                    'admin'
                ],
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Admin Content"
                })
            }));
            const dashboardButton = _react1.screen.getByText('Go to Dashboard');
            await user.click(dashboardButton);
            // Should redirect to staff dashboard for employee role
            expect(mockPush).toHaveBeenCalledWith('/staff');
        });
    });
    describe('when loading', ()=>{
        it('shows loading state', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_RoleGuard.default, {
                allowedRoles: [
                    'staff'
                ],
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    children: "Protected Content"
                })
            }), {
                authContextValue: _testutils.mockLoadingAuthContextValue
            });
            expect(_react1.screen.getByText('Checking permissions...')).toBeInTheDocument();
        });
    });
});
describe('AdminGuard', ()=>{
    it('allows access for HR user', ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_RoleGuard.AdminGuard, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Admin Content"
            })
        }), {
            authContextValue: {
                ..._testutils.mockUnauthenticatedAuthContextValue,
                user: _testutils.mockAdminUser,
                isAuthenticated: true,
                isLoading: false
            }
        });
        expect(_react1.screen.getByText('Admin Content')).toBeInTheDocument();
    });
    it('denies access for employee user', ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_RoleGuard.AdminGuard, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Admin Content"
            })
        }));
        expect(_react1.screen.getByText('Access Denied')).toBeInTheDocument();
    });
});
describe('StaffGuard', ()=>{
    it('allows access for employee user', ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_RoleGuard.StaffGuard, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Staff Content"
            })
        }));
        expect(_react1.screen.getByText('Staff Content')).toBeInTheDocument();
    });
    it('denies access for HR user', ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_RoleGuard.StaffGuard, {
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                children: "Staff Content"
            })
        }), {
            authContextValue: {
                ..._testutils.mockUnauthenticatedAuthContextValue,
                user: _testutils.mockAdminUser,
                isAuthenticated: true,
                isLoading: false
            }
        });
        expect(_react1.screen.getByText('Access Denied')).toBeInTheDocument();
    });
});
describe('useRoleCheck', ()=>{
    const TestComponent = ()=>{
        const { hasRole, isAdmin, isStaff, isHR, userRole } = (0, _RoleGuard.useRoleCheck)();
        return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    "data-testid": "has-admin-role",
                    children: hasRole([
                        'admin'
                    ]).toString()
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    "data-testid": "has-staff-role",
                    children: hasRole([
                        'staff'
                    ]).toString()
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    "data-testid": "is-admin",
                    children: isAdmin().toString()
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    "data-testid": "is-staff",
                    children: isStaff().toString()
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    "data-testid": "is-hr",
                    children: isHR().toString()
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    "data-testid": "user-role",
                    children: userRole
                })
            ]
        });
    };
    it('returns correct role checks for employee user', ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestComponent, {}));
        expect(_react1.screen.getByTestId('has-admin-role')).toHaveTextContent('false');
        expect(_react1.screen.getByTestId('has-staff-role')).toHaveTextContent('true');
        expect(_react1.screen.getByTestId('is-admin')).toHaveTextContent('false');
        expect(_react1.screen.getByTestId('is-staff')).toHaveTextContent('true');
        expect(_react1.screen.getByTestId('is-hr')).toHaveTextContent('false');
        expect(_react1.screen.getByTestId('user-role')).toHaveTextContent('employee');
    });
    it('returns correct role checks for HR user', ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestComponent, {}), {
            authContextValue: {
                ..._testutils.mockUnauthenticatedAuthContextValue,
                user: _testutils.mockAdminUser,
                isAuthenticated: true,
                isLoading: false
            }
        });
        expect(_react1.screen.getByTestId('has-admin-role')).toHaveTextContent('true');
        expect(_react1.screen.getByTestId('has-staff-role')).toHaveTextContent('false');
        expect(_react1.screen.getByTestId('is-admin')).toHaveTextContent('true');
        expect(_react1.screen.getByTestId('is-staff')).toHaveTextContent('false');
        expect(_react1.screen.getByTestId('is-hr')).toHaveTextContent('true');
        expect(_react1.screen.getByTestId('user-role')).toHaveTextContent('hr');
    });
    it('returns null values when user is not authenticated', ()=>{
        (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestComponent, {}), {
            authContextValue: _testutils.mockUnauthenticatedAuthContextValue
        });
        expect(_react1.screen.getByTestId('has-admin-role')).toHaveTextContent('false');
        expect(_react1.screen.getByTestId('has-staff-role')).toHaveTextContent('false');
        expect(_react1.screen.getByTestId('is-admin')).toHaveTextContent('false');
        expect(_react1.screen.getByTestId('is-staff')).toHaveTextContent('false');
        expect(_react1.screen.getByTestId('is-hr')).toHaveTextContent('false');
        expect(_react1.screen.getByTestId('user-role')).toHaveTextContent('');
    });
});

//# sourceMappingURL=data:application/json;base64,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