'use client';

import React, { useState } from 'react';
import {
  Building,
  Users,
  FileText,
  Bell,
  Eye,
  Download,
  Search,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Globe,
  Award,
  TrendingUp
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { formatDate } from '@/lib/utils';

const SupervisorCompanyPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  // Mock company data
  const companyInfo = {
    name: 'WorkFlo Technologies Ltd',
    founded: '2015-01-15',
    employees: 250,
    departments: 8,
    locations: 3,
    headquarters: 'Nairobi, Kenya',
    website: 'www.workflo.co.ke',
    phone: '+*********** 456',
    email: '<EMAIL>'
  };

  const departments = [
    {
      id: 1,
      name: 'Operations',
      head: '<PERSON>',
      employees: 45,
      description: 'Core business operations and process management',
      location: 'Nairobi Office'
    },
    {
      id: 2,
      name: 'Human Resources',
      head: '<PERSON>',
      employees: 12,
      description: 'Employee relations and organizational development',
      location: 'Nairobi Office'
    },
    {
      id: 3,
      name: 'Finance',
      head: 'Grace Wanjiku',
      employees: 18,
      description: 'Financial planning and accounting services',
      location: 'Nairobi Office'
    },
    {
      id: 4,
      name: 'Technology',
      head: 'David Kimani',
      employees: 35,
      description: 'Software development and IT infrastructure',
      location: 'Nairobi Office'
    }
  ];

  const companyNews = [
    {
      id: 1,
      title: 'Q4 Performance Results Announced',
      content: 'Company achieves 25% growth in Q4, exceeding all targets.',
      date: '2024-12-15',
      category: 'Performance',
      author: 'CEO Office'
    },
    {
      id: 2,
      title: 'New Office Opening in Mombasa',
      content: 'WorkFlo expands operations with new branch office in Mombasa.',
      date: '2024-12-10',
      category: 'Expansion',
      author: 'Operations Team'
    },
    {
      id: 3,
      title: 'Employee Recognition Awards 2024',
      content: 'Celebrating outstanding performance and dedication of our team members.',
      date: '2024-12-05',
      category: 'Recognition',
      author: 'HR Department'
    }
  ];

  const policies = [
    {
      id: 1,
      title: 'Employee Handbook',
      description: 'Comprehensive guide to company policies and procedures',
      category: 'General',
      last_updated: '2024-01-15',
      size: '2.4 MB'
    },
    {
      id: 2,
      title: 'Code of Conduct',
      description: 'Ethical guidelines and professional standards',
      category: 'Ethics',
      last_updated: '2024-06-01',
      size: '1.2 MB'
    },
    {
      id: 3,
      title: 'Leave Policy',
      description: 'Guidelines for leave applications and approvals',
      category: 'HR',
      last_updated: '2024-03-20',
      size: '0.8 MB'
    },
    {
      id: 4,
      title: 'Remote Work Policy',
      description: 'Guidelines for flexible and remote work arrangements',
      category: 'HR',
      last_updated: '2024-09-15',
      size: '1.1 MB'
    }
  ];

  const tabs = [
    { id: 'overview', label: 'Company Overview', icon: Building },
    { id: 'departments', label: 'Departments', icon: Users },
    { id: 'policies', label: 'Policies', icon: FileText },
    { id: 'news', label: 'Company News', icon: Bell }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Company Information</h1>
          <p className="text-gray-600 mt-1">
            Access company details, policies, and organizational information
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Download Directory
          </Button>
          <Button variant="primary">
            <Eye className="h-4 w-4 mr-2" />
            Org Chart
          </Button>
        </div>
      </div>

      {/* Company Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Total Employees</p>
                <p className="text-lg font-semibold text-gray-900">{companyInfo.employees}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Building className="h-5 w-5 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Departments</p>
                <p className="text-lg font-semibold text-gray-900">{companyInfo.departments}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <MapPin className="h-5 w-5 text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Locations</p>
                <p className="text-lg font-semibold text-gray-900">{companyInfo.locations}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Calendar className="h-5 w-5 text-orange-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Years in Business</p>
                <p className="text-lg font-semibold text-gray-900">
                  {new Date().getFullYear() - new Date(companyInfo.founded).getFullYear()}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Company Details</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
                    <p className="text-gray-900 font-medium">{companyInfo.name}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Founded</label>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                      <p className="text-gray-900">{formatDate(companyInfo.founded, 'MMMM dd, yyyy')}</p>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Headquarters</label>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                      <p className="text-gray-900">{companyInfo.headquarters}</p>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Website</label>
                    <div className="flex items-center">
                      <Globe className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={`https://${companyInfo.website}`} className="text-blue-600 hover:text-blue-800">
                        {companyInfo.website}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 text-gray-400 mr-2" />
                      <p className="text-gray-900">{companyInfo.phone}</p>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={`mailto:${companyInfo.email}`} className="text-blue-600 hover:text-blue-800">
                        {companyInfo.email}
                      </a>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Business Hours</label>
                    <p className="text-gray-900">Monday - Friday: 8:00 AM - 5:00 PM</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Time Zone</label>
                    <p className="text-gray-900">East Africa Time (EAT)</p>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {activeTab === 'departments' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {departments.map((department) => (
              <Card key={department.id}>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">{department.name}</h3>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {department.employees} employees
                    </span>
                  </div>
                  <p className="text-gray-600 mb-4">{department.description}</p>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Department Head</span>
                      <span className="font-medium">{department.head}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Location</span>
                      <span className="font-medium">{department.location}</span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button variant="secondary" size="sm" className="w-full">
                      <Users className="h-3 w-3 mr-2" />
                      View Team
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {activeTab === 'policies' && (
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">Company Policies</h3>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search policies..."
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                  />
                </div>
              </div>

              <div className="space-y-4">
                {policies.map((policy) => (
                  <div key={policy.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 bg-gray-100 rounded-lg">
                          <FileText className="h-6 w-6 text-gray-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{policy.title}</h4>
                          <p className="text-sm text-gray-600">{policy.description}</p>
                          <div className="flex items-center space-x-4 mt-2">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              {policy.category}
                            </span>
                            <span className="text-xs text-gray-500">
                              Updated {formatDate(policy.last_updated, 'MMM dd, yyyy')}
                            </span>
                            <span className="text-xs text-gray-500">{policy.size}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="secondary" size="sm">
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                        <Button variant="secondary" size="sm">
                          <Download className="h-3 w-3 mr-1" />
                          Download
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        )}

        {activeTab === 'news' && (
          <div className="space-y-6">
            {companyNews.map((news) => (
              <Card key={news.id}>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {news.category}
                      </span>
                      <span className="text-sm text-gray-500">
                        {formatDate(news.date, 'MMM dd, yyyy')}
                      </span>
                    </div>
                    <span className="text-sm text-gray-500">By {news.author}</span>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{news.title}</h3>
                  <p className="text-gray-600">{news.content}</p>
                  <div className="mt-4">
                    <Button variant="secondary" size="sm">
                      <Eye className="h-3 w-3 mr-2" />
                      Read More
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SupervisorCompanyPage;
