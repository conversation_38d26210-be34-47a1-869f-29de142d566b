80f858f73c299acb662c30a7ab203b9d
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    BiostarTokenManager: function() {
        return BiostarTokenManager;
    },
    biostarApi: function() {
        return biostarApi;
    },
    biostarClient: function() {
        return biostarClient;
    }
});
const _axios = /*#__PURE__*/ _interop_require_default(require("axios"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// BioStar 2 API Configuration
const BIOSTAR_API_URL = process.env.NEXT_PUBLIC_BIOSTAR_API_URL || 'https://bs2api.biostar2.com';
const BIOSTAR_USERNAME = process.env.NEXT_PUBLIC_BIOSTAR_USERNAME || '';
const BIOSTAR_PASSWORD = process.env.NEXT_PUBLIC_BIOSTAR_PASSWORD || '';
// Create axios instance for BioStar 2 API
const biostarClient = _axios.default.create({
    baseURL: BIOSTAR_API_URL,
    timeout: 15000,
    headers: {
        'Content-Type': 'application/json'
    }
});
// BioStar Token Management
class BiostarTokenManager {
    static{
        this.BIOSTAR_TOKEN_KEY = 'biostar_access_token';
    }
    static{
        this.BIOSTAR_TOKEN_EXPIRY_KEY = 'biostar_token_expiry';
    }
    static getToken() {
        if (typeof window === 'undefined') return null;
        const token = localStorage.getItem(this.BIOSTAR_TOKEN_KEY);
        const expiry = localStorage.getItem(this.BIOSTAR_TOKEN_EXPIRY_KEY);
        if (token && expiry) {
            const expiryTime = parseInt(expiry);
            if (Date.now() < expiryTime) {
                return token;
            } else {
                this.clearToken();
            }
        }
        return null;
    }
    static setToken(token, expiresIn) {
        if (typeof window === 'undefined') return;
        const expiryTime = Date.now() + expiresIn * 1000;
        localStorage.setItem(this.BIOSTAR_TOKEN_KEY, token);
        localStorage.setItem(this.BIOSTAR_TOKEN_EXPIRY_KEY, expiryTime.toString());
    }
    static clearToken() {
        if (typeof window === 'undefined') return;
        localStorage.removeItem(this.BIOSTAR_TOKEN_KEY);
        localStorage.removeItem(this.BIOSTAR_TOKEN_EXPIRY_KEY);
    }
}
// Request interceptor to add auth token
biostarClient.interceptors.request.use(async (config)=>{
    let token = BiostarTokenManager.getToken();
    // If no valid token, authenticate first
    if (!token) {
        try {
            const authResponse = await biostarClient.post('/login', {
                username: BIOSTAR_USERNAME,
                password: BIOSTAR_PASSWORD
            });
            token = authResponse.data.access_token;
            BiostarTokenManager.setToken(token, authResponse.data.expires_in);
        } catch (error) {
            console.error('BioStar authentication failed:', error);
            throw error;
        }
    }
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Response interceptor to handle token refresh
biostarClient.interceptors.response.use((response)=>response, async (error)=>{
    const originalRequest = error.config;
    if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        BiostarTokenManager.clearToken();
        try {
            const authResponse = await biostarClient.post('/login', {
                username: BIOSTAR_USERNAME,
                password: BIOSTAR_PASSWORD
            });
            const token = authResponse.data.access_token;
            BiostarTokenManager.setToken(token, authResponse.data.expires_in);
            // Retry original request with new token
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return biostarClient(originalRequest);
        } catch (authError) {
            console.error('BioStar re-authentication failed:', authError);
            return Promise.reject(authError);
        }
    }
    return Promise.reject(error);
});
// BioStar API Service
class BiostarApiService {
    // Generic request method
    async request(config) {
        try {
            const response = await biostarClient(config);
            return response.data;
        } catch (error) {
            console.error('BioStar API Request Error:', error);
            throw error;
        }
    }
    // GET request
    async get(url, config) {
        return this.request({
            ...config,
            method: 'GET',
            url
        });
    }
    // POST request
    async post(url, data, config) {
        return this.request({
            ...config,
            method: 'POST',
            url,
            data
        });
    }
    // PUT request
    async put(url, data, config) {
        return this.request({
            ...config,
            method: 'PUT',
            url,
            data
        });
    }
    // DELETE request
    async delete(url, config) {
        return this.request({
            ...config,
            method: 'DELETE',
            url
        });
    }
    // Authentication
    async authenticate() {
        return this.post('/login', {
            username: BIOSTAR_USERNAME,
            password: BIOSTAR_PASSWORD
        });
    }
    // Users
    async getUsers(limit = 100, offset = 0) {
        return this.get(`/users?limit=${limit}&offset=${offset}`);
    }
    async getUserById(userId) {
        return this.get(`/users/${userId}`);
    }
    async createUser(userData) {
        return this.post('/users', userData);
    }
    async updateUser(userId, userData) {
        return this.put(`/users/${userId}`, userData);
    }
    async deleteUser(userId) {
        return this.delete(`/users/${userId}`);
    }
    // Events (Access logs)
    async getEvents(startDate, endDate, userId, deviceId, limit = 100, offset = 0) {
        const params = new URLSearchParams();
        if (startDate) params.append('start_datetime', startDate);
        if (endDate) params.append('end_datetime', endDate);
        if (userId) params.append('user_id', userId);
        if (deviceId) params.append('device_id', deviceId);
        params.append('limit', limit.toString());
        params.append('offset', offset.toString());
        return this.get(`/events?${params.toString()}`);
    }
    // Devices
    async getDevices() {
        return this.get('/devices');
    }
    async getDeviceById(deviceId) {
        return this.get(`/devices/${deviceId}`);
    }
    // Attendance Reports
    async getAttendanceReport(startDate, endDate, userId) {
        const params = new URLSearchParams();
        params.append('start_date', startDate);
        params.append('end_date', endDate);
        if (userId) params.append('user_id', userId);
        return this.get(`/attendance/report?${params.toString()}`);
    }
    // Real-time monitoring
    async getRealtimeEvents(limit = 50) {
        return this.get(`/events/realtime?limit=${limit}`);
    }
    // Health check
    async healthCheck() {
        return this.get('/health');
    }
}
const biostarApi = new BiostarApiService();

//# sourceMappingURL=data:application/json;base64,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