{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/app/(auth)/(staff)/staff/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Calendar,\n  Clock,\n  User,\n  FileText,\n  TrendingUp,\n  CheckCircle,\n  Target,\n  Activity,\n  Users,\n  Wifi,\n  WifiOff\n} from 'lucide-react';\nimport Card from '@/components/ui/Card';\nimport { useAuth } from '@/providers/AuthProvider';\nimport { cn, formatDate } from '@/lib/utils';\nimport { useBiostarAttendance } from '@/hooks/useBiostarAttendance';\n\nconst StaffDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [currentTime, setCurrentTime] = useState(new Date());\n\n  // Use BioStar attendance hook\n  const {\n    summary: attendanceSummary,\n    realtimeUpdates,\n    connected: biostarConnected,\n    loading\n  } = useBiostarAttendance({\n    employeeId: user?.employee_id,\n    autoRefresh: true,\n    enableRealTime: true\n  });\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  const todayStats = {\n    checkInTime: attendanceSummary?.checkInTime ?\n      new Date(attendanceSummary.checkInTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) :\n      'Not checked in',\n    hoursWorked: attendanceSummary?.hoursWorked?.toFixed(1) || '0.0',\n    tasksCompleted: 4,\n    meetingsToday: 2,\n    leaveBalance: 15,\n    pendingApprovals: 1,\n    attendanceStatus: attendanceSummary?.todayStatus || 'ABSENT',\n    weeklyHours: attendanceSummary?.weeklyHours?.toFixed(1) || '0.0',\n    monthlyAttendance: attendanceSummary?.monthlyAttendance || 0\n  };\n\n  const recentActivities = [\n    {\n      id: 1,\n      type: 'check_in',\n      description: 'Checked in for the day',\n      time: '08:30 AM',\n      icon: Clock,\n      color: 'text-green-500'\n    },\n    {\n      id: 2,\n      type: 'task',\n      description: 'Completed project review',\n      time: '10:15 AM',\n      icon: CheckCircle,\n      color: 'text-blue-500'\n    },\n    {\n      id: 3,\n      type: 'meeting',\n      description: 'Team standup meeting',\n      time: '11:00 AM',\n      icon: Users,\n      color: 'text-purple-500'\n    },\n    {\n      id: 4,\n      type: 'document',\n      description: 'Updated timesheet',\n      time: '02:30 PM',\n      icon: FileText,\n      color: 'text-orange-500'\n    }\n  ];\n\n  const upcomingEvents = [\n    {\n      id: 1,\n      title: 'Project Review Meeting',\n      time: '3:00 PM',\n      type: 'meeting',\n      participants: 5\n    },\n    {\n      id: 2,\n      title: 'Training Session: React Best Practices',\n      time: 'Tomorrow 10:00 AM',\n      type: 'training',\n      duration: '2 hours'\n    },\n    {\n      id: 3,\n      title: 'Performance Review',\n      time: 'Friday 2:00 PM',\n      type: 'review',\n      with: 'Manager'\n    }\n  ];\n\n  return (\n    <div className=\"space-y-4 sm:space-y-6\">\n      {/* Welcome Header */}\n      <div className=\"bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-4 sm:p-6 text-white\">\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n          <div className=\"flex-1\">\n            <h1 className=\"text-xl sm:text-2xl font-bold leading-tight\">\n              Good {currentTime.getHours() < 12 ? 'Morning' : currentTime.getHours() < 18 ? 'Afternoon' : 'Evening'}, {user?.first_name}!\n            </h1>\n            <p className=\"text-orange-100 mt-1 text-sm sm:text-base\">\n              {formatDate(new Date().toISOString(), 'EEEE, MMMM dd, yyyy')}\n            </p>\n            <div className=\"flex flex-col sm:flex-row sm:items-center mt-3 sm:mt-2 space-y-2 sm:space-y-0 sm:space-x-4\">\n              <div className=\"flex items-center space-x-1\">\n                {biostarConnected ? (\n                  <Wifi className=\"h-4 w-4 text-green-300\" />\n                ) : (\n                  <WifiOff className=\"h-4 w-4 text-red-300\" />\n                )}\n                <span className=\"text-sm text-orange-100\">\n                  BioStar {biostarConnected ? 'Connected' : 'Disconnected'}\n                </span>\n              </div>\n              <div className=\"flex items-center space-x-1\">\n                <div className={cn(\n                  \"w-2 h-2 rounded-full\",\n                  todayStats.attendanceStatus === 'PRESENT' ? 'bg-green-300' :\n                  todayStats.attendanceStatus === 'LATE' ? 'bg-yellow-300' :\n                  todayStats.attendanceStatus === 'EARLY_OUT' ? 'bg-orange-300' :\n                  'bg-red-300'\n                )} />\n                <span className=\"text-sm text-orange-100\">\n                  {todayStats.attendanceStatus}\n                </span>\n              </div>\n            </div>\n          </div>\n          <div className=\"text-center sm:text-right\">\n            <div className=\"text-2xl sm:text-3xl font-bold\">\n              {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n            </div>\n            <div className=\"text-orange-100 text-sm\">\n              Current Time\n            </div>\n            {loading && (\n              <div className=\"text-orange-100 text-xs mt-1\">\n                Loading attendance...\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Today's Stats */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6\">\n        <Card>\n          <div className=\"p-4 sm:p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg flex-shrink-0\">\n                <Clock className=\"h-5 w-5 sm:h-6 sm:w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-3 sm:ml-4 min-w-0 flex-1\">\n                <p className=\"text-xs sm:text-sm font-medium text-gray-600 truncate\">Check-in Time</p>\n                <p className=\"text-lg sm:text-2xl font-bold text-gray-900 truncate\">{todayStats.checkInTime}</p>\n              </div>\n            </div>\n          </div>\n        </Card>\n\n        <Card>\n          <div className=\"p-4 sm:p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg flex-shrink-0\">\n                <Activity className=\"h-5 w-5 sm:h-6 sm:w-6 text-blue-600\" />\n              </div>\n              <div className=\"ml-3 sm:ml-4 min-w-0 flex-1\">\n                <p className=\"text-xs sm:text-sm font-medium text-gray-600 truncate\">Hours Worked</p>\n                <p className=\"text-lg sm:text-2xl font-bold text-gray-900 truncate\">{todayStats.hoursWorked}h</p>\n              </div>\n            </div>\n          </div>\n        </Card>\n\n        <Card>\n          <div className=\"p-4 sm:p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-lg flex-shrink-0\">\n                <Target className=\"h-5 w-5 sm:h-6 sm:w-6 text-purple-600\" />\n              </div>\n              <div className=\"ml-3 sm:ml-4 min-w-0 flex-1\">\n                <p className=\"text-xs sm:text-sm font-medium text-gray-600 truncate\">Tasks Completed</p>\n                <p className=\"text-lg sm:text-2xl font-bold text-gray-900 truncate\">{todayStats.tasksCompleted}</p>\n              </div>\n            </div>\n          </div>\n        </Card>\n\n        <Card>\n          <div className=\"p-4 sm:p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-orange-100 rounded-lg flex-shrink-0\">\n                <Calendar className=\"h-5 w-5 sm:h-6 sm:w-6 text-orange-600\" />\n              </div>\n              <div className=\"ml-3 sm:ml-4 min-w-0 flex-1\">\n                <p className=\"text-xs sm:text-sm font-medium text-gray-600 truncate\">Weekly Hours</p>\n                <p className=\"text-lg sm:text-2xl font-bold text-gray-900 truncate\">{todayStats.weeklyHours}h</p>\n              </div>\n            </div>\n          </div>\n        </Card>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6\">\n        {/* Recent Activities */}\n        <Card>\n          <div className=\"p-4 sm:p-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-base sm:text-lg font-medium text-gray-900\">Today's Activities</h3>\n              {biostarConnected && (\n                <div className=\"flex items-center space-x-1\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                  <span className=\"text-xs text-gray-500\">Live</span>\n                </div>\n              )}\n            </div>\n            <div className=\"space-y-3 sm:space-y-4\">\n              {/* Real-time BioStar updates */}\n              {realtimeUpdates.slice(0, 2).map((update, index) => (\n                <div key={`realtime-${index}`} className=\"flex items-start space-x-3\">\n                  <div className=\"p-2 rounded-full bg-blue-100 flex-shrink-0\">\n                    <Clock className=\"h-4 w-4 text-blue-600\" />\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium text-gray-900 truncate\">\n                      {update.employee_name} - {update.event_type.replace('_', ' ')}\n                    </p>\n                    <p className=\"text-xs text-gray-500 truncate\">\n                      {new Date(update.timestamp).toLocaleTimeString()} at {update.device_name}\n                    </p>\n                  </div>\n                </div>\n              ))}\n\n              {/* Static activities */}\n              {recentActivities.map((activity) => {\n                const Icon = activity.icon;\n                return (\n                  <div key={activity.id} className=\"flex items-start space-x-3\">\n                    <div className={cn('p-2 rounded-full bg-gray-100 flex-shrink-0', activity.color)}>\n                      <Icon className=\"h-4 w-4\" />\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <p className=\"text-sm font-medium text-gray-900 truncate\">{activity.description}</p>\n                      <p className=\"text-xs text-gray-500\">{activity.time}</p>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        </Card>\n\n        {/* Upcoming Events */}\n        <Card>\n          <div className=\"p-4 sm:p-6\">\n            <h3 className=\"text-base sm:text-lg font-medium text-gray-900 mb-4\">Upcoming Events</h3>\n            <div className=\"space-y-3 sm:space-y-4\">\n              {upcomingEvents.map((event) => (\n                <div key={event.id} className=\"border-l-4 border-orange-500 pl-3 sm:pl-4\">\n                  <h4 className=\"text-sm font-medium text-gray-900 leading-tight\">{event.title}</h4>\n                  <p className=\"text-xs text-gray-500 mt-1\">{event.time}</p>\n                  {event.participants && (\n                    <p className=\"text-xs text-gray-500\">{event.participants} participants</p>\n                  )}\n                  {event.duration && (\n                    <p className=\"text-xs text-gray-500\">Duration: {event.duration}</p>\n                  )}\n                  {event.with && (\n                    <p className=\"text-xs text-gray-500\">With: {event.with}</p>\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n        </Card>\n      </div>\n\n      {/* Quick Actions */}\n      <Card>\n        <div className=\"p-4 sm:p-6\">\n          <h3 className=\"text-base sm:text-lg font-medium text-gray-900 mb-4\">Quick Actions</h3>\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4\">\n            <button className=\"p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center touch-manipulation\">\n              <FileText className=\"h-5 w-5 sm:h-6 sm:w-6 text-orange-500 mx-auto mb-2\" />\n              <span className=\"text-xs sm:text-sm font-medium text-gray-900 leading-tight\">Apply Leave</span>\n            </button>\n            <button className=\"p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center touch-manipulation\">\n              <Clock className=\"h-5 w-5 sm:h-6 sm:w-6 text-blue-500 mx-auto mb-2\" />\n              <span className=\"text-xs sm:text-sm font-medium text-gray-900 leading-tight\">Check Out</span>\n            </button>\n            <button className=\"p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center touch-manipulation\">\n              <User className=\"h-5 w-5 sm:h-6 sm:w-6 text-green-500 mx-auto mb-2\" />\n              <span className=\"text-xs sm:text-sm font-medium text-gray-900 leading-tight\">Update Profile</span>\n            </button>\n            <button className=\"p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center touch-manipulation\">\n              <TrendingUp className=\"h-5 w-5 sm:h-6 sm:w-6 text-purple-500 mx-auto mb-2\" />\n              <span className=\"text-xs sm:text-sm font-medium text-gray-900 leading-tight\">View Performance</span>\n            </button>\n          </div>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default StaffDashboard;\n"], "names": ["StaffDashboard", "user", "useAuth", "currentTime", "setCurrentTime", "useState", "Date", "summary", "attendanceSummary", "realtimeUpdates", "connected", "biostarConnected", "loading", "useBiostarAttendance", "employeeId", "employee_id", "autoRefresh", "enableRealTime", "useEffect", "timer", "setInterval", "clearInterval", "todayStats", "checkInTime", "toLocaleTimeString", "hour", "minute", "hoursWorked", "toFixed", "tasksCompleted", "meetingsToday", "leaveBalance", "pendingApprovals", "attendanceStatus", "todayStatus", "weeklyHours", "monthlyAttendance", "recentActivities", "id", "type", "description", "time", "icon", "Clock", "color", "CheckCircle", "Users", "FileText", "upcomingEvents", "title", "participants", "duration", "with", "div", "className", "h1", "getHours", "first_name", "p", "formatDate", "toISOString", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "span", "cn", "Card", "Activity", "Target", "Calendar", "h3", "slice", "map", "update", "index", "employee_name", "event_type", "replace", "timestamp", "device_name", "activity", "Icon", "event", "h4", "button", "User", "TrendingUp"], "mappings": "AAAA;;;;;+BA6UA;;;eAAA;;;;+DA3U2C;6BAapC;6DACU;8BACO;uBACO;sCACM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErC,MAAMA,iBAA2B;IAC/B,MAAM,EAAEC,IAAI,EAAE,GAAGC,IAAAA,qBAAO;IACxB,MAAM,CAACC,aAAaC,eAAe,GAAGC,IAAAA,eAAQ,EAAC,IAAIC;IAEnD,8BAA8B;IAC9B,MAAM,EACJC,SAASC,iBAAiB,EAC1BC,eAAe,EACfC,WAAWC,gBAAgB,EAC3BC,OAAO,EACR,GAAGC,IAAAA,0CAAoB,EAAC;QACvBC,YAAYb,MAAMc;QAClBC,aAAa;QACbC,gBAAgB;IAClB;IAEAC,IAAAA,gBAAS,EAAC;QACR,MAAMC,QAAQC,YAAY;YACxBhB,eAAe,IAAIE;QACrB,GAAG;QAEH,OAAO,IAAMe,cAAcF;IAC7B,GAAG,EAAE;IAEL,MAAMG,aAAa;QACjBC,aAAaf,mBAAmBe,cAC9B,IAAIjB,KAAKE,kBAAkBe,WAAW,EAAEC,kBAAkB,CAAC,EAAE,EAAE;YAAEC,MAAM;YAAWC,QAAQ;QAAU,KACpG;QACFC,aAAanB,mBAAmBmB,aAAaC,QAAQ,MAAM;QAC3DC,gBAAgB;QAChBC,eAAe;QACfC,cAAc;QACdC,kBAAkB;QAClBC,kBAAkBzB,mBAAmB0B,eAAe;QACpDC,aAAa3B,mBAAmB2B,aAAaP,QAAQ,MAAM;QAC3DQ,mBAAmB5B,mBAAmB4B,qBAAqB;IAC7D;IAEA,MAAMC,mBAAmB;QACvB;YACEC,IAAI;YACJC,MAAM;YACNC,aAAa;YACbC,MAAM;YACNC,MAAMC,kBAAK;YACXC,OAAO;QACT;QACA;YACEN,IAAI;YACJC,MAAM;YACNC,aAAa;YACbC,MAAM;YACNC,MAAMG,wBAAW;YACjBD,OAAO;QACT;QACA;YACEN,IAAI;YACJC,MAAM;YACNC,aAAa;YACbC,MAAM;YACNC,MAAMI,kBAAK;YACXF,OAAO;QACT;QACA;YACEN,IAAI;YACJC,MAAM;YACNC,aAAa;YACbC,MAAM;YACNC,MAAMK,qBAAQ;YACdH,OAAO;QACT;KACD;IAED,MAAMI,iBAAiB;QACrB;YACEV,IAAI;YACJW,OAAO;YACPR,MAAM;YACNF,MAAM;YACNW,cAAc;QAChB;QACA;YACEZ,IAAI;YACJW,OAAO;YACPR,MAAM;YACNF,MAAM;YACNY,UAAU;QACZ;QACA;YACEb,IAAI;YACJW,OAAO;YACPR,MAAM;YACNF,MAAM;YACNa,MAAM;QACR;KACD;IAED,qBACE,sBAACC;QAAIC,WAAU;;0BAEb,qBAACD;gBAAIC,WAAU;0BACb,cAAA,sBAACD;oBAAIC,WAAU;;sCACb,sBAACD;4BAAIC,WAAU;;8CACb,sBAACC;oCAAGD,WAAU;;wCAA8C;wCACpDnD,YAAYqD,QAAQ,KAAK,KAAK,YAAYrD,YAAYqD,QAAQ,KAAK,KAAK,cAAc;wCAAU;wCAAGvD,MAAMwD;wCAAW;;;8CAE5H,qBAACC;oCAAEJ,WAAU;8CACVK,IAAAA,iBAAU,EAAC,IAAIrD,OAAOsD,WAAW,IAAI;;8CAExC,sBAACP;oCAAIC,WAAU;;sDACb,sBAACD;4CAAIC,WAAU;;gDACZ3C,iCACC,qBAACkD,iBAAI;oDAACP,WAAU;mEAEhB,qBAACQ,oBAAO;oDAACR,WAAU;;8DAErB,sBAACS;oDAAKT,WAAU;;wDAA0B;wDAC/B3C,mBAAmB,cAAc;;;;;sDAG9C,sBAAC0C;4CAAIC,WAAU;;8DACb,qBAACD;oDAAIC,WAAWU,IAAAA,SAAE,EAChB,wBACA1C,WAAWW,gBAAgB,KAAK,YAAY,iBAC5CX,WAAWW,gBAAgB,KAAK,SAAS,kBACzCX,WAAWW,gBAAgB,KAAK,cAAc,kBAC9C;;8DAEF,qBAAC8B;oDAAKT,WAAU;8DACbhC,WAAWW,gBAAgB;;;;;;;;sCAKpC,sBAACoB;4BAAIC,WAAU;;8CACb,qBAACD;oCAAIC,WAAU;8CACZnD,YAAYqB,kBAAkB,CAAC,EAAE,EAAE;wCAAEC,MAAM;wCAAWC,QAAQ;oCAAU;;8CAE3E,qBAAC2B;oCAAIC,WAAU;8CAA0B;;gCAGxC1C,yBACC,qBAACyC;oCAAIC,WAAU;8CAA+B;;;;;;;0BAStD,sBAACD;gBAAIC,WAAU;;kCACb,qBAACW,aAAI;kCACH,cAAA,qBAACZ;4BAAIC,WAAU;sCACb,cAAA,sBAACD;gCAAIC,WAAU;;kDACb,qBAACD;wCAAIC,WAAU;kDACb,cAAA,qBAACX,kBAAK;4CAACW,WAAU;;;kDAEnB,sBAACD;wCAAIC,WAAU;;0DACb,qBAACI;gDAAEJ,WAAU;0DAAwD;;0DACrE,qBAACI;gDAAEJ,WAAU;0DAAwDhC,WAAWC,WAAW;;;;;;;;kCAMnG,qBAAC0C,aAAI;kCACH,cAAA,qBAACZ;4BAAIC,WAAU;sCACb,cAAA,sBAACD;gCAAIC,WAAU;;kDACb,qBAACD;wCAAIC,WAAU;kDACb,cAAA,qBAACY,qBAAQ;4CAACZ,WAAU;;;kDAEtB,sBAACD;wCAAIC,WAAU;;0DACb,qBAACI;gDAAEJ,WAAU;0DAAwD;;0DACrE,sBAACI;gDAAEJ,WAAU;;oDAAwDhC,WAAWK,WAAW;oDAAC;;;;;;;;;kCAMpG,qBAACsC,aAAI;kCACH,cAAA,qBAACZ;4BAAIC,WAAU;sCACb,cAAA,sBAACD;gCAAIC,WAAU;;kDACb,qBAACD;wCAAIC,WAAU;kDACb,cAAA,qBAACa,mBAAM;4CAACb,WAAU;;;kDAEpB,sBAACD;wCAAIC,WAAU;;0DACb,qBAACI;gDAAEJ,WAAU;0DAAwD;;0DACrE,qBAACI;gDAAEJ,WAAU;0DAAwDhC,WAAWO,cAAc;;;;;;;;kCAMtG,qBAACoC,aAAI;kCACH,cAAA,qBAACZ;4BAAIC,WAAU;sCACb,cAAA,sBAACD;gCAAIC,WAAU;;kDACb,qBAACD;wCAAIC,WAAU;kDACb,cAAA,qBAACc,qBAAQ;4CAACd,WAAU;;;kDAEtB,sBAACD;wCAAIC,WAAU;;0DACb,qBAACI;gDAAEJ,WAAU;0DAAwD;;0DACrE,sBAACI;gDAAEJ,WAAU;;oDAAwDhC,WAAWa,WAAW;oDAAC;;;;;;;;;;;0BAOtG,sBAACkB;gBAAIC,WAAU;;kCAEb,qBAACW,aAAI;kCACH,cAAA,sBAACZ;4BAAIC,WAAU;;8CACb,sBAACD;oCAAIC,WAAU;;sDACb,qBAACe;4CAAGf,WAAU;sDAAiD;;wCAC9D3C,kCACC,sBAAC0C;4CAAIC,WAAU;;8DACb,qBAACD;oDAAIC,WAAU;;8DACf,qBAACS;oDAAKT,WAAU;8DAAwB;;;;;;8CAI9C,sBAACD;oCAAIC,WAAU;;wCAEZ7C,gBAAgB6D,KAAK,CAAC,GAAG,GAAGC,GAAG,CAAC,CAACC,QAAQC,sBACxC,sBAACpB;gDAA8BC,WAAU;;kEACvC,qBAACD;wDAAIC,WAAU;kEACb,cAAA,qBAACX,kBAAK;4DAACW,WAAU;;;kEAEnB,sBAACD;wDAAIC,WAAU;;0EACb,sBAACI;gEAAEJ,WAAU;;oEACVkB,OAAOE,aAAa;oEAAC;oEAAIF,OAAOG,UAAU,CAACC,OAAO,CAAC,KAAK;;;0EAE3D,sBAAClB;gEAAEJ,WAAU;;oEACV,IAAIhD,KAAKkE,OAAOK,SAAS,EAAErD,kBAAkB;oEAAG;oEAAKgD,OAAOM,WAAW;;;;;;+CATpE,CAAC,SAAS,EAAEL,OAAO;wCAgB9BpC,iBAAiBkC,GAAG,CAAC,CAACQ;4CACrB,MAAMC,OAAOD,SAASrC,IAAI;4CAC1B,qBACE,sBAACW;gDAAsBC,WAAU;;kEAC/B,qBAACD;wDAAIC,WAAWU,IAAAA,SAAE,EAAC,8CAA8Ce,SAASnC,KAAK;kEAC7E,cAAA,qBAACoC;4DAAK1B,WAAU;;;kEAElB,sBAACD;wDAAIC,WAAU;;0EACb,qBAACI;gEAAEJ,WAAU;0EAA8CyB,SAASvC,WAAW;;0EAC/E,qBAACkB;gEAAEJ,WAAU;0EAAyByB,SAAStC,IAAI;;;;;+CAN7CsC,SAASzC,EAAE;wCAUzB;;;;;;kCAMN,qBAAC2B,aAAI;kCACH,cAAA,sBAACZ;4BAAIC,WAAU;;8CACb,qBAACe;oCAAGf,WAAU;8CAAsD;;8CACpE,qBAACD;oCAAIC,WAAU;8CACZN,eAAeuB,GAAG,CAAC,CAACU,sBACnB,sBAAC5B;4CAAmBC,WAAU;;8DAC5B,qBAAC4B;oDAAG5B,WAAU;8DAAmD2B,MAAMhC,KAAK;;8DAC5E,qBAACS;oDAAEJ,WAAU;8DAA8B2B,MAAMxC,IAAI;;gDACpDwC,MAAM/B,YAAY,kBACjB,sBAACQ;oDAAEJ,WAAU;;wDAAyB2B,MAAM/B,YAAY;wDAAC;;;gDAE1D+B,MAAM9B,QAAQ,kBACb,sBAACO;oDAAEJ,WAAU;;wDAAwB;wDAAW2B,MAAM9B,QAAQ;;;gDAE/D8B,MAAM7B,IAAI,kBACT,sBAACM;oDAAEJ,WAAU;;wDAAwB;wDAAO2B,MAAM7B,IAAI;;;;2CAVhD6B,MAAM3C,EAAE;;;;;;;0BAoB5B,qBAAC2B,aAAI;0BACH,cAAA,sBAACZ;oBAAIC,WAAU;;sCACb,qBAACe;4BAAGf,WAAU;sCAAsD;;sCACpE,sBAACD;4BAAIC,WAAU;;8CACb,sBAAC6B;oCAAO7B,WAAU;;sDAChB,qBAACP,qBAAQ;4CAACO,WAAU;;sDACpB,qBAACS;4CAAKT,WAAU;sDAA6D;;;;8CAE/E,sBAAC6B;oCAAO7B,WAAU;;sDAChB,qBAACX,kBAAK;4CAACW,WAAU;;sDACjB,qBAACS;4CAAKT,WAAU;sDAA6D;;;;8CAE/E,sBAAC6B;oCAAO7B,WAAU;;sDAChB,qBAAC8B,iBAAI;4CAAC9B,WAAU;;sDAChB,qBAACS;4CAAKT,WAAU;sDAA6D;;;;8CAE/E,sBAAC6B;oCAAO7B,WAAU;;sDAChB,qBAAC+B,uBAAU;4CAAC/B,WAAU;;sDACtB,qBAACS;4CAAKT,WAAU;sDAA6D;;;;;;;;;;;AAO3F;MAEA,WAAetD"}