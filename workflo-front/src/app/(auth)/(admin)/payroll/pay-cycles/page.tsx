'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Calendar,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Clock,
  CheckCircle,
  Users,
  DollarSign,
  Download
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { cn, formatDate, formatCurrency } from '@/lib/utils';
import PayCycleModal from '@/components/payroll/PayCycleModal';
import DeleteConfirmationModal from '@/components/payroll/DeleteConfirmationModal';

interface PayCycle {
  id: number;
  pay_period: string;
  start_date: string;
  end_date: string;
  pay_date: string;
  paid: boolean;
  total_employees: number;
  total_amount: number;
  created_at: string;
}

const PayCyclesPage: React.FC = () => {
  const router = useRouter();
  const [payCycles, setPayCycles] = useState<PayCycle[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'completed'>('all');

  // Modal states
  const [isPayCycleModalOpen, setIsPayCycleModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedPayCycle, setSelectedPayCycle] = useState<PayCycle | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');

  useEffect(() => {
    fetchPayCycles();
  }, []);

  const fetchPayCycles = async () => {
    try {
      // Mock data for now - replace with actual API call when backend is ready
      const mockPayCycles: PayCycle[] = [
        {
          id: 1,
          pay_period: 'January 2024',
          start_date: '2024-01-01',
          end_date: '2024-01-31',
          pay_date: '2024-01-31',
          paid: true,
          total_employees: 156,
          total_amount: 12500000,
          created_at: '2024-01-01'
        },
        {
          id: 2,
          pay_period: 'February 2024',
          start_date: '2024-02-01',
          end_date: '2024-02-29',
          pay_date: '2024-02-29',
          paid: false,
          total_employees: 158,
          total_amount: 12800000,
          created_at: '2024-02-01'
        },
        {
          id: 3,
          pay_period: 'March 2024',
          start_date: '2024-03-01',
          end_date: '2024-03-31',
          pay_date: '2024-03-31',
          paid: false,
          total_employees: 160,
          total_amount: 13000000,
          created_at: '2024-03-01'
        },
        {
          id: 4,
          pay_period: 'April 2024',
          start_date: '2024-04-01',
          end_date: '2024-04-30',
          pay_date: '2024-04-30',
          paid: false,
          total_employees: 162,
          total_amount: 13200000,
          created_at: '2024-04-01'
        }
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      setPayCycles(mockPayCycles);
    } catch (error) {
      console.error('Error fetching pay cycles:', error);
    } finally {
      setLoading(false);
    }
  };

  // CRUD Operations
  const handleCreatePayCycle = () => {
    setModalMode('create');
    setSelectedPayCycle(null);
    setIsPayCycleModalOpen(true);
  };

  const handleViewPayCycle = (payCycle: PayCycle) => {
    router.push(`/payroll/pay-cycles/${payCycle.id}`);
  };

  const handleEditPayCycle = (payCycle: PayCycle) => {
    setModalMode('edit');
    setSelectedPayCycle(payCycle);
    setIsPayCycleModalOpen(true);
  };

  const handleDeletePayCycle = (payCycle: PayCycle) => {
    setSelectedPayCycle(payCycle);
    setIsDeleteModalOpen(true);
  };

  const handleSavePayCycle = async (payCycleData: any) => {
    try {
      if (modalMode === 'create') {
        // Create new pay cycle
        const newPayCycle: PayCycle = {
          ...payCycleData,
          id: Math.max(...payCycles.map(p => p.id), 0) + 1,
          created_at: new Date().toISOString()
        };
        setPayCycles(prev => [...prev, newPayCycle]);
      } else {
        // Update existing pay cycle
        setPayCycles(prev =>
          prev.map(cycle =>
            cycle.id === selectedPayCycle?.id
              ? { ...payCycleData, id: cycle.id, created_at: cycle.created_at }
              : cycle
          )
        );
      }
    } catch (error) {
      console.error('Error saving pay cycle:', error);
      throw error;
    }
  };

  const handleConfirmDelete = async () => {
    try {
      if (selectedPayCycle) {
        setPayCycles(prev => prev.filter(cycle => cycle.id !== selectedPayCycle.id));
      }
    } catch (error) {
      console.error('Error deleting pay cycle:', error);
      throw error;
    }
  };

  const filteredPayCycles = payCycles.filter(cycle => {
    const matchesSearch = cycle.pay_period.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = selectedFilter === 'all' ||
      (selectedFilter === 'pending' && !cycle.paid) ||
      (selectedFilter === 'completed' && cycle.paid);

    return matchesSearch && matchesFilter;
  });

  const getStatusColor = (paid: boolean) => {
    return paid ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
  };

  const getStatusIcon = (paid: boolean) => {
    return paid ? CheckCircle : Clock;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Pay Cycles</h1>
          <p className="text-sm text-gray-600 mt-1">
            Manage payroll cycles with configurable pay periods and dates
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="primary" onClick={handleCreatePayCycle}>
            <Plus className="h-4 w-4 mr-2" />
            Create Pay Cycle
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="text"
              placeholder="Search pay cycles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value as any)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="all">All Pay Cycles</option>
              <option value="pending">Pending</option>
              <option value="completed">Completed</option>
            </select>
          </div>
        </div>
      </div>

      {/* Pay Cycles Table */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Pay Cycles Overview</h3>

          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading pay cycles...</p>
            </div>
          ) : filteredPayCycles.length === 0 ? (
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No pay cycles found
              </h3>
              <p className="text-gray-600 mb-6">
                Get started by creating your first pay cycle with configurable start/end dates.
              </p>
              <Button variant="primary">
                <Plus className="h-4 w-4 mr-2" />
                Create Pay Cycle
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pay Period
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Period Dates
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pay Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Employees
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredPayCycles.map((cycle) => {
                    const StatusIcon = getStatusIcon(cycle.paid);

                    return (
                      <tr key={cycle.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {cycle.pay_period}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {formatDate(cycle.start_date)} - {formatDate(cycle.end_date)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {formatDate(cycle.pay_date)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm text-gray-900">
                            <Users className="h-4 w-4 mr-1 text-gray-400" />
                            {cycle.total_employees}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm font-medium text-gray-900">
                            <DollarSign className="h-4 w-4 mr-1 text-gray-400" />
                            {formatCurrency(cycle.total_amount, 'KSH')}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={cn(
                            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                            getStatusColor(cycle.paid)
                          )}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {cycle.paid ? 'Completed' : 'Pending'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              title="View Details"
                              onClick={() => handleViewPayCycle(cycle)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              title="Edit Pay Cycle"
                              onClick={() => handleEditPayCycle(cycle)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              title="Delete Pay Cycle"
                              onClick={() => handleDeletePayCycle(cycle)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Pay Cycles</p>
                <p className="text-2xl font-bold text-gray-900">{payCycles.length}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Pending Cycles</p>
                <p className="text-2xl font-bold text-gray-900">
                  {payCycles.filter(c => !c.paid).length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Completed Cycles</p>
                <p className="text-2xl font-bold text-gray-900">
                  {payCycles.filter(c => c.paid).length}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Modals */}
      <PayCycleModal
        isOpen={isPayCycleModalOpen}
        onClose={() => setIsPayCycleModalOpen(false)}
        onSave={handleSavePayCycle}
        payCycle={selectedPayCycle}
        existingPayCycles={payCycles}
        mode={modalMode}
      />

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Pay Cycle"
        message="Are you sure you want to delete this pay cycle? This action cannot be undone."
        itemName={selectedPayCycle ? `${selectedPayCycle.pay_period} (${formatDate(selectedPayCycle.start_date)} - ${formatDate(selectedPayCycle.end_date)})` : ''}
        warningMessage={selectedPayCycle?.paid ? "This pay cycle has already been paid. Deleting it may affect payroll records." : undefined}
      />
    </div>
  );
};

export default PayCyclesPage;
