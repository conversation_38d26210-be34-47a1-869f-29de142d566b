{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/__tests__/employee-biostar-view.test.tsx"], "sourcesContent": ["import React from 'react';\nimport { render, screen, waitFor, fireEvent } from '@testing-library/react';\nimport { AuthProvider } from '@/providers/AuthProvider';\nimport StaffBiostarPage from '@/app/(auth)/(staff)/staff/info/biostar/page';\nimport EmployeeBiostarWidget from '@/components/biostar/EmployeeBiostarWidget';\nimport { useBiostarAttendance } from '@/hooks/useBiostarAttendance';\n\n// Mock the BioStar attendance hook\njest.mock('@/hooks/useBiostarAttendance');\nconst mockUseBiostarAttendance = useBiostarAttendance as jest.MockedFunction<typeof useBiostarAttendance>;\n\n// Mock the BioStar API\njest.mock('@/lib/biostarApi', () => ({\n  biostarApi: {\n    getUserById: jest.fn(),\n    getEvents: jest.fn(),\n  },\n}));\n\n// Mock the AuthProvider\njest.mock('@/providers/AuthProvider', () => ({\n  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,\n  useAuth: () => ({\n    user: {\n      id: 1,\n      employee_id: 'EMP001',\n      first_name: 'John',\n      last_name: 'Doe',\n      email: '<EMAIL>',\n      role: 'employee',\n    }\n  })\n}));\n\n// Mock Next.js router\njest.mock('next/navigation', () => ({\n  useRouter: () => ({\n    push: jest.fn(),\n    back: jest.fn(),\n  }),\n  useParams: () => ({}),\n}));\n\nconst mockUser = {\n  id: 1,\n  employee_id: 'EMP001',\n  first_name: 'John',\n  last_name: 'Doe',\n  email: '<EMAIL>',\n  role: 'employee',\n};\n\nconst mockBiostarData = {\n  todayAttendance: {\n    id: 'att-001',\n    employee_id: 'EMP001',\n    employee_name: 'John Doe',\n    date: '2024-12-21',\n    first_in: '2024-12-21T08:30:00Z',\n    last_out: null,\n    total_hours: 0,\n    break_time: 0,\n    overtime: 0,\n    status: 'PRESENT' as const,\n    events: [],\n    biostar_synced: true,\n  },\n  devices: [\n    {\n      id: 'dev-001',\n      name: 'Main Entrance',\n      ip: '*************',\n      port: 8080,\n      status: 'ONLINE' as const,\n      type: 'Fingerprint Scanner',\n      location: 'Main Building',\n    },\n    {\n      id: 'dev-002',\n      name: 'Back Entrance',\n      ip: '*************',\n      port: 8080,\n      status: 'OFFLINE' as const,\n      type: 'Face Recognition',\n      location: 'Back Building',\n    },\n  ],\n  connected: true,\n  loading: false,\n  error: null,\n  summary: {\n    todayStatus: 'PRESENT' as const,\n    checkInTime: '2024-12-21T08:30:00Z',\n    hoursWorked: 0,\n    breakTime: 0,\n    overtime: 0,\n    weeklyHours: 32,\n    monthlyAttendance: 20,\n  },\n};\n\nconst TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (\n  <AuthProvider>\n    {children}\n  </AuthProvider>\n);\n\n// Import the mocked API\nconst { biostarApi } = require('@/lib/biostarApi');\n\ndescribe('Employee BioStar View', () => {\n  beforeEach(() => {\n    // Mock API responses\n    biostarApi.getUserById.mockResolvedValue({\n      id: 'user-001',\n      user_id: 'EMP001',\n      name: 'John Doe',\n      email: '<EMAIL>',\n      department: 'Engineering',\n      position: 'Software Engineer',\n      disabled: false,\n      created: '2024-01-01T00:00:00Z',\n      updated: '2024-12-21T00:00:00Z'\n    });\n\n    biostarApi.getEvents.mockResolvedValue({\n      results: [],\n      count: 0\n    });\n\n    mockUseBiostarAttendance.mockReturnValue({\n      ...mockBiostarData,\n      attendanceRecords: [],\n      realtimeUpdates: [],\n      refresh: jest.fn(),\n      getAttendanceRange: jest.fn(),\n    });\n  });\n\n  afterEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('StaffBiostarPage', () => {\n    it('should render the BioStar profile page', async () => {\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('BioStar Profile')).toBeInTheDocument();\n        expect(screen.getByText('Manage your biometric data and view access history')).toBeInTheDocument();\n      });\n    });\n\n    it('should show connection status', async () => {\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('BioStar Connected')).toBeInTheDocument();\n      });\n    });\n\n    it('should display profile status cards', async () => {\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Profile Status')).toBeInTheDocument();\n        expect(screen.getByText('Fingerprints')).toBeInTheDocument();\n        expect(screen.getByText('Face Templates')).toBeInTheDocument();\n        expect(screen.getByText('Devices')).toBeInTheDocument();\n      });\n    });\n\n    it('should show tab navigation', async () => {\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Biometric Profile')).toBeInTheDocument();\n        expect(screen.getByText('Devices')).toBeInTheDocument();\n        expect(screen.getByText('Access History')).toBeInTheDocument();\n        expect(screen.getByText('Security')).toBeInTheDocument();\n      });\n    });\n\n    it('should switch between tabs', async () => {\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        const devicesTab = screen.getByText('Devices');\n        fireEvent.click(devicesTab);\n        expect(screen.getByText('System Status')).toBeInTheDocument();\n      });\n    });\n\n    it('should show device information in devices tab', async () => {\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        const devicesTab = screen.getByText('Devices');\n        fireEvent.click(devicesTab);\n\n        expect(screen.getByText('Available Devices')).toBeInTheDocument();\n        expect(screen.getByText('Main Entrance')).toBeInTheDocument();\n        expect(screen.getByText('Back Entrance')).toBeInTheDocument();\n      });\n    });\n\n    it('should handle connection errors', async () => {\n      mockUseBiostarAttendance.mockReturnValue({\n        ...mockBiostarData,\n        connected: false,\n        error: 'Connection failed',\n        attendanceRecords: [],\n        realtimeUpdates: [],\n        refresh: jest.fn(),\n        getAttendanceRange: jest.fn(),\n      });\n\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Connection Error')).toBeInTheDocument();\n        expect(screen.getByText('Connection failed')).toBeInTheDocument();\n      });\n    });\n\n    it('should show refresh button and handle refresh', async () => {\n      const mockRefresh = jest.fn();\n      mockUseBiostarAttendance.mockReturnValue({\n        ...mockBiostarData,\n        attendanceRecords: [],\n        realtimeUpdates: [],\n        refresh: mockRefresh,\n        getAttendanceRange: jest.fn(),\n      });\n\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        const refreshButton = screen.getByText('Refresh');\n        fireEvent.click(refreshButton);\n        expect(mockRefresh).toHaveBeenCalled();\n      });\n    });\n  });\n\n  describe('EmployeeBiostarWidget', () => {\n    it('should render the BioStar widget', async () => {\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('BioStar Profile')).toBeInTheDocument();\n      });\n    });\n\n    it('should show connection status in widget', async () => {\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Connected')).toBeInTheDocument();\n      });\n    });\n\n    it('should display biometric stats', async () => {\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Fingerprints')).toBeInTheDocument();\n        expect(screen.getByText('Face Template')).toBeInTheDocument();\n      });\n    });\n\n    it('should show today\\'s attendance status', async () => {\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Today\\'s Status')).toBeInTheDocument();\n        expect(screen.getByText('PRESENT')).toBeInTheDocument();\n      });\n    });\n\n    it('should display recent access events', async () => {\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" showFullDetails={true} />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Recent Access')).toBeInTheDocument();\n      });\n    });\n\n    it('should show device status summary', async () => {\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Devices Online')).toBeInTheDocument();\n        expect(screen.getByText('1/2')).toBeInTheDocument(); // 1 online out of 2 total\n      });\n    });\n\n    it('should have link to full profile', async () => {\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('View Full BioStar Profile →')).toBeInTheDocument();\n      });\n    });\n\n    it('should handle disconnected state', async () => {\n      mockUseBiostarAttendance.mockReturnValue({\n        ...mockBiostarData,\n        connected: false,\n        attendanceRecords: [],\n        realtimeUpdates: [],\n        refresh: jest.fn(),\n        getAttendanceRange: jest.fn(),\n      });\n\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(screen.getByText('Disconnected')).toBeInTheDocument();\n      });\n    });\n\n    it('should show loading state', () => {\n      mockUseBiostarAttendance.mockReturnValue({\n        ...mockBiostarData,\n        loading: true,\n        attendanceRecords: [],\n        realtimeUpdates: [],\n        refresh: jest.fn(),\n        getAttendanceRange: jest.fn(),\n      });\n\n      render(\n        <TestWrapper>\n          <EmployeeBiostarWidget employeeId=\"EMP001\" />\n        </TestWrapper>\n      );\n\n      expect(screen.getByText('Loading BioStar data...')).toBeInTheDocument();\n    });\n  });\n\n  describe('Integration Tests', () => {\n    it('should integrate with useBiostarAttendance hook correctly', async () => {\n      const mockGetAttendanceRange = jest.fn();\n      mockUseBiostarAttendance.mockReturnValue({\n        ...mockBiostarData,\n        attendanceRecords: [],\n        realtimeUpdates: [],\n        refresh: jest.fn(),\n        getAttendanceRange: mockGetAttendanceRange,\n      });\n\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        expect(mockUseBiostarAttendance).toHaveBeenCalledWith({\n          employeeId: undefined, // No user context in test\n          autoRefresh: true,\n          enableRealTime: false,\n        });\n      });\n    });\n\n    it('should handle empty device list', async () => {\n      mockUseBiostarAttendance.mockReturnValue({\n        ...mockBiostarData,\n        devices: [],\n        attendanceRecords: [],\n        realtimeUpdates: [],\n        refresh: jest.fn(),\n        getAttendanceRange: jest.fn(),\n      });\n\n      render(\n        <TestWrapper>\n          <StaffBiostarPage />\n        </TestWrapper>\n      );\n\n      await waitFor(() => {\n        const devicesTab = screen.getByText('Devices');\n        fireEvent.click(devicesTab);\n        expect(screen.getByText('No Devices Found')).toBeInTheDocument();\n      });\n    });\n  });\n});\n"], "names": ["jest", "mock", "biostarApi", "getUserById", "fn", "getEvents", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "div", "useAuth", "user", "id", "employee_id", "first_name", "last_name", "email", "role", "useRouter", "push", "back", "useParams", "mockUseBiostarAttendance", "useBiostarAttendance", "mockUser", "mockBiostarData", "todayAttendance", "employee_name", "date", "first_in", "last_out", "total_hours", "break_time", "overtime", "status", "events", "biostar_synced", "devices", "name", "ip", "port", "type", "location", "connected", "loading", "error", "summary", "todayStatus", "checkInTime", "hoursWorked", "breakTime", "weeklyHours", "monthlyAttendance", "TestWrapper", "require", "describe", "beforeEach", "mockResolvedValue", "user_id", "department", "position", "disabled", "created", "updated", "results", "count", "mockReturnValue", "attendanceRecords", "realtimeUpdates", "refresh", "getAttendanceRange", "after<PERSON>ach", "clearAllMocks", "it", "render", "StaffBiostarPage", "waitFor", "expect", "screen", "getByText", "toBeInTheDocument", "devicesTab", "fireEvent", "click", "mockRefresh", "refreshButton", "toHaveBeenCalled", "EmployeeBiostarWidget", "employeeId", "showFullDetails", "mockGetAttendanceRange", "toHaveBeenCalledWith", "undefined", "autoRefresh", "enableRealTime"], "mappings": ";AAOA,mCAAmC;AACnCA,KAAKC,IAAI,CAAC;AAGV,uBAAuB;AACvBD,KAAKC,IAAI,CAAC,oBAAoB,IAAO,CAAA;QACnCC,YAAY;YACVC,aAAaH,KAAKI,EAAE;YACpBC,WAAWL,KAAKI,EAAE;QACpB;IACF,CAAA;AAEA,wBAAwB;AACxBJ,KAAKC,IAAI,CAAC,4BAA4B,IAAO,CAAA;QAC3CK,cAAc,CAAC,EAAEC,QAAQ,EAAiC,iBAAK,qBAACC;0BAAKD;;QACrEE,SAAS,IAAO,CAAA;gBACdC,MAAM;oBACJC,IAAI;oBACJC,aAAa;oBACbC,YAAY;oBACZC,WAAW;oBACXC,OAAO;oBACPC,MAAM;gBACR;YACF,CAAA;IACF,CAAA;AAEA,sBAAsB;AACtBhB,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCgB,WAAW,IAAO,CAAA;gBAChBC,MAAMlB,KAAKI,EAAE;gBACbe,MAAMnB,KAAKI,EAAE;YACf,CAAA;QACAgB,WAAW,IAAO,CAAA,CAAC,CAAA;IACrB,CAAA;;;;;8DAzCkB;wBACiC;8BACtB;6DACA;8EACK;sCACG;;;;;;AAIrC,MAAMC,2BAA2BC,0CAAoB;AAkCrD,MAAMC,WAAW;IACfZ,IAAI;IACJC,aAAa;IACbC,YAAY;IACZC,WAAW;IACXC,OAAO;IACPC,MAAM;AACR;AAEA,MAAMQ,kBAAkB;IACtBC,iBAAiB;QACfd,IAAI;QACJC,aAAa;QACbc,eAAe;QACfC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,aAAa;QACbC,YAAY;QACZC,UAAU;QACVC,QAAQ;QACRC,QAAQ,EAAE;QACVC,gBAAgB;IAClB;IACAC,SAAS;QACP;YACEzB,IAAI;YACJ0B,MAAM;YACNC,IAAI;YACJC,MAAM;YACNN,QAAQ;YACRO,MAAM;YACNC,UAAU;QACZ;QACA;YACE9B,IAAI;YACJ0B,MAAM;YACNC,IAAI;YACJC,MAAM;YACNN,QAAQ;YACRO,MAAM;YACNC,UAAU;QACZ;KACD;IACDC,WAAW;IACXC,SAAS;IACTC,OAAO;IACPC,SAAS;QACPC,aAAa;QACbC,aAAa;QACbC,aAAa;QACbC,WAAW;QACXjB,UAAU;QACVkB,aAAa;QACbC,mBAAmB;IACrB;AACF;AAEA,MAAMC,cAAuD,CAAC,EAAE7C,QAAQ,EAAE,iBACxE,qBAACD,0BAAY;kBACVC;;AAIL,wBAAwB;AACxB,MAAM,EAAEL,UAAU,EAAE,GAAGmD,QAAQ;AAE/BC,SAAS,yBAAyB;IAChCC,WAAW;QACT,qBAAqB;QACrBrD,WAAWC,WAAW,CAACqD,iBAAiB,CAAC;YACvC7C,IAAI;YACJ8C,SAAS;YACTpB,MAAM;YACNtB,OAAO;YACP2C,YAAY;YACZC,UAAU;YACVC,UAAU;YACVC,SAAS;YACTC,SAAS;QACX;QAEA5D,WAAWG,SAAS,CAACmD,iBAAiB,CAAC;YACrCO,SAAS,EAAE;YACXC,OAAO;QACT;QAEA3C,yBAAyB4C,eAAe,CAAC;YACvC,GAAGzC,eAAe;YAClB0C,mBAAmB,EAAE;YACrBC,iBAAiB,EAAE;YACnBC,SAASpE,KAAKI,EAAE;YAChBiE,oBAAoBrE,KAAKI,EAAE;QAC7B;IACF;IAEAkE,UAAU;QACRtE,KAAKuE,aAAa;IACpB;IAEAjB,SAAS,oBAAoB;QAC3BkB,GAAG,0CAA0C;YAC3CC,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACsB,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;gBAC7DH,OAAOC,cAAM,CAACC,SAAS,CAAC,uDAAuDC,iBAAiB;YAClG;QACF;QAEAP,GAAG,iCAAiC;YAClCC,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACsB,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,sBAAsBC,iBAAiB;YACjE;QACF;QAEAP,GAAG,uCAAuC;YACxCC,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACsB,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,mBAAmBC,iBAAiB;gBAC5DH,OAAOC,cAAM,CAACC,SAAS,CAAC,iBAAiBC,iBAAiB;gBAC1DH,OAAOC,cAAM,CAACC,SAAS,CAAC,mBAAmBC,iBAAiB;gBAC5DH,OAAOC,cAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;YACvD;QACF;QAEAP,GAAG,8BAA8B;YAC/BC,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACsB,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,sBAAsBC,iBAAiB;gBAC/DH,OAAOC,cAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;gBACrDH,OAAOC,cAAM,CAACC,SAAS,CAAC,mBAAmBC,iBAAiB;gBAC5DH,OAAOC,cAAM,CAACC,SAAS,CAAC,aAAaC,iBAAiB;YACxD;QACF;QAEAP,GAAG,8BAA8B;YAC/BC,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACsB,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZ,MAAMK,aAAaH,cAAM,CAACC,SAAS,CAAC;gBACpCG,iBAAS,CAACC,KAAK,CAACF;gBAChBJ,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC7D;QACF;QAEAP,GAAG,iDAAiD;YAClDC,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACsB,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZ,MAAMK,aAAaH,cAAM,CAACC,SAAS,CAAC;gBACpCG,iBAAS,CAACC,KAAK,CAACF;gBAEhBJ,OAAOC,cAAM,CAACC,SAAS,CAAC,sBAAsBC,iBAAiB;gBAC/DH,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;gBAC3DH,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC7D;QACF;QAEAP,GAAG,mCAAmC;YACpCnD,yBAAyB4C,eAAe,CAAC;gBACvC,GAAGzC,eAAe;gBAClBkB,WAAW;gBACXE,OAAO;gBACPsB,mBAAmB,EAAE;gBACrBC,iBAAiB,EAAE;gBACnBC,SAASpE,KAAKI,EAAE;gBAChBiE,oBAAoBrE,KAAKI,EAAE;YAC7B;YAEAqE,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACsB,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;gBAC9DH,OAAOC,cAAM,CAACC,SAAS,CAAC,sBAAsBC,iBAAiB;YACjE;QACF;QAEAP,GAAG,iDAAiD;YAClD,MAAMW,cAAcnF,KAAKI,EAAE;YAC3BiB,yBAAyB4C,eAAe,CAAC;gBACvC,GAAGzC,eAAe;gBAClB0C,mBAAmB,EAAE;gBACrBC,iBAAiB,EAAE;gBACnBC,SAASe;gBACTd,oBAAoBrE,KAAKI,EAAE;YAC7B;YAEAqE,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACsB,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZ,MAAMS,gBAAgBP,cAAM,CAACC,SAAS,CAAC;gBACvCG,iBAAS,CAACC,KAAK,CAACE;gBAChBR,OAAOO,aAAaE,gBAAgB;YACtC;QACF;IACF;IAEA/B,SAAS,yBAAyB;QAChCkB,GAAG,oCAAoC;YACrCC,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACkC,8BAAqB;oBAACC,YAAW;;;YAItC,MAAMZ,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;YAC/D;QACF;QAEAP,GAAG,2CAA2C;YAC5CC,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACkC,8BAAqB;oBAACC,YAAW;;;YAItC,MAAMZ,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,cAAcC,iBAAiB;YACzD;QACF;QAEAP,GAAG,kCAAkC;YACnCC,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACkC,8BAAqB;oBAACC,YAAW;;;YAItC,MAAMZ,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,iBAAiBC,iBAAiB;gBAC1DH,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC7D;QACF;QAEAP,GAAG,0CAA0C;YAC3CC,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACkC,8BAAqB;oBAACC,YAAW;;;YAItC,MAAMZ,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;gBAC7DH,OAAOC,cAAM,CAACC,SAAS,CAAC,YAAYC,iBAAiB;YACvD;QACF;QAEAP,GAAG,uCAAuC;YACxCC,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACkC,8BAAqB;oBAACC,YAAW;oBAASC,iBAAiB;;;YAIhE,MAAMb,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC7D;QACF;QAEAP,GAAG,qCAAqC;YACtCC,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACkC,8BAAqB;oBAACC,YAAW;;;YAItC,MAAMZ,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,mBAAmBC,iBAAiB;gBAC5DH,OAAOC,cAAM,CAACC,SAAS,CAAC,QAAQC,iBAAiB,IAAI,0BAA0B;YACjF;QACF;QAEAP,GAAG,oCAAoC;YACrCC,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACkC,8BAAqB;oBAACC,YAAW;;;YAItC,MAAMZ,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,gCAAgCC,iBAAiB;YAC3E;QACF;QAEAP,GAAG,oCAAoC;YACrCnD,yBAAyB4C,eAAe,CAAC;gBACvC,GAAGzC,eAAe;gBAClBkB,WAAW;gBACXwB,mBAAmB,EAAE;gBACrBC,iBAAiB,EAAE;gBACnBC,SAASpE,KAAKI,EAAE;gBAChBiE,oBAAoBrE,KAAKI,EAAE;YAC7B;YAEAqE,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACkC,8BAAqB;oBAACC,YAAW;;;YAItC,MAAMZ,IAAAA,eAAO,EAAC;gBACZC,OAAOC,cAAM,CAACC,SAAS,CAAC,iBAAiBC,iBAAiB;YAC5D;QACF;QAEAP,GAAG,6BAA6B;YAC9BnD,yBAAyB4C,eAAe,CAAC;gBACvC,GAAGzC,eAAe;gBAClBmB,SAAS;gBACTuB,mBAAmB,EAAE;gBACrBC,iBAAiB,EAAE;gBACnBC,SAASpE,KAAKI,EAAE;gBAChBiE,oBAAoBrE,KAAKI,EAAE;YAC7B;YAEAqE,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACkC,8BAAqB;oBAACC,YAAW;;;YAItCX,OAAOC,cAAM,CAACC,SAAS,CAAC,4BAA4BC,iBAAiB;QACvE;IACF;IAEAzB,SAAS,qBAAqB;QAC5BkB,GAAG,6DAA6D;YAC9D,MAAMiB,yBAAyBzF,KAAKI,EAAE;YACtCiB,yBAAyB4C,eAAe,CAAC;gBACvC,GAAGzC,eAAe;gBAClB0C,mBAAmB,EAAE;gBACrBC,iBAAiB,EAAE;gBACnBC,SAASpE,KAAKI,EAAE;gBAChBiE,oBAAoBoB;YACtB;YAEAhB,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACsB,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZC,OAAOvD,0BAA0BqE,oBAAoB,CAAC;oBACpDH,YAAYI;oBACZC,aAAa;oBACbC,gBAAgB;gBAClB;YACF;QACF;QAEArB,GAAG,mCAAmC;YACpCnD,yBAAyB4C,eAAe,CAAC;gBACvC,GAAGzC,eAAe;gBAClBY,SAAS,EAAE;gBACX8B,mBAAmB,EAAE;gBACrBC,iBAAiB,EAAE;gBACnBC,SAASpE,KAAKI,EAAE;gBAChBiE,oBAAoBrE,KAAKI,EAAE;YAC7B;YAEAqE,IAAAA,cAAM,gBACJ,qBAACrB;0BACC,cAAA,qBAACsB,aAAgB;;YAIrB,MAAMC,IAAAA,eAAO,EAAC;gBACZ,MAAMK,aAAaH,cAAM,CAACC,SAAS,CAAC;gBACpCG,iBAAS,CAACC,KAAK,CAACF;gBAChBJ,OAAOC,cAAM,CAACC,SAAS,CAAC,qBAAqBC,iBAAiB;YAChE;QACF;IACF;AACF"}