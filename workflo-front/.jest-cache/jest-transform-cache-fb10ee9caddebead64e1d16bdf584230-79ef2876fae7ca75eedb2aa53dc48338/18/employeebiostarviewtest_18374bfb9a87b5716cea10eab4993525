6ba7e8ec65db4b98938330141ee88be0
"use strict";
// Mock the BioStar attendance hook
jest.mock('@/hooks/useBiostarAttendance');
// Mock the BioStar API
jest.mock('@/lib/biostarApi', ()=>({
        biostarApi: {
            getUserById: jest.fn(),
            getEvents: jest.fn()
        }
    }));
// Mock Next.js router
jest.mock('next/navigation', ()=>({
        useRouter: ()=>({
                push: jest.fn(),
                back: jest.fn()
            }),
        useParams: ()=>({})
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _react1 = require("@testing-library/react");
const _AuthProvider = require("../providers/AuthProvider");
const _page = /*#__PURE__*/ _interop_require_default(require("../app/(auth)/(staff)/staff/info/biostar/page"));
const _EmployeeBiostarWidget = /*#__PURE__*/ _interop_require_default(require("../components/biostar/EmployeeBiostarWidget"));
const _useBiostarAttendance = require("../hooks/useBiostarAttendance");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockUseBiostarAttendance = _useBiostarAttendance.useBiostarAttendance;
const mockUser = {
    id: 1,
    employee_id: 'EMP001',
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
    role: 'employee'
};
const mockBiostarData = {
    todayAttendance: {
        id: 'att-001',
        employee_id: 'EMP001',
        employee_name: 'John Doe',
        date: '2024-12-21',
        first_in: '2024-12-21T08:30:00Z',
        last_out: null,
        total_hours: 0,
        break_time: 0,
        overtime: 0,
        status: 'PRESENT',
        events: [],
        biostar_synced: true
    },
    devices: [
        {
            id: 'dev-001',
            name: 'Main Entrance',
            ip: '*************',
            port: 8080,
            status: 'ONLINE',
            type: 'Fingerprint Scanner',
            location: 'Main Building'
        },
        {
            id: 'dev-002',
            name: 'Back Entrance',
            ip: '*************',
            port: 8080,
            status: 'OFFLINE',
            type: 'Face Recognition',
            location: 'Back Building'
        }
    ],
    connected: true,
    loading: false,
    error: null,
    summary: {
        todayStatus: 'PRESENT',
        checkInTime: '2024-12-21T08:30:00Z',
        hoursWorked: 0,
        breakTime: 0,
        overtime: 0,
        weeklyHours: 32,
        monthlyAttendance: 20
    }
};
const TestWrapper = ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)(_AuthProvider.AuthProvider, {
        children: children
    });
describe('Employee BioStar View', ()=>{
    beforeEach(()=>{
        mockUseBiostarAttendance.mockReturnValue({
            ...mockBiostarData,
            attendanceRecords: [],
            realtimeUpdates: [],
            refresh: jest.fn(),
            getAttendanceRange: jest.fn()
        });
    });
    afterEach(()=>{
        jest.clearAllMocks();
    });
    describe('StaffBiostarPage', ()=>{
        it('should render the BioStar profile page', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('BioStar Profile')).toBeInTheDocument();
                expect(_react1.screen.getByText('Manage your biometric data and view access history')).toBeInTheDocument();
            });
        });
        it('should show connection status', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('BioStar Connected')).toBeInTheDocument();
            });
        });
        it('should display profile status cards', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Profile Status')).toBeInTheDocument();
                expect(_react1.screen.getByText('Fingerprints')).toBeInTheDocument();
                expect(_react1.screen.getByText('Face Templates')).toBeInTheDocument();
                expect(_react1.screen.getByText('Devices')).toBeInTheDocument();
            });
        });
        it('should show tab navigation', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Biometric Profile')).toBeInTheDocument();
                expect(_react1.screen.getByText('Devices')).toBeInTheDocument();
                expect(_react1.screen.getByText('Access History')).toBeInTheDocument();
                expect(_react1.screen.getByText('Security')).toBeInTheDocument();
            });
        });
        it('should switch between tabs', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                const devicesTab = _react1.screen.getByText('Devices');
                _react1.fireEvent.click(devicesTab);
                expect(_react1.screen.getByText('System Status')).toBeInTheDocument();
            });
        });
        it('should show device information in devices tab', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                const devicesTab = _react1.screen.getByText('Devices');
                _react1.fireEvent.click(devicesTab);
                expect(_react1.screen.getByText('Available Devices')).toBeInTheDocument();
                expect(_react1.screen.getByText('Main Entrance')).toBeInTheDocument();
                expect(_react1.screen.getByText('Back Entrance')).toBeInTheDocument();
            });
        });
        it('should handle connection errors', async ()=>{
            mockUseBiostarAttendance.mockReturnValue({
                ...mockBiostarData,
                connected: false,
                error: 'Connection failed',
                attendanceRecords: [],
                realtimeUpdates: [],
                refresh: jest.fn(),
                getAttendanceRange: jest.fn()
            });
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Connection Error')).toBeInTheDocument();
                expect(_react1.screen.getByText('Connection failed')).toBeInTheDocument();
            });
        });
        it('should show refresh button and handle refresh', async ()=>{
            const mockRefresh = jest.fn();
            mockUseBiostarAttendance.mockReturnValue({
                ...mockBiostarData,
                attendanceRecords: [],
                realtimeUpdates: [],
                refresh: mockRefresh,
                getAttendanceRange: jest.fn()
            });
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                const refreshButton = _react1.screen.getByText('Refresh');
                _react1.fireEvent.click(refreshButton);
                expect(mockRefresh).toHaveBeenCalled();
            });
        });
    });
    describe('EmployeeBiostarWidget', ()=>{
        it('should render the BioStar widget', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('BioStar Profile')).toBeInTheDocument();
            });
        });
        it('should show connection status in widget', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Connected')).toBeInTheDocument();
            });
        });
        it('should display biometric stats', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Fingerprints')).toBeInTheDocument();
                expect(_react1.screen.getByText('Face Template')).toBeInTheDocument();
            });
        });
        it('should show today\'s attendance status', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Today\'s Status')).toBeInTheDocument();
                expect(_react1.screen.getByText('PRESENT')).toBeInTheDocument();
            });
        });
        it('should display recent access events', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001",
                    showFullDetails: true
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Recent Access')).toBeInTheDocument();
            });
        });
        it('should show device status summary', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Devices Online')).toBeInTheDocument();
                expect(_react1.screen.getByText('1/2')).toBeInTheDocument(); // 1 online out of 2 total
            });
        });
        it('should have link to full profile', async ()=>{
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('View Full BioStar Profile →')).toBeInTheDocument();
            });
        });
        it('should handle disconnected state', async ()=>{
            mockUseBiostarAttendance.mockReturnValue({
                ...mockBiostarData,
                connected: false,
                attendanceRecords: [],
                realtimeUpdates: [],
                refresh: jest.fn(),
                getAttendanceRange: jest.fn()
            });
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.getByText('Disconnected')).toBeInTheDocument();
            });
        });
        it('should show loading state', ()=>{
            mockUseBiostarAttendance.mockReturnValue({
                ...mockBiostarData,
                loading: true,
                attendanceRecords: [],
                realtimeUpdates: [],
                refresh: jest.fn(),
                getAttendanceRange: jest.fn()
            });
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_EmployeeBiostarWidget.default, {
                    employeeId: "EMP001"
                })
            }));
            expect(_react1.screen.getByText('Loading BioStar data...')).toBeInTheDocument();
        });
    });
    describe('Integration Tests', ()=>{
        it('should integrate with useBiostarAttendance hook correctly', async ()=>{
            const mockGetAttendanceRange = jest.fn();
            mockUseBiostarAttendance.mockReturnValue({
                ...mockBiostarData,
                attendanceRecords: [],
                realtimeUpdates: [],
                refresh: jest.fn(),
                getAttendanceRange: mockGetAttendanceRange
            });
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                expect(mockUseBiostarAttendance).toHaveBeenCalledWith({
                    employeeId: undefined,
                    autoRefresh: true,
                    enableRealTime: false
                });
            });
        });
        it('should handle empty device list', async ()=>{
            mockUseBiostarAttendance.mockReturnValue({
                ...mockBiostarData,
                devices: [],
                attendanceRecords: [],
                realtimeUpdates: [],
                refresh: jest.fn(),
                getAttendanceRange: jest.fn()
            });
            (0, _react1.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(TestWrapper, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_page.default, {})
            }));
            await (0, _react1.waitFor)(()=>{
                const devicesTab = _react1.screen.getByText('Devices');
                _react1.fireEvent.click(devicesTab);
                expect(_react1.screen.getByText('No Devices Found')).toBeInTheDocument();
            });
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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