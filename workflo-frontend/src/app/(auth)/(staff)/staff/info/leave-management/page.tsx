'use client';

import React, { useState } from 'react';
import {
  Calendar,
  Plus,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Edit,
  Trash2,
  Filter,
  Download,
  FileText,
  Settings,
  BarChart3,
  Gift
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { cn, formatDate } from '@/lib/utils';

const StaffLeaveManagementPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('application');
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [selectedLeaveType, setSelectedLeaveType] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  const tabs = [
    { id: 'application', name: 'Leave Application', icon: Plus },
    { id: 'types', name: 'Leave Types', icon: Settings },
    { id: 'holidays', name: 'Holidays', icon: Calendar },
    { id: 'timeoff', name: 'Time Off', icon: Clock },
    { id: 'balance', name: 'Leave Balance', icon: BarChart3 }
  ];

  // Mock leave data - in real app, this would come from API
  const leaveBalance = {
    annual: { total: 21, used: 6, remaining: 15 },
    sick: { total: 14, used: 2, remaining: 12 },
    maternity: { total: 90, used: 0, remaining: 90 },
    paternity: { total: 14, used: 0, remaining: 14 },
    compassionate: { total: 7, used: 1, remaining: 6 }
  };

  const leaveApplications = [
    {
      id: 1,
      leave_type: 'annual',
      start_date: '2024-12-25',
      end_date: '2024-12-29',
      days_requested: 5,
      reason: 'Christmas holiday with family',
      status: 'pending',
      applied_date: '2024-12-01',
      approved_by: null,
      reviewer_comments: null
    },
    {
      id: 2,
      leave_type: 'sick',
      start_date: '2024-11-15',
      end_date: '2024-11-16',
      days_requested: 2,
      reason: 'Flu symptoms and fever',
      status: 'approved',
      applied_date: '2024-11-14',
      approved_by: 'John Smith',
      reviewer_comments: 'Approved. Get well soon!'
    },
    {
      id: 3,
      leave_type: 'annual',
      start_date: '2024-10-10',
      end_date: '2024-10-12',
      days_requested: 3,
      reason: 'Personal matters',
      status: 'approved',
      applied_date: '2024-09-25',
      approved_by: 'John Smith',
      reviewer_comments: 'Approved'
    },
    {
      id: 4,
      leave_type: 'compassionate',
      start_date: '2024-09-05',
      end_date: '2024-09-05',
      days_requested: 1,
      reason: 'Attending funeral',
      status: 'approved',
      applied_date: '2024-09-04',
      approved_by: 'John Smith',
      reviewer_comments: 'Approved with sympathy'
    }
  ];

  // Mock data for new tabs
  const leaveTypes = [
    {
      id: 1,
      name: 'Annual Leave',
      description: 'Yearly vacation leave for rest and recreation',
      max_days: 21,
      carry_forward: true,
      requires_approval: true,
      notice_period: 7
    },
    {
      id: 2,
      name: 'Sick Leave',
      description: 'Medical leave for illness or medical appointments',
      max_days: 14,
      carry_forward: false,
      requires_approval: false,
      notice_period: 0
    },
    {
      id: 3,
      name: 'Maternity Leave',
      description: 'Leave for new mothers',
      max_days: 90,
      carry_forward: false,
      requires_approval: true,
      notice_period: 30
    },
    {
      id: 4,
      name: 'Paternity Leave',
      description: 'Leave for new fathers',
      max_days: 14,
      carry_forward: false,
      requires_approval: true,
      notice_period: 14
    }
  ];

  const holidays = [
    { id: 1, name: 'New Year\'s Day', date: '2025-01-01', type: 'Public Holiday' },
    { id: 2, name: 'Good Friday', date: '2025-04-18', type: 'Public Holiday' },
    { id: 3, name: 'Easter Monday', date: '2025-04-21', type: 'Public Holiday' },
    { id: 4, name: 'Labour Day', date: '2025-05-01', type: 'Public Holiday' },
    { id: 5, name: 'Madaraka Day', date: '2025-06-01', type: 'Public Holiday' },
    { id: 6, name: 'Mashujaa Day', date: '2025-10-20', type: 'Public Holiday' },
    { id: 7, name: 'Jamhuri Day', date: '2025-12-12', type: 'Public Holiday' },
    { id: 8, name: 'Christmas Day', date: '2025-12-25', type: 'Public Holiday' },
    { id: 9, name: 'Boxing Day', date: '2025-12-26', type: 'Public Holiday' }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'rejected': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getLeaveTypeColor = (type: string) => {
    switch (type) {
      case 'annual': return 'bg-blue-100 text-blue-800';
      case 'sick': return 'bg-red-100 text-red-800';
      case 'maternity': return 'bg-pink-100 text-pink-800';
      case 'paternity': return 'bg-purple-100 text-purple-800';
      case 'compassionate': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredApplications = filterStatus === 'all'
    ? leaveApplications
    : leaveApplications.filter(app => app.status === filterStatus);

  const handleApplyLeave = () => {
    setShowApplicationForm(true);
  };

  const handleSubmitApplication = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement leave application submission
    console.log('Submitting leave application');
    setShowApplicationForm(false);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Leave Management</h1>
          <p className="text-gray-600 mt-1">
            Manage your leave applications and view your leave balance
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Export Leave History
          </Button>
          <Button variant="primary" onClick={handleApplyLeave}>
            <Plus className="h-4 w-4 mr-2" />
            Apply for Leave
          </Button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  'flex items-center py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap',
                  activeTab === tab.id
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'application' && (
        <div className="space-y-6">
          {/* Leave Balance Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {Object.entries(leaveBalance).map(([type, balance]) => (
              <Card key={type}>
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-gray-700 capitalize">
                      {type.replace('_', ' ')} Leave
                    </h3>
                    <span className={cn(
                      'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                      getLeaveTypeColor(type)
                    )}>
                      {balance.remaining}
                    </span>
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs text-gray-600">
                      <span>Total: {balance.total}</span>
                      <span>Used: {balance.used}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-orange-500 h-2 rounded-full"
                        style={{ width: `${(balance.used / balance.total) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Leave Applications */}
          <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Leave Applications</h3>
            <div className="flex items-center space-x-2">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
              <Filter className="h-4 w-4 text-gray-400" />
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Leave Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Dates
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Days
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Applied Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredApplications.map((application) => (
                  <tr key={application.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={cn(
                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize',
                        getLeaveTypeColor(application.leave_type)
                      )}>
                        {application.leave_type.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(application.start_date)} - {formatDate(application.end_date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {application.days_requested}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getStatusIcon(application.status)}
                        <span className={cn(
                          'ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize',
                          getStatusColor(application.status)
                        )}>
                          {application.status}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(application.applied_date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center space-x-2">
                        <button className="text-orange-600 hover:text-orange-900">
                          <Eye className="h-4 w-4" />
                        </button>
                        {application.status === 'pending' && (
                          <>
                            <button className="text-blue-600 hover:text-blue-900">
                              <Edit className="h-4 w-4" />
                            </button>
                            <button className="text-red-600 hover:text-red-900">
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </Card>

      {/* Leave Application Form Modal */}
      {showApplicationForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Apply for Leave</h3>
            <form onSubmit={handleSubmitApplication} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Leave Type
                </label>
                <select
                  value={selectedLeaveType}
                  onChange={(e) => setSelectedLeaveType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                  required
                >
                  <option value="">Select leave type</option>
                  <option value="annual">Annual Leave</option>
                  <option value="sick">Sick Leave</option>
                  <option value="maternity">Maternity Leave</option>
                  <option value="paternity">Paternity Leave</option>
                  <option value="compassionate">Compassionate Leave</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Start Date
                  </label>
                  <input
                    type="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    End Date
                  </label>
                  <input
                    type="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reason
                </label>
                <textarea
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                  placeholder="Please provide a reason for your leave application"
                  required
                ></textarea>
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => setShowApplicationForm(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" variant="primary">
                  Submit Application
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
        </div>
      )}

      {/* Leave Types Tab */}
      {activeTab === 'types' && (
        <div className="space-y-6">
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Available Leave Types</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {leaveTypes.map((type) => (
                  <div key={type.id} className="border border-gray-200 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">{type.name}</h4>
                    <p className="text-sm text-gray-600 mb-3">{type.description}</p>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Maximum Days:</span>
                        <span className="font-medium">{type.max_days} days</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Carry Forward:</span>
                        <span className={cn(
                          'px-2 py-1 rounded-full text-xs font-medium',
                          type.carry_forward ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        )}>
                          {type.carry_forward ? 'Yes' : 'No'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Requires Approval:</span>
                        <span className={cn(
                          'px-2 py-1 rounded-full text-xs font-medium',
                          type.requires_approval ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                        )}>
                          {type.requires_approval ? 'Yes' : 'No'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Notice Period:</span>
                        <span className="font-medium">{type.notice_period} days</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Holidays Tab */}
      {activeTab === 'holidays' && (
        <div className="space-y-6">
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Company Holidays 2025</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Holiday Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Day of Week
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {holidays.map((holiday) => (
                      <tr key={holiday.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {holiday.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(holiday.date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {holiday.type}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(holiday.date).toLocaleDateString('en-US', { weekday: 'long' })}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Time Off Tab */}
      {activeTab === 'timeoff' && (
        <div className="space-y-6">
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Time Off Integration</h3>
              <div className="text-center py-8">
                <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-gray-900 mb-2">Time Off Tracking</h4>
                <p className="text-gray-600 mb-4">
                  This section integrates with your attendance records to show time off patterns and usage.
                </p>
                <Button variant="primary">
                  <Clock className="h-4 w-4 mr-2" />
                  View Attendance Records
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Leave Balance Tab */}
      {activeTab === 'balance' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Object.entries(leaveBalance).map(([type, balance]) => (
              <Card key={type}>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900 capitalize">
                      {type.replace('_', ' ')} Leave
                    </h3>
                    <span className={cn(
                      'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
                      getLeaveTypeColor(type)
                    )}>
                      {balance.remaining} days left
                    </span>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Total Allocated:</span>
                      <span className="font-medium">{balance.total} days</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Used:</span>
                      <span className="font-medium">{balance.used} days</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Remaining:</span>
                      <span className="font-medium text-green-600">{balance.remaining} days</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-orange-500 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${(balance.used / balance.total) * 100}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-500 text-center">
                      {Math.round((balance.used / balance.total) * 100)}% used
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default StaffLeaveManagementPage;
