# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, <PERSON> <<EMAIL>>, 2016
# <AUTHOR> <EMAIL>, 2020
# Dummy Iam, 2021
# <PERSON><PERSON><PERSON> / <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
# <PERSON> <<EMAIL>>, 2015,2019
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2014,2016
# Juyoung Lim, 2024
# <PERSON> <<EMAIL>>, 2025
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# 최소영, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# 정훈 이, 2021
# 박태진, 2021
# <PERSON> <<EMAIL>>, 2019
# Youngkwang <PERSON>, 2024
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 11:30-0500\n"
"PO-Revision-Date: 2025-04-01 15:04-0500\n"
"Last-Translator: Lee Dogeon <<EMAIL>>, 2025\n"
"Language-Team: Korean (http://app.transifex.com/django/django/language/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#, python-format
msgid "Delete selected %(verbose_name_plural)s"
msgstr "선택된 %(verbose_name_plural)s 을/를 삭제합니다."

#, python-format
msgid "Successfully deleted %(count)d %(items)s."
msgstr "%(count)d개의 %(items)s 을/를 성공적으로 삭제하였습니다."

#, python-format
msgid "Cannot delete %(name)s"
msgstr "%(name)s를 삭제할 수 없습니다."

msgid "Delete multiple objects"
msgstr "여러 개의 오브젝트 삭제"

msgid "Administration"
msgstr "관리"

msgid "All"
msgstr "모두"

msgid "Yes"
msgstr "예"

msgid "No"
msgstr "아니오"

msgid "Unknown"
msgstr "알 수 없습니다."

msgid "Any date"
msgstr "언제나"

msgid "Today"
msgstr "오늘"

msgid "Past 7 days"
msgstr "지난 7일"

msgid "This month"
msgstr "이번 달"

msgid "This year"
msgstr "이번 해"

msgid "No date"
msgstr "날짜 없음"

msgid "Has date"
msgstr "날짜 있음"

msgid "Empty"
msgstr "비어 있음"

msgid "Not empty"
msgstr "비어 있지 않음"

#, python-format
msgid ""
"Please enter the correct %(username)s and password for a staff account. Note "
"that both fields may be case-sensitive."
msgstr ""
"관리자 계정의 %(username)s 와 비밀번호를 입력해주세요. 대소문자를 구분해서 입"
"력해주세요."

msgid "Action:"
msgstr "액션:"

#, python-format
msgid "Add another %(verbose_name)s"
msgstr "%(verbose_name)s 더 추가하기"

msgid "Remove"
msgstr "삭제하기"

msgid "Addition"
msgstr "추가"

msgid "Change"
msgstr "변경"

msgid "Deletion"
msgstr "삭제"

msgid "action time"
msgstr "액션 타임"

msgid "user"
msgstr "사용자"

msgid "content type"
msgstr "콘텐츠 타입"

msgid "object id"
msgstr "오브젝트 아이디"

#. Translators: 'repr' means representation
#. (https://docs.python.org/library/functions.html#repr)
msgid "object repr"
msgstr "오브젝트 표현"

msgid "action flag"
msgstr "액션 플래그"

msgid "change message"
msgstr "메시지 변경"

msgid "log entry"
msgstr "로그 엔트리"

msgid "log entries"
msgstr "로그 엔트리"

#, python-format
msgid "Added “%(object)s”."
msgstr "\"%(object)s\"이/가 추가되었습니다."

#, python-format
msgid "Changed “%(object)s” — %(changes)s"
msgstr "\"%(object)s\"이/가 \"%(changes)s\"(으)로 변경되었습니다."

#, python-format
msgid "Deleted “%(object)s.”"
msgstr "%(object)s를 삭제했습니다."

msgid "LogEntry Object"
msgstr "로그 엔트리 객체"

#, python-brace-format
msgid "Added {name} “{object}”."
msgstr "{name} “{object}개체”를 추가했습니다."

msgid "Added."
msgstr "추가되었습니다."

msgid "and"
msgstr "또한"

#, python-brace-format
msgid "Changed {fields} for {name} “{object}”."
msgstr "{name} “{object}개체”의 {fields}필드를 변경했습니다."

#, python-brace-format
msgid "Changed {fields}."
msgstr "{fields}가 변경되었습니다."

#, python-brace-format
msgid "Deleted {name} “{object}”."
msgstr "{name} “{object}개체”를 삭제했습니다."

msgid "No fields changed."
msgstr "변경된 필드가 없습니다."

msgid "None"
msgstr "없음"

msgid "Hold down “Control”, or “Command” on a Mac, to select more than one."
msgstr ""
"하나 이상을 선택하려면 \"Control\" 키를 누른 채로 선택해주세요. Mac의 경우에"
"는 \"Command\" 키를 눌러주세요."

msgid "Select this object for an action - {}"
msgstr "작업에 대한 객체를 선택합니다. - {}"

#, python-brace-format
msgid "The {name} “{obj}” was added successfully."
msgstr "{name} \"{obj}\"가 성공적으로 추가되었습니다."

msgid "You may edit it again below."
msgstr "아래 내용을 수정해야 합니다."

#, python-brace-format
msgid ""
"The {name} “{obj}” was added successfully. You may add another {name} below."
msgstr ""
"{name} \"{obj}\"가 성공적으로 추가되었습니다.  아래에서 다른 {name}을 추가할 "
"수 있습니다."

#, python-brace-format
msgid ""
"The {name} “{obj}” was changed successfully. You may edit it again below."
msgstr ""
"{name} \"{obj}\"가 성공적으로 변경되었습니다. 아래에서 다시 수정할 수 있습니"
"다."

#, python-brace-format
msgid ""
"The {name} “{obj}” was changed successfully. You may add another {name} "
"below."
msgstr ""
"{name} \"{obj}\"가 성공적으로 변경되었습니다. 아래에서 다른 {name}을 추가할 "
"수 있습니다."

#, python-brace-format
msgid "The {name} “{obj}” was changed successfully."
msgstr "{name} \"{obj}\"가 성공적으로 변경되었습니다."

msgid ""
"Items must be selected in order to perform actions on them. No items have "
"been changed."
msgstr ""
"항목들에 액션을 적용하기 위해선 먼저 항목들이 선택되어 있어야 합니다. 아무 항"
"목도 변경되지 않았습니다."

msgid "No action selected."
msgstr "액션이 선택되지 않았습니다."

#, python-format
msgid "The %(name)s “%(obj)s” was deleted successfully."
msgstr "%(name)s \"%(obj)s\"이/가 성공적으로 삭제되었습니다."

#, python-format
msgid "%(name)s with ID “%(key)s” doesn’t exist. Perhaps it was deleted?"
msgstr ""
"ID \"%(key)s\"을/를 지닌%(name)s이/가 존재하지 않습니다. 삭제된 값이 아닌지 "
"확인해주세요."

#, python-format
msgid "Add %s"
msgstr "%s 추가"

#, python-format
msgid "Change %s"
msgstr "%s 변경"

#, python-format
msgid "View %s"
msgstr "뷰 %s"

msgid "Database error"
msgstr "데이터베이스 오류"

#, python-format
msgid "%(count)s %(name)s was changed successfully."
msgid_plural "%(count)s %(name)s were changed successfully."
msgstr[0] "%(count)s개의 %(name)s이/가 변경되었습니다."

#, python-format
msgid "%(total_count)s selected"
msgid_plural "All %(total_count)s selected"
msgstr[0] "총 %(total_count)s개가 선택되었습니다."

#, python-format
msgid "0 of %(cnt)s selected"
msgstr "%(cnt)s 중 아무것도 선택되지 않았습니다."

msgid "Delete"
msgstr "삭제"

#, python-format
msgid "Change history: %s"
msgstr "변경 히스토리: %s"

#. Translators: Model verbose name and instance
#. representation, suitable to be an item in a
#. list.
#, python-format
msgid "%(class_name)s %(instance)s"
msgstr "%(class_name)s %(instance)s"

#, python-format
msgid ""
"Deleting %(class_name)s %(instance)s would require deleting the following "
"protected related objects: %(related_objects)s"
msgstr ""
"%(class_name)s %(instance)s 을/를 삭제하려면 다음 보호상태의 연관된 오브젝트"
"들을 삭제해야 합니다: %(related_objects)s"

msgid "Django site admin"
msgstr "Django 사이트 관리"

msgid "Django administration"
msgstr "Django 관리"

msgid "Site administration"
msgstr "사이트 관리"

msgid "Log in"
msgstr "로그인"

#, python-format
msgid "%(app)s administration"
msgstr "%(app)s 관리"

msgid "Page not found"
msgstr "페이지를 찾을 수 없습니다."

msgid "We’re sorry, but the requested page could not be found."
msgstr "죄송합니다, 요청한 페이지를 찾을 수 없습니다."

msgid "Home"
msgstr "홈"

msgid "Server error"
msgstr "서버 오류"

msgid "Server error (500)"
msgstr "서버 오류 (500)"

msgid "Server Error <em>(500)</em>"
msgstr "서버 오류 <em>(500)</em>"

msgid ""
"There’s been an error. It’s been reported to the site administrators via "
"email and should be fixed shortly. Thanks for your patience."
msgstr ""
"오류가 발생했습니다. 사이트 관리자들에게 이메일로 보고되었고 단시일 내에 수정"
"될 것입니다. 기다려주셔서 감사합니다."

msgid "Run the selected action"
msgstr "선택한 액션을 실행합니다."

msgid "Go"
msgstr "실행"

msgid "Click here to select the objects across all pages"
msgstr "모든 페이지의 항목들을 선택하려면 여기를 클릭하세요."

#, python-format
msgid "Select all %(total_count)s %(module_name)s"
msgstr "%(total_count)s개의 %(module_name)s 모두를 선택합니다."

msgid "Clear selection"
msgstr "선택 해제"

msgid "Breadcrumbs"
msgstr "사용자 위치"

#, python-format
msgid "Models in the %(name)s application"
msgstr "%(name)s 애플리케이션의 모델"

msgid "Model name"
msgstr ""

msgid "Add link"
msgstr ""

msgid "Change or view list link"
msgstr ""

msgid "Add"
msgstr "추가"

msgid "View"
msgstr "보기"

msgid "You don’t have permission to view or edit anything."
msgstr "독자는 뷰 및 수정 권한이 없습니다."

msgid "After you’ve created a user, you’ll be able to edit more user options."
msgstr ""

msgid "Error:"
msgstr ""

msgid "Change password"
msgstr "비밀번호 변경"

msgid "Set password"
msgstr "비밀번호 설정"

msgid "Please correct the error below."
msgid_plural "Please correct the errors below."
msgstr[0] "아래 오류들을 수정하기 바랍니다. "

#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr "<strong>%(username)s</strong> 새로운 비밀번호를 입력하세요."

msgid ""
"This action will <strong>enable</strong> password-based authentication for "
"this user."
msgstr ""
"이 작업은 이 사용자에 대해 비밀번호 기반 인증을 <strong>활성화</strong>합니"
"다."

msgid "Disable password-based authentication"
msgstr "비밀번호 기반 인증 비활성화"

msgid "Enable password-based authentication"
msgstr "비밀번호 기반 인증 활성화"

msgid "Skip to main content"
msgstr "메인 콘텐츠로 이동"

msgid "Welcome,"
msgstr "환영합니다,"

msgid "View site"
msgstr "사이트 보기"

msgid "Documentation"
msgstr "문서"

msgid "Log out"
msgstr "로그아웃"

#, python-format
msgid "Add %(name)s"
msgstr "%(name)s 추가"

msgid "History"
msgstr "히스토리"

msgid "View on site"
msgstr "사이트에서 보기"

msgid "Filter"
msgstr "필터"

msgid "Hide counts"
msgstr "개수 숨기기"

msgid "Show counts"
msgstr "개수 표시"

msgid "Clear all filters"
msgstr "모든 필터 삭제"

msgid "Remove from sorting"
msgstr "정렬에서 "

#, python-format
msgid "Sorting priority: %(priority_number)s"
msgstr "정렬 조건 : %(priority_number)s"

msgid "Toggle sorting"
msgstr "정렬 "

msgid "Toggle theme (current theme: auto)"
msgstr "테마 토글 (현재 테마:자동)"

msgid "Toggle theme (current theme: light)"
msgstr "테마 토글 (현재 테마: 밝음)"

msgid "Toggle theme (current theme: dark)"
msgstr "테마 토글 (현재 테마: 어두움)"

#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would result in deleting "
"related objects, but your account doesn't have permission to delete the "
"following types of objects:"
msgstr ""
"%(object_name)s \"%(escaped_object)s\" 을/를 삭제하면서관련 오브젝트를 제거하"
"고자 했으나, 지금 사용하시는 계정은 다음 타입의 오브젝트를 제거할 권한이 없습"
"니다. :"

#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would require deleting the "
"following protected related objects:"
msgstr ""
"%(object_name)s '%(escaped_object)s'를 삭제하려면 다음 보호상태의 연관된 오브"
"젝트들을 삭제해야 합니다."

#, python-format
msgid ""
"Are you sure you want to delete the %(object_name)s \"%(escaped_object)s\"? "
"All of the following related items will be deleted:"
msgstr ""
"정말로 %(object_name)s \"%(escaped_object)s\"을/를 삭제하시겠습니까? 다음의 "
"관련 항목들이 모두 삭제됩니다. :"

msgid "Objects"
msgstr "오브젝트"

msgid "Yes, I’m sure"
msgstr "네, 확신합니다. "

msgid "No, take me back"
msgstr "아뇨, 돌려주세요."

#, python-format
msgid ""
"Deleting the selected %(objects_name)s would result in deleting related "
"objects, but your account doesn't have permission to delete the following "
"types of objects:"
msgstr ""
"연관 오브젝트 삭제로 선택한 %(objects_name)s의 삭제 중, 그러나 당신의 계정은 "
"다음 오브젝트의 삭제 권한이 없습니다. "

#, python-format
msgid ""
"Deleting the selected %(objects_name)s would require deleting the following "
"protected related objects:"
msgstr ""
"%(objects_name)s를 삭제하려면 다음 보호상태의 연관된 오브젝트들을 삭제해야 합"
"니다."

#, python-format
msgid ""
"Are you sure you want to delete the selected %(objects_name)s? All of the "
"following objects and their related items will be deleted:"
msgstr ""
"선택한 %(objects_name)s를 정말 삭제하시겠습니까? 다음의 오브젝트와 연관 아이"
"템들이 모두 삭제됩니다:"

msgid "Delete?"
msgstr "삭제"

#, python-format
msgid " By %(filter_title)s "
msgstr "%(filter_title)s (으)로"

msgid "Summary"
msgstr "개요"

msgid "Recent actions"
msgstr "최근 활동"

msgid "My actions"
msgstr "나의 활동"

msgid "None available"
msgstr "이용할 수 없습니다."

msgid "Added:"
msgstr "추가되었습니다:"

msgid "Changed:"
msgstr "변경:"

msgid "Deleted:"
msgstr "삭제:"

msgid "Unknown content"
msgstr "알 수 없는 형식입니다."

msgid ""
"Something’s wrong with your database installation. Make sure the appropriate "
"database tables have been created, and make sure the database is readable by "
"the appropriate user."
msgstr ""
"당신의 데이터베이스 설치, 설치본에 오류가 있습니다. \n"
"적합한 데이터베이스 테이블이 생성되었는지 확인하고, 데이터베이스가 적합한 사"
"용자가 열람할 수 있는 지 확인하십시오. "

#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr ""
"%(username)s 로 인증되어 있지만, 이 페이지에 접근 가능한 권한이 없습니다. 다"
"른 계정으로 로그인하시겠습니까?"

msgid "Forgotten your login credentials?"
msgstr ""

msgid "Toggle navigation"
msgstr "토글 메뉴"

msgid "Sidebar"
msgstr "사이드바"

msgid "Start typing to filter…"
msgstr "필터에 타이핑 시작..."

msgid "Filter navigation items"
msgstr "탐색 항목 필터링"

msgid "Date/time"
msgstr "날짜/시간"

msgid "User"
msgstr "사용자"

msgid "Action"
msgstr "액션"

msgid "entry"
msgid_plural "entries"
msgstr[0] "항목"

msgid ""
"This object doesn’t have a change history. It probably wasn’t added via this "
"admin site."
msgstr ""
"이 개체는 변경 기록이 없습니다. 아마도 이 관리자 사이트를 통해 추가되지 않았"
"을 것입니다. "

msgid "Show all"
msgstr "모두 표시"

msgid "Save"
msgstr "저장"

msgid "Popup closing…"
msgstr "팝업 닫는중..."

msgid "Search"
msgstr "검색"

#, python-format
msgid "%(counter)s result"
msgid_plural "%(counter)s results"
msgstr[0] "결과 %(counter)s개 나옴"

#, python-format
msgid "%(full_result_count)s total"
msgstr "총 %(full_result_count)s건"

msgid "Save as new"
msgstr "새로 저장"

msgid "Save and add another"
msgstr "저장 및 다른 이름으로 추가"

msgid "Save and continue editing"
msgstr "저장 및 편집 계속"

msgid "Save and view"
msgstr "저장하고 조회하기"

msgid "Close"
msgstr "닫기"

#, python-format
msgid "Change selected %(model)s"
msgstr "선택된 %(model)s 변경"

#, python-format
msgid "Add another %(model)s"
msgstr "%(model)s 추가"

#, python-format
msgid "Delete selected %(model)s"
msgstr "선택된 %(model)s 제거"

#, python-format
msgid "View selected %(model)s"
msgstr "선택된 %(model)s 보기"

msgid "Thanks for spending some quality time with the web site today."
msgstr "사이트를 이용해 주셔서 고맙습니다."

msgid "Log in again"
msgstr "다시 로그인하기"

msgid "Password change"
msgstr "비밀번호 변경"

msgid "Your password was changed."
msgstr "비밀번호가 변경되었습니다."

msgid ""
"Please enter your old password, for security’s sake, and then enter your new "
"password twice so we can verify you typed it in correctly."
msgstr ""
"독자의 과거 비밀번호를 입력한 후, 보안을 위해 새로운 비밀번호을 두 번 입력하"
"여 옳은 입력인 지 확인할 수 있도록 하십시오."

msgid "Change my password"
msgstr "비밀번호 변경"

msgid "Password reset"
msgstr "비밀번호 초기화"

msgid "Your password has been set.  You may go ahead and log in now."
msgstr "비밀번호가 설정되었습니다. 이제 로그인하세요."

msgid "Password reset confirmation"
msgstr "비밀번호 초기화 확인"

msgid ""
"Please enter your new password twice so we can verify you typed it in "
"correctly."
msgstr ""
"새로운 비밀번호를 정확히 입력했는지 확인할 수 있도록 두 번 입력하시기 바랍니"
"다."

msgid "New password:"
msgstr "새로운 비밀번호:"

msgid "Confirm password:"
msgstr "새로운 비밀번호 (확인):"

msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a new password reset."
msgstr ""
"비밀번호 초기화 링크가 이미 사용되어 올바르지 않습니다. 비밀번호 초기화를 다"
"시 해주세요."

msgid ""
"We’ve emailed you instructions for setting your password, if an account "
"exists with the email you entered. You should receive them shortly."
msgstr ""
"계정이 존재한다면, 독자가 입력한 이메일로 비밀번호 설정 안내문을 발송했습니"
"다. 곧 수신할 수 있을 것입니다. "

msgid ""
"If you don’t receive an email, please make sure you’ve entered the address "
"you registered with, and check your spam folder."
msgstr ""
"만약 이메일을 받지 못하였다면, 등록하신 이메일을 다시 확인하시거나 스팸 메일"
"함을 확인해주세요."

#, python-format
msgid ""
"You're receiving this email because you requested a password reset for your "
"user account at %(site_name)s."
msgstr ""
"%(site_name)s의 계정 비밀번호를 초기화하기 위한 요청으로 이 이메일이 전송되었"
"습니다."

msgid "Please go to the following page and choose a new password:"
msgstr "다음 페이지에서 새 비밀번호를 선택하세요."

msgid "In case you’ve forgotten, you are:"
msgstr ""

msgid "Thanks for using our site!"
msgstr "사이트를 이용해 주셔서 고맙습니다."

#, python-format
msgid "The %(site_name)s team"
msgstr "%(site_name)s 팀"

msgid ""
"Forgotten your password? Enter your email address below, and we’ll email "
"instructions for setting a new one."
msgstr ""
"비밀번호를 잊어버렸나요? 이메일 주소를 아래에 입력하시면 새로운 비밀번호를 설"
"정하는 절차를 이메일로 보내드리겠습니다."

msgid "Email address:"
msgstr "이메일 주소:"

msgid "Reset my password"
msgstr "비밀번호 초기화"

msgid "Select all objects on this page for an action"
msgstr "작업에 대한 이 페이지의 모든 객체를 선택합니다."

msgid "All dates"
msgstr "언제나"

#, python-format
msgid "Select %s"
msgstr "%s 선택"

#, python-format
msgid "Select %s to change"
msgstr "변경할 %s 선택"

#, python-format
msgid "Select %s to view"
msgstr "보기위한 1%s 를(을) 선택"

msgid "Date:"
msgstr "날짜:"

msgid "Time:"
msgstr "시각:"

msgid "Lookup"
msgstr "찾아보기"

msgid "Currently:"
msgstr "현재:"

msgid "Change:"
msgstr "변경:"
