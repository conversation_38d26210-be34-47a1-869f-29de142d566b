'use client';

import React, { useState, useEffect } from 'react';
import {
  Star,
  Plus,
  Search,
  Filter,
  TrendingUp,
  Target,
  Award,
  Calendar,
  User,
  MessageSquare,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { cn, formatDate } from '@/lib/utils';

interface PerformanceReview {
  id: number;
  employee_id: number;
  reviewer_id: number;
  reviewer_name: string;
  review_period: string;
  review_type: 'annual' | 'quarterly' | 'probation' | 'project';
  overall_rating: number;
  ratings: {
    technical_skills: number;
    communication: number;
    teamwork: number;
    leadership: number;
    problem_solving: number;
    time_management: number;
  };
  goals: Goal[];
  strengths: string[];
  areas_for_improvement: string[];
  reviewer_comments: string;
  employee_comments?: string;
  status: 'draft' | 'pending_employee' | 'pending_manager' | 'completed';
  created_date: string;
  completed_date?: string;
  next_review_date?: string;
}

interface Goal {
  id: number;
  title: string;
  description: string;
  target_date: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'overdue';
  progress_percentage: number;
  priority: 'low' | 'medium' | 'high';
}

interface PerformanceMetric {
  category: string;
  current_rating: number;
  previous_rating?: number;
  target_rating: number;
  trend: 'up' | 'down' | 'stable';
}

interface PerformanceTabProps {
  employeeId: number;
  employeeName: string;
  isCurrentUser?: boolean;
}

const PerformanceTab: React.FC<PerformanceTabProps> = ({
  employeeId,
  employeeName,
  isCurrentUser = false
}) => {
  const [activeSubTab, setActiveSubTab] = useState<'reviews' | 'goals' | 'metrics' | 'feedback'>('reviews');
  const [performanceReviews, setPerformanceReviews] = useState<PerformanceReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterStatus, setFilterStatus] = useState('');

  // Mock data
  const mockPerformanceReviews: PerformanceReview[] = [
    {
      id: 1,
      employee_id: employeeId,
      reviewer_id: 2,
      reviewer_name: 'John Smith',
      review_period: '2024 Q1',
      review_type: 'quarterly',
      overall_rating: 4.2,
      ratings: {
        technical_skills: 4.5,
        communication: 4.0,
        teamwork: 4.3,
        leadership: 3.8,
        problem_solving: 4.4,
        time_management: 4.1
      },
      goals: [
        {
          id: 1,
          title: 'Complete React Certification',
          description: 'Obtain React Developer certification to enhance frontend skills',
          target_date: '2024-03-31',
          status: 'completed',
          progress_percentage: 100,
          priority: 'high'
        },
        {
          id: 2,
          title: 'Lead Team Project',
          description: 'Successfully lead the new customer portal project',
          target_date: '2024-04-30',
          status: 'in_progress',
          progress_percentage: 75,
          priority: 'high'
        }
      ],
      strengths: [
        'Excellent technical problem-solving abilities',
        'Strong collaboration with team members',
        'Proactive in learning new technologies'
      ],
      areas_for_improvement: [
        'Could improve presentation skills',
        'Time management during peak periods'
      ],
      reviewer_comments: 'Maria has shown excellent growth this quarter. Her technical skills continue to improve, and she has taken on more leadership responsibilities. Looking forward to seeing her continued development.',
      employee_comments: 'Thank you for the feedback. I agree with the areas for improvement and will focus on enhancing my presentation skills.',
      status: 'completed',
      created_date: '2024-01-15T00:00:00Z',
      completed_date: '2024-01-25T00:00:00Z',
      next_review_date: '2024-04-15T00:00:00Z'
    },
    {
      id: 2,
      employee_id: employeeId,
      reviewer_id: 2,
      reviewer_name: 'John Smith',
      review_period: '2023 Annual',
      review_type: 'annual',
      overall_rating: 3.9,
      ratings: {
        technical_skills: 4.2,
        communication: 3.7,
        teamwork: 4.1,
        leadership: 3.5,
        problem_solving: 4.0,
        time_management: 3.8
      },
      goals: [
        {
          id: 3,
          title: 'Improve Code Quality',
          description: 'Implement better testing practices and code reviews',
          target_date: '2023-12-31',
          status: 'completed',
          progress_percentage: 100,
          priority: 'medium'
        }
      ],
      strengths: [
        'Strong technical foundation',
        'Reliable team member',
        'Good attention to detail'
      ],
      areas_for_improvement: [
        'Communication with stakeholders',
        'Taking initiative on projects'
      ],
      reviewer_comments: 'Maria has been a solid contributor to the team. She has strong technical skills and is very reliable. Would like to see her take on more leadership opportunities.',
      status: 'completed',
      created_date: '2023-12-01T00:00:00Z',
      completed_date: '2023-12-15T00:00:00Z',
      next_review_date: '2024-01-15T00:00:00Z'
    }
  ];

  const mockPerformanceMetrics: PerformanceMetric[] = [
    {
      category: 'Technical Skills',
      current_rating: 4.5,
      previous_rating: 4.2,
      target_rating: 4.7,
      trend: 'up'
    },
    {
      category: 'Communication',
      current_rating: 4.0,
      previous_rating: 3.7,
      target_rating: 4.3,
      trend: 'up'
    },
    {
      category: 'Teamwork',
      current_rating: 4.3,
      previous_rating: 4.1,
      target_rating: 4.5,
      trend: 'up'
    },
    {
      category: 'Leadership',
      current_rating: 3.8,
      previous_rating: 3.5,
      target_rating: 4.2,
      trend: 'up'
    },
    {
      category: 'Problem Solving',
      current_rating: 4.4,
      previous_rating: 4.0,
      target_rating: 4.6,
      trend: 'up'
    },
    {
      category: 'Time Management',
      current_rating: 4.1,
      previous_rating: 3.8,
      target_rating: 4.4,
      trend: 'up'
    }
  ];

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setTimeout(() => {
        setPerformanceReviews(mockPerformanceReviews);
        setLoading(false);
      }, 1000);
    };

    loadData();
  }, [employeeId]);

  const filteredReviews = performanceReviews.filter(review => {
    const matchesSearch = review.review_period.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         review.reviewer_comments.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = !filterType || review.review_type === filterType;
    const matchesStatus = !filterStatus || review.status === filterStatus;
    return matchesSearch && matchesType && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'pending_employee': return 'bg-yellow-100 text-yellow-800';
      case 'pending_manager': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'draft': return <Edit className="h-4 w-4" />;
      case 'pending_employee': case 'pending_manager': return <Clock className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getGoalStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'not_started': return 'bg-gray-100 text-gray-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStarRating = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: 'h-3 w-3',
      md: 'h-4 w-4',
      lg: 'h-5 w-5'
    };

    return (
      <div className="flex items-center space-x-1">
        {Array.from({ length: 5 }, (_, index) => (
          <Star
            key={index}
            className={cn(
              sizeClasses[size],
              index < Math.floor(rating)
                ? 'text-yellow-400 fill-current'
                : index < rating
                ? 'text-yellow-400 fill-current opacity-50'
                : 'text-gray-300'
            )}
          />
        ))}
        <span className="ml-2 text-sm font-medium text-gray-700">
          {rating.toFixed(1)}
        </span>
      </div>
    );
  };

  const latestReview = performanceReviews.find(r => r.status === 'completed');
  const overallRating = latestReview?.overall_rating || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Performance Reviews</h3>
          <p className="text-sm text-gray-600 mt-1">
            Track performance evaluations and goals for {employeeName}
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          {latestReview && (
            <div className="text-right">
              <div className="text-sm text-gray-600">Current Rating</div>
              {renderStarRating(overallRating, 'lg')}
            </div>
          )}
          {!isCurrentUser && (
            <Button variant="primary">
              <Plus className="h-4 w-4 mr-2" />
              New Review
            </Button>
          )}
        </div>
      </div>

      {/* Sub Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {[
            { key: 'reviews', label: 'Performance Reviews', icon: Star },
            { key: 'goals', label: 'Goals & Objectives', icon: Target },
            { key: 'metrics', label: 'Performance Metrics', icon: TrendingUp },
            { key: 'feedback', label: '360° Feedback', icon: MessageSquare },
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.key}
                onClick={() => setActiveSubTab(tab.key as any)}
                className={cn(
                  'py-2 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2',
                  activeSubTab === tab.key
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeSubTab === 'reviews' && (
        <PerformanceReviewsView
          reviews={filteredReviews}
          loading={loading}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          filterType={filterType}
          setFilterType={setFilterType}
          filterStatus={filterStatus}
          setFilterStatus={setFilterStatus}
          renderStarRating={renderStarRating}
        />
      )}

      {activeSubTab === 'goals' && (
        <GoalsView reviews={performanceReviews} loading={loading} />
      )}

      {activeSubTab === 'metrics' && (
        <MetricsView metrics={mockPerformanceMetrics} renderStarRating={renderStarRating} />
      )}

      {activeSubTab === 'feedback' && (
        <FeedbackView employeeName={employeeName} />
      )}
    </div>
  );
};

// Performance Reviews View Component
interface PerformanceReviewsViewProps {
  reviews: PerformanceReview[];
  loading: boolean;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filterType: string;
  setFilterType: (type: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  renderStarRating: (rating: number, size?: 'sm' | 'md' | 'lg') => React.ReactElement;
}

const PerformanceReviewsView: React.FC<PerformanceReviewsViewProps> = ({
  reviews,
  loading,
  searchQuery,
  setSearchQuery,
  filterType,
  setFilterType,
  filterStatus,
  setFilterStatus,
  renderStarRating
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'pending_employee': return 'bg-yellow-100 text-yellow-800';
      case 'pending_manager': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'draft': return <Edit className="h-4 w-4" />;
      case 'pending_employee': case 'pending_manager': return <Clock className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <div className="flex-1">
          <Input
            placeholder="Search reviews..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="h-4 w-4" />}
          />
        </div>
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="">All Types</option>
          <option value="annual">Annual</option>
          <option value="quarterly">Quarterly</option>
          <option value="probation">Probation</option>
          <option value="project">Project</option>
        </select>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="">All Status</option>
          <option value="completed">Completed</option>
          <option value="pending_employee">Pending Employee</option>
          <option value="pending_manager">Pending Manager</option>
          <option value="draft">Draft</option>
        </select>
      </div>

      {/* Reviews List */}
      {loading ? (
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      ) : reviews.length === 0 ? (
        <div className="text-center py-12">
          <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No performance reviews found
          </h3>
          <p className="text-gray-600">
            No reviews found for the selected criteria.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {reviews.map((review) => (
            <Card key={review.id}>
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="text-lg font-medium text-gray-900">
                      {review.review_period} - {review.review_type.replace('_', ' ').toUpperCase()}
                    </h4>
                    <p className="text-sm text-gray-600">
                      Reviewed by {review.reviewer_name}
                    </p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={cn(
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      getStatusColor(review.status)
                    )}>
                      {getStatusIcon(review.status)}
                      <span className="ml-1">{review.status.replace('_', ' ').toUpperCase()}</span>
                    </span>
                    {renderStarRating(review.overall_rating)}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h5 className="text-sm font-medium text-gray-900 mb-2">Ratings Breakdown</h5>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Technical Skills:</span>
                        {renderStarRating(review.ratings.technical_skills, 'sm')}
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Communication:</span>
                        {renderStarRating(review.ratings.communication, 'sm')}
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Teamwork:</span>
                        {renderStarRating(review.ratings.teamwork, 'sm')}
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium text-gray-900 mb-2">Goals Progress</h5>
                    <div className="space-y-2">
                      {review.goals.slice(0, 2).map((goal) => (
                        <div key={goal.id} className="text-sm">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-gray-900 truncate">{goal.title}</span>
                            <span className="text-xs text-gray-600">{goal.progress_percentage}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-1.5">
                            <div
                              className="bg-orange-600 h-1.5 rounded-full transition-all duration-300"
                              style={{ width: `${goal.progress_percentage}%` }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium text-gray-900 mb-2">Comments</h5>
                    <p className="text-sm text-gray-600 line-clamp-3">
                      {review.reviewer_comments}
                    </p>
                    {review.completed_date && (
                      <p className="text-xs text-gray-500 mt-2">
                        Completed: {formatDate(review.completed_date)}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

// Goals View Component
interface GoalsViewProps {
  reviews: PerformanceReview[];
  loading: boolean;
}

const GoalsView: React.FC<GoalsViewProps> = ({ reviews, loading }) => {
  const allGoals = reviews.flatMap(review => review.goals);

  const getGoalStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'not_started': return 'bg-gray-100 text-gray-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {allGoals.map((goal) => (
        <Card key={goal.id}>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex-1">
                <h4 className="text-lg font-medium text-gray-900">{goal.title}</h4>
                <p className="text-sm text-gray-600 mt-1">{goal.description}</p>
              </div>
              <div className="flex items-center space-x-2">
                <span className={cn(
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  getPriorityColor(goal.priority)
                )}>
                  {goal.priority.toUpperCase()}
                </span>
                <span className={cn(
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  getGoalStatusColor(goal.status)
                )}>
                  {goal.status.replace('_', ' ').toUpperCase()}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Target Date</label>
                <p className="text-sm text-gray-900">{formatDate(goal.target_date)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Progress</label>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-orange-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${goal.progress_percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900">{goal.progress_percentage}%</span>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Status</label>
                <p className="text-sm text-gray-900 capitalize">{goal.status.replace('_', ' ')}</p>
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

// Metrics View Component
interface MetricsViewProps {
  metrics: any[];
  renderStarRating: (rating: number, size?: 'sm' | 'md' | 'lg') => React.ReactElement;
}

const MetricsView: React.FC<MetricsViewProps> = ({ metrics, renderStarRating }) => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {metrics.map((metric, index) => (
          <Card key={index}>
            <div className="p-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">{metric.category}</h4>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Current:</span>
                  {renderStarRating(metric.current_rating, 'sm')}
                </div>

                {metric.previous_rating && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Previous:</span>
                    {renderStarRating(metric.previous_rating, 'sm')}
                  </div>
                )}

                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Target:</span>
                  {renderStarRating(metric.target_rating, 'sm')}
                </div>
              </div>

              <div className="mt-4 flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">Trend:</span>
                <div className="flex items-center">
                  {metric.trend === 'up' && <TrendingUp className="h-4 w-4 text-green-600" />}
                  {metric.trend === 'down' && <TrendingUp className="h-4 w-4 text-red-600 transform rotate-180" />}
                  {metric.trend === 'stable' && <div className="h-4 w-4 bg-gray-400 rounded-full"></div>}
                  <span className={cn(
                    "ml-1 text-sm font-medium",
                    metric.trend === 'up' && "text-green-600",
                    metric.trend === 'down' && "text-red-600",
                    metric.trend === 'stable' && "text-gray-600"
                  )}>
                    {metric.trend.toUpperCase()}
                  </span>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

// Feedback View Component
interface FeedbackViewProps {
  employeeName: string;
}

const FeedbackView: React.FC<FeedbackViewProps> = ({ employeeName }) => {
  return (
    <div className="text-center py-12">
      <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        360° Feedback System
      </h3>
      <p className="text-gray-600 mb-6">
        Comprehensive feedback collection from peers, supervisors, and subordinates for {employeeName}.
      </p>
      <Button variant="primary">
        <Plus className="h-4 w-4 mr-2" />
        Request Feedback
      </Button>
    </div>
  );
};

export default PerformanceTab;
