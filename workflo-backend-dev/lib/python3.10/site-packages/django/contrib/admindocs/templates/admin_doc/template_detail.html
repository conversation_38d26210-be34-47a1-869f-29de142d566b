{% extends "admin/base_site.html" %}
{% load i18n %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; <a href="{% url 'django-admindocs-docroot' %}">{% translate 'Documentation' %}</a>
&rsaquo; {% translate 'Templates' %}
&rsaquo; {{ name }}
</div>
{% endblock %}

{% block title %}{% blocktranslate %}Template: {{ name }}{% endblocktranslate %}{% endblock %}

{% block content %}
<h1>{% blocktranslate %}Template: <q>{{ name }}</q>{% endblocktranslate %}</h1>

{# Translators: Search is not a verb here, it qualifies path (a search path) #}
<h2>{% blocktranslate %}Search path for template <q>{{ name }}</q>:{% endblocktranslate %}</h2>
<ol>
{% for template in templates|dictsort:"order" %}
    <li><code>{{ template.file }}</code>{% if not template.exists %} <em>{% translate '(does not exist)' %}</em>{% endif %}</li>
{% endfor %}
</ol>

<p><a href="{% url 'django-admindocs-docroot' %}">&lsaquo; {% translate 'Back to Documentation' %}</a></p>
{% endblock %}
