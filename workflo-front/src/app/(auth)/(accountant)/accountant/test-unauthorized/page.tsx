'use client';

import React from 'react';
import RoleGuard from '@/components/auth/RoleGuard';
import Card from '@/components/ui/Card';

const TestUnauthorizedPage: React.FC = () => {
  return (
    <RoleGuard allowedRoles={['admin', 'hr']}>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Admin Only Page</h1>
          <p className="text-gray-600">This page is only accessible to admin and HR users</p>
        </div>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Features</h3>
          <p className="text-gray-600">
            This content should only be visible to admin and HR users. 
            If you're seeing this as an accountant, something is wrong!
          </p>
        </Card>
      </div>
    </RoleGuard>
  );
};

export default TestUnauthorizedPage;
