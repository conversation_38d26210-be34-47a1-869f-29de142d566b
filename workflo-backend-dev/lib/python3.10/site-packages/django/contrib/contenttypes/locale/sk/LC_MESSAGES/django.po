# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2012
# <PERSON> <<EMAIL>>, 2017,2023
# <PERSON>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2023-12-04 19:22+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017,2023\n"
"Language-Team: Slovak (http://app.transifex.com/django/django/language/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n "
">= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

msgid "Content Types"
msgstr "Typy Obsahu"

msgid "python model class name"
msgstr "pythonovské meno triedy modelu"

msgid "content type"
msgstr "typ obsahu"

msgid "content types"
msgstr "typy obsahu"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Objekt typu obsahu %(ct_id)s  nemá pridružený model"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Objekt %(obj_id)s typu obsahu %(ct_id)s neexistuje"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr " Objekty %(ct_name)s nemajú metódu get_absolute_url()"
