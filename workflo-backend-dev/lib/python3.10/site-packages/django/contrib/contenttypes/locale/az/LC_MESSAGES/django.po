# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2011
# Emin Mast<PERSON> <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2016
# Ni<PERSON><PERSON>, 2024
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2024-08-07 19:22+0000\n"
"Last-Translator: Nijat Mammadov, 2024\n"
"Language-Team: Azerbaijani (http://app.transifex.com/django/django/language/"
"az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Məzmun Növləri"

msgid "python model class name"
msgstr "python modelinin sinif (class) adı"

msgid "content type"
msgstr "məzmun tipi"

msgid "content types"
msgstr "məzmun tipləri"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "%(ct_id)s məzmun növü obyekti ilə əlaqəli model yoxdur"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "%(ct_id)s məzmun növlü %(obj_id)s obyekti mövcut deyil"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s obyektlərinin get_absolute_url() metodu yoxdur"
