# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Tamil (http://www.transifex.com/django/django/language/ta/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ta\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr ""

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr "உதாரணம்: '/about/contact/'. முன்னும் பின்னும் '/' உள்ளதை உறுதி செய்க."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""

msgid "URL is missing a leading slash."
msgstr ""

msgid "URL is missing a trailing slash."
msgstr ""

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""

msgid "title"
msgstr "தலைப்பு"

msgid "content"
msgstr "பொருளடக்கம்"

msgid "enable comments"
msgstr "விமர்சனங்களை செயலாக்கு"

msgid "template name"
msgstr "வார்ப்புரு பெயர்"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"உதாரணம்  'flatpages/contact_page'. இது இல்லையெனில் 'flatpages/default' என்பதே "
"பயன்படுத்தப்படும்.ப்படும்."

msgid "registration required"
msgstr "முன்பதிவு தேவை"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"இது தெரிவு செய்யப்பட்டிருந்தால், உள்நுழைந்த பயனர்கள் மட்டுமே இந்தப் பக்கத்தை பார்க்க முடியும்."

msgid "sites"
msgstr ""

msgid "flat page"
msgstr "எளிய பக்கம்"

msgid "flat pages"
msgstr "எளிய பக்கங்கள்"
