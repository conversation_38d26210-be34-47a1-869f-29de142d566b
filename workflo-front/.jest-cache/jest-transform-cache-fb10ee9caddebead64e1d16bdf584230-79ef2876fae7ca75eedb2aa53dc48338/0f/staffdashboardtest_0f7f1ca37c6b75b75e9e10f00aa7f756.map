{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/__tests__/integration/staff-dashboard.test.tsx"], "sourcesContent": ["import React from 'react';\nimport { screen, waitFor } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nimport { render } from '@/__tests__/utils/test-utils';\nimport StaffDashboard from '@/app/(auth)/(staff)/staff/page';\n\n// Mock the current time for consistent testing\nconst mockDate = new Date('2024-12-20T10:30:00Z');\njest.useFakeTimers();\njest.setSystemTime(mockDate);\n\ndescribe('Staff Dashboard Integration', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  afterAll(() => {\n    jest.useRealTimers();\n  });\n\n  describe('dashboard rendering', () => {\n    it('renders welcome message with user name', () => {\n      render(<StaffDashboard />);\n\n      expect(screen.getByText(/Good morning, John!/)).toBeInTheDocument();\n      expect(screen.getByText(/Friday, December 20, 2024/)).toBeInTheDocument();\n    });\n\n    it('displays current time', () => {\n      render(<StaffDashboard />);\n\n      // The time should be displayed (mocked to 10:30:00)\n      expect(screen.getByText(/10:30/)).toBeInTheDocument();\n    });\n\n    it('shows today\\'s stats cards', () => {\n      render(<StaffDashboard />);\n\n      expect(screen.getByText('Check-in Time')).toBeInTheDocument();\n      expect(screen.getByText('Hours Worked')).toBeInTheDocument();\n      expect(screen.getByText('Tasks Completed')).toBeInTheDocument();\n      expect(screen.getByText('Leave Balance')).toBeInTheDocument();\n    });\n  });\n\n  describe('quick actions', () => {\n    it('renders all quick action buttons', () => {\n      render(<StaffDashboard />);\n\n      expect(screen.getByText('Check In')).toBeInTheDocument();\n      expect(screen.getByText('Apply Leave')).toBeInTheDocument();\n      expect(screen.getByText('View Payslip')).toBeInTheDocument();\n      expect(screen.getByText('Submit Ticket')).toBeInTheDocument();\n    });\n\n    it('handles check-in action', async () => {\n      const user = userEvent.setup();\n      \n      render(<StaffDashboard />);\n\n      const checkInButton = screen.getByText('Check In');\n      await user.click(checkInButton);\n\n      // Should show some feedback or change state\n      // This would depend on the actual implementation\n    });\n  });\n\n  describe('recent activities', () => {\n    it('displays recent activities section', () => {\n      render(<StaffDashboard />);\n\n      expect(screen.getByText('Recent Activities')).toBeInTheDocument();\n      \n      // Check for some sample activities\n      expect(screen.getByText('Checked in at 08:30 AM')).toBeInTheDocument();\n      expect(screen.getByText('Submitted leave application')).toBeInTheDocument();\n      expect(screen.getByText('Completed project milestone')).toBeInTheDocument();\n    });\n\n    it('shows view all activities link', () => {\n      render(<StaffDashboard />);\n\n      expect(screen.getByText('View All Activities')).toBeInTheDocument();\n    });\n  });\n\n  describe('upcoming events', () => {\n    it('displays upcoming events section', () => {\n      render(<StaffDashboard />);\n\n      expect(screen.getByText('Upcoming Events')).toBeInTheDocument();\n      \n      // Check for sample events\n      expect(screen.getByText('Team Meeting')).toBeInTheDocument();\n      expect(screen.getByText('Performance Review')).toBeInTheDocument();\n      expect(screen.getByText('Company Holiday')).toBeInTheDocument();\n    });\n\n    it('shows event details', () => {\n      render(<StaffDashboard />);\n\n      expect(screen.getByText('Weekly team sync meeting')).toBeInTheDocument();\n      expect(screen.getByText('Q4 performance review session')).toBeInTheDocument();\n      expect(screen.getByText('Christmas Day - Office Closed')).toBeInTheDocument();\n    });\n  });\n\n  describe('announcements', () => {\n    it('displays announcements section', () => {\n      render(<StaffDashboard />);\n\n      expect(screen.getByText('Announcements')).toBeInTheDocument();\n      \n      // Check for sample announcements\n      expect(screen.getByText('New Health Insurance Policy')).toBeInTheDocument();\n      expect(screen.getByText('Office Renovation Update')).toBeInTheDocument();\n    });\n\n    it('shows announcement details', () => {\n      render(<StaffDashboard />);\n\n      expect(screen.getByText(/Enhanced health insurance coverage/)).toBeInTheDocument();\n      expect(screen.getByText(/Office renovation will begin/)).toBeInTheDocument();\n    });\n  });\n\n  describe('responsive behavior', () => {\n    it('adapts layout for mobile screens', () => {\n      // Mock mobile viewport\n      Object.defineProperty(window, 'innerWidth', {\n        writable: true,\n        configurable: true,\n        value: 375,\n      });\n\n      render(<StaffDashboard />);\n\n      // Check that grid layouts adapt to mobile\n      const statsGrid = screen.getByText('Check-in Time').closest('.grid');\n      expect(statsGrid).toHaveClass('grid-cols-1');\n    });\n  });\n\n  describe('real-time updates', () => {\n    it('updates time every second', async () => {\n      render(<StaffDashboard />);\n\n      // Initial time\n      expect(screen.getByText(/10:30/)).toBeInTheDocument();\n\n      // Advance time by 1 minute\n      jest.advanceTimersByTime(60000);\n\n      await waitFor(() => {\n        expect(screen.getByText(/10:31/)).toBeInTheDocument();\n      });\n    });\n\n    it('updates working hours when checked in', async () => {\n      const user = userEvent.setup();\n      \n      render(<StaffDashboard />);\n\n      // Simulate check-in\n      const checkInButton = screen.getByText('Check In');\n      await user.click(checkInButton);\n\n      // Advance time to simulate working\n      jest.advanceTimersByTime(3600000); // 1 hour\n\n      // Should show updated working hours\n      await waitFor(() => {\n        expect(screen.getByText(/1:00/)).toBeInTheDocument(); // 1 hour worked\n      });\n    });\n  });\n\n  describe('error handling', () => {\n    it('handles missing user data gracefully', () => {\n      render(<StaffDashboard />, {\n        authContextValue: {\n          user: null,\n          isAuthenticated: true,\n          isLoading: false,\n          login: jest.fn(),\n          logout: jest.fn(),\n          getCurrentUser: jest.fn(),\n          updateProfile: jest.fn(),\n          changePassword: jest.fn(),\n        }\n      });\n\n      // Should still render without crashing\n      expect(screen.getByText(/Good morning/)).toBeInTheDocument();\n    });\n\n    it('shows loading state when user data is loading', () => {\n      render(<StaffDashboard />, {\n        authContextValue: {\n          user: null,\n          isAuthenticated: false,\n          isLoading: true,\n          login: jest.fn(),\n          logout: jest.fn(),\n          getCurrentUser: jest.fn(),\n          updateProfile: jest.fn(),\n          changePassword: jest.fn(),\n        }\n      });\n\n      // Should show loading state\n      expect(screen.getByText('Checking permissions...')).toBeInTheDocument();\n    });\n  });\n\n  describe('accessibility', () => {\n    it('has proper heading structure', () => {\n      render(<StaffDashboard />);\n\n      // Check for proper heading hierarchy\n      const mainHeading = screen.getByRole('heading', { level: 1 });\n      expect(mainHeading).toBeInTheDocument();\n\n      const sectionHeadings = screen.getAllByRole('heading', { level: 2 });\n      expect(sectionHeadings.length).toBeGreaterThan(0);\n    });\n\n    it('has accessible buttons', () => {\n      render(<StaffDashboard />);\n\n      const buttons = screen.getAllByRole('button');\n      buttons.forEach(button => {\n        expect(button).toBeVisible();\n        // Each button should have accessible text\n        expect(button).toHaveTextContent(/.+/);\n      });\n    });\n\n    it('has proper ARIA labels where needed', () => {\n      render(<StaffDashboard />);\n\n      // Check for ARIA labels on interactive elements\n      const timeDisplay = screen.getByText(/10:30/);\n      expect(timeDisplay.closest('[role=\"timer\"]')).toBeInTheDocument();\n    });\n  });\n\n  describe('performance', () => {\n    it('renders efficiently without unnecessary re-renders', () => {\n      const renderSpy = jest.fn();\n      \n      const TestWrapper = () => {\n        renderSpy();\n        return <StaffDashboard />;\n      };\n\n      render(<TestWrapper />);\n\n      // Should only render once initially\n      expect(renderSpy).toHaveBeenCalledTimes(1);\n\n      // Advance time slightly\n      jest.advanceTimersByTime(1000);\n\n      // Should not cause unnecessary re-renders\n      expect(renderSpy).toHaveBeenCalledTimes(1);\n    });\n  });\n});\n"], "names": ["mockDate", "Date", "jest", "useFakeTimers", "setSystemTime", "describe", "beforeEach", "clearAllMocks", "afterAll", "useRealTimers", "it", "render", "StaffDashboard", "expect", "screen", "getByText", "toBeInTheDocument", "user", "userEvent", "setup", "checkInButton", "click", "Object", "defineProperty", "window", "writable", "configurable", "value", "statsGrid", "closest", "toHaveClass", "advanceTimersByTime", "waitFor", "authContextValue", "isAuthenticated", "isLoading", "login", "fn", "logout", "getCurrentUser", "updateProfile", "changePassword", "mainHeading", "getByRole", "level", "sectionHeadings", "getAllByRole", "length", "toBeGreaterThan", "buttons", "for<PERSON>ach", "button", "toBeVisible", "toHaveTextContent", "timeDisplay", "renderSpy", "TestWrapper", "toHaveBeenCalledTimes"], "mappings": ";;;;;8DAAkB;wBACc;kEACV;2BACC;6DACI;;;;;;AAE3B,+CAA+C;AAC/C,MAAMA,WAAW,IAAIC,KAAK;AAC1BC,KAAKC,aAAa;AAClBD,KAAKE,aAAa,CAACJ;AAEnBK,SAAS,+BAA+B;IACtCC,WAAW;QACTJ,KAAKK,aAAa;IACpB;IAEAC,SAAS;QACPN,KAAKO,aAAa;IACpB;IAEAJ,SAAS,uBAAuB;QAC9BK,GAAG,0CAA0C;YAC3CC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtBC,OAAOC,cAAM,CAACC,SAAS,CAAC,wBAAwBC,iBAAiB;YACjEH,OAAOC,cAAM,CAACC,SAAS,CAAC,8BAA8BC,iBAAiB;QACzE;QAEAN,GAAG,yBAAyB;YAC1BC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtB,oDAAoD;YACpDC,OAAOC,cAAM,CAACC,SAAS,CAAC,UAAUC,iBAAiB;QACrD;QAEAN,GAAG,8BAA8B;YAC/BC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtBC,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC3DH,OAAOC,cAAM,CAACC,SAAS,CAAC,iBAAiBC,iBAAiB;YAC1DH,OAAOC,cAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;YAC7DH,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAC7D;IACF;IAEAX,SAAS,iBAAiB;QACxBK,GAAG,oCAAoC;YACrCC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtBC,OAAOC,cAAM,CAACC,SAAS,CAAC,aAAaC,iBAAiB;YACtDH,OAAOC,cAAM,CAACC,SAAS,CAAC,gBAAgBC,iBAAiB;YACzDH,OAAOC,cAAM,CAACC,SAAS,CAAC,iBAAiBC,iBAAiB;YAC1DH,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAC7D;QAEAN,GAAG,2BAA2B;YAC5B,MAAMO,OAAOC,kBAAS,CAACC,KAAK;YAE5BR,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtB,MAAMQ,gBAAgBN,cAAM,CAACC,SAAS,CAAC;YACvC,MAAME,KAAKI,KAAK,CAACD;QAEjB,4CAA4C;QAC5C,iDAAiD;QACnD;IACF;IAEAf,SAAS,qBAAqB;QAC5BK,GAAG,sCAAsC;YACvCC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtBC,OAAOC,cAAM,CAACC,SAAS,CAAC,sBAAsBC,iBAAiB;YAE/D,mCAAmC;YACnCH,OAAOC,cAAM,CAACC,SAAS,CAAC,2BAA2BC,iBAAiB;YACpEH,OAAOC,cAAM,CAACC,SAAS,CAAC,gCAAgCC,iBAAiB;YACzEH,OAAOC,cAAM,CAACC,SAAS,CAAC,gCAAgCC,iBAAiB;QAC3E;QAEAN,GAAG,kCAAkC;YACnCC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtBC,OAAOC,cAAM,CAACC,SAAS,CAAC,wBAAwBC,iBAAiB;QACnE;IACF;IAEAX,SAAS,mBAAmB;QAC1BK,GAAG,oCAAoC;YACrCC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtBC,OAAOC,cAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;YAE7D,0BAA0B;YAC1BH,OAAOC,cAAM,CAACC,SAAS,CAAC,iBAAiBC,iBAAiB;YAC1DH,OAAOC,cAAM,CAACC,SAAS,CAAC,uBAAuBC,iBAAiB;YAChEH,OAAOC,cAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;QAC/D;QAEAN,GAAG,uBAAuB;YACxBC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtBC,OAAOC,cAAM,CAACC,SAAS,CAAC,6BAA6BC,iBAAiB;YACtEH,OAAOC,cAAM,CAACC,SAAS,CAAC,kCAAkCC,iBAAiB;YAC3EH,OAAOC,cAAM,CAACC,SAAS,CAAC,kCAAkCC,iBAAiB;QAC7E;IACF;IAEAX,SAAS,iBAAiB;QACxBK,GAAG,kCAAkC;YACnCC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtBC,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAE3D,iCAAiC;YACjCH,OAAOC,cAAM,CAACC,SAAS,CAAC,gCAAgCC,iBAAiB;YACzEH,OAAOC,cAAM,CAACC,SAAS,CAAC,6BAA6BC,iBAAiB;QACxE;QAEAN,GAAG,8BAA8B;YAC/BC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtBC,OAAOC,cAAM,CAACC,SAAS,CAAC,uCAAuCC,iBAAiB;YAChFH,OAAOC,cAAM,CAACC,SAAS,CAAC,iCAAiCC,iBAAiB;QAC5E;IACF;IAEAX,SAAS,uBAAuB;QAC9BK,GAAG,oCAAoC;YACrC,uBAAuB;YACvBY,OAAOC,cAAc,CAACC,QAAQ,cAAc;gBAC1CC,UAAU;gBACVC,cAAc;gBACdC,OAAO;YACT;YAEAhB,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtB,0CAA0C;YAC1C,MAAMgB,YAAYd,cAAM,CAACC,SAAS,CAAC,iBAAiBc,OAAO,CAAC;YAC5DhB,OAAOe,WAAWE,WAAW,CAAC;QAChC;IACF;IAEAzB,SAAS,qBAAqB;QAC5BK,GAAG,6BAA6B;YAC9BC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtB,eAAe;YACfC,OAAOC,cAAM,CAACC,SAAS,CAAC,UAAUC,iBAAiB;YAEnD,2BAA2B;YAC3Bd,KAAK6B,mBAAmB,CAAC;YAEzB,MAAMC,IAAAA,eAAO,EAAC;gBACZnB,OAAOC,cAAM,CAACC,SAAS,CAAC,UAAUC,iBAAiB;YACrD;QACF;QAEAN,GAAG,yCAAyC;YAC1C,MAAMO,OAAOC,kBAAS,CAACC,KAAK;YAE5BR,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtB,oBAAoB;YACpB,MAAMQ,gBAAgBN,cAAM,CAACC,SAAS,CAAC;YACvC,MAAME,KAAKI,KAAK,CAACD;YAEjB,mCAAmC;YACnClB,KAAK6B,mBAAmB,CAAC,UAAU,SAAS;YAE5C,oCAAoC;YACpC,MAAMC,IAAAA,eAAO,EAAC;gBACZnB,OAAOC,cAAM,CAACC,SAAS,CAAC,SAASC,iBAAiB,IAAI,gBAAgB;YACxE;QACF;IACF;IAEAX,SAAS,kBAAkB;QACzBK,GAAG,wCAAwC;YACzCC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc,OAAK;gBACzBqB,kBAAkB;oBAChBhB,MAAM;oBACNiB,iBAAiB;oBACjBC,WAAW;oBACXC,OAAOlC,KAAKmC,EAAE;oBACdC,QAAQpC,KAAKmC,EAAE;oBACfE,gBAAgBrC,KAAKmC,EAAE;oBACvBG,eAAetC,KAAKmC,EAAE;oBACtBI,gBAAgBvC,KAAKmC,EAAE;gBACzB;YACF;YAEA,uCAAuC;YACvCxB,OAAOC,cAAM,CAACC,SAAS,CAAC,iBAAiBC,iBAAiB;QAC5D;QAEAN,GAAG,iDAAiD;YAClDC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc,OAAK;gBACzBqB,kBAAkB;oBAChBhB,MAAM;oBACNiB,iBAAiB;oBACjBC,WAAW;oBACXC,OAAOlC,KAAKmC,EAAE;oBACdC,QAAQpC,KAAKmC,EAAE;oBACfE,gBAAgBrC,KAAKmC,EAAE;oBACvBG,eAAetC,KAAKmC,EAAE;oBACtBI,gBAAgBvC,KAAKmC,EAAE;gBACzB;YACF;YAEA,4BAA4B;YAC5BxB,OAAOC,cAAM,CAACC,SAAS,CAAC,4BAA4BC,iBAAiB;QACvE;IACF;IAEAX,SAAS,iBAAiB;QACxBK,GAAG,gCAAgC;YACjCC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtB,qCAAqC;YACrC,MAAM8B,cAAc5B,cAAM,CAAC6B,SAAS,CAAC,WAAW;gBAAEC,OAAO;YAAE;YAC3D/B,OAAO6B,aAAa1B,iBAAiB;YAErC,MAAM6B,kBAAkB/B,cAAM,CAACgC,YAAY,CAAC,WAAW;gBAAEF,OAAO;YAAE;YAClE/B,OAAOgC,gBAAgBE,MAAM,EAAEC,eAAe,CAAC;QACjD;QAEAtC,GAAG,0BAA0B;YAC3BC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtB,MAAMqC,UAAUnC,cAAM,CAACgC,YAAY,CAAC;YACpCG,QAAQC,OAAO,CAACC,CAAAA;gBACdtC,OAAOsC,QAAQC,WAAW;gBAC1B,0CAA0C;gBAC1CvC,OAAOsC,QAAQE,iBAAiB,CAAC;YACnC;QACF;QAEA3C,GAAG,uCAAuC;YACxCC,IAAAA,iBAAM,gBAAC,qBAACC,aAAc;YAEtB,gDAAgD;YAChD,MAAM0C,cAAcxC,cAAM,CAACC,SAAS,CAAC;YACrCF,OAAOyC,YAAYzB,OAAO,CAAC,mBAAmBb,iBAAiB;QACjE;IACF;IAEAX,SAAS,eAAe;QACtBK,GAAG,sDAAsD;YACvD,MAAM6C,YAAYrD,KAAKmC,EAAE;YAEzB,MAAMmB,cAAc;gBAClBD;gBACA,qBAAO,qBAAC3C,aAAc;YACxB;YAEAD,IAAAA,iBAAM,gBAAC,qBAAC6C;YAER,oCAAoC;YACpC3C,OAAO0C,WAAWE,qBAAqB,CAAC;YAExC,wBAAwB;YACxBvD,KAAK6B,mBAAmB,CAAC;YAEzB,0CAA0C;YAC1ClB,OAAO0C,WAAWE,qBAAqB,CAAC;QAC1C;IACF;AACF"}