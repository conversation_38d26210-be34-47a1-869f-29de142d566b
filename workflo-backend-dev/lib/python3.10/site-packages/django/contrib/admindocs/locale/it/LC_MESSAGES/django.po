# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON>, 2016
# <PERSON>, 2014
# <AUTHOR> <EMAIL>, 2015-2016,2019,2021
# <PERSON> <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-01-15 16:05+0000\n"
"Last-Translator: palmux <<EMAIL>>\n"
"Language-Team: Italian (http://www.transifex.com/django/django/language/"
"it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: it\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Documentazione Amministrativa"

msgid "Home"
msgstr "Pagina iniziale"

msgid "Documentation"
msgstr "Documentazione"

msgid "Bookmarklets"
msgstr "Bookmarklet"

msgid "Documentation bookmarklets"
msgstr "Bookmarklet alla documentazione"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Per installare i bookmarklets, trascina il link sulla barra dei segnalibri o "
"clicca col tasto desto sul link ed aggiungilo ai tuoi segnalibri. Ora potrai "
"selezione un bookmarklet da ogni pagina del sito."

msgid "Documentation for this page"
msgstr "Documentazione per questa pagina"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Porta da qualsiasi pagina alla documentazione della view che genera quella "
"pagina."

msgid "Tags"
msgstr "Tag"

msgid "List of all the template tags and their functions."
msgstr "Lista di tutti i template tags e delle loro funzioni."

msgid "Filters"
msgstr "Filtri"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"I Filtri sono azioni che possono essere applicate alle variabili all'interno "
"di un Template per alterarne l'output."

msgid "Models"
msgstr "Modelli"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"I Modelli sono descrizioni di tutti gli oggetti nel sistema e dei campi ad "
"essi associati. Ogni modello ha una lista di campi accessibili come "
"variabili nel template."

msgid "Views"
msgstr "View"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Ogni pagina sul sito pubblico è generata da una View. La View definisce "
"quale Template è usato per generare la pagina e quali oggetti sono "
"disponibili in quel Template."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Strumenti per il tuo browser per accedere velocemente a funzionalità "
"dell'admin."

msgid "Please install docutils"
msgstr "Installa docutils, per favore"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Il sistema di documentazione dell'admin richiede la libreria Python <a href="
"\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Chiedi ai tuoi amministratori di installare <a href=\"%(link)s\">docutils</"
"a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Campi"

msgid "Field"
msgstr "Campo"

msgid "Type"
msgstr "Tipo"

msgid "Description"
msgstr "Descrizione"

msgid "Methods with arguments"
msgstr "Metodi con argomenti"

msgid "Method"
msgstr "Metodo"

msgid "Arguments"
msgstr "Argomenti"

msgid "Back to Model documentation"
msgstr "Torna alla documentzione del Model"

msgid "Model documentation"
msgstr "Documentazione Modello"

msgid "Model groups"
msgstr "Gruppi del Model"

msgid "Templates"
msgstr "Template"

#, python-format
msgid "Template: %(name)s"
msgstr "Template: \"%(name)s\""

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Template: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Percorso di ricerca per il template <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(non esiste)"

msgid "Back to Documentation"
msgstr "Torna alla Documentazione"

msgid "Template filters"
msgstr "Filtri Template"

msgid "Template filter documentation"
msgstr "Documentazione del filtro template"

msgid "Built-in filters"
msgstr "Filtri inclusi"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Per usare questi filtri, inserisci <code>%(code)s</code> nel tuo template "
"prima di usare il filtro."

msgid "Template tags"
msgstr "Tag Template"

msgid "Template tag documentation"
msgstr "Documentazione del Tag Template"

msgid "Built-in tags"
msgstr "Tag inclusi"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Per usare questi tag, inserisci <code>%(code)s</code> nel tuo template prima "
"di usare il tag."

#, python-format
msgid "View: %(name)s"
msgstr "View: %(name)s"

msgid "Context:"
msgstr "Contesto:"

msgid "Templates:"
msgstr "Template:"

msgid "Back to View documentation"
msgstr "Torna alla documentazione della View"

msgid "View documentation"
msgstr "Documentazione View"

msgid "Jump to namespace"
msgstr "Vai al namespace"

msgid "Empty namespace"
msgstr "Namespace vuoto"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Views per il namespace %(name)s"

msgid "Views by empty namespace"
msgstr "Views per il namespace vuoto"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Funzione di View: <code>%(full_name)s</code>. Nome: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "tag:"

msgid "filter:"
msgstr "filtro:"

msgid "view:"
msgstr "view:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "App %(app_label)r non trovata"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Modello %(model_name)r non trovato nell'app %(app_label)r"

msgid "model:"
msgstr "modello:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "l'oggetto `%(app_label)s.%(data_type)s` collegato"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "oggetti `%(app_label)s.%(object_name)s` collegati"

#, python-format
msgid "all %s"
msgstr "tutti %s"

#, python-format
msgid "number of %s"
msgstr "numero di %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s non sembra essere un oggetto urlpattern"
