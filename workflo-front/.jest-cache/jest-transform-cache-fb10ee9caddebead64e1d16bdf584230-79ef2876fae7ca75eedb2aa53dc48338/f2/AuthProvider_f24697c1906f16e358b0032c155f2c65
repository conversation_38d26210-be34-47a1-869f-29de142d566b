3baaed85f11ce0ccae446d9dfe17f532
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    AuthProvider: function() {
        return AuthProvider;
    },
    useAuth: function() {
        return useAuth;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _api = require("../lib/api");
const _authPersistence = require("../lib/authPersistence");
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const AuthContext = /*#__PURE__*/ (0, _react.createContext)(null);
const useAuth = ()=>{
    const context = (0, _react.useContext)(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
const AuthProvider = ({ children })=>{
    const [state, setState] = (0, _react.useState)({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        isInitialized: false,
        isHydrated: false
    });
    const updateState = (updates)=>{
        setState((prev)=>({
                ...prev,
                ...updates
            }));
    };
    const login = (0, _react.useCallback)(async (credentials)=>{
        try {
            updateState({
                isLoading: true,
                error: null
            });
            const tokens = await _api.authApi.login(credentials);
            _api.TokenManager.setTokens(tokens);
            const user = await _api.authApi.getCurrentUser();
            updateState({
                user,
                isAuthenticated: true,
                isLoading: false,
                error: null
            });
            _authPersistence.AuthPersistence.save(user, true);
            // Log successful login with role information
            console.log(`Login successful for user: ${user.email}, role: ${user.role}`);
        } catch (error) {
            const errorMessage = error.response?.data?.message || error.response?.data?.detail || error.message || 'Login failed. Please check your credentials.';
            updateState({
                user: null,
                isAuthenticated: false,
                isLoading: false,
                error: errorMessage
            });
            _authPersistence.AuthPersistence.clear();
            throw error;
        }
    }, []);
    const logout = (0, _react.useCallback)(()=>{
        _api.TokenManager.clearTokens();
        _authPersistence.AuthPersistence.clear();
        updateState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
            isInitialized: false
        });
        if (typeof window !== 'undefined') {
            window.location.href = '/login';
        }
    }, []);
    const getCurrentUser = (0, _react.useCallback)(async ()=>{
        try {
            // Only show loading if we don't have a user already
            if (!state.user) {
                updateState({
                    isLoading: true,
                    error: null
                });
            }
            const token = _api.TokenManager.getAccessToken();
            if (!token) {
                updateState({
                    user: null,
                    isAuthenticated: false,
                    isLoading: false,
                    error: null
                });
                _authPersistence.AuthPersistence.clear();
                return;
            }
            const user = await _api.authApi.getCurrentUser();
            updateState({
                user,
                isAuthenticated: true,
                isLoading: false,
                error: null
            });
            _authPersistence.AuthPersistence.save(user, true);
        } catch (error) {
            _api.TokenManager.clearTokens();
            _authPersistence.AuthPersistence.clear();
            updateState({
                user: null,
                isAuthenticated: false,
                isLoading: false,
                error: null
            });
        }
    }, [
        state.user
    ]);
    const clearError = (0, _react.useCallback)(()=>{
        updateState({
            error: null
        });
    }, []);
    const initialize = (0, _react.useCallback)(async ()=>{
        if (state.isInitialized) return;
        try {
            // First, try to load from persistence without showing loading
            const persisted = _authPersistence.AuthPersistence.load();
            if (persisted && persisted.isAuthenticated && persisted.user) {
                updateState({
                    user: persisted.user,
                    isAuthenticated: persisted.isAuthenticated,
                    isInitialized: true
                });
                // Validate in background without loading state
                try {
                    await getCurrentUser();
                } catch (error) {
                    // If validation fails, clear everything
                    _authPersistence.AuthPersistence.clear();
                    updateState({
                        user: null,
                        isAuthenticated: false,
                        error: null
                    });
                }
            } else {
                // No persisted data, validate with server
                await getCurrentUser();
                updateState({
                    isInitialized: true
                });
            }
        } catch (error) {
            // If validation fails, clear everything
            _authPersistence.AuthPersistence.clear();
            updateState({
                user: null,
                isAuthenticated: false,
                error: null,
                isInitialized: true
            });
        }
    }, [
        state.isInitialized,
        getCurrentUser
    ]);
    // Initialize on mount
    (0, _react.useEffect)(()=>{
        updateState({
            isHydrated: true
        });
        initialize();
    }, []);
    const contextValue = (0, _react.useMemo)(()=>({
            ...state,
            login,
            logout,
            getCurrentUser,
            clearError,
            initialize
        }), [
        state,
        login,
        logout,
        getCurrentUser,
        clearError,
        initialize
    ]);
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(AuthContext.Provider, {
        value: contextValue,
        children: children
    });
};

//# sourceMappingURL=data:application/json;base64,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