'use client';

import React, { useState, useEffect } from 'react';
import {
  Settings,
  X,
  Save,
  DollarSign,
  Clock,
  Calendar,
  AlertTriangle,
  Info,
  ToggleLeft
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { cn } from '@/lib/utils';

interface SalaryConfig {
  isDynamic: boolean;
  hourlyRate: number;
  overtimeMultiplier: number;
  workingHoursPerDay: number;
  workingDaysPerMonth: number;
  leaveDeductions: {
    unpaidLeavePerDay: number;
    lateArrivalPerHour: number;
    earlyDeparturePerHour: number;
    absenteeismPerDay: number;
  };
  bonuses: {
    perfectAttendanceBonus: number;
    overtimeBonus: number;
    performanceBonus: number;
  };
  penalties: {
    excessiveAbsenceThreshold: number; // days per month
    excessiveAbsencePenalty: number; // percentage of salary
    lateArrivalThreshold: number; // times per month
    lateArrivalPenalty: number; // fixed amount
  };
}

interface SalaryConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (config: SalaryConfig) => void;
  currentConfig?: SalaryConfig;
}

const SalaryConfigModal: React.FC<SalaryConfigModalProps> = ({
  isOpen,
  onClose,
  onSave,
  currentConfig
}) => {
  const [config, setConfig] = useState<SalaryConfig>({
    isDynamic: false,
    hourlyRate: 3250,
    overtimeMultiplier: 1.5,
    workingHoursPerDay: 8,
    workingDaysPerMonth: 22,
    leaveDeductions: {
      unpaidLeavePerDay: 13000,
      lateArrivalPerHour: 1950,
      earlyDeparturePerHour: 1950,
      absenteeismPerDay: 19500
    },
    bonuses: {
      perfectAttendanceBonus: 26000,
      overtimeBonus: 6500,
      performanceBonus: 39000
    },
    penalties: {
      excessiveAbsenceThreshold: 3,
      excessiveAbsencePenalty: 5,
      lateArrivalThreshold: 5,
      lateArrivalPenalty: 3250
    }
  });

  const [activeTab, setActiveTab] = useState<'general' | 'deductions' | 'bonuses' | 'penalties'>('general');

  useEffect(() => {
    if (currentConfig) {
      setConfig(currentConfig);
    }
  }, [currentConfig]);

  const handleSave = () => {
    onSave(config);
    onClose();
  };

  const updateConfig = (path: string, value: any) => {
    setConfig(prev => {
      const keys = path.split('.');
      const newConfig = { ...prev };
      let current: any = newConfig;

      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4 overflow-y-auto">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[95vh] my-8 flex flex-col min-h-0">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Settings className="h-6 w-6 text-orange-600 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">Salary Configuration</h3>
              <p className="text-sm text-gray-600">Configure dynamic salary calculations</p>
            </div>
          </div>
          <button onClick={onClose} className="p-2 hover:bg-gray-100 rounded-full">
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Dynamic Toggle */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <ToggleLeft className="h-5 w-5 text-orange-600 mr-3" />
              <div>
                <h4 className="text-base font-medium text-gray-900">Dynamic Salary Calculation</h4>
                <p className="text-sm text-gray-600">
                  Link salary to attendance, time tracking, and leave data
                </p>
              </div>
            </div>
            <button
              onClick={() => updateConfig('isDynamic', !config.isDynamic)}
              className={cn(
                "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                config.isDynamic ? "bg-orange-600" : "bg-gray-200"
              )}
            >
              <span
                className={cn(
                  "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                  config.isDynamic ? "translate-x-6" : "translate-x-1"
                )}
              />
            </button>
          </div>

          {config.isDynamic && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <div className="flex items-start">
                <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium mb-1">Dynamic Mode Enabled</p>
                  <p>Salaries will be automatically calculated based on:</p>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Daily attendance and working hours</li>
                    <li>Overtime hours and bonuses</li>
                    <li>Leave deductions and penalties</li>
                    <li>Performance-based adjustments</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex flex-col min-h-0">
          {config.isDynamic && (
            <>
              {/* Tabs */}
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6">
                  {[
                    { key: 'general', label: 'General Settings', icon: Settings },
                    { key: 'deductions', label: 'Deductions', icon: Calendar },
                    { key: 'bonuses', label: 'Bonuses', icon: DollarSign },
                    { key: 'penalties', label: 'Penalties', icon: AlertTriangle },
                  ].map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.key}
                        onClick={() => setActiveTab(tab.key as any)}
                        className={cn(
                          'py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2',
                          activeTab === tab.key
                            ? 'border-orange-500 text-orange-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        )}
                      >
                        <Icon className="h-4 w-4" />
                        <span>{tab.label}</span>
                      </button>
                    );
                  })}
                </nav>
              </div>

              {/* Tab Content */}
              <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(95vh - 300px)' }}>
                {activeTab === 'general' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Hourly Rate (KSH)
                        </label>
                        <Input
                          type="number"
                          value={config.hourlyRate}
                          onChange={(e) => updateConfig('hourlyRate', parseFloat(e.target.value) || 0)}
                          placeholder="3250"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Overtime Multiplier
                        </label>
                        <Input
                          type="number"
                          step="0.1"
                          value={config.overtimeMultiplier}
                          onChange={(e) => updateConfig('overtimeMultiplier', parseFloat(e.target.value) || 0)}
                          placeholder="1.5"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Working Hours Per Day
                        </label>
                        <Input
                          type="number"
                          value={config.workingHoursPerDay}
                          onChange={(e) => updateConfig('workingHoursPerDay', parseInt(e.target.value) || 0)}
                          placeholder="8"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Working Days Per Month
                        </label>
                        <Input
                          type="number"
                          value={config.workingDaysPerMonth}
                          onChange={(e) => updateConfig('workingDaysPerMonth', parseInt(e.target.value) || 0)}
                          placeholder="22"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'deductions' && (
                  <div className="space-y-6">
                    <h4 className="text-lg font-medium text-gray-900">Leave & Attendance Deductions</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Unpaid Leave Per Day (KSH)
                        </label>
                        <Input
                          type="number"
                          value={config.leaveDeductions.unpaidLeavePerDay}
                          onChange={(e) => updateConfig('leaveDeductions.unpaidLeavePerDay', parseFloat(e.target.value) || 0)}
                          placeholder="13000"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Late Arrival Per Hour (KSH)
                        </label>
                        <Input
                          type="number"
                          value={config.leaveDeductions.lateArrivalPerHour}
                          onChange={(e) => updateConfig('leaveDeductions.lateArrivalPerHour', parseFloat(e.target.value) || 0)}
                          placeholder="1950"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Early Departure Per Hour (KSH)
                        </label>
                        <Input
                          type="number"
                          value={config.leaveDeductions.earlyDeparturePerHour}
                          onChange={(e) => updateConfig('leaveDeductions.earlyDeparturePerHour', parseFloat(e.target.value) || 0)}
                          placeholder="1950"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Absenteeism Per Day (KSH)
                        </label>
                        <Input
                          type="number"
                          value={config.leaveDeductions.absenteeismPerDay}
                          onChange={(e) => updateConfig('leaveDeductions.absenteeismPerDay', parseFloat(e.target.value) || 0)}
                          placeholder="19500"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'bonuses' && (
                  <div className="space-y-6">
                    <h4 className="text-lg font-medium text-gray-900">Performance & Attendance Bonuses</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Perfect Attendance Bonus (KSH)
                        </label>
                        <Input
                          type="number"
                          value={config.bonuses.perfectAttendanceBonus}
                          onChange={(e) => updateConfig('bonuses.perfectAttendanceBonus', parseFloat(e.target.value) || 0)}
                          placeholder="26000"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Overtime Bonus (KSH)
                        </label>
                        <Input
                          type="number"
                          value={config.bonuses.overtimeBonus}
                          onChange={(e) => updateConfig('bonuses.overtimeBonus', parseFloat(e.target.value) || 0)}
                          placeholder="6500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Performance Bonus (KSH)
                        </label>
                        <Input
                          type="number"
                          value={config.bonuses.performanceBonus}
                          onChange={(e) => updateConfig('bonuses.performanceBonus', parseFloat(e.target.value) || 0)}
                          placeholder="39000"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'penalties' && (
                  <div className="space-y-6">
                    <h4 className="text-lg font-medium text-gray-900">Penalty Thresholds & Amounts</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Excessive Absence Threshold (days/month)
                        </label>
                        <Input
                          type="number"
                          value={config.penalties.excessiveAbsenceThreshold}
                          onChange={(e) => updateConfig('penalties.excessiveAbsenceThreshold', parseInt(e.target.value) || 0)}
                          placeholder="3"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Excessive Absence Penalty (% of salary)
                        </label>
                        <Input
                          type="number"
                          value={config.penalties.excessiveAbsencePenalty}
                          onChange={(e) => updateConfig('penalties.excessiveAbsencePenalty', parseFloat(e.target.value) || 0)}
                          placeholder="5"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Late Arrival Threshold (times/month)
                        </label>
                        <Input
                          type="number"
                          value={config.penalties.lateArrivalThreshold}
                          onChange={(e) => updateConfig('penalties.lateArrivalThreshold', parseInt(e.target.value) || 0)}
                          placeholder="5"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Late Arrival Penalty (KSH)
                        </label>
                        <Input
                          type="number"
                          value={config.penalties.lateArrivalPenalty}
                          onChange={(e) => updateConfig('penalties.lateArrivalPenalty', parseFloat(e.target.value) || 0)}
                          placeholder="3250"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </>
          )}

          {!config.isDynamic && (
            <div className="flex-1 overflow-y-auto">
              <div className="p-6">
                <div className="text-center py-12">
                  <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Static Salary Mode
                  </h3>
                  <p className="text-gray-600">
                    Salaries are calculated using fixed amounts without attendance or time tracking integration.
                    Enable dynamic mode above to configure automatic calculations.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <Button type="button" variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} variant="primary">
            <Save className="h-4 w-4 mr-2" />
            Save Configuration
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SalaryConfigModal;
