db7fe65cba859e966f6531cc47843eab
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    createMockApiError: function() {
        return createMockApiError;
    },
    createMockApiResponse: function() {
        return createMockApiResponse;
    },
    createMockFile: function() {
        return createMockFile;
    },
    createMockFormData: function() {
        return createMockFormData;
    },
    mockAdminUser: function() {
        return mockAdminUser;
    },
    mockAuthContextValue: function() {
        return mockAuthContextValue;
    },
    mockLoadingAuthContextValue: function() {
        return mockLoadingAuthContextValue;
    },
    mockLocalStorage: function() {
        return mockLocalStorage;
    },
    mockNotificationContextValue: function() {
        return mockNotificationContextValue;
    },
    mockSessionStorage: function() {
        return mockSessionStorage;
    },
    mockUnauthenticatedAuthContextValue: function() {
        return mockUnauthenticatedAuthContextValue;
    },
    mockUser: function() {
        return mockUser;
    },
    render: function() {
        return customRender;
    },
    waitFor: function() {
        return waitFor;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _react1 = _export_star(require("@testing-library/react"), exports);
function _export_star(from, to) {
    Object.keys(from).forEach(function(k) {
        if (k !== "default" && !Object.prototype.hasOwnProperty.call(to, k)) {
            Object.defineProperty(to, k, {
                enumerable: true,
                get: function() {
                    return from[k];
                }
            });
        }
    });
    return from;
}
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockUser = {
    id: 1,
    employee_id: 'EMP001',
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
    phone_number: '+254700123456',
    role: 'employee',
    department: 'Engineering',
    position: 'Software Engineer',
    hire_date: '2022-01-15',
    is_active: true,
    profile_picture: null,
    created_at: '2022-01-15T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z'
};
const mockAdminUser = {
    ...mockUser,
    id: 2,
    employee_id: 'EMP002',
    first_name: 'Jane',
    last_name: 'Smith',
    email: '<EMAIL>',
    role: 'hr',
    position: 'HR Manager'
};
const mockAuthContextValue = {
    user: mockUser,
    isAuthenticated: true,
    isLoading: false,
    login: jest.fn(),
    logout: jest.fn(),
    getCurrentUser: jest.fn(),
    updateProfile: jest.fn(),
    changePassword: jest.fn()
};
const mockUnauthenticatedAuthContextValue = {
    user: null,
    isAuthenticated: false,
    isLoading: false,
    login: jest.fn(),
    logout: jest.fn(),
    getCurrentUser: jest.fn(),
    updateProfile: jest.fn(),
    changePassword: jest.fn()
};
const mockLoadingAuthContextValue = {
    user: null,
    isAuthenticated: false,
    isLoading: true,
    login: jest.fn(),
    logout: jest.fn(),
    getCurrentUser: jest.fn(),
    updateProfile: jest.fn(),
    changePassword: jest.fn()
};
const mockNotificationContextValue = {
    notifications: [],
    unreadCount: 0,
    isConnected: true,
    connectionState: 'connected',
    markAsRead: jest.fn(),
    markAllAsRead: jest.fn(),
    clearNotifications: jest.fn(),
    requestPermission: jest.fn().mockResolvedValue('granted')
};
const AllTheProviders = ({ children, authContextValue = mockAuthContextValue, notificationContextValue = mockNotificationContextValue, withAuth = true, withNotifications = true })=>{
    let component = /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {
        children: children
    });
    if (withNotifications) {
        // Mock the notification provider
        const MockNotificationProvider = ({ children })=>{
            const NotificationContext = /*#__PURE__*/ _react.default.createContext(notificationContextValue);
            return /*#__PURE__*/ (0, _jsxruntime.jsx)(NotificationContext.Provider, {
                value: notificationContextValue,
                children: children
            });
        };
        component = /*#__PURE__*/ (0, _jsxruntime.jsx)(MockNotificationProvider, {
            children: component
        });
    }
    if (withAuth) {
        // Mock the auth provider
        const MockAuthProvider = ({ children })=>{
            const AuthContext = /*#__PURE__*/ _react.default.createContext(authContextValue);
            return /*#__PURE__*/ (0, _jsxruntime.jsx)(AuthContext.Provider, {
                value: authContextValue,
                children: children
            });
        };
        component = /*#__PURE__*/ (0, _jsxruntime.jsx)(MockAuthProvider, {
            children: component
        });
    }
    return component;
};
const customRender = (ui, options = {})=>{
    const { authContextValue, notificationContextValue, withAuth, withNotifications, ...renderOptions } = options;
    return (0, _react1.render)(ui, {
        wrapper: ({ children })=>/*#__PURE__*/ (0, _jsxruntime.jsx)(AllTheProviders, {
                authContextValue: authContextValue,
                notificationContextValue: notificationContextValue,
                withAuth: withAuth,
                withNotifications: withNotifications,
                children: children
            }),
        ...renderOptions
    });
};
const createMockApiResponse = (data, status = 200)=>({
        data,
        status,
        statusText: 'OK',
        headers: {},
        config: {}
    });
const createMockApiError = (message, status = 500)=>({
        response: {
            data: {
                message
            },
            status,
            statusText: 'Internal Server Error'
        },
        message
    });
const waitFor = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));
const createMockFile = (name = 'test.pdf', size = 1024, type = 'application/pdf')=>{
    const file = new File([
        'test content'
    ], name, {
        type
    });
    Object.defineProperty(file, 'size', {
        value: size
    });
    return file;
};
const createMockFormData = (data)=>{
    const formData = new FormData();
    Object.entries(data).forEach(([key, value])=>{
        formData.append(key, value);
    });
    return formData;
};
const mockLocalStorage = (data = {})=>{
    const localStorageMock = {
        getItem: jest.fn((key)=>data[key] || null),
        setItem: jest.fn((key, value)=>{
            data[key] = value;
        }),
        removeItem: jest.fn((key)=>{
            delete data[key];
        }),
        clear: jest.fn(()=>{
            Object.keys(data).forEach((key)=>delete data[key]);
        })
    };
    Object.defineProperty(window, 'localStorage', {
        value: localStorageMock,
        writable: true
    });
    return localStorageMock;
};
const mockSessionStorage = (data = {})=>{
    const sessionStorageMock = {
        getItem: jest.fn((key)=>data[key] || null),
        setItem: jest.fn((key, value)=>{
            data[key] = value;
        }),
        removeItem: jest.fn((key)=>{
            delete data[key];
        }),
        clear: jest.fn(()=>{
            Object.keys(data).forEach((key)=>delete data[key]);
        })
    };
    Object.defineProperty(window, 'sessionStorage', {
        value: sessionStorageMock,
        writable: true
    });
    return sessionStorageMock;
};

//# sourceMappingURL=data:application/json;base64,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