'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  User,
  Bell,
  Shield,
  Eye,
  EyeOff,
  Save,
  Smartphone,
  Mail,
  Lock,
  Globe,
  Moon,
  Sun,
  Monitor,
  Check,
  X
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { cn } from '@/lib/utils';

const StaffSettingsPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('account');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Settings state
  const [accountSettings, setAccountSettings] = useState({
    email: user?.email || '',
    phone: user?.phone_number || '',
    language: 'en',
    timezone: 'Africa/Nairobi',
    theme: 'system'
  });

  const [notificationSettings, setNotificationSettings] = useState({
    email_notifications: true,
    push_notifications: true,
    sms_notifications: false,
    leave_updates: true,
    payroll_updates: true,
    performance_updates: true,
    company_announcements: true,
    system_maintenance: true
  });

  const [privacySettings, setPrivacySettings] = useState({
    profile_visibility: 'company',
    contact_visibility: 'team',
    activity_tracking: true,
    data_analytics: true,
    marketing_emails: false
  });

  const [passwordForm, setPasswordForm] = useState({
    current_password: '',
    new_password: '',
    confirm_password: ''
  });

  const tabs = [
    { id: 'account', label: 'Account', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'privacy', label: 'Privacy & Security', icon: Shield },
    { id: 'password', label: 'Change Password', icon: Lock }
  ];

  const handleAccountSettingsChange = (field: string, value: string | boolean) => {
    setAccountSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNotificationSettingsChange = (field: string, value: boolean) => {
    setNotificationSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePrivacySettingsChange = (field: string, value: string | boolean) => {
    setPrivacySettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePasswordFormChange = (field: string, value: string) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveAccountSettings = () => {
    // TODO: Implement save account settings
    console.log('Saving account settings:', accountSettings);
  };

  const handleSaveNotificationSettings = () => {
    // TODO: Implement save notification settings
    console.log('Saving notification settings:', notificationSettings);
  };

  const handleSavePrivacySettings = () => {
    // TODO: Implement save privacy settings
    console.log('Saving privacy settings:', privacySettings);
  };

  const handleChangePassword = (e: React.FormEvent) => {
    e.preventDefault();
    if (passwordForm.new_password !== passwordForm.confirm_password) {
      alert('New passwords do not match');
      return;
    }
    // TODO: Implement password change
    console.log('Changing password');
    setPasswordForm({
      current_password: '',
      new_password: '',
      confirm_password: ''
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600 mt-1">
          Manage your account preferences and security settings
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <div className="lg:col-span-1">
          <Card>
            <div className="p-4">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={cn(
                        'flex items-center space-x-3 w-full px-3 py-2 text-left text-sm font-medium rounded-md transition-colors',
                        activeTab === tab.id
                          ? 'bg-orange-50 text-orange-700 border-r-2 border-orange-500'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      )}
                    >
                      <Icon className={cn(
                        'h-4 w-4',
                        activeTab === tab.id ? 'text-orange-500' : 'text-gray-400'
                      )} />
                      <span>{tab.label}</span>
                    </button>
                  );
                })}
              </nav>
            </div>
          </Card>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          {activeTab === 'account' && (
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Account Settings</h3>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address
                      </label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <input
                          type="email"
                          value={accountSettings.email}
                          onChange={(e) => handleAccountSettingsChange('email', e.target.value)}
                          className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Phone Number
                      </label>
                      <div className="relative">
                        <Smartphone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <input
                          type="tel"
                          value={accountSettings.phone}
                          onChange={(e) => handleAccountSettingsChange('phone', e.target.value)}
                          className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Language
                      </label>
                      <div className="relative">
                        <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <select
                          value={accountSettings.language}
                          onChange={(e) => handleAccountSettingsChange('language', e.target.value)}
                          className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                        >
                          <option value="en">English</option>
                          <option value="sw">Swahili</option>
                          <option value="fr">French</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Timezone
                      </label>
                      <select
                        value={accountSettings.timezone}
                        onChange={(e) => handleAccountSettingsChange('timezone', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                      >
                        <option value="Africa/Nairobi">Africa/Nairobi (EAT)</option>
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">America/New_York (EST)</option>
                        <option value="Europe/London">Europe/London (GMT)</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Theme Preference
                    </label>
                    <div className="flex space-x-4">
                      {[
                        { value: 'light', label: 'Light', icon: Sun },
                        { value: 'dark', label: 'Dark', icon: Moon },
                        { value: 'system', label: 'System', icon: Monitor }
                      ].map((theme) => {
                        const Icon = theme.icon;
                        return (
                          <button
                            key={theme.value}
                            onClick={() => handleAccountSettingsChange('theme', theme.value)}
                            className={cn(
                              'flex items-center space-x-2 px-4 py-2 border rounded-md transition-colors',
                              accountSettings.theme === theme.value
                                ? 'border-orange-500 bg-orange-50 text-orange-700'
                                : 'border-gray-300 hover:bg-gray-50'
                            )}
                          >
                            <Icon className="h-4 w-4" />
                            <span className="text-sm">{theme.label}</span>
                          </button>
                        );
                      })}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button variant="primary" onClick={handleSaveAccountSettings}>
                      <Save className="h-4 w-4 mr-2" />
                      Save Changes
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          )}

          {activeTab === 'notifications' && (
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Preferences</h3>
                <div className="space-y-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Delivery Methods</h4>
                    <div className="space-y-3">
                      {[
                        { key: 'email_notifications', label: 'Email Notifications', description: 'Receive notifications via email' },
                        { key: 'push_notifications', label: 'Push Notifications', description: 'Receive browser push notifications' },
                        { key: 'sms_notifications', label: 'SMS Notifications', description: 'Receive notifications via SMS' }
                      ].map((item) => (
                        <div key={item.key} className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-900">{item.label}</p>
                            <p className="text-xs text-gray-500">{item.description}</p>
                          </div>
                          <button
                            onClick={() => handleNotificationSettingsChange(item.key, !notificationSettings[item.key as keyof typeof notificationSettings])}
                            className={cn(
                              'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
                              notificationSettings[item.key as keyof typeof notificationSettings]
                                ? 'bg-orange-500'
                                : 'bg-gray-200'
                            )}
                          >
                            <span
                              className={cn(
                                'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                                notificationSettings[item.key as keyof typeof notificationSettings]
                                  ? 'translate-x-6'
                                  : 'translate-x-1'
                              )}
                            />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Notification Types</h4>
                    <div className="space-y-3">
                      {[
                        { key: 'leave_updates', label: 'Leave Updates', description: 'Leave application status changes' },
                        { key: 'payroll_updates', label: 'Payroll Updates', description: 'Salary and payslip notifications' },
                        { key: 'performance_updates', label: 'Performance Updates', description: 'Performance review notifications' },
                        { key: 'company_announcements', label: 'Company Announcements', description: 'Important company news and updates' },
                        { key: 'system_maintenance', label: 'System Maintenance', description: 'System downtime and maintenance alerts' }
                      ].map((item) => (
                        <div key={item.key} className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-900">{item.label}</p>
                            <p className="text-xs text-gray-500">{item.description}</p>
                          </div>
                          <button
                            onClick={() => handleNotificationSettingsChange(item.key, !notificationSettings[item.key as keyof typeof notificationSettings])}
                            className={cn(
                              'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
                              notificationSettings[item.key as keyof typeof notificationSettings]
                                ? 'bg-orange-500'
                                : 'bg-gray-200'
                            )}
                          >
                            <span
                              className={cn(
                                'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                                notificationSettings[item.key as keyof typeof notificationSettings]
                                  ? 'translate-x-6'
                                  : 'translate-x-1'
                              )}
                            />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button variant="primary" onClick={handleSaveNotificationSettings}>
                      <Save className="h-4 w-4 mr-2" />
                      Save Changes
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          )}

          {activeTab === 'privacy' && (
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Privacy & Security</h3>
                <div className="space-y-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Profile Visibility</h4>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Who can see your profile?
                        </label>
                        <select
                          value={privacySettings.profile_visibility}
                          onChange={(e) => handlePrivacySettingsChange('profile_visibility', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                        >
                          <option value="company">Everyone in company</option>
                          <option value="team">Team members only</option>
                          <option value="managers">Managers only</option>
                          <option value="private">Private</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Who can see your contact information?
                        </label>
                        <select
                          value={privacySettings.contact_visibility}
                          onChange={(e) => handlePrivacySettingsChange('contact_visibility', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                        >
                          <option value="company">Everyone in company</option>
                          <option value="team">Team members only</option>
                          <option value="managers">Managers only</option>
                          <option value="private">Private</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Data & Analytics</h4>
                    <div className="space-y-3">
                      {[
                        { key: 'activity_tracking', label: 'Activity Tracking', description: 'Allow tracking of your activity for analytics' },
                        { key: 'data_analytics', label: 'Data Analytics', description: 'Use your data to improve platform experience' },
                        { key: 'marketing_emails', label: 'Marketing Emails', description: 'Receive promotional emails and updates' }
                      ].map((item) => (
                        <div key={item.key} className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-900">{item.label}</p>
                            <p className="text-xs text-gray-500">{item.description}</p>
                          </div>
                          <button
                            onClick={() => handlePrivacySettingsChange(item.key, !privacySettings[item.key as keyof typeof privacySettings])}
                            className={cn(
                              'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
                              privacySettings[item.key as keyof typeof privacySettings]
                                ? 'bg-orange-500'
                                : 'bg-gray-200'
                            )}
                          >
                            <span
                              className={cn(
                                'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                                privacySettings[item.key as keyof typeof privacySettings]
                                  ? 'translate-x-6'
                                  : 'translate-x-1'
                              )}
                            />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button variant="primary" onClick={handleSavePrivacySettings}>
                      <Save className="h-4 w-4 mr-2" />
                      Save Changes
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          )}

          {activeTab === 'password' && (
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Change Password</h3>
                <form onSubmit={handleChangePassword} className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Current Password
                    </label>
                    <div className="relative">
                      <input
                        type={showCurrentPassword ? 'text' : 'password'}
                        value={passwordForm.current_password}
                        onChange={(e) => handlePasswordFormChange('current_password', e.target.value)}
                        className="w-full pr-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      New Password
                    </label>
                    <div className="relative">
                      <input
                        type={showNewPassword ? 'text' : 'password'}
                        value={passwordForm.new_password}
                        onChange={(e) => handlePasswordFormChange('new_password', e.target.value)}
                        className="w-full pr-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Confirm New Password
                    </label>
                    <div className="relative">
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        value={passwordForm.confirm_password}
                        onChange={(e) => handlePasswordFormChange('confirm_password', e.target.value)}
                        className="w-full pr-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <h4 className="text-sm font-medium text-blue-800 mb-2">Password Requirements:</h4>
                    <ul className="text-xs text-blue-700 space-y-1">
                      <li>• At least 8 characters long</li>
                      <li>• Contains at least one uppercase letter</li>
                      <li>• Contains at least one lowercase letter</li>
                      <li>• Contains at least one number</li>
                      <li>• Contains at least one special character</li>
                    </ul>
                  </div>

                  <div className="flex justify-end">
                    <Button type="submit" variant="primary">
                      <Lock className="h-4 w-4 mr-2" />
                      Change Password
                    </Button>
                  </div>
                </form>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default StaffSettingsPage;
