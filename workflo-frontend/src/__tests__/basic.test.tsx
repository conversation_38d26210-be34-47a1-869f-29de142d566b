import React from 'react';
import { render, screen } from '@testing-library/react';

// Simple test to verify Jest and React Testing Library are working
describe('Basic Test Setup', () => {
  it('should render a simple component', () => {
    const TestComponent = () => <div>Hello, World!</div>;
    
    render(<TestComponent />);
    
    expect(screen.getByText('Hello, World!')).toBeInTheDocument();
  });

  it('should handle basic assertions', () => {
    expect(1 + 1).toBe(2);
    expect('hello').toMatch(/hello/);
    expect([1, 2, 3]).toHaveLength(3);
  });

  it('should mock functions', () => {
    const mockFn = jest.fn();
    mockFn('test');
    
    expect(mockFn).toHaveBeenCalledWith('test');
    expect(mockFn).toHaveBeenCalledTimes(1);
  });
});
