'use client';

import React, { useState } from 'react';
import {
  Star,
  TrendingUp,
  Target,
  Award,
  Calendar,
  Eye,
  Download,
  Plus,
  CheckCircle,
  Clock,
  BarChart3,
  Users,
  MessageSquare,
  Edit
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { cn, formatDate } from '@/lib/utils';

const StaffPerformancePage: React.FC = () => {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState('2024');

  // Mock performance data - in real app, this would come from API
  const performanceOverview = {
    overall_rating: 4.8,
    total_reviews: 4,
    goals_completed: 12,
    goals_in_progress: 3,
    achievements: 5,
    last_review_date: '2024-10-15'
  };

  const performanceReviews = [
    {
      id: 1,
      period: 'Q4 2024',
      review_date: '2024-10-15',
      reviewer: '<PERSON>',
      overall_rating: 4.8,
      technical_skills: 4.9,
      communication: 4.7,
      teamwork: 4.8,
      leadership: 4.6,
      goal_achievement: 4.9,
      status: 'completed',
      comments: 'Excellent performance this quarter. Strong technical leadership and great collaboration with the team.'
    },
    {
      id: 2,
      period: 'Q3 2024',
      review_date: '2024-07-15',
      reviewer: 'John Smith',
      overall_rating: 4.6,
      technical_skills: 4.7,
      communication: 4.5,
      teamwork: 4.6,
      leadership: 4.4,
      goal_achievement: 4.8,
      status: 'completed',
      comments: 'Good progress on all fronts. Continue to develop leadership skills.'
    },
    {
      id: 3,
      period: 'Q2 2024',
      review_date: '2024-04-15',
      reviewer: 'John Smith',
      overall_rating: 4.4,
      technical_skills: 4.5,
      communication: 4.3,
      teamwork: 4.4,
      leadership: 4.2,
      goal_achievement: 4.6,
      status: 'completed',
      comments: 'Solid performance. Focus on improving communication and presentation skills.'
    }
  ];

  const currentGoals = [
    {
      id: 1,
      title: 'Complete React Advanced Certification',
      description: 'Obtain advanced React certification to improve frontend development skills',
      target_date: '2024-12-31',
      progress: 75,
      status: 'in_progress',
      category: 'skill_development'
    },
    {
      id: 2,
      title: 'Lead Mobile App Project',
      description: 'Successfully lead the development of the new mobile application',
      target_date: '2025-02-28',
      progress: 40,
      status: 'in_progress',
      category: 'project_leadership'
    },
    {
      id: 3,
      title: 'Mentor Junior Developers',
      description: 'Provide mentorship to 2 junior developers in the team',
      target_date: '2024-12-31',
      progress: 90,
      status: 'in_progress',
      category: 'leadership'
    },
    {
      id: 4,
      title: 'Improve Code Review Process',
      description: 'Implement and document improved code review guidelines',
      target_date: '2024-11-30',
      progress: 100,
      status: 'completed',
      category: 'process_improvement'
    }
  ];

  const achievements = [
    {
      id: 1,
      title: 'Employee of the Month',
      date: '2024-10-01',
      description: 'Outstanding performance in Q3 project delivery',
      type: 'recognition'
    },
    {
      id: 2,
      title: 'Innovation Award',
      date: '2024-08-15',
      description: 'Implemented CI/CD pipeline reducing deployment time by 50%',
      type: 'innovation'
    },
    {
      id: 3,
      title: 'Team Leadership Excellence',
      date: '2024-06-01',
      description: 'Successfully led cross-functional team of 8 members',
      type: 'leadership'
    },
    {
      id: 4,
      title: 'Technical Certification',
      date: '2024-04-20',
      description: 'Completed AWS Solutions Architect certification',
      type: 'certification'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'skill_development': return 'bg-purple-100 text-purple-800';
      case 'project_leadership': return 'bg-blue-100 text-blue-800';
      case 'leadership': return 'bg-orange-100 text-orange-800';
      case 'process_improvement': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAchievementIcon = (type: string) => {
    switch (type) {
      case 'recognition': return <Award className="h-4 w-4 text-yellow-500" />;
      case 'innovation': return <TrendingUp className="h-4 w-4 text-blue-500" />;
      case 'leadership': return <Users className="h-4 w-4 text-purple-500" />;
      case 'certification': return <Star className="h-4 w-4 text-green-500" />;
      default: return <Award className="h-4 w-4 text-gray-500" />;
    }
  };

  const renderStarRating = (rating: number) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={cn(
              'h-4 w-4',
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            )}
          />
        ))}
        <span className="ml-2 text-sm font-medium text-gray-900">{rating.toFixed(1)}</span>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Performance</h1>
          <p className="text-gray-600 mt-1">
            Track your performance reviews, goals, and achievements
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
          >
            <option value="2024">2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
          </select>
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Overall Rating</p>
                <p className="text-2xl font-bold text-gray-900">{performanceOverview.overall_rating}/5.0</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Reviews</p>
                <p className="text-2xl font-bold text-gray-900">{performanceOverview.total_reviews}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Target className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Goals Completed</p>
                <p className="text-2xl font-bold text-gray-900">{performanceOverview.goals_completed}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Award className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Achievements</p>
                <p className="text-2xl font-bold text-gray-900">{performanceOverview.achievements}</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Goals */}
        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Current Goals</h3>
              <Button variant="secondary" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Goal
              </Button>
            </div>
            <div className="space-y-4">
              {currentGoals.map((goal) => (
                <div key={goal.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-900">{goal.title}</h4>
                    <div className="flex items-center space-x-2">
                      <span className={cn(
                        'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                        getCategoryColor(goal.category)
                      )}>
                        {goal.category.replace('_', ' ')}
                      </span>
                      <span className={cn(
                        'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                        getStatusColor(goal.status)
                      )}>
                        {goal.status === 'completed' ? <CheckCircle className="h-3 w-3 mr-1" /> : <Clock className="h-3 w-3 mr-1" />}
                        {goal.status.replace('_', ' ')}
                      </span>
                    </div>
                  </div>
                  <p className="text-xs text-gray-600 mb-3">{goal.description}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex-1 mr-4">
                      <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                        <span>Progress</span>
                        <span>{goal.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={cn(
                            'h-2 rounded-full',
                            goal.progress === 100 ? 'bg-green-500' : 'bg-orange-500'
                          )}
                          style={{ width: `${goal.progress}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">
                      Due: {formatDate(goal.target_date)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>

        {/* Recent Achievements */}
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Achievements</h3>
            <div className="space-y-4">
              {achievements.map((achievement) => (
                <div key={achievement.id} className="flex items-start space-x-3">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    {getAchievementIcon(achievement.type)}
                  </div>
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900">{achievement.title}</h4>
                    <p className="text-xs text-gray-600 mt-1">{achievement.description}</p>
                    <p className="text-xs text-gray-400 mt-1">{formatDate(achievement.date)}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>

      {/* Performance Reviews */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Reviews</h3>
          <div className="space-y-6">
            {performanceReviews.map((review) => (
              <div key={review.id} className="border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="text-lg font-medium text-gray-900">{review.period}</h4>
                    <p className="text-sm text-gray-600">Reviewed by {review.reviewer} on {formatDate(review.review_date)}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={cn(
                      'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
                      getStatusColor(review.status)
                    )}>
                      {review.status}
                    </span>
                    <Button variant="secondary" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-4">
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Overall Rating</p>
                    {renderStarRating(review.overall_rating)}
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Technical Skills</p>
                    {renderStarRating(review.technical_skills)}
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Communication</p>
                    {renderStarRating(review.communication)}
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Teamwork</p>
                    {renderStarRating(review.teamwork)}
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Leadership</p>
                    {renderStarRating(review.leadership)}
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Goal Achievement</p>
                    {renderStarRating(review.goal_achievement)}
                  </div>
                </div>

                {review.comments && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-start space-x-2">
                      <MessageSquare className="h-4 w-4 text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-700">Manager Comments</p>
                        <p className="text-sm text-gray-600 mt-1">{review.comments}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default StaffPerformancePage;
