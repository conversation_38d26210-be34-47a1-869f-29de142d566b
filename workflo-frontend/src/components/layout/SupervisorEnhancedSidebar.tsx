'use client';

import React, { useState, useMemo, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  X,
  Home,
  User,
  Briefcase,
  DollarSign,
  Calendar,
  FileText,
  BarChart3,
  Clock,
  Users,
  Bell,
  Plus,
  CalendarCheck,
  Building,
  Info,
  Settings,
  Shield,
  Eye,
  Edit,
  Upload,
  Target,
  Activity,
  BellRing,
  AlertTriangle,
  Megaphone,
  AlertCircle,
  TrendingUp,
  UserCheck,
  CheckCircle,
  XCircle,
  UserPlus,
  ClipboardList,
  ChevronDown,
  ChevronRight,
  Search,
  Filter,
  Star,
  Bookmark,
  Archive,
  Download,
  Share,
  MoreHorizontal,
  Zap,
  Award,
  Layers,
  Grid,
  List,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials, formatDate } from '@/lib/utils';

interface SupervisorEnhancedSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  description?: string;
  badge?: string | number;
  children?: {
    name: string;
    href: string;
    icon: React.ComponentType<any>;
    badge?: string | number;
  }[];
  isExpanded?: boolean;
}

interface QuickAction {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  description: string;
  badge?: string | number;
  priority: 'high' | 'medium' | 'low';
  color: string;
}

const SupervisorEnhancedSidebar: React.FC<SupervisorEnhancedSidebarProps> = ({ isOpen, onClose }) => {
  const { user } = useAuth();
  const pathname = usePathname();
  const [expandedSections, setExpandedSections] = useState<string[]>(['main']);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');

  // Toggle section expansion
  const toggleSection = useCallback((sectionName: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionName)
        ? prev.filter(s => s !== sectionName)
        : [...prev, sectionName]
    );
  }, []);

  // Main navigation structure
  const navigationSections = useMemo(() => [
    {
      name: 'main',
      title: 'Main Navigation',
      items: [
        {
          name: 'Dashboard',
          href: '/supervisor',
          icon: Home,
          description: 'Overview and team statistics'
        },
        {
          name: 'Personal Info',
          href: '/supervisor/info',
          icon: User,
          description: 'Personal information and settings',
          children: [
            { name: 'Profile', href: '/supervisor/info/profile', icon: User },
            { name: 'Employment', href: '/supervisor/info/employment', icon: Briefcase },
            { name: 'Salary', href: '/supervisor/info/salary', icon: DollarSign },
            { name: 'Documents', href: '/supervisor/info/documents', icon: FileText },
            { name: 'Leave Management', href: '/supervisor/info/leave-management', icon: Calendar },
            { name: 'Performance', href: '/supervisor/info/performance', icon: BarChart3 },
            { name: 'Time Off', href: '/supervisor/info/timeoff', icon: Clock }
          ]
        },
        {
          name: 'Company',
          href: '/supervisor/company',
          icon: Building,
          description: 'Company information and structure'
        },
        {
          name: 'Team Management',
          href: '/supervisor/manage',
          icon: Users,
          description: 'Manage team members and approvals',
          badge: 5,
          children: [
            { name: 'Leave Management', href: '/supervisor/manage/leave-management', icon: CalendarCheck, badge: 3 },
            { name: 'Overtime Management', href: '/supervisor/manage/overtime', icon: Clock, badge: 2 }
          ]
        },
        {
          name: 'Notifications',
          href: '/supervisor/notifications',
          icon: BellRing,
          description: 'Alerts and notifications',
          badge: 12
        }
      ]
    }
  ], []);

  // Quick actions based on current context
  const quickActions = useMemo((): QuickAction[] => [
    {
      name: 'Approve Leave',
      href: '/supervisor/manage/leave-management?action=approve',
      icon: CalendarCheck,
      description: 'Review pending leave requests',
      badge: 3,
      priority: 'high',
      color: 'bg-red-50 text-red-700 border-red-200'
    },
    {
      name: 'Manage Overtime',
      href: '/supervisor/manage/overtime?action=approve',
      icon: Clock,
      description: 'Handle overtime applications',
      badge: 2,
      priority: 'medium',
      color: 'bg-orange-50 text-orange-700 border-orange-200'
    },
    {
      name: 'Team Performance',
      href: '/supervisor/manage?view=performance',
      icon: BarChart3,
      description: 'View team metrics',
      priority: 'low',
      color: 'bg-blue-50 text-blue-700 border-blue-200'
    },
    {
      name: 'Check Notifications',
      href: '/supervisor/notifications',
      icon: Bell,
      description: 'View all notifications',
      badge: 12,
      priority: 'medium',
      color: 'bg-purple-50 text-purple-700 border-purple-200'
    }
  ], []);

  // Filter navigation items based on search
  const filteredNavigation = useMemo(() => {
    if (!searchQuery) return navigationSections;

    return navigationSections.map(section => ({
      ...section,
      items: section.items.filter(item =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.children?.some(child =>
          child.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
      )
    })).filter(section => section.items.length > 0);
  }, [navigationSections, searchQuery]);

  // Check if current path matches navigation item
  const isActiveItem = useCallback((href: string) => {
    if (href === '/supervisor') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  }, [pathname]);

  return (
    <>
      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed left-0 top-0 h-full bg-white shadow-xl z-30 transform transition-transform duration-300 ease-in-out",
          "md:relative md:translate-x-0 md:shadow-lg md:top-0 md:h-full",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
        style={{ width: '368px' }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-blue-700">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-white font-semibold">Supervisor Panel</h2>
              <p className="text-blue-100 text-xs">Team Management</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg text-white hover:bg-white hover:bg-opacity-20 transition-colors md:hidden"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* User Info */}
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white font-medium">
                {getInitials(user?.first_name || '', user?.last_name || '')}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="font-medium text-gray-900 truncate">
                {user?.first_name} {user?.last_name}
              </p>
              <p className="text-sm text-gray-500">Team Supervisor</p>
              <p className="text-xs text-gray-400">Managing 12 team members</p>
            </div>
          </div>
        </div>

        {/* Search and View Controls */}
        <div className="p-4 border-b border-gray-200 space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search navigation..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('list')}
                className={cn(
                  "p-1.5 rounded",
                  viewMode === 'list' ? "bg-blue-100 text-blue-600" : "text-gray-400 hover:text-gray-600"
                )}
              >
                <List className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={cn(
                  "p-1.5 rounded",
                  viewMode === 'grid' ? "bg-blue-100 text-blue-600" : "text-gray-400 hover:text-gray-600"
                )}
              >
                <Grid className="h-4 w-4" />
              </button>
            </div>
            <button className="p-1.5 text-gray-400 hover:text-gray-600 rounded">
              <Filter className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Quick Actions */}
          <div className="p-4 border-b border-gray-200">
            <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
              <Zap className="h-4 w-4 mr-2 text-yellow-500" />
              Quick Actions
            </h3>
            <div className={cn(
              "space-y-2",
              viewMode === 'grid' && "grid grid-cols-2 gap-2 space-y-0"
            )}>
              {quickActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <Link
                    key={index}
                    href={action.href}
                    onClick={onClose}
                    className={cn(
                      "flex items-center p-3 rounded-lg border transition-colors hover:shadow-sm",
                      action.color,
                      viewMode === 'grid' && "flex-col text-center space-y-1"
                    )}
                  >
                    <div className={cn(
                      "flex items-center",
                      viewMode === 'grid' ? "flex-col" : "flex-row space-x-3"
                    )}>
                      <Icon className={cn(
                        "h-5 w-5",
                        viewMode === 'grid' && "mb-1"
                      )} />
                      <div className={cn(
                        "flex-1 min-w-0",
                        viewMode === 'grid' && "text-center"
                      )}>
                        <p className={cn(
                          "font-medium",
                          viewMode === 'grid' ? "text-xs" : "text-sm"
                        )}>
                          {action.name}
                        </p>
                        {viewMode === 'list' && (
                          <p className="text-xs opacity-75">{action.description}</p>
                        )}
                      </div>
                      {action.badge && (
                        <span className={cn(
                          "px-2 py-1 text-xs font-medium rounded-full",
                          action.priority === 'high' ? "bg-red-100 text-red-800" :
                          action.priority === 'medium' ? "bg-orange-100 text-orange-800" :
                          "bg-blue-100 text-blue-800"
                        )}>
                          {action.badge}
                        </span>
                      )}
                    </div>
                  </Link>
                );
              })}
            </div>
          </div>

          {/* Navigation Sections */}
          {filteredNavigation.map((section) => (
            <div key={section.name} className="border-b border-gray-200">
              <button
                onClick={() => toggleSection(section.name)}
                className="w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
              >
                <h3 className="font-semibold text-gray-900">{section.title}</h3>
                {expandedSections.includes(section.name) ? (
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-gray-500" />
                )}
              </button>

              {expandedSections.includes(section.name) && (
                <div className="pb-4">
                  {section.items.map((item, index) => {
                    const Icon = item.icon;
                    const isActive = isActiveItem(item.href);
                    const hasChildren = item.children && item.children.length > 0;
                    const isExpanded = expandedSections.includes(`${section.name}-${item.name}`);

                    return (
                      <div key={index}>
                        {/* Main Item */}
                        <div className="px-4">
                          {hasChildren ? (
                            <button
                              onClick={() => toggleSection(`${section.name}-${item.name}`)}
                              className={cn(
                                "w-full flex items-center justify-between p-3 rounded-lg transition-colors",
                                isActive
                                  ? "bg-blue-50 text-blue-700 border-l-4 border-blue-500"
                                  : "text-gray-700 hover:bg-gray-50"
                              )}
                            >
                              <div className="flex items-center space-x-3">
                                <Icon className="h-5 w-5" />
                                <div className="flex-1 text-left">
                                  <p className="font-medium">{item.name}</p>
                                  {item.description && (
                                    <p className="text-xs opacity-75">{item.description}</p>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                {item.badge && (
                                  <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                                    {item.badge}
                                  </span>
                                )}
                                {isExpanded ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <ChevronRight className="h-4 w-4" />
                                )}
                              </div>
                            </button>
                          ) : (
                            <Link
                              href={item.href}
                              onClick={onClose}
                              className={cn(
                                "flex items-center justify-between p-3 rounded-lg transition-colors",
                                isActive
                                  ? "bg-blue-50 text-blue-700 border-l-4 border-blue-500"
                                  : "text-gray-700 hover:bg-gray-50"
                              )}
                            >
                              <div className="flex items-center space-x-3">
                                <Icon className="h-5 w-5" />
                                <div className="flex-1">
                                  <p className="font-medium">{item.name}</p>
                                  {item.description && (
                                    <p className="text-xs opacity-75">{item.description}</p>
                                  )}
                                </div>
                              </div>
                              {item.badge && (
                                <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                                  {item.badge}
                                </span>
                              )}
                            </Link>
                          )}
                        </div>

                        {/* Children Items */}
                        {hasChildren && isExpanded && (
                          <div className="ml-4 mt-2 space-y-1">
                            {item.children!.map((child, childIndex) => {
                              const ChildIcon = child.icon;
                              const isChildActive = isActiveItem(child.href);

                              return (
                                <Link
                                  key={childIndex}
                                  href={child.href}
                                  onClick={onClose}
                                  className={cn(
                                    "flex items-center justify-between p-2 mx-4 rounded-lg transition-colors",
                                    isChildActive
                                      ? "bg-blue-50 text-blue-700 border-l-2 border-blue-500"
                                      : "text-gray-600 hover:bg-gray-50"
                                  )}
                                >
                                  <div className="flex items-center space-x-3">
                                    <ChildIcon className="h-4 w-4" />
                                    <span className="text-sm font-medium">{child.name}</span>
                                  </div>
                                  {child.badge && (
                                    <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                                      {child.badge}
                                    </span>
                                  )}
                                </Link>
                              );
                            })}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          ))}

          {/* Team Statistics */}
          <div className="p-4 border-b border-gray-200">
            <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
              <BarChart3 className="h-4 w-4 mr-2 text-green-500" />
              Team Overview
            </h3>
            <div className="space-y-3">
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Team Status</span>
                  <TrendingUp className="h-4 w-4 text-green-500" />
                </div>
                <div className="grid grid-cols-3 gap-2 text-center">
                  <div>
                    <div className="text-lg font-semibold text-green-600">10</div>
                    <div className="text-xs text-gray-500">Present</div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-orange-600">2</div>
                    <div className="text-xs text-gray-500">On Leave</div>
                  </div>
                  <div>
                    <div className="text-lg font-semibold text-blue-600">12</div>
                    <div className="text-xs text-gray-500">Total</div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Pending Actions</span>
                  <AlertCircle className="h-4 w-4 text-orange-500" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-600">Leave Requests</span>
                    <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">3</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-600">Overtime Apps</span>
                    <span className="px-2 py-1 text-xs font-medium bg-orange-100 text-orange-800 rounded-full">2</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="p-4">
            <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
              <Activity className="h-4 w-4 mr-2 text-purple-500" />
              Recent Activity
            </h3>
            <div className="space-y-3">
              {[
                { action: 'Approved leave request', user: 'John Doe', time: '2 hours ago', type: 'approval' },
                { action: 'New overtime application', user: 'Jane Smith', time: '4 hours ago', type: 'pending' },
                { action: 'Team meeting scheduled', user: 'You', time: '1 day ago', type: 'info' }
              ].map((activity, index) => (
                <div key={index} className="flex items-start space-x-3 p-2 rounded-lg hover:bg-gray-50">
                  <div className={cn(
                    "w-2 h-2 rounded-full mt-2",
                    activity.type === 'approval' ? "bg-green-500" :
                    activity.type === 'pending' ? "bg-orange-500" : "bg-blue-500"
                  )} />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                    <p className="text-xs text-gray-500">{activity.user} • {activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-gray-600">System Online</span>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-1.5 text-gray-400 hover:text-gray-600 rounded">
                <Settings className="h-4 w-4" />
              </button>
              <button className="p-1.5 text-gray-400 hover:text-gray-600 rounded">
                <MoreHorizontal className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SupervisorEnhancedSidebar;