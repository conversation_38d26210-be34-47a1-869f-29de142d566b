{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/__tests__/components/auth/RoleGuard.test.tsx"], "sourcesContent": ["import React from 'react';\nimport { screen, waitFor } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nimport { render, mockUser, mockAdminUser, mockUnauthenticatedAuthContextValue, mockLoadingAuthContextValue } from '@/__tests__/utils/test-utils';\nimport RoleGuard, { AdminGuard, StaffGuard, useRoleCheck } from '@/components/auth/RoleGuard';\n\n// Mock useRouter\nconst mockPush = jest.fn();\nconst mockReplace = jest.fn();\nconst mockBack = jest.fn();\n\njest.mock('next/navigation', () => ({\n  useRouter: () => ({\n    push: mockPush,\n    replace: mockReplace,\n    back: mockBack,\n  }),\n}));\n\ndescribe('RoleGuard', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('when user is authenticated and has required role', () => {\n    it('renders children for staff role', () => {\n      render(\n        <RoleGuard allowedRoles={['staff']}>\n          <div>Staff Content</div>\n        </RoleGuard>\n      );\n\n      expect(screen.getByText('Staff Content')).toBeInTheDocument();\n    });\n\n    it('renders children for admin role when user is HR', () => {\n      render(\n        <RoleGuard allowedRoles={['admin']}>\n          <div>Admin Content</div>\n        </RoleGuard>,\n        {\n          authContextValue: {\n            ...mockUnauthenticatedAuthContextValue,\n            user: mockAdminUser,\n            isAuthenticated: true,\n            isLoading: false,\n          }\n        }\n      );\n\n      expect(screen.getByText('Admin Content')).toBeInTheDocument();\n    });\n  });\n\n  describe('when user is not authenticated', () => {\n    it('shows authentication required message', () => {\n      render(\n        <RoleGuard allowedRoles={['staff']}>\n          <div>Protected Content</div>\n        </RoleGuard>,\n        {\n          authContextValue: mockUnauthenticatedAuthContextValue\n        }\n      );\n\n      expect(screen.getByText('Authentication Required')).toBeInTheDocument();\n      expect(screen.getByText('Please log in to access this page.')).toBeInTheDocument();\n    });\n\n    it('redirects to login when redirectTo is provided', () => {\n      render(\n        <RoleGuard allowedRoles={['staff']} redirectTo=\"/login\">\n          <div>Protected Content</div>\n        </RoleGuard>,\n        {\n          authContextValue: mockUnauthenticatedAuthContextValue\n        }\n      );\n\n      expect(mockReplace).toHaveBeenCalledWith('/login');\n    });\n\n    it('navigates to login when login button is clicked', async () => {\n      const user = userEvent.setup();\n      \n      render(\n        <RoleGuard allowedRoles={['staff']}>\n          <div>Protected Content</div>\n        </RoleGuard>,\n        {\n          authContextValue: mockUnauthenticatedAuthContextValue\n        }\n      );\n\n      const loginButton = screen.getByText('Go to Login');\n      await user.click(loginButton);\n\n      expect(mockPush).toHaveBeenCalledWith('/login');\n    });\n  });\n\n  describe('when user lacks required role', () => {\n    it('shows access denied message', () => {\n      render(\n        <RoleGuard allowedRoles={['admin']}>\n          <div>Admin Content</div>\n        </RoleGuard>\n      );\n\n      expect(screen.getByText('Access Denied')).toBeInTheDocument();\n      expect(screen.getByText(/You don't have permission to access this page/)).toBeInTheDocument();\n      expect(screen.getByText(/Your current role \\(employee\\) is not authorized/)).toBeInTheDocument();\n    });\n\n    it('shows custom fallback when provided', () => {\n      const customFallback = <div>Custom Access Denied</div>;\n      \n      render(\n        <RoleGuard allowedRoles={['admin']} fallback={customFallback}>\n          <div>Admin Content</div>\n        </RoleGuard>\n      );\n\n      expect(screen.getByText('Custom Access Denied')).toBeInTheDocument();\n      expect(screen.queryByText('Access Denied')).not.toBeInTheDocument();\n    });\n\n    it('navigates back when go back button is clicked', async () => {\n      const user = userEvent.setup();\n      \n      render(\n        <RoleGuard allowedRoles={['admin']}>\n          <div>Admin Content</div>\n        </RoleGuard>\n      );\n\n      const backButton = screen.getByText('Go Back');\n      await user.click(backButton);\n\n      expect(mockBack).toHaveBeenCalled();\n    });\n\n    it('navigates to staff dashboard for employee role', async () => {\n      const user = userEvent.setup();\n      \n      render(\n        <RoleGuard allowedRoles={['admin']}>\n          <div>Admin Content</div>\n        </RoleGuard>\n      );\n\n      const dashboardButton = screen.getByText('Go to Dashboard');\n      await user.click(dashboardButton);\n\n      // Should redirect to staff dashboard for employee role\n      expect(mockPush).toHaveBeenCalledWith('/staff');\n    });\n  });\n\n  describe('when loading', () => {\n    it('shows loading state', () => {\n      render(\n        <RoleGuard allowedRoles={['staff']}>\n          <div>Protected Content</div>\n        </RoleGuard>,\n        {\n          authContextValue: mockLoadingAuthContextValue\n        }\n      );\n\n      expect(screen.getByText('Checking permissions...')).toBeInTheDocument();\n    });\n  });\n});\n\ndescribe('AdminGuard', () => {\n  it('allows access for HR user', () => {\n    render(\n      <AdminGuard>\n        <div>Admin Content</div>\n      </AdminGuard>,\n      {\n        authContextValue: {\n          ...mockUnauthenticatedAuthContextValue,\n          user: mockAdminUser,\n          isAuthenticated: true,\n          isLoading: false,\n        }\n      }\n    );\n\n    expect(screen.getByText('Admin Content')).toBeInTheDocument();\n  });\n\n  it('denies access for employee user', () => {\n    render(\n      <AdminGuard>\n        <div>Admin Content</div>\n      </AdminGuard>\n    );\n\n    expect(screen.getByText('Access Denied')).toBeInTheDocument();\n  });\n});\n\ndescribe('StaffGuard', () => {\n  it('allows access for employee user', () => {\n    render(\n      <StaffGuard>\n        <div>Staff Content</div>\n      </StaffGuard>\n    );\n\n    expect(screen.getByText('Staff Content')).toBeInTheDocument();\n  });\n\n  it('denies access for HR user', () => {\n    render(\n      <StaffGuard>\n        <div>Staff Content</div>\n      </StaffGuard>,\n      {\n        authContextValue: {\n          ...mockUnauthenticatedAuthContextValue,\n          user: mockAdminUser,\n          isAuthenticated: true,\n          isLoading: false,\n        }\n      }\n    );\n\n    expect(screen.getByText('Access Denied')).toBeInTheDocument();\n  });\n});\n\ndescribe('useRoleCheck', () => {\n  const TestComponent: React.FC = () => {\n    const { hasRole, isAdmin, isStaff, isHR, userRole } = useRoleCheck();\n\n    return (\n      <div>\n        <div data-testid=\"has-admin-role\">{hasRole(['admin']).toString()}</div>\n        <div data-testid=\"has-staff-role\">{hasRole(['staff']).toString()}</div>\n        <div data-testid=\"is-admin\">{isAdmin().toString()}</div>\n        <div data-testid=\"is-staff\">{isStaff().toString()}</div>\n        <div data-testid=\"is-hr\">{isHR().toString()}</div>\n        <div data-testid=\"user-role\">{userRole}</div>\n      </div>\n    );\n  };\n\n  it('returns correct role checks for employee user', () => {\n    render(<TestComponent />);\n\n    expect(screen.getByTestId('has-admin-role')).toHaveTextContent('false');\n    expect(screen.getByTestId('has-staff-role')).toHaveTextContent('true');\n    expect(screen.getByTestId('is-admin')).toHaveTextContent('false');\n    expect(screen.getByTestId('is-staff')).toHaveTextContent('true');\n    expect(screen.getByTestId('is-hr')).toHaveTextContent('false');\n    expect(screen.getByTestId('user-role')).toHaveTextContent('employee');\n  });\n\n  it('returns correct role checks for HR user', () => {\n    render(<TestComponent />, {\n      authContextValue: {\n        ...mockUnauthenticatedAuthContextValue,\n        user: mockAdminUser,\n        isAuthenticated: true,\n        isLoading: false,\n      }\n    });\n\n    expect(screen.getByTestId('has-admin-role')).toHaveTextContent('true');\n    expect(screen.getByTestId('has-staff-role')).toHaveTextContent('false');\n    expect(screen.getByTestId('is-admin')).toHaveTextContent('true');\n    expect(screen.getByTestId('is-staff')).toHaveTextContent('false');\n    expect(screen.getByTestId('is-hr')).toHaveTextContent('true');\n    expect(screen.getByTestId('user-role')).toHaveTextContent('hr');\n  });\n\n  it('returns null values when user is not authenticated', () => {\n    render(<TestComponent />, {\n      authContextValue: mockUnauthenticatedAuthContextValue\n    });\n\n    expect(screen.getByTestId('has-admin-role')).toHaveTextContent('false');\n    expect(screen.getByTestId('has-staff-role')).toHaveTextContent('false');\n    expect(screen.getByTestId('is-admin')).toHaveTextContent('false');\n    expect(screen.getByTestId('is-staff')).toHaveTextContent('false');\n    expect(screen.getByTestId('is-hr')).toHaveTextContent('false');\n    expect(screen.getByTestId('user-role')).toHaveTextContent('');\n  });\n});\n"], "names": ["jest", "mock", "useRouter", "push", "mockPush", "replace", "mockReplace", "back", "mockBack", "fn", "describe", "beforeEach", "clearAllMocks", "it", "render", "<PERSON><PERSON><PERSON>", "allowedRoles", "div", "expect", "screen", "getByText", "toBeInTheDocument", "authContextValue", "mockUnauthenticatedAuthContextValue", "user", "mockAdminUser", "isAuthenticated", "isLoading", "redirectTo", "toHaveBeenCalledWith", "userEvent", "setup", "loginButton", "click", "customFallback", "fallback", "queryByText", "not", "backButton", "toHaveBeenCalled", "dashboardButton", "mockLoadingAuthContextValue", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "TestComponent", "hasRole", "isAdmin", "isStaff", "isHR", "userRole", "useRoleCheck", "data-testid", "toString", "getByTestId", "toHaveTextContent"], "mappings": ";AAWAA,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCC,WAAW,IAAO,CAAA;gBAChBC,MAAMC;gBACNC,SAASC;gBACTC,MAAMC;YACR,CAAA;IACF,CAAA;;;;;8DAjBkB;wBACc;kEACV;2BAC4F;mEAClD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhE,iBAAiB;AACjB,MAAMJ,WAAWJ,KAAKS,EAAE;AACxB,MAAMH,cAAcN,KAAKS,EAAE;AAC3B,MAAMD,WAAWR,KAAKS,EAAE;AAUxBC,SAAS,aAAa;IACpBC,WAAW;QACTX,KAAKY,aAAa;IACpB;IAEAF,SAAS,oDAAoD;QAC3DG,GAAG,mCAAmC;YACpCC,IAAAA,iBAAM,gBACJ,qBAACC,kBAAS;gBAACC,cAAc;oBAAC;iBAAQ;0BAChC,cAAA,qBAACC;8BAAI;;;YAITC,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAC7D;QAEAR,GAAG,mDAAmD;YACpDC,IAAAA,iBAAM,gBACJ,qBAACC,kBAAS;gBAACC,cAAc;oBAAC;iBAAQ;0BAChC,cAAA,qBAACC;8BAAI;;gBAEP;gBACEK,kBAAkB;oBAChB,GAAGC,8CAAmC;oBACtCC,MAAMC,wBAAa;oBACnBC,iBAAiB;oBACjBC,WAAW;gBACb;YACF;YAGFT,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAC7D;IACF;IAEAX,SAAS,kCAAkC;QACzCG,GAAG,yCAAyC;YAC1CC,IAAAA,iBAAM,gBACJ,qBAACC,kBAAS;gBAACC,cAAc;oBAAC;iBAAQ;0BAChC,cAAA,qBAACC;8BAAI;;gBAEP;gBACEK,kBAAkBC,8CAAmC;YACvD;YAGFL,OAAOC,cAAM,CAACC,SAAS,CAAC,4BAA4BC,iBAAiB;YACrEH,OAAOC,cAAM,CAACC,SAAS,CAAC,uCAAuCC,iBAAiB;QAClF;QAEAR,GAAG,kDAAkD;YACnDC,IAAAA,iBAAM,gBACJ,qBAACC,kBAAS;gBAACC,cAAc;oBAAC;iBAAQ;gBAAEY,YAAW;0BAC7C,cAAA,qBAACX;8BAAI;;gBAEP;gBACEK,kBAAkBC,8CAAmC;YACvD;YAGFL,OAAOZ,aAAauB,oBAAoB,CAAC;QAC3C;QAEAhB,GAAG,mDAAmD;YACpD,MAAMW,OAAOM,kBAAS,CAACC,KAAK;YAE5BjB,IAAAA,iBAAM,gBACJ,qBAACC,kBAAS;gBAACC,cAAc;oBAAC;iBAAQ;0BAChC,cAAA,qBAACC;8BAAI;;gBAEP;gBACEK,kBAAkBC,8CAAmC;YACvD;YAGF,MAAMS,cAAcb,cAAM,CAACC,SAAS,CAAC;YACrC,MAAMI,KAAKS,KAAK,CAACD;YAEjBd,OAAOd,UAAUyB,oBAAoB,CAAC;QACxC;IACF;IAEAnB,SAAS,iCAAiC;QACxCG,GAAG,+BAA+B;YAChCC,IAAAA,iBAAM,gBACJ,qBAACC,kBAAS;gBAACC,cAAc;oBAAC;iBAAQ;0BAChC,cAAA,qBAACC;8BAAI;;;YAITC,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC3DH,OAAOC,cAAM,CAACC,SAAS,CAAC,kDAAkDC,iBAAiB;YAC3FH,OAAOC,cAAM,CAACC,SAAS,CAAC,qDAAqDC,iBAAiB;QAChG;QAEAR,GAAG,uCAAuC;YACxC,MAAMqB,+BAAiB,qBAACjB;0BAAI;;YAE5BH,IAAAA,iBAAM,gBACJ,qBAACC,kBAAS;gBAACC,cAAc;oBAAC;iBAAQ;gBAAEmB,UAAUD;0BAC5C,cAAA,qBAACjB;8BAAI;;;YAITC,OAAOC,cAAM,CAACC,SAAS,CAAC,yBAAyBC,iBAAiB;YAClEH,OAAOC,cAAM,CAACiB,WAAW,CAAC,kBAAkBC,GAAG,CAAChB,iBAAiB;QACnE;QAEAR,GAAG,iDAAiD;YAClD,MAAMW,OAAOM,kBAAS,CAACC,KAAK;YAE5BjB,IAAAA,iBAAM,gBACJ,qBAACC,kBAAS;gBAACC,cAAc;oBAAC;iBAAQ;0BAChC,cAAA,qBAACC;8BAAI;;;YAIT,MAAMqB,aAAanB,cAAM,CAACC,SAAS,CAAC;YACpC,MAAMI,KAAKS,KAAK,CAACK;YAEjBpB,OAAOV,UAAU+B,gBAAgB;QACnC;QAEA1B,GAAG,kDAAkD;YACnD,MAAMW,OAAOM,kBAAS,CAACC,KAAK;YAE5BjB,IAAAA,iBAAM,gBACJ,qBAACC,kBAAS;gBAACC,cAAc;oBAAC;iBAAQ;0BAChC,cAAA,qBAACC;8BAAI;;;YAIT,MAAMuB,kBAAkBrB,cAAM,CAACC,SAAS,CAAC;YACzC,MAAMI,KAAKS,KAAK,CAACO;YAEjB,uDAAuD;YACvDtB,OAAOd,UAAUyB,oBAAoB,CAAC;QACxC;IACF;IAEAnB,SAAS,gBAAgB;QACvBG,GAAG,uBAAuB;YACxBC,IAAAA,iBAAM,gBACJ,qBAACC,kBAAS;gBAACC,cAAc;oBAAC;iBAAQ;0BAChC,cAAA,qBAACC;8BAAI;;gBAEP;gBACEK,kBAAkBmB,sCAA2B;YAC/C;YAGFvB,OAAOC,cAAM,CAACC,SAAS,CAAC,4BAA4BC,iBAAiB;QACvE;IACF;AACF;AAEAX,SAAS,cAAc;IACrBG,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBACJ,qBAAC4B,qBAAU;sBACT,cAAA,qBAACzB;0BAAI;;YAEP;YACEK,kBAAkB;gBAChB,GAAGC,8CAAmC;gBACtCC,MAAMC,wBAAa;gBACnBC,iBAAiB;gBACjBC,WAAW;YACb;QACF;QAGFT,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;IAC7D;IAEAR,GAAG,mCAAmC;QACpCC,IAAAA,iBAAM,gBACJ,qBAAC4B,qBAAU;sBACT,cAAA,qBAACzB;0BAAI;;;QAITC,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;IAC7D;AACF;AAEAX,SAAS,cAAc;IACrBG,GAAG,mCAAmC;QACpCC,IAAAA,iBAAM,gBACJ,qBAAC6B,qBAAU;sBACT,cAAA,qBAAC1B;0BAAI;;;QAITC,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;IAC7D;IAEAR,GAAG,6BAA6B;QAC9BC,IAAAA,iBAAM,gBACJ,qBAAC6B,qBAAU;sBACT,cAAA,qBAAC1B;0BAAI;;YAEP;YACEK,kBAAkB;gBAChB,GAAGC,8CAAmC;gBACtCC,MAAMC,wBAAa;gBACnBC,iBAAiB;gBACjBC,WAAW;YACb;QACF;QAGFT,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;IAC7D;AACF;AAEAX,SAAS,gBAAgB;IACvB,MAAMkC,gBAA0B;QAC9B,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGC,IAAAA,uBAAY;QAElE,qBACE,sBAACjC;;8BACC,qBAACA;oBAAIkC,eAAY;8BAAkBN,QAAQ;wBAAC;qBAAQ,EAAEO,QAAQ;;8BAC9D,qBAACnC;oBAAIkC,eAAY;8BAAkBN,QAAQ;wBAAC;qBAAQ,EAAEO,QAAQ;;8BAC9D,qBAACnC;oBAAIkC,eAAY;8BAAYL,UAAUM,QAAQ;;8BAC/C,qBAACnC;oBAAIkC,eAAY;8BAAYJ,UAAUK,QAAQ;;8BAC/C,qBAACnC;oBAAIkC,eAAY;8BAASH,OAAOI,QAAQ;;8BACzC,qBAACnC;oBAAIkC,eAAY;8BAAaF;;;;IAGpC;IAEApC,GAAG,iDAAiD;QAClDC,IAAAA,iBAAM,gBAAC,qBAAC8B;QAER1B,OAAOC,cAAM,CAACkC,WAAW,CAAC,mBAAmBC,iBAAiB,CAAC;QAC/DpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,mBAAmBC,iBAAiB,CAAC;QAC/DpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,aAAaC,iBAAiB,CAAC;QACzDpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,aAAaC,iBAAiB,CAAC;QACzDpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,UAAUC,iBAAiB,CAAC;QACtDpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,cAAcC,iBAAiB,CAAC;IAC5D;IAEAzC,GAAG,2CAA2C;QAC5CC,IAAAA,iBAAM,gBAAC,qBAAC8B,oBAAkB;YACxBtB,kBAAkB;gBAChB,GAAGC,8CAAmC;gBACtCC,MAAMC,wBAAa;gBACnBC,iBAAiB;gBACjBC,WAAW;YACb;QACF;QAEAT,OAAOC,cAAM,CAACkC,WAAW,CAAC,mBAAmBC,iBAAiB,CAAC;QAC/DpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,mBAAmBC,iBAAiB,CAAC;QAC/DpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,aAAaC,iBAAiB,CAAC;QACzDpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,aAAaC,iBAAiB,CAAC;QACzDpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,UAAUC,iBAAiB,CAAC;QACtDpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,cAAcC,iBAAiB,CAAC;IAC5D;IAEAzC,GAAG,sDAAsD;QACvDC,IAAAA,iBAAM,gBAAC,qBAAC8B,oBAAkB;YACxBtB,kBAAkBC,8CAAmC;QACvD;QAEAL,OAAOC,cAAM,CAACkC,WAAW,CAAC,mBAAmBC,iBAAiB,CAAC;QAC/DpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,mBAAmBC,iBAAiB,CAAC;QAC/DpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,aAAaC,iBAAiB,CAAC;QACzDpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,aAAaC,iBAAiB,CAAC;QACzDpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,UAAUC,iBAAiB,CAAC;QACtDpC,OAAOC,cAAM,CAACkC,WAAW,CAAC,cAAcC,iBAAiB,CAAC;IAC5D;AACF"}