'use client';

import React, { useEffect, useState, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/providers/AuthProvider';
import SplashScreen from '@/components/ui/SplashScreen';
import { MinimalLoadingScreen } from '@/components/ui/LoadingStates';

interface AuthGuardProps {
  children: React.ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const router = useRouter();
  const pathname = usePathname();
  const {
    isAuthenticated,
    isLoading,
    isInitialized,
    isHydrated,
    user
  } = useAuth();

  const [showSplash, setShowSplash] = useState(true);
  const hasRedirectedRef = useRef(false);
  const splashTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Define public routes that don't require authentication
  const publicRoutes = ['/login', '/', '/status', '/test'];
  const isPublicRoute = publicRoutes.includes(pathname);

  // Helper function to get role-based redirect path
  const getRoleBasedRedirectPath = (user: any) => {
    if (!user) return '/login';

    // Role-based routing
    switch (user.role) {
      case 'supervisor':
        return '/supervisor';
      case 'employee':
        return '/staff';
      case 'hr':
      case 'admin':
      case 'accountant':
        return '/dashboard';
      default:
        return '/dashboard';
    }
  };

  // Reset redirect flag when pathname changes
  useEffect(() => {
    hasRedirectedRef.current = false;
  }, [pathname]);

  // Initialize on mount and handle splash screen
  useEffect(() => {
    if (isHydrated && isInitialized) {
      // Clear any existing timeout
      if (splashTimeoutRef.current) {
        clearTimeout(splashTimeoutRef.current);
      }

      // Minimal delay for splash screen to prevent flashing
      splashTimeoutRef.current = setTimeout(() => {
        setShowSplash(false);
      }, 100); // Reduced to 100ms for faster loading
    }

    return () => {
      if (splashTimeoutRef.current) {
        clearTimeout(splashTimeoutRef.current);
      }
    };
  }, [isHydrated, isInitialized]);

  // Handle redirections
  useEffect(() => {
    if (!isInitialized || isLoading || showSplash || hasRedirectedRef.current) return;

    if (!isAuthenticated && !isPublicRoute) {
      hasRedirectedRef.current = true;
      router.replace('/login');
      return;
    }

    if (isAuthenticated && user && pathname === '/login') {
      hasRedirectedRef.current = true;
      const redirectPath = getRoleBasedRedirectPath(user);
      router.replace(redirectPath);
      return;
    }

    if (isAuthenticated && user && pathname === '/') {
      hasRedirectedRef.current = true;
      const redirectPath = getRoleBasedRedirectPath(user);
      router.replace(redirectPath);
      return;
    }
  }, [isInitialized, isLoading, isAuthenticated, isPublicRoute, pathname, router, showSplash, user, getRoleBasedRedirectPath]);

  // Show splash screen during initialization
  if (showSplash || !isHydrated || !isInitialized) {
    return (
      <SplashScreen
        isVisible={true}
        onComplete={() => {}}
        duration={300}
      />
    );
  }

  // Show loading for protected routes while checking auth
  if (isLoading) {
    return <MinimalLoadingScreen message="Authenticating..." />;
  }

  // Show login prompt for unauthenticated users on protected routes
  if (!isAuthenticated && !isPublicRoute) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Authentication Required
          </h2>
          <p className="text-gray-600 mb-4">
            Please log in to access this page.
          </p>
          <button
            onClick={() => router.push('/login')}
            className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  // Render children for authenticated users or public routes
  return <>{children}</>;
};

export default AuthGuard;
