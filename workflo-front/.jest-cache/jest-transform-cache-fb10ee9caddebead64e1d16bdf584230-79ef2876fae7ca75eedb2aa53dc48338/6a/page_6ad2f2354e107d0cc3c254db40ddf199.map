{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/app/(auth)/(staff)/staff/info/biostar/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>gerprint,\n  Eye,\n  Wifi,\n  WifiOff,\n  Clock,\n  MapPin,\n  Activity,\n  Shield,\n  RefreshCw,\n  CheckCircle,\n  AlertCircle,\n  Monitor,\n  User,\n  Database,\n  History,\n  Settings\n} from 'lucide-react';\nimport Card from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport { useAuth } from '@/providers/AuthProvider';\nimport { cn, formatDate } from '@/lib/utils';\nimport { useBiostarAttendance } from '@/hooks/useBiostarAttendance';\nimport { BiometricUser, BiometricEvent, BiometricDevice } from '@/types';\n\ninterface BiometricProfile {\n  user: BiometricUser | null;\n  enrolledFingerprints: number;\n  enrolledFaces: number;\n  lastSync: string | null;\n  accessLevel: string;\n  isActive: boolean;\n}\n\nconst StaffBiostarPage: React.FC = () => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState<'profile' | 'devices' | 'history' | 'security'>('profile');\n  const [biometricProfile, setBiometricProfile] = useState<BiometricProfile | null>(null);\n  const [recentEvents, setRecentEvents] = useState<BiometricEvent[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // Use BioStar attendance hook for device and connectivity data\n  const {\n    devices,\n    connected: biostarConnected,\n    error: connectionError,\n    refresh: refreshAttendance\n  } = useBiostarAttendance({\n    employeeId: user?.employee_id,\n    autoRefresh: true,\n    enableRealTime: false\n  });\n\n  useEffect(() => {\n    loadBiometricData();\n  }, [user?.employee_id]);\n\n  const loadBiometricData = async () => {\n    if (!user?.employee_id) {\n      setLoading(false);\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n\n      // Use mock data instead of API calls\n      const mockBiostarUser = {\n        id: 'user-001',\n        user_id: user.employee_id,\n        name: `${user.first_name} ${user.last_name}`,\n        email: user.email,\n        department: 'Engineering',\n        position: 'Software Engineer',\n        disabled: false,\n        created: '2024-01-01T00:00:00Z',\n        updated: new Date().toISOString()\n      };\n\n      // Mock recent access events\n      const mockEvents: BiometricEvent[] = Array.from({ length: 8 }, (_, i) => ({\n        id: `event-${i}`,\n        user_id: user.employee_id,\n        device_id: `dev-${(i % 3) + 1}`,\n        event_type: (i % 2 === 0 ? 'ENTRY' : 'EXIT') as 'ENTRY' | 'EXIT' | 'DENIED',\n        datetime: new Date(Date.now() - i * 2 * 60 * 60 * 1000).toISOString(), // Every 2 hours\n        user_name: `${user.first_name} ${user.last_name}`,\n        device_name: ['Main Entrance', 'Office Floor', 'Back Entrance'][i % 3]\n      }));\n\n      setBiometricProfile({\n        user: mockBiostarUser,\n        enrolledFingerprints: 2,\n        enrolledFaces: 1,\n        lastSync: new Date().toISOString(),\n        accessLevel: 'Standard Employee',\n        isActive: true\n      });\n\n      setRecentEvents(mockEvents);\n\n    } catch (error) {\n      console.error('Failed to load biometric data:', error);\n      // Set default profile even on error\n      setBiometricProfile({\n        user: null,\n        enrolledFingerprints: 2,\n        enrolledFaces: 1,\n        lastSync: new Date().toISOString(),\n        accessLevel: 'Standard Employee',\n        isActive: true\n      });\n      setRecentEvents([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await Promise.all([\n      loadBiometricData(),\n      refreshAttendance()\n    ]);\n    setRefreshing(false);\n  };\n\n\n\n  const tabs = [\n    { id: 'profile', label: 'Biometric Profile', icon: User },\n    { id: 'devices', label: 'Devices', icon: Monitor },\n    { id: 'history', label: 'Access History', icon: History },\n    { id: 'security', label: 'Security', icon: Shield }\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center p-8\">\n        <RefreshCw className=\"h-8 w-8 animate-spin text-gray-400\" />\n        <span className=\"ml-2 text-gray-600\">Loading biometric data...</span>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">BioStar Profile</h1>\n          <p className=\"text-gray-600 mt-1\">\n            Manage your biometric data and view access history\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"flex items-center space-x-2\">\n            {biostarConnected ? (\n              <Wifi className=\"h-4 w-4 text-green-500\" />\n            ) : (\n              <WifiOff className=\"h-4 w-4 text-red-500\" />\n            )}\n            <span className=\"text-sm text-gray-600\">\n              BioStar {biostarConnected ? 'Connected' : 'Disconnected'}\n            </span>\n          </div>\n          <Button\n            variant=\"secondary\"\n            onClick={handleRefresh}\n            disabled={refreshing}\n          >\n            <RefreshCw className={cn(\"h-4 w-4 mr-2\", refreshing && \"animate-spin\")} />\n            {refreshing ? 'Refreshing...' : 'Refresh'}\n          </Button>\n        </div>\n      </div>\n\n      {/* Connection Error Alert */}\n      {connectionError && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <AlertCircle className=\"h-5 w-5 text-red-500 mr-2\" />\n            <div>\n              <h3 className=\"text-sm font-medium text-red-800\">Connection Error</h3>\n              <p className=\"text-sm text-red-700 mt-1\">{connectionError}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Status Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <Card>\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <User className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Profile Status</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {biometricProfile?.isActive ? 'Active' : 'Inactive'}\n                </p>\n              </div>\n            </div>\n          </div>\n        </Card>\n\n        <Card>\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <Fingerprint className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Fingerprints</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {biometricProfile?.enrolledFingerprints || 0}\n                </p>\n              </div>\n            </div>\n          </div>\n        </Card>\n\n        <Card>\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <Eye className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Face Templates</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {biometricProfile?.enrolledFaces || 0}\n                </p>\n              </div>\n            </div>\n          </div>\n        </Card>\n\n        <Card>\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-orange-100 rounded-lg\">\n                <Monitor className=\"h-6 w-6 text-orange-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Devices</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {devices.filter(d => d.status === 'ONLINE').length}/{devices.length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </Card>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon;\n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={cn(\n                  'flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm',\n                  activeTab === tab.id\n                    ? 'border-orange-500 text-orange-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                )}\n              >\n                <Icon className=\"h-4 w-4\" />\n                <span>{tab.label}</span>\n              </button>\n            );\n          })}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      <div className=\"mt-6\">\n        {activeTab === 'profile' && (\n          <BiometricProfileTab\n            profile={biometricProfile}\n            user={user}\n          />\n        )}\n\n        {activeTab === 'devices' && (\n          <DevicesTab\n            devices={devices}\n            connected={biostarConnected}\n          />\n        )}\n\n        {activeTab === 'history' && (\n          <AccessHistoryTab\n            events={recentEvents}\n            employeeId={user?.employee_id}\n          />\n        )}\n\n        {activeTab === 'security' && (\n          <SecurityTab\n            profile={biometricProfile}\n            user={user}\n          />\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Biometric Profile Tab Component\ninterface BiometricProfileTabProps {\n  profile: BiometricProfile | null;\n  user: any;\n}\n\nconst BiometricProfileTab: React.FC<BiometricProfileTabProps> = ({ profile, user }) => {\n  return (\n    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      {/* Profile Information */}\n      <Card>\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Profile Information</h3>\n          <div className=\"space-y-4\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">Employee ID</span>\n              <span className=\"font-medium\">{user?.employee_id || 'N/A'}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">Full Name</span>\n              <span className=\"font-medium\">{profile?.user?.name || `${user?.first_name} ${user?.last_name}`}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">Email</span>\n              <span className=\"font-medium\">{profile?.user?.email || user?.email}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">Department</span>\n              <span className=\"font-medium\">{profile?.user?.department || 'N/A'}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">Position</span>\n              <span className=\"font-medium\">{profile?.user?.position || 'N/A'}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">Access Level</span>\n              <span className=\"font-medium\">{profile?.accessLevel || 'Standard'}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">Status</span>\n              <span className={cn(\n                'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',\n                profile?.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n              )}>\n                {profile?.isActive ? 'Active' : 'Inactive'}\n              </span>\n            </div>\n          </div>\n        </div>\n      </Card>\n\n      {/* Biometric Data */}\n      <Card>\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Biometric Enrollment</h3>\n          <div className=\"space-y-6\">\n            {/* Fingerprints */}\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <Fingerprint className=\"h-5 w-5 text-blue-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium text-gray-900\">Fingerprints</p>\n                  <p className=\"text-sm text-gray-600\">{profile?.enrolledFingerprints || 0} enrolled</p>\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-gray-900\">{profile?.enrolledFingerprints || 0}/10</div>\n                <div className=\"text-xs text-gray-500\">Max: 10</div>\n              </div>\n            </div>\n\n            {/* Face Recognition */}\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"p-2 bg-purple-100 rounded-lg\">\n                  <Eye className=\"h-5 w-5 text-purple-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium text-gray-900\">Face Templates</p>\n                  <p className=\"text-sm text-gray-600\">{profile?.enrolledFaces || 0} enrolled</p>\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-gray-900\">{profile?.enrolledFaces || 0}/5</div>\n                <div className=\"text-xs text-gray-500\">Max: 5</div>\n              </div>\n            </div>\n\n            {/* Last Sync */}\n            <div className=\"pt-4 border-t border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Last Sync</span>\n                <span className=\"font-medium\">\n                  {profile?.lastSync ?\n                    formatDate(profile.lastSync, 'MMM dd, yyyy HH:mm') :\n                    'Never'\n                  }\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\n// Devices Tab Component\ninterface DevicesTabProps {\n  devices: BiometricDevice[];\n  connected: boolean;\n}\n\nconst DevicesTab: React.FC<DevicesTabProps> = ({ devices, connected }) => {\n  return (\n    <div className=\"space-y-6\">\n      {/* Connection Status */}\n      <Card>\n        <div className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              {connected ? (\n                <Wifi className=\"h-6 w-6 text-green-500\" />\n              ) : (\n                <WifiOff className=\"h-6 w-6 text-red-500\" />\n              )}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900\">System Status</h3>\n                <p className=\"text-sm text-gray-600\">\n                  BioStar system is {connected ? 'online and accessible' : 'offline or unreachable'}\n                </p>\n              </div>\n            </div>\n            <span className={cn(\n              'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',\n              connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n            )}>\n              {connected ? 'Connected' : 'Disconnected'}\n            </span>\n          </div>\n        </div>\n      </Card>\n\n      {/* Devices List */}\n      <Card>\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Available Devices</h3>\n          {devices.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <Monitor className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Devices Found</h3>\n              <p className=\"text-gray-600\">No biometric devices are currently available.</p>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {devices.map((device) => (\n                <div key={device.id} className=\"border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Monitor className=\"h-5 w-5 text-gray-600\" />\n                      <h4 className=\"font-medium text-gray-900\">{device.name}</h4>\n                    </div>\n                    <span className={cn(\n                      'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',\n                      device.status === 'ONLINE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                    )}>\n                      {device.status}\n                    </span>\n                  </div>\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Location</span>\n                      <span className=\"font-medium\">{device.location || 'Unknown'}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">IP Address</span>\n                      <span className=\"font-medium\">{device.ip}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Type</span>\n                      <span className=\"font-medium\">{device.type}</span>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </Card>\n    </div>\n  );\n};\n\n// Access History Tab Component\ninterface AccessHistoryTabProps {\n  events: BiometricEvent[];\n  employeeId?: string;\n}\n\nconst AccessHistoryTab: React.FC<AccessHistoryTabProps> = ({ events }) => {\n  const [selectedPeriod, setSelectedPeriod] = useState('7_days');\n  const [filteredEvents, setFilteredEvents] = useState<BiometricEvent[]>(events);\n\n  useEffect(() => {\n    // Filter events based on selected period\n    const now = new Date();\n    let startDate: Date;\n\n    switch (selectedPeriod) {\n      case '1_day':\n        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n        break;\n      case '7_days':\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n        break;\n      case '30_days':\n        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n        break;\n      default:\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n    }\n\n    const filtered = events.filter(event =>\n      new Date(event.datetime) >= startDate\n    );\n    setFilteredEvents(filtered);\n  }, [events, selectedPeriod]);\n\n  const getEventTypeIcon = (eventType: string) => {\n    switch (eventType) {\n      case 'ENTRY': return <CheckCircle className=\"h-4 w-4 text-green-500\" />;\n      case 'EXIT': return <Activity className=\"h-4 w-4 text-blue-500\" />;\n      case 'DENIED': return <AlertCircle className=\"h-4 w-4 text-red-500\" />;\n      default: return <Clock className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getEventTypeColor = (eventType: string) => {\n    switch (eventType) {\n      case 'ENTRY': return 'bg-green-100 text-green-800';\n      case 'EXIT': return 'bg-blue-100 text-blue-800';\n      case 'DENIED': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Filter Controls */}\n      <Card>\n        <div className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-medium text-gray-900\">Access History</h3>\n            <select\n              value={selectedPeriod}\n              onChange={(e) => setSelectedPeriod(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500\"\n            >\n              <option value=\"1_day\">Last 24 Hours</option>\n              <option value=\"7_days\">Last 7 Days</option>\n              <option value=\"30_days\">Last 30 Days</option>\n            </select>\n          </div>\n        </div>\n      </Card>\n\n      {/* Events List */}\n      <Card>\n        <div className=\"p-6\">\n          {filteredEvents.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <History className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Access Events</h3>\n              <p className=\"text-gray-600\">No access events found for the selected period.</p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {filteredEvents.map((event, index) => (\n                <div key={`${event.id}-${index}`} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n                  <div className=\"flex items-center space-x-4\">\n                    {getEventTypeIcon(event.event_type)}\n                    <div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"font-medium text-gray-900\">\n                          {event.event_type === 'ENTRY' ? 'Check In' :\n                           event.event_type === 'EXIT' ? 'Check Out' : 'Access Denied'}\n                        </span>\n                        <span className={cn(\n                          'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',\n                          getEventTypeColor(event.event_type)\n                        )}>\n                          {event.event_type}\n                        </span>\n                      </div>\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-600 mt-1\">\n                        <div className=\"flex items-center space-x-1\">\n                          <Clock className=\"h-3 w-3\" />\n                          <span>{formatDate(event.datetime, 'MMM dd, yyyy HH:mm:ss')}</span>\n                        </div>\n                        {event.device_name && (\n                          <div className=\"flex items-center space-x-1\">\n                            <Monitor className=\"h-3 w-3\" />\n                            <span>{event.device_name}</span>\n                          </div>\n                        )}\n                        <div className=\"flex items-center space-x-1\">\n                          <MapPin className=\"h-3 w-3\" />\n                          <span>Main Office</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </Card>\n    </div>\n  );\n};\n\n// Security Tab Component\ninterface SecurityTabProps {\n  profile: BiometricProfile | null;\n  user: any;\n}\n\nconst SecurityTab: React.FC<SecurityTabProps> = ({ profile }) => {\n  const [showSecuritySettings, setShowSecuritySettings] = useState(false);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Security Overview */}\n      <Card>\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Security Overview</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"p-3 bg-green-100 rounded-full w-12 h-12 mx-auto mb-2\">\n                <Shield className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <h4 className=\"font-medium text-gray-900\">Account Status</h4>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                {profile?.isActive ? 'Active & Secure' : 'Inactive'}\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"p-3 bg-blue-100 rounded-full w-12 h-12 mx-auto mb-2\">\n                <Database className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <h4 className=\"font-medium text-gray-900\">Data Sync</h4>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                {profile?.lastSync ? 'Recently Synced' : 'Not Synced'}\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"p-3 bg-purple-100 rounded-full w-12 h-12 mx-auto mb-2\">\n                <Fingerprint className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <h4 className=\"font-medium text-gray-900\">Biometric Security</h4>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                {(profile?.enrolledFingerprints || 0) + (profile?.enrolledFaces || 0) > 0 ? 'Enrolled' : 'Not Enrolled'}\n              </p>\n            </div>\n          </div>\n        </div>\n      </Card>\n\n      {/* Security Settings */}\n      <Card>\n        <div className=\"p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-medium text-gray-900\">Security Settings</h3>\n            <Button\n              variant=\"secondary\"\n              onClick={() => setShowSecuritySettings(!showSecuritySettings)}\n            >\n              <Settings className=\"h-4 w-4 mr-2\" />\n              {showSecuritySettings ? 'Hide Settings' : 'Show Settings'}\n            </Button>\n          </div>\n\n          {showSecuritySettings && (\n            <div className=\"space-y-4 border-t border-gray-200 pt-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Two-Factor Authentication</h4>\n                  <p className=\"text-sm text-gray-600\">Add an extra layer of security to your account</p>\n                </div>\n                <Button variant=\"secondary\" size=\"sm\">\n                  Enable\n                </Button>\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Access Notifications</h4>\n                  <p className=\"text-sm text-gray-600\">Get notified when your biometric data is used</p>\n                </div>\n                <Button variant=\"secondary\" size=\"sm\">\n                  Configure\n                </Button>\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Data Privacy</h4>\n                  <p className=\"text-sm text-gray-600\">Manage how your biometric data is stored and used</p>\n                </div>\n                <Button variant=\"secondary\" size=\"sm\">\n                  Review\n                </Button>\n              </div>\n            </div>\n          )}\n        </div>\n      </Card>\n\n      {/* Privacy Information */}\n      <Card>\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Privacy & Data Protection</h3>\n          <div className=\"space-y-4 text-sm text-gray-600\">\n            <div className=\"flex items-start space-x-2\">\n              <CheckCircle className=\"h-4 w-4 text-green-500 mt-0.5\" />\n              <p>Your biometric data is encrypted and stored securely</p>\n            </div>\n            <div className=\"flex items-start space-x-2\">\n              <CheckCircle className=\"h-4 w-4 text-green-500 mt-0.5\" />\n              <p>Access logs are maintained for security and compliance purposes</p>\n            </div>\n            <div className=\"flex items-start space-x-2\">\n              <CheckCircle className=\"h-4 w-4 text-green-500 mt-0.5\" />\n              <p>Your data is only used for authentication and attendance tracking</p>\n            </div>\n            <div className=\"flex items-start space-x-2\">\n              <CheckCircle className=\"h-4 w-4 text-green-500 mt-0.5\" />\n              <p>You can request data deletion upon employment termination</p>\n            </div>\n          </div>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default StaffBiostarPage;\n"], "names": ["StaffBiostarPage", "user", "useAuth", "activeTab", "setActiveTab", "useState", "biometricProfile", "setBiometricProfile", "recentEvents", "setRecentEvents", "loading", "setLoading", "refreshing", "setRefreshing", "devices", "connected", "biostarConnected", "error", "connectionError", "refresh", "refreshAttendance", "useBiostarAttendance", "employeeId", "employee_id", "autoRefresh", "enableRealTime", "useEffect", "loadBiometricData", "Promise", "resolve", "setTimeout", "mockBiostarUser", "id", "user_id", "name", "first_name", "last_name", "email", "department", "position", "disabled", "created", "updated", "Date", "toISOString", "mockEvents", "Array", "from", "length", "_", "i", "device_id", "event_type", "datetime", "now", "user_name", "device_name", "enrolledFingerprints", "enrolledFaces", "lastSync", "accessLevel", "isActive", "console", "handleRefresh", "all", "tabs", "label", "icon", "User", "Monitor", "History", "Shield", "div", "className", "RefreshCw", "span", "h1", "p", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "variant", "onClick", "cn", "AlertCircle", "h3", "Card", "Fingerprint", "Eye", "filter", "d", "status", "nav", "map", "tab", "Icon", "button", "BiometricProfileTab", "profile", "DevicesTab", "AccessHistoryTab", "events", "SecurityTab", "formatDate", "device", "h4", "location", "ip", "type", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "filteredEvents", "setFilteredEvents", "startDate", "getTime", "filtered", "event", "getEventTypeIcon", "eventType", "CheckCircle", "Activity", "Clock", "getEventTypeColor", "select", "value", "onChange", "e", "target", "option", "index", "MapPin", "showSecuritySettings", "setShowSecuritySettings", "Database", "Settings", "size"], "mappings": "AAAA;;;;;+BAmwBA;;;eAAA;;;;+DAjwB2C;6BAkBpC;6DACU;+DACE;8BACK;uBACO;sCACM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrC,MAAMA,mBAA6B;IACjC,MAAM,EAAEC,IAAI,EAAE,GAAGC,IAAAA,qBAAO;IACxB,MAAM,CAACC,WAAWC,aAAa,GAAGC,IAAAA,eAAQ,EAAiD;IAC3F,MAAM,CAACC,kBAAkBC,oBAAoB,GAAGF,IAAAA,eAAQ,EAA0B;IAClF,MAAM,CAACG,cAAcC,gBAAgB,GAAGJ,IAAAA,eAAQ,EAAmB,EAAE;IACrE,MAAM,CAACK,SAASC,WAAW,GAAGN,IAAAA,eAAQ,EAAC;IACvC,MAAM,CAACO,YAAYC,cAAc,GAAGR,IAAAA,eAAQ,EAAC;IAE7C,+DAA+D;IAC/D,MAAM,EACJS,OAAO,EACPC,WAAWC,gBAAgB,EAC3BC,OAAOC,eAAe,EACtBC,SAASC,iBAAiB,EAC3B,GAAGC,IAAAA,0CAAoB,EAAC;QACvBC,YAAYrB,MAAMsB;QAClBC,aAAa;QACbC,gBAAgB;IAClB;IAEAC,IAAAA,gBAAS,EAAC;QACRC;IACF,GAAG;QAAC1B,MAAMsB;KAAY;IAEtB,MAAMI,oBAAoB;QACxB,IAAI,CAAC1B,MAAMsB,aAAa;YACtBZ,WAAW;YACX;QACF;QAEA,IAAI;YACFA,WAAW;YAEX,qBAAqB;YACrB,MAAM,IAAIiB,QAAQC,CAAAA,UAAWC,WAAWD,SAAS;YAEjD,qCAAqC;YACrC,MAAME,kBAAkB;gBACtBC,IAAI;gBACJC,SAAShC,KAAKsB,WAAW;gBACzBW,MAAM,GAAGjC,KAAKkC,UAAU,CAAC,CAAC,EAAElC,KAAKmC,SAAS,EAAE;gBAC5CC,OAAOpC,KAAKoC,KAAK;gBACjBC,YAAY;gBACZC,UAAU;gBACVC,UAAU;gBACVC,SAAS;gBACTC,SAAS,IAAIC,OAAOC,WAAW;YACjC;YAEA,4BAA4B;YAC5B,MAAMC,aAA+BC,MAAMC,IAAI,CAAC;gBAAEC,QAAQ;YAAE,GAAG,CAACC,GAAGC,IAAO,CAAA;oBACxElB,IAAI,CAAC,MAAM,EAAEkB,GAAG;oBAChBjB,SAAShC,KAAKsB,WAAW;oBACzB4B,WAAW,CAAC,IAAI,EAAE,AAACD,IAAI,IAAK,GAAG;oBAC/BE,YAAaF,IAAI,MAAM,IAAI,UAAU;oBACrCG,UAAU,IAAIV,KAAKA,KAAKW,GAAG,KAAKJ,IAAI,IAAI,KAAK,KAAK,MAAMN,WAAW;oBACnEW,WAAW,GAAGtD,KAAKkC,UAAU,CAAC,CAAC,EAAElC,KAAKmC,SAAS,EAAE;oBACjDoB,aAAa;wBAAC;wBAAiB;wBAAgB;qBAAgB,CAACN,IAAI,EAAE;gBACxE,CAAA;YAEA3C,oBAAoB;gBAClBN,MAAM8B;gBACN0B,sBAAsB;gBACtBC,eAAe;gBACfC,UAAU,IAAIhB,OAAOC,WAAW;gBAChCgB,aAAa;gBACbC,UAAU;YACZ;YAEApD,gBAAgBoC;QAElB,EAAE,OAAO5B,OAAO;YACd6C,QAAQ7C,KAAK,CAAC,kCAAkCA;YAChD,oCAAoC;YACpCV,oBAAoB;gBAClBN,MAAM;gBACNwD,sBAAsB;gBACtBC,eAAe;gBACfC,UAAU,IAAIhB,OAAOC,WAAW;gBAChCgB,aAAa;gBACbC,UAAU;YACZ;YACApD,gBAAgB,EAAE;QACpB,SAAU;YACRE,WAAW;QACb;IACF;IAEA,MAAMoD,gBAAgB;QACpBlD,cAAc;QACd,MAAMe,QAAQoC,GAAG,CAAC;YAChBrC;YACAP;SACD;QACDP,cAAc;IAChB;IAIA,MAAMoD,OAAO;QACX;YAAEjC,IAAI;YAAWkC,OAAO;YAAqBC,MAAMC,iBAAI;QAAC;QACxD;YAAEpC,IAAI;YAAWkC,OAAO;YAAWC,MAAME,oBAAO;QAAC;QACjD;YAAErC,IAAI;YAAWkC,OAAO;YAAkBC,MAAMG,oBAAO;QAAC;QACxD;YAAEtC,IAAI;YAAYkC,OAAO;YAAYC,MAAMI,mBAAM;QAAC;KACnD;IAED,IAAI7D,SAAS;QACX,qBACE,sBAAC8D;YAAIC,WAAU;;8BACb,qBAACC,sBAAS;oBAACD,WAAU;;8BACrB,qBAACE;oBAAKF,WAAU;8BAAqB;;;;IAG3C;IAEA,qBACE,sBAACD;QAAIC,WAAU;;0BAEb,sBAACD;gBAAIC,WAAU;;kCACb,sBAACD;;0CACC,qBAACI;gCAAGH,WAAU;0CAAmC;;0CACjD,qBAACI;gCAAEJ,WAAU;0CAAqB;;;;kCAIpC,sBAACD;wBAAIC,WAAU;;0CACb,sBAACD;gCAAIC,WAAU;;oCACZzD,iCACC,qBAAC8D,iBAAI;wCAACL,WAAU;uDAEhB,qBAACM,oBAAO;wCAACN,WAAU;;kDAErB,sBAACE;wCAAKF,WAAU;;4CAAwB;4CAC7BzD,mBAAmB,cAAc;;;;;0CAG9C,sBAACgE,eAAM;gCACLC,SAAQ;gCACRC,SAASnB;gCACTvB,UAAU5B;;kDAEV,qBAAC8D,sBAAS;wCAACD,WAAWU,IAAAA,SAAE,EAAC,gBAAgBvE,cAAc;;oCACtDA,aAAa,kBAAkB;;;;;;;YAMrCM,iCACC,qBAACsD;gBAAIC,WAAU;0BACb,cAAA,sBAACD;oBAAIC,WAAU;;sCACb,qBAACW,wBAAW;4BAACX,WAAU;;sCACvB,sBAACD;;8CACC,qBAACa;oCAAGZ,WAAU;8CAAmC;;8CACjD,qBAACI;oCAAEJ,WAAU;8CAA6BvD;;;;;;;0BAOlD,sBAACsD;gBAAIC,WAAU;;kCACb,qBAACa,aAAI;kCACH,cAAA,qBAACd;4BAAIC,WAAU;sCACb,cAAA,sBAACD;gCAAIC,WAAU;;kDACb,qBAACD;wCAAIC,WAAU;kDACb,cAAA,qBAACL,iBAAI;4CAACK,WAAU;;;kDAElB,sBAACD;wCAAIC,WAAU;;0DACb,qBAACI;gDAAEJ,WAAU;0DAAoC;;0DACjD,qBAACI;gDAAEJ,WAAU;0DACVnE,kBAAkBuD,WAAW,WAAW;;;;;;;;kCAOnD,qBAACyB,aAAI;kCACH,cAAA,qBAACd;4BAAIC,WAAU;sCACb,cAAA,sBAACD;gCAAIC,WAAU;;kDACb,qBAACD;wCAAIC,WAAU;kDACb,cAAA,qBAACc,wBAAW;4CAACd,WAAU;;;kDAEzB,sBAACD;wCAAIC,WAAU;;0DACb,qBAACI;gDAAEJ,WAAU;0DAAoC;;0DACjD,qBAACI;gDAAEJ,WAAU;0DACVnE,kBAAkBmD,wBAAwB;;;;;;;;kCAOrD,qBAAC6B,aAAI;kCACH,cAAA,qBAACd;4BAAIC,WAAU;sCACb,cAAA,sBAACD;gCAAIC,WAAU;;kDACb,qBAACD;wCAAIC,WAAU;kDACb,cAAA,qBAACe,gBAAG;4CAACf,WAAU;;;kDAEjB,sBAACD;wCAAIC,WAAU;;0DACb,qBAACI;gDAAEJ,WAAU;0DAAoC;;0DACjD,qBAACI;gDAAEJ,WAAU;0DACVnE,kBAAkBoD,iBAAiB;;;;;;;;kCAO9C,qBAAC4B,aAAI;kCACH,cAAA,qBAACd;4BAAIC,WAAU;sCACb,cAAA,sBAACD;gCAAIC,WAAU;;kDACb,qBAACD;wCAAIC,WAAU;kDACb,cAAA,qBAACJ,oBAAO;4CAACI,WAAU;;;kDAErB,sBAACD;wCAAIC,WAAU;;0DACb,qBAACI;gDAAEJ,WAAU;0DAAoC;;0DACjD,sBAACI;gDAAEJ,WAAU;;oDACV3D,QAAQ2E,MAAM,CAACC,CAAAA,IAAKA,EAAEC,MAAM,KAAK,UAAU3C,MAAM;oDAAC;oDAAElC,QAAQkC,MAAM;;;;;;;;;;;0BAS/E,qBAACwB;gBAAIC,WAAU;0BACb,cAAA,qBAACmB;oBAAInB,WAAU;8BACZR,KAAK4B,GAAG,CAAC,CAACC;wBACT,MAAMC,OAAOD,IAAI3B,IAAI;wBACrB,qBACE,sBAAC6B;4BAECd,SAAS,IAAM9E,aAAa0F,IAAI9D,EAAE;4BAClCyC,WAAWU,IAAAA,SAAE,EACX,wEACAhF,cAAc2F,IAAI9D,EAAE,GAChB,sCACA;;8CAGN,qBAAC+D;oCAAKtB,WAAU;;8CAChB,qBAACE;8CAAMmB,IAAI5B,KAAK;;;2BAVX4B,IAAI9D,EAAE;oBAajB;;;0BAKJ,sBAACwC;gBAAIC,WAAU;;oBACZtE,cAAc,2BACb,qBAAC8F;wBACCC,SAAS5F;wBACTL,MAAMA;;oBAITE,cAAc,2BACb,qBAACgG;wBACCrF,SAASA;wBACTC,WAAWC;;oBAIdb,cAAc,2BACb,qBAACiG;wBACCC,QAAQ7F;wBACRc,YAAYrB,MAAMsB;;oBAIrBpB,cAAc,4BACb,qBAACmG;wBACCJ,SAAS5F;wBACTL,MAAMA;;;;;;AAMlB;AAQA,MAAMgG,sBAA0D,CAAC,EAAEC,OAAO,EAAEjG,IAAI,EAAE;IAChF,qBACE,sBAACuE;QAAIC,WAAU;;0BAEb,qBAACa,aAAI;0BACH,cAAA,sBAACd;oBAAIC,WAAU;;sCACb,qBAACY;4BAAGZ,WAAU;sCAAyC;;sCACvD,sBAACD;4BAAIC,WAAU;;8CACb,sBAACD;oCAAIC,WAAU;;sDACb,qBAACE;4CAAKF,WAAU;sDAAwB;;sDACxC,qBAACE;4CAAKF,WAAU;sDAAexE,MAAMsB,eAAe;;;;8CAEtD,sBAACiD;oCAAIC,WAAU;;sDACb,qBAACE;4CAAKF,WAAU;sDAAwB;;sDACxC,qBAACE;4CAAKF,WAAU;sDAAeyB,SAASjG,MAAMiC,QAAQ,GAAGjC,MAAMkC,WAAW,CAAC,EAAElC,MAAMmC,WAAW;;;;8CAEhG,sBAACoC;oCAAIC,WAAU;;sDACb,qBAACE;4CAAKF,WAAU;sDAAwB;;sDACxC,qBAACE;4CAAKF,WAAU;sDAAeyB,SAASjG,MAAMoC,SAASpC,MAAMoC;;;;8CAE/D,sBAACmC;oCAAIC,WAAU;;sDACb,qBAACE;4CAAKF,WAAU;sDAAwB;;sDACxC,qBAACE;4CAAKF,WAAU;sDAAeyB,SAASjG,MAAMqC,cAAc;;;;8CAE9D,sBAACkC;oCAAIC,WAAU;;sDACb,qBAACE;4CAAKF,WAAU;sDAAwB;;sDACxC,qBAACE;4CAAKF,WAAU;sDAAeyB,SAASjG,MAAMsC,YAAY;;;;8CAE5D,sBAACiC;oCAAIC,WAAU;;sDACb,qBAACE;4CAAKF,WAAU;sDAAwB;;sDACxC,qBAACE;4CAAKF,WAAU;sDAAeyB,SAAStC,eAAe;;;;8CAEzD,sBAACY;oCAAIC,WAAU;;sDACb,qBAACE;4CAAKF,WAAU;sDAAwB;;sDACxC,qBAACE;4CAAKF,WAAWU,IAAAA,SAAE,EACjB,uEACAe,SAASrC,WAAW,gCAAgC;sDAEnDqC,SAASrC,WAAW,WAAW;;;;;;;;;0BAQ1C,qBAACyB,aAAI;0BACH,cAAA,sBAACd;oBAAIC,WAAU;;sCACb,qBAACY;4BAAGZ,WAAU;sCAAyC;;sCACvD,sBAACD;4BAAIC,WAAU;;8CAEb,sBAACD;oCAAIC,WAAU;;sDACb,sBAACD;4CAAIC,WAAU;;8DACb,qBAACD;oDAAIC,WAAU;8DACb,cAAA,qBAACc,wBAAW;wDAACd,WAAU;;;8DAEzB,sBAACD;;sEACC,qBAACK;4DAAEJ,WAAU;sEAA4B;;sEACzC,sBAACI;4DAAEJ,WAAU;;gEAAyByB,SAASzC,wBAAwB;gEAAE;;;;;;;sDAG7E,sBAACe;4CAAIC,WAAU;;8DACb,sBAACD;oDAAIC,WAAU;;wDAAoCyB,SAASzC,wBAAwB;wDAAE;;;8DACtF,qBAACe;oDAAIC,WAAU;8DAAwB;;;;;;8CAK3C,sBAACD;oCAAIC,WAAU;;sDACb,sBAACD;4CAAIC,WAAU;;8DACb,qBAACD;oDAAIC,WAAU;8DACb,cAAA,qBAACe,gBAAG;wDAACf,WAAU;;;8DAEjB,sBAACD;;sEACC,qBAACK;4DAAEJ,WAAU;sEAA4B;;sEACzC,sBAACI;4DAAEJ,WAAU;;gEAAyByB,SAASxC,iBAAiB;gEAAE;;;;;;;sDAGtE,sBAACc;4CAAIC,WAAU;;8DACb,sBAACD;oDAAIC,WAAU;;wDAAoCyB,SAASxC,iBAAiB;wDAAE;;;8DAC/E,qBAACc;oDAAIC,WAAU;8DAAwB;;;;;;8CAK3C,qBAACD;oCAAIC,WAAU;8CACb,cAAA,sBAACD;wCAAIC,WAAU;;0DACb,qBAACE;gDAAKF,WAAU;0DAAwB;;0DACxC,qBAACE;gDAAKF,WAAU;0DACbyB,SAASvC,WACR4C,IAAAA,iBAAU,EAACL,QAAQvC,QAAQ,EAAE,wBAC7B;;;;;;;;;;;;AAUpB;AAQA,MAAMwC,aAAwC,CAAC,EAAErF,OAAO,EAAEC,SAAS,EAAE;IACnE,qBACE,sBAACyD;QAAIC,WAAU;;0BAEb,qBAACa,aAAI;0BACH,cAAA,qBAACd;oBAAIC,WAAU;8BACb,cAAA,sBAACD;wBAAIC,WAAU;;0CACb,sBAACD;gCAAIC,WAAU;;oCACZ1D,0BACC,qBAAC+D,iBAAI;wCAACL,WAAU;uDAEhB,qBAACM,oBAAO;wCAACN,WAAU;;kDAErB,sBAACD;;0DACC,qBAACa;gDAAGZ,WAAU;0DAAoC;;0DAClD,sBAACI;gDAAEJ,WAAU;;oDAAwB;oDAChB1D,YAAY,0BAA0B;;;;;;;0CAI/D,qBAAC4D;gCAAKF,WAAWU,IAAAA,SAAE,EACjB,uEACApE,YAAY,gCAAgC;0CAE3CA,YAAY,cAAc;;;;;;0BAOnC,qBAACuE,aAAI;0BACH,cAAA,sBAACd;oBAAIC,WAAU;;sCACb,qBAACY;4BAAGZ,WAAU;sCAAyC;;wBACtD3D,QAAQkC,MAAM,KAAK,kBAClB,sBAACwB;4BAAIC,WAAU;;8CACb,qBAACJ,oBAAO;oCAACI,WAAU;;8CACnB,qBAACY;oCAAGZ,WAAU;8CAAyC;;8CACvD,qBAACI;oCAAEJ,WAAU;8CAAgB;;;2CAG/B,qBAACD;4BAAIC,WAAU;sCACZ3D,QAAQ+E,GAAG,CAAC,CAACW,uBACZ,sBAAChC;oCAAoBC,WAAU;;sDAC7B,sBAACD;4CAAIC,WAAU;;8DACb,sBAACD;oDAAIC,WAAU;;sEACb,qBAACJ,oBAAO;4DAACI,WAAU;;sEACnB,qBAACgC;4DAAGhC,WAAU;sEAA6B+B,OAAOtE,IAAI;;;;8DAExD,qBAACyC;oDAAKF,WAAWU,IAAAA,SAAE,EACjB,uEACAqB,OAAOb,MAAM,KAAK,WAAW,gCAAgC;8DAE5Da,OAAOb,MAAM;;;;sDAGlB,sBAACnB;4CAAIC,WAAU;;8DACb,sBAACD;oDAAIC,WAAU;;sEACb,qBAACE;4DAAKF,WAAU;sEAAgB;;sEAChC,qBAACE;4DAAKF,WAAU;sEAAe+B,OAAOE,QAAQ,IAAI;;;;8DAEpD,sBAAClC;oDAAIC,WAAU;;sEACb,qBAACE;4DAAKF,WAAU;sEAAgB;;sEAChC,qBAACE;4DAAKF,WAAU;sEAAe+B,OAAOG,EAAE;;;;8DAE1C,sBAACnC;oDAAIC,WAAU;;sEACb,qBAACE;4DAAKF,WAAU;sEAAgB;;sEAChC,qBAACE;4DAAKF,WAAU;sEAAe+B,OAAOI,IAAI;;;;;;;mCAxBtCJ,OAAOxE,EAAE;;;;;;;AAmCnC;AAQA,MAAMoE,mBAAoD,CAAC,EAAEC,MAAM,EAAE;IACnE,MAAM,CAACQ,gBAAgBC,kBAAkB,GAAGzG,IAAAA,eAAQ,EAAC;IACrD,MAAM,CAAC0G,gBAAgBC,kBAAkB,GAAG3G,IAAAA,eAAQ,EAAmBgG;IAEvE3E,IAAAA,gBAAS,EAAC;QACR,yCAAyC;QACzC,MAAM4B,MAAM,IAAIX;QAChB,IAAIsE;QAEJ,OAAQJ;YACN,KAAK;gBACHI,YAAY,IAAItE,KAAKW,IAAI4D,OAAO,KAAK,KAAK,KAAK,KAAK;gBACpD;YACF,KAAK;gBACHD,YAAY,IAAItE,KAAKW,IAAI4D,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;gBACxD;YACF,KAAK;gBACHD,YAAY,IAAItE,KAAKW,IAAI4D,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;gBACzD;YACF;gBACED,YAAY,IAAItE,KAAKW,IAAI4D,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;QAC5D;QAEA,MAAMC,WAAWd,OAAOZ,MAAM,CAAC2B,CAAAA,QAC7B,IAAIzE,KAAKyE,MAAM/D,QAAQ,KAAK4D;QAE9BD,kBAAkBG;IACpB,GAAG;QAACd;QAAQQ;KAAe;IAE3B,MAAMQ,mBAAmB,CAACC;QACxB,OAAQA;YACN,KAAK;gBAAS,qBAAO,qBAACC,wBAAW;oBAAC9C,WAAU;;YAC5C,KAAK;gBAAQ,qBAAO,qBAAC+C,qBAAQ;oBAAC/C,WAAU;;YACxC,KAAK;gBAAU,qBAAO,qBAACW,wBAAW;oBAACX,WAAU;;YAC7C;gBAAS,qBAAO,qBAACgD,kBAAK;oBAAChD,WAAU;;QACnC;IACF;IAEA,MAAMiD,oBAAoB,CAACJ;QACzB,OAAQA;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,sBAAC9C;QAAIC,WAAU;;0BAEb,qBAACa,aAAI;0BACH,cAAA,qBAACd;oBAAIC,WAAU;8BACb,cAAA,sBAACD;wBAAIC,WAAU;;0CACb,qBAACY;gCAAGZ,WAAU;0CAAoC;;0CAClD,sBAACkD;gCACCC,OAAOf;gCACPgB,UAAU,CAACC,IAAMhB,kBAAkBgB,EAAEC,MAAM,CAACH,KAAK;gCACjDnD,WAAU;;kDAEV,qBAACuD;wCAAOJ,OAAM;kDAAQ;;kDACtB,qBAACI;wCAAOJ,OAAM;kDAAS;;kDACvB,qBAACI;wCAAOJ,OAAM;kDAAU;;;;;;;;0BAOhC,qBAACtC,aAAI;0BACH,cAAA,qBAACd;oBAAIC,WAAU;8BACZsC,eAAe/D,MAAM,KAAK,kBACzB,sBAACwB;wBAAIC,WAAU;;0CACb,qBAACH,oBAAO;gCAACG,WAAU;;0CACnB,qBAACY;gCAAGZ,WAAU;0CAAyC;;0CACvD,qBAACI;gCAAEJ,WAAU;0CAAgB;;;uCAG/B,qBAACD;wBAAIC,WAAU;kCACZsC,eAAelB,GAAG,CAAC,CAACuB,OAAOa,sBAC1B,qBAACzD;gCAAiCC,WAAU;0CAC1C,cAAA,sBAACD;oCAAIC,WAAU;;wCACZ4C,iBAAiBD,MAAMhE,UAAU;sDAClC,sBAACoB;;8DACC,sBAACA;oDAAIC,WAAU;;sEACb,qBAACE;4DAAKF,WAAU;sEACb2C,MAAMhE,UAAU,KAAK,UAAU,aAC/BgE,MAAMhE,UAAU,KAAK,SAAS,cAAc;;sEAE/C,qBAACuB;4DAAKF,WAAWU,IAAAA,SAAE,EACjB,uEACAuC,kBAAkBN,MAAMhE,UAAU;sEAEjCgE,MAAMhE,UAAU;;;;8DAGrB,sBAACoB;oDAAIC,WAAU;;sEACb,sBAACD;4DAAIC,WAAU;;8EACb,qBAACgD,kBAAK;oEAAChD,WAAU;;8EACjB,qBAACE;8EAAM4B,IAAAA,iBAAU,EAACa,MAAM/D,QAAQ,EAAE;;;;wDAEnC+D,MAAM5D,WAAW,kBAChB,sBAACgB;4DAAIC,WAAU;;8EACb,qBAACJ,oBAAO;oEAACI,WAAU;;8EACnB,qBAACE;8EAAMyC,MAAM5D,WAAW;;;;sEAG5B,sBAACgB;4DAAIC,WAAU;;8EACb,qBAACyD,mBAAM;oEAACzD,WAAU;;8EAClB,qBAACE;8EAAK;;;;;;;;;;+BA7BN,GAAGyC,MAAMpF,EAAE,CAAC,CAAC,EAAEiG,OAAO;;;;;;AA0ChD;AAQA,MAAM3B,cAA0C,CAAC,EAAEJ,OAAO,EAAE;IAC1D,MAAM,CAACiC,sBAAsBC,wBAAwB,GAAG/H,IAAAA,eAAQ,EAAC;IAEjE,qBACE,sBAACmE;QAAIC,WAAU;;0BAEb,qBAACa,aAAI;0BACH,cAAA,sBAACd;oBAAIC,WAAU;;sCACb,qBAACY;4BAAGZ,WAAU;sCAAyC;;sCACvD,sBAACD;4BAAIC,WAAU;;8CACb,sBAACD;oCAAIC,WAAU;;sDACb,qBAACD;4CAAIC,WAAU;sDACb,cAAA,qBAACF,mBAAM;gDAACE,WAAU;;;sDAEpB,qBAACgC;4CAAGhC,WAAU;sDAA4B;;sDAC1C,qBAACI;4CAAEJ,WAAU;sDACVyB,SAASrC,WAAW,oBAAoB;;;;8CAG7C,sBAACW;oCAAIC,WAAU;;sDACb,qBAACD;4CAAIC,WAAU;sDACb,cAAA,qBAAC4D,qBAAQ;gDAAC5D,WAAU;;;sDAEtB,qBAACgC;4CAAGhC,WAAU;sDAA4B;;sDAC1C,qBAACI;4CAAEJ,WAAU;sDACVyB,SAASvC,WAAW,oBAAoB;;;;8CAG7C,sBAACa;oCAAIC,WAAU;;sDACb,qBAACD;4CAAIC,WAAU;sDACb,cAAA,qBAACc,wBAAW;gDAACd,WAAU;;;sDAEzB,qBAACgC;4CAAGhC,WAAU;sDAA4B;;sDAC1C,qBAACI;4CAAEJ,WAAU;sDACV,AAACyB,CAAAA,SAASzC,wBAAwB,CAAA,IAAMyC,CAAAA,SAASxC,iBAAiB,CAAA,IAAK,IAAI,aAAa;;;;;;;;;0BAQnG,qBAAC4B,aAAI;0BACH,cAAA,sBAACd;oBAAIC,WAAU;;sCACb,sBAACD;4BAAIC,WAAU;;8CACb,qBAACY;oCAAGZ,WAAU;8CAAoC;;8CAClD,sBAACO,eAAM;oCACLC,SAAQ;oCACRC,SAAS,IAAMkD,wBAAwB,CAACD;;sDAExC,qBAACG,qBAAQ;4CAAC7D,WAAU;;wCACnB0D,uBAAuB,kBAAkB;;;;;wBAI7CA,sCACC,sBAAC3D;4BAAIC,WAAU;;8CACb,sBAACD;oCAAIC,WAAU;;sDACb,sBAACD;;8DACC,qBAACiC;oDAAGhC,WAAU;8DAA4B;;8DAC1C,qBAACI;oDAAEJ,WAAU;8DAAwB;;;;sDAEvC,qBAACO,eAAM;4CAACC,SAAQ;4CAAYsD,MAAK;sDAAK;;;;8CAKxC,sBAAC/D;oCAAIC,WAAU;;sDACb,sBAACD;;8DACC,qBAACiC;oDAAGhC,WAAU;8DAA4B;;8DAC1C,qBAACI;oDAAEJ,WAAU;8DAAwB;;;;sDAEvC,qBAACO,eAAM;4CAACC,SAAQ;4CAAYsD,MAAK;sDAAK;;;;8CAKxC,sBAAC/D;oCAAIC,WAAU;;sDACb,sBAACD;;8DACC,qBAACiC;oDAAGhC,WAAU;8DAA4B;;8DAC1C,qBAACI;oDAAEJ,WAAU;8DAAwB;;;;sDAEvC,qBAACO,eAAM;4CAACC,SAAQ;4CAAYsD,MAAK;sDAAK;;;;;;;;;0BAUhD,qBAACjD,aAAI;0BACH,cAAA,sBAACd;oBAAIC,WAAU;;sCACb,qBAACY;4BAAGZ,WAAU;sCAAyC;;sCACvD,sBAACD;4BAAIC,WAAU;;8CACb,sBAACD;oCAAIC,WAAU;;sDACb,qBAAC8C,wBAAW;4CAAC9C,WAAU;;sDACvB,qBAACI;sDAAE;;;;8CAEL,sBAACL;oCAAIC,WAAU;;sDACb,qBAAC8C,wBAAW;4CAAC9C,WAAU;;sDACvB,qBAACI;sDAAE;;;;8CAEL,sBAACL;oCAAIC,WAAU;;sDACb,qBAAC8C,wBAAW;4CAAC9C,WAAU;;sDACvB,qBAACI;sDAAE;;;;8CAEL,sBAACL;oCAAIC,WAAU;;sDACb,qBAAC8C,wBAAW;4CAAC9C,WAAU;;sDACvB,qBAACI;sDAAE;;;;;;;;;;;AAOjB;MAEA,WAAe7E"}