{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\nimport { format, parseISO, isValid } from 'date-fns';\n\n/**\n * Utility function to merge class names\n */\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n/**\n * Format date string to readable format\n */\nexport function formatDate(dateString: string, formatStr: string = 'MMM dd, yyyy'): string {\n  try {\n    const date = parseISO(dateString);\n    if (!isValid(date)) {\n      return 'Invalid Date';\n    }\n    return format(date, formatStr);\n  } catch (error) {\n    return 'Invalid Date';\n  }\n}\n\n/**\n * Format currency amount\n */\nexport function formatCurrency(amount: number, currency: string = 'KSH'): string {\n  const locale = currency === 'KSH' || currency === 'KES' ? 'en-KE' : 'en-US';\n  return new Intl.NumberFormat(locale, {\n    style: 'currency',\n    currency: currency,\n  }).format(amount);\n}\n\n/**\n * Format phone number\n */\nexport function formatPhoneNumber(phone: string): string {\n  // Remove all non-digit characters\n  const cleaned = phone.replace(/\\D/g, '');\n\n  // Format as +254 XXX XXX XXX for Kenyan numbers\n  if (cleaned.startsWith('254') && cleaned.length === 12) {\n    return `+${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 9)} ${cleaned.slice(9)}`;\n  }\n\n  // Format as 0XXX XXX XXX for local numbers\n  if (cleaned.startsWith('0') && cleaned.length === 10) {\n    return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;\n  }\n\n  return phone;\n}\n\n/**\n * Calculate days between two dates\n */\nexport function calculateDaysBetween(startDate: string, endDate: string): number {\n  const start = parseISO(startDate);\n  const end = parseISO(endDate);\n\n  if (!isValid(start) || !isValid(end)) {\n    return 0;\n  }\n\n  const diffTime = Math.abs(end.getTime() - start.getTime());\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n  return diffDays;\n}\n\n/**\n * Get initials from full name\n */\nexport function getInitials(firstName: string, lastName: string): string {\n  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();\n}\n\n/**\n * Capitalize first letter of each word\n */\nexport function capitalizeWords(str: string): string {\n  return str.replace(/\\w\\S*/g, (txt) =>\n    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()\n  );\n}\n\n/**\n * Generate random color for avatars\n */\nexport function generateAvatarColor(name: string): string {\n  const colors = [\n    'bg-red-500',\n    'bg-blue-500',\n    'bg-green-500',\n    'bg-yellow-500',\n    'bg-purple-500',\n    'bg-pink-500',\n    'bg-indigo-500',\n    'bg-teal-500',\n  ];\n\n  const index = name.charCodeAt(0) % colors.length;\n  return colors[index];\n}\n\n/**\n * Validate email format\n */\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Validate Kenyan phone number\n */\nexport function isValidKenyanPhone(phone: string): boolean {\n  const cleaned = phone.replace(/\\D/g, '');\n\n  // Check for +254XXXXXXXXX or 254XXXXXXXXX format\n  if (cleaned.startsWith('254') && cleaned.length === 12) {\n    return true;\n  }\n\n  // Check for 0XXXXXXXXX format\n  if (cleaned.startsWith('0') && cleaned.length === 10) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * Debounce function\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n/**\n * Truncate text with ellipsis\n */\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) {\n    return text;\n  }\n\n  return text.slice(0, maxLength) + '...';\n}\n\n/**\n * Get status color class\n */\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    active: 'text-green-600 bg-green-100',\n    inactive: 'text-red-600 bg-red-100',\n    pending: 'text-yellow-600 bg-yellow-100',\n    approved: 'text-green-600 bg-green-100',\n    rejected: 'text-red-600 bg-red-100',\n    completed: 'text-blue-600 bg-blue-100',\n    in_progress: 'text-orange-600 bg-orange-100',\n    not_started: 'text-gray-600 bg-gray-100',\n  };\n\n  return statusColors[status] || 'text-gray-600 bg-gray-100';\n}\n\n/**\n * Format file size\n */\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n\n/**\n * Generate employee ID\n */\nexport function generateEmployeeId(departmentCode: string, sequence: number): string {\n  const year = new Date().getFullYear().toString().slice(-2);\n  const paddedSequence = sequence.toString().padStart(3, '0');\n  return `${departmentCode}${year}${paddedSequence}`;\n}\n\n/**\n * Calculate age from date of birth\n */\nexport function calculateAge(dateOfBirth: string): number {\n  const today = new Date();\n  const birthDate = parseISO(dateOfBirth);\n\n  if (!isValid(birthDate)) {\n    return 0;\n  }\n\n  let age = today.getFullYear() - birthDate.getFullYear();\n  const monthDiff = today.getMonth() - birthDate.getMonth();\n\n  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\n    age--;\n  }\n\n  return age;\n}\n\n/**\n * Check if date is weekend\n */\nexport function isWeekend(date: string): boolean {\n  const day = parseISO(date).getDay();\n  return day === 0 || day === 6; // Sunday = 0, Saturday = 6\n}\n\n/**\n * Get business days between two dates (excluding weekends)\n */\nexport function getBusinessDays(startDate: string, endDate: string): number {\n  const start = parseISO(startDate);\n  const end = parseISO(endDate);\n\n  if (!isValid(start) || !isValid(end)) {\n    return 0;\n  }\n\n  let count = 0;\n  const current = new Date(start);\n\n  while (current <= end) {\n    const dayOfWeek = current.getDay();\n    if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not Sunday or Saturday\n      count++;\n    }\n    current.setDate(current.getDate() + 1);\n  }\n\n  return count;\n}\n\n/**\n * Sleep function for delays\n */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n/**\n * Deep clone object\n */\nexport function deepClone<T>(obj: T): T {\n  return JSON.parse(JSON.stringify(obj));\n}\n\n/**\n * Check if object is empty\n */\nexport function isEmpty(obj: any): boolean {\n  if (obj == null) return true;\n  if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;\n  if (typeof obj === 'object') return Object.keys(obj).length === 0;\n  return false;\n}\n"], "names": ["calculateAge", "calculateDaysBetween", "capitalizeWords", "cn", "debounce", "deepClone", "formatCurrency", "formatDate", "formatFileSize", "formatPhoneNumber", "generateAvatarColor", "generateEmployeeId", "getBusinessDays", "getInitials", "getStatusColor", "isEmpty", "isValidEmail", "isValidKenyanPhone", "isWeekend", "sleep", "truncateText", "inputs", "clsx", "dateString", "formatStr", "date", "parseISO", "<PERSON><PERSON><PERSON><PERSON>", "format", "error", "amount", "currency", "locale", "Intl", "NumberFormat", "style", "phone", "cleaned", "replace", "startsWith", "length", "slice", "startDate", "endDate", "start", "end", "diffTime", "Math", "abs", "getTime", "diffDays", "ceil", "firstName", "lastName", "char<PERSON>t", "toUpperCase", "str", "txt", "substr", "toLowerCase", "name", "colors", "index", "charCodeAt", "email", "emailRegex", "test", "func", "wait", "timeout", "args", "clearTimeout", "setTimeout", "text", "max<PERSON><PERSON><PERSON>", "status", "statusColors", "active", "inactive", "pending", "approved", "rejected", "completed", "in_progress", "not_started", "bytes", "k", "sizes", "i", "floor", "log", "parseFloat", "pow", "toFixed", "departmentCode", "sequence", "year", "Date", "getFullYear", "toString", "paddedSequence", "padStart", "dateOfBirth", "today", "birthDate", "age", "monthDiff", "getMonth", "getDate", "day", "getDay", "count", "current", "dayOfWeek", "setDate", "ms", "Promise", "resolve", "obj", "JSON", "parse", "stringify", "Array", "isArray", "Object", "keys"], "mappings": ";;;;;;;;;;;IA4MgBA,YAAY;eAAZA;;IAjJAC,oBAAoB;eAApBA;;IAwBAC,eAAe;eAAfA;;IA7EAC,EAAE;eAAFA;;IAoIAC,QAAQ;eAARA;;IA+HAC,SAAS;eAATA;;IA7OAC,cAAc;eAAdA;;IAfAC,UAAU;eAAVA;;IAyKAC,cAAc;eAAdA;;IA/IAC,iBAAiB;eAAjBA;;IAqDAC,mBAAmB;eAAnBA;;IAuGAC,kBAAkB;eAAlBA;;IAsCAC,eAAe;eAAfA;;IA7JAC,WAAW;eAAXA;;IAwFAC,cAAc;eAAdA;;IA4GAC,OAAO;eAAPA;;IAjKAC,YAAY;eAAZA;;IAQAC,kBAAkB;eAAlBA;;IA0GAC,SAAS;eAATA;;IAiCAC,KAAK;eAALA;;IAzGAC,YAAY;eAAZA;;;sBAzJsB;yBACI;AAKnC,SAASjB,GAAG,GAAGkB,MAAoB;IACxC,OAAOC,IAAAA,UAAI,EAACD;AACd;AAKO,SAASd,WAAWgB,UAAkB,EAAEC,YAAoB,cAAc;IAC/E,IAAI;QACF,MAAMC,OAAOC,IAAAA,iBAAQ,EAACH;QACtB,IAAI,CAACI,IAAAA,gBAAO,EAACF,OAAO;YAClB,OAAO;QACT;QACA,OAAOG,IAAAA,eAAM,EAACH,MAAMD;IACtB,EAAE,OAAOK,OAAO;QACd,OAAO;IACT;AACF;AAKO,SAASvB,eAAewB,MAAc,EAAEC,WAAmB,KAAK;IACrE,MAAMC,SAASD,aAAa,SAASA,aAAa,QAAQ,UAAU;IACpE,OAAO,IAAIE,KAAKC,YAAY,CAACF,QAAQ;QACnCG,OAAO;QACPJ,UAAUA;IACZ,GAAGH,MAAM,CAACE;AACZ;AAKO,SAASrB,kBAAkB2B,KAAa;IAC7C,kCAAkC;IAClC,MAAMC,UAAUD,MAAME,OAAO,CAAC,OAAO;IAErC,gDAAgD;IAChD,IAAID,QAAQE,UAAU,CAAC,UAAUF,QAAQG,MAAM,KAAK,IAAI;QACtD,OAAO,CAAC,CAAC,EAAEH,QAAQI,KAAK,CAAC,GAAG,GAAG,CAAC,EAAEJ,QAAQI,KAAK,CAAC,GAAG,GAAG,CAAC,EAAEJ,QAAQI,KAAK,CAAC,GAAG,GAAG,CAAC,EAAEJ,QAAQI,KAAK,CAAC,IAAI;IACpG;IAEA,2CAA2C;IAC3C,IAAIJ,QAAQE,UAAU,CAAC,QAAQF,QAAQG,MAAM,KAAK,IAAI;QACpD,OAAO,GAAGH,QAAQI,KAAK,CAAC,GAAG,GAAG,CAAC,EAAEJ,QAAQI,KAAK,CAAC,GAAG,GAAG,CAAC,EAAEJ,QAAQI,KAAK,CAAC,IAAI;IAC5E;IAEA,OAAOL;AACT;AAKO,SAASnC,qBAAqByC,SAAiB,EAAEC,OAAe;IACrE,MAAMC,QAAQlB,IAAAA,iBAAQ,EAACgB;IACvB,MAAMG,MAAMnB,IAAAA,iBAAQ,EAACiB;IAErB,IAAI,CAAChB,IAAAA,gBAAO,EAACiB,UAAU,CAACjB,IAAAA,gBAAO,EAACkB,MAAM;QACpC,OAAO;IACT;IAEA,MAAMC,WAAWC,KAAKC,GAAG,CAACH,IAAII,OAAO,KAAKL,MAAMK,OAAO;IACvD,MAAMC,WAAWH,KAAKI,IAAI,CAACL,WAAY,CAAA,OAAO,KAAK,KAAK,EAAC;IAEzD,OAAOI;AACT;AAKO,SAASrC,YAAYuC,SAAiB,EAAEC,QAAgB;IAC7D,OAAO,GAAGD,UAAUE,MAAM,CAAC,KAAKD,SAASC,MAAM,CAAC,IAAI,CAACC,WAAW;AAClE;AAKO,SAASrD,gBAAgBsD,GAAW;IACzC,OAAOA,IAAIlB,OAAO,CAAC,UAAU,CAACmB,MAC5BA,IAAIH,MAAM,CAAC,GAAGC,WAAW,KAAKE,IAAIC,MAAM,CAAC,GAAGC,WAAW;AAE3D;AAKO,SAASjD,oBAAoBkD,IAAY;IAC9C,MAAMC,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAMC,QAAQF,KAAKG,UAAU,CAAC,KAAKF,OAAOrB,MAAM;IAChD,OAAOqB,MAAM,CAACC,MAAM;AACtB;AAKO,SAAS9C,aAAagD,KAAa;IACxC,MAAMC,aAAa;IACnB,OAAOA,WAAWC,IAAI,CAACF;AACzB;AAKO,SAAS/C,mBAAmBmB,KAAa;IAC9C,MAAMC,UAAUD,MAAME,OAAO,CAAC,OAAO;IAErC,iDAAiD;IACjD,IAAID,QAAQE,UAAU,CAAC,UAAUF,QAAQG,MAAM,KAAK,IAAI;QACtD,OAAO;IACT;IAEA,8BAA8B;IAC9B,IAAIH,QAAQE,UAAU,CAAC,QAAQF,QAAQG,MAAM,KAAK,IAAI;QACpD,OAAO;IACT;IAEA,OAAO;AACT;AAKO,SAASpC,SACd+D,IAAO,EACPC,IAAY;IAEZ,IAAIC;IAEJ,OAAO,CAAC,GAAGC;QACTC,aAAaF;QACbA,UAAUG,WAAW,IAAML,QAAQG,OAAOF;IAC5C;AACF;AAKO,SAAShD,aAAaqD,IAAY,EAAEC,SAAiB;IAC1D,IAAID,KAAKjC,MAAM,IAAIkC,WAAW;QAC5B,OAAOD;IACT;IAEA,OAAOA,KAAKhC,KAAK,CAAC,GAAGiC,aAAa;AACpC;AAKO,SAAS5D,eAAe6D,MAAc;IAC3C,MAAMC,eAAuC;QAC3CC,QAAQ;QACRC,UAAU;QACVC,SAAS;QACTC,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,aAAa;QACbC,aAAa;IACf;IAEA,OAAOR,YAAY,CAACD,OAAO,IAAI;AACjC;AAKO,SAASnE,eAAe6E,KAAa;IAC1C,IAAIA,UAAU,GAAG,OAAO;IAExB,MAAMC,IAAI;IACV,MAAMC,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAMC,IAAIzC,KAAK0C,KAAK,CAAC1C,KAAK2C,GAAG,CAACL,SAAStC,KAAK2C,GAAG,CAACJ;IAEhD,OAAOK,WAAW,AAACN,CAAAA,QAAQtC,KAAK6C,GAAG,CAACN,GAAGE,EAAC,EAAGK,OAAO,CAAC,MAAM,MAAMN,KAAK,CAACC,EAAE;AACzE;AAKO,SAAS7E,mBAAmBmF,cAAsB,EAAEC,QAAgB;IACzE,MAAMC,OAAO,IAAIC,OAAOC,WAAW,GAAGC,QAAQ,GAAG1D,KAAK,CAAC,CAAC;IACxD,MAAM2D,iBAAiBL,SAASI,QAAQ,GAAGE,QAAQ,CAAC,GAAG;IACvD,OAAO,GAAGP,iBAAiBE,OAAOI,gBAAgB;AACpD;AAKO,SAASpG,aAAasG,WAAmB;IAC9C,MAAMC,QAAQ,IAAIN;IAClB,MAAMO,YAAY9E,IAAAA,iBAAQ,EAAC4E;IAE3B,IAAI,CAAC3E,IAAAA,gBAAO,EAAC6E,YAAY;QACvB,OAAO;IACT;IAEA,IAAIC,MAAMF,MAAML,WAAW,KAAKM,UAAUN,WAAW;IACrD,MAAMQ,YAAYH,MAAMI,QAAQ,KAAKH,UAAUG,QAAQ;IAEvD,IAAID,YAAY,KAAMA,cAAc,KAAKH,MAAMK,OAAO,KAAKJ,UAAUI,OAAO,IAAK;QAC/EH;IACF;IAEA,OAAOA;AACT;AAKO,SAASvF,UAAUO,IAAY;IACpC,MAAMoF,MAAMnF,IAAAA,iBAAQ,EAACD,MAAMqF,MAAM;IACjC,OAAOD,QAAQ,KAAKA,QAAQ,GAAG,2BAA2B;AAC5D;AAKO,SAASjG,gBAAgB8B,SAAiB,EAAEC,OAAe;IAChE,MAAMC,QAAQlB,IAAAA,iBAAQ,EAACgB;IACvB,MAAMG,MAAMnB,IAAAA,iBAAQ,EAACiB;IAErB,IAAI,CAAChB,IAAAA,gBAAO,EAACiB,UAAU,CAACjB,IAAAA,gBAAO,EAACkB,MAAM;QACpC,OAAO;IACT;IAEA,IAAIkE,QAAQ;IACZ,MAAMC,UAAU,IAAIf,KAAKrD;IAEzB,MAAOoE,WAAWnE,IAAK;QACrB,MAAMoE,YAAYD,QAAQF,MAAM;QAChC,IAAIG,cAAc,KAAKA,cAAc,GAAG;YACtCF;QACF;QACAC,QAAQE,OAAO,CAACF,QAAQJ,OAAO,KAAK;IACtC;IAEA,OAAOG;AACT;AAKO,SAAS5F,MAAMgG,EAAU;IAC9B,OAAO,IAAIC,QAAQC,CAAAA,UAAW7C,WAAW6C,SAASF;AACpD;AAKO,SAAS9G,UAAaiH,GAAM;IACjC,OAAOC,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAACH;AACnC;AAKO,SAASvG,QAAQuG,GAAQ;IAC9B,IAAIA,OAAO,MAAM,OAAO;IACxB,IAAII,MAAMC,OAAO,CAACL,QAAQ,OAAOA,QAAQ,UAAU,OAAOA,IAAI9E,MAAM,KAAK;IACzE,IAAI,OAAO8E,QAAQ,UAAU,OAAOM,OAAOC,IAAI,CAACP,KAAK9E,MAAM,KAAK;IAChE,OAAO;AACT"}