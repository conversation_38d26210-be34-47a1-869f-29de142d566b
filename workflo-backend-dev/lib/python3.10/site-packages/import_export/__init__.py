try:
    # import from _version.py generated by setuptools_scm during release
    from ._version import version as __version__
except ImportError:
    # return a valid version if running in a context where no
    # version available (e.g. local build)
    from os import path as _path

    from setuptools_scm import get_version as _gv

    __version__ = _gv(_path.join(_path.dirname(__file__), _path.pardir))
