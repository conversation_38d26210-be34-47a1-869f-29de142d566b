'use client';

import React from 'react';
import Link from 'next/link';
import { useAuth } from '@/providers/AuthProvider';
import {
  User,
  Briefcase,
  DollarSign,
  Calendar,
  FileText,
  Star,
  Clock,
  ChevronRight,
  Edit,
  Eye,
  Download,
  Plus,
  Activity,
  TrendingUp
} from 'lucide-react';

interface InfoCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  href: string;
  color: string;
  stats?: {
    label: string;
    value: string;
  };
  actions?: {
    label: string;
    href: string;
    icon: React.ComponentType<any>;
  }[];
}

const InfoCard: React.FC<InfoCardProps> = ({ title, description, icon: Icon, href, color, stats, actions }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
      <Link href={href} className="block p-6">
        <div className="flex items-start space-x-4">
          <div className={`p-3 rounded-lg ${color}`}>
            <Icon className="h-6 w-6 text-white" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
            <p className="text-gray-600 text-sm mb-3">{description}</p>
            {stats && (
              <div className="flex items-center text-sm text-gray-500 mb-3">
                <TrendingUp className="h-4 w-4 mr-1" />
                <span>{stats.label}: </span>
                <span className="font-medium text-gray-900 ml-1">{stats.value}</span>
              </div>
            )}
          </div>
          <ChevronRight className="h-5 w-5 text-gray-400 flex-shrink-0" />
        </div>
      </Link>
      
      {actions && actions.length > 0 && (
        <div className="border-t border-gray-200 px-6 py-3">
          <div className="flex flex-wrap gap-2">
            {actions.map((action, index) => {
              const ActionIcon = action.icon;
              return (
                <Link
                  key={index}
                  href={action.href}
                  className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  <ActionIcon className="h-3 w-3 mr-1" />
                  {action.label}
                </Link>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

const SupervisorInfoHub: React.FC = () => {
  const { user } = useAuth();

  const infoSections = [
    {
      title: 'Profile',
      description: 'Manage your personal information, contact details, and account preferences.',
      icon: User,
      href: '/supervisor/info/profile',
      color: 'bg-blue-500',
      stats: {
        label: 'Last updated',
        value: '2 days ago'
      },
      actions: [
        { label: 'Edit Profile', href: '/supervisor/info/profile?action=edit', icon: Edit },
        { label: 'View Details', href: '/supervisor/info/profile', icon: Eye }
      ]
    },
    {
      title: 'Employment',
      description: 'View your job details, employment history, and organizational information.',
      icon: Briefcase,
      href: '/supervisor/info/employment',
      color: 'bg-green-500',
      stats: {
        label: 'Years of service',
        value: '3.5 years'
      },
      actions: [
        { label: 'View History', href: '/supervisor/info/employment', icon: Eye },
        { label: 'Download Certificate', href: '/supervisor/info/employment?action=download', icon: Download }
      ]
    },
    {
      title: 'Salary',
      description: 'Access your salary information, payslips, tax details, and benefits.',
      icon: DollarSign,
      href: '/supervisor/info/salary',
      color: 'bg-purple-500',
      stats: {
        label: 'Current salary',
        value: 'KSH 120,000'
      },
      actions: [
        { label: 'View Payslip', href: '/supervisor/info/salary?view=payslip', icon: Eye },
        { label: 'Download Statement', href: '/supervisor/info/salary?action=download', icon: Download }
      ]
    },
    {
      title: 'Leave Management',
      description: 'Apply for leave, check your leave balance, and view leave history.',
      icon: Calendar,
      href: '/supervisor/info/leave-management',
      color: 'bg-orange-500',
      stats: {
        label: 'Available leave',
        value: '15 days'
      },
      actions: [
        { label: 'Apply Leave', href: '/supervisor/info/leave-management?action=apply', icon: Plus },
        { label: 'View Balance', href: '/supervisor/info/leave-management', icon: Eye }
      ]
    },
    {
      title: 'Documents',
      description: 'Access your personal and employment documents, certificates, and files.',
      icon: FileText,
      href: '/supervisor/info/documents',
      color: 'bg-indigo-500',
      stats: {
        label: 'Total documents',
        value: '12 files'
      },
      actions: [
        { label: 'Upload Document', href: '/supervisor/info/documents?action=upload', icon: Plus },
        { label: 'View All', href: '/supervisor/info/documents', icon: Eye }
      ]
    },
    {
      title: 'Performance',
      description: 'View your performance reviews, goals, achievements, and feedback.',
      icon: Star,
      href: '/supervisor/info/performance',
      color: 'bg-yellow-500',
      stats: {
        label: 'Latest rating',
        value: '4.5/5.0'
      },
      actions: [
        { label: 'View Reviews', href: '/supervisor/info/performance', icon: Eye },
        { label: 'Set Goals', href: '/supervisor/info/performance?action=goals', icon: Plus }
      ]
    },
    {
      title: 'Time Off',
      description: 'Manage your time off requests, comp time, and flexible work arrangements.',
      icon: Clock,
      href: '/supervisor/info/timeoff',
      color: 'bg-red-500',
      stats: {
        label: 'Pending requests',
        value: '1 request'
      },
      actions: [
        { label: 'Request Time Off', href: '/supervisor/info/timeoff?action=request', icon: Plus },
        { label: 'View History', href: '/supervisor/info/timeoff', icon: Eye }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Personal Information Hub</h1>
            <p className="text-blue-100 mt-2">
              Manage your personal information, employment details, and work-related data
            </p>
          </div>
          <div className="hidden sm:block">
            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <User className="h-8 w-8 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Activity className="h-5 w-5 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Profile Completion</p>
              <p className="text-lg font-semibold text-gray-900">95%</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <FileText className="h-5 w-5 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Documents</p>
              <p className="text-lg font-semibold text-gray-900">12 files</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Calendar className="h-5 w-5 text-orange-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Leave Balance</p>
              <p className="text-lg font-semibold text-gray-900">15 days</p>
            </div>
          </div>
        </div>
      </div>

      {/* Info Sections Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {infoSections.map((section, index) => (
          <InfoCard
            key={index}
            title={section.title}
            description={section.description}
            icon={section.icon}
            href={section.href}
            color={section.color}
            stats={section.stats}
            actions={section.actions}
          />
        ))}
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Activity className="h-5 w-5 mr-2 text-blue-500" />
            Recent Activity
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between py-2">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-900">Profile updated successfully</span>
              </div>
              <span className="text-xs text-gray-500">2 days ago</span>
            </div>
            <div className="flex items-center justify-between py-2">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-gray-900">New document uploaded</span>
              </div>
              <span className="text-xs text-gray-500">1 week ago</span>
            </div>
            <div className="flex items-center justify-between py-2">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span className="text-sm text-gray-900">Leave request approved</span>
              </div>
              <span className="text-xs text-gray-500">2 weeks ago</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupervisorInfoHub;
