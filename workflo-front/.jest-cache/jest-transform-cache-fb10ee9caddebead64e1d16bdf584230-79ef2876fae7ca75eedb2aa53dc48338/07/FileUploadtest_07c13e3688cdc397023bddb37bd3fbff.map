{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/__tests__/components/ui/FileUpload.test.tsx"], "sourcesContent": ["import React from 'react';\nimport { screen, waitFor, fireEvent } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nimport { render, createMockFile } from '@/__tests__/utils/test-utils';\nimport FileUpload from '@/components/ui/FileUpload';\nimport fileUploadService from '@/lib/fileUpload';\n\n// Mock the file upload service\njest.mock('@/lib/fileUpload', () => ({\n  __esModule: true,\n  default: {\n    uploadFile: jest.fn(),\n    formatFileSize: jest.fn((size: number) => `${size} bytes`),\n    isImage: jest.fn((file: File) => file.type.startsWith('image/')),\n    isPDF: jest.fn((file: File) => file.type === 'application/pdf'),\n    createPreviewUrl: jest.fn(() => 'mock-preview-url'),\n    revokePreviewUrl: jest.fn(),\n  },\n}));\n\nconst mockFileUploadService = fileUploadService as jest.Mocked<typeof fileUploadService>;\n\ndescribe('FileUpload', () => {\n  const defaultProps = {\n    endpoint: '/api/upload',\n    onUpload: jest.fn(),\n  };\n\n  beforeEach(() => {\n    jest.clearAllMocks();\n    mockFileUploadService.uploadFile.mockResolvedValue({\n      success: true,\n      url: 'https://example.com/file.pdf',\n      filename: 'file.pdf',\n      size: 1024,\n    });\n  });\n\n  describe('rendering', () => {\n    it('renders upload area with default content', () => {\n      render(<FileUpload {...defaultProps} />);\n\n      expect(screen.getByText('Click to upload or drag and drop')).toBeInTheDocument();\n      expect(screen.getByText('All file types accepted')).toBeInTheDocument();\n    });\n\n    it('renders custom children when provided', () => {\n      render(\n        <FileUpload {...defaultProps}>\n          <div>Custom Upload Area</div>\n        </FileUpload>\n      );\n\n      expect(screen.getByText('Custom Upload Area')).toBeInTheDocument();\n      expect(screen.queryByText('Click to upload or drag and drop')).not.toBeInTheDocument();\n    });\n\n    it('shows accepted formats when accept prop is provided', () => {\n      render(<FileUpload {...defaultProps} accept=\".pdf,.doc\" />);\n\n      expect(screen.getByText('Accepted formats: .pdf,.doc')).toBeInTheDocument();\n    });\n\n    it('shows max size when maxSize prop is provided', () => {\n      render(<FileUpload {...defaultProps} maxSize={5 * 1024 * 1024} />);\n\n      expect(screen.getByText('Max size: 5242880 bytes')).toBeInTheDocument();\n    });\n  });\n\n  describe('file selection', () => {\n    it('handles file selection via input', async () => {\n      const user = userEvent.setup();\n      const file = createMockFile('test.pdf');\n\n      render(<FileUpload {...defaultProps} />);\n\n      const input = screen.getByRole('button', { name: /click to upload/i });\n      await user.click(input);\n\n      // Simulate file selection\n      const fileInput = document.querySelector('input[type=\"file\"]') as HTMLInputElement;\n      Object.defineProperty(fileInput, 'files', {\n        value: [file],\n        writable: false,\n      });\n      fireEvent.change(fileInput);\n\n      await waitFor(() => {\n        expect(screen.getByText('Selected Files (1)')).toBeInTheDocument();\n        expect(screen.getByText('test.pdf')).toBeInTheDocument();\n      });\n    });\n\n    it('handles multiple file selection when multiple is true', async () => {\n      const user = userEvent.setup();\n      const files = [createMockFile('test1.pdf'), createMockFile('test2.pdf')];\n\n      render(<FileUpload {...defaultProps} multiple />);\n\n      const input = screen.getByRole('button', { name: /click to upload/i });\n      await user.click(input);\n\n      const fileInput = document.querySelector('input[type=\"file\"]') as HTMLInputElement;\n      Object.defineProperty(fileInput, 'files', {\n        value: files,\n        writable: false,\n      });\n      fireEvent.change(fileInput);\n\n      await waitFor(() => {\n        expect(screen.getByText('Selected Files (2)')).toBeInTheDocument();\n        expect(screen.getByText('test1.pdf')).toBeInTheDocument();\n        expect(screen.getByText('test2.pdf')).toBeInTheDocument();\n      });\n    });\n\n    it('limits files to maxFiles when specified', async () => {\n      const user = userEvent.setup();\n      const files = [\n        createMockFile('test1.pdf'),\n        createMockFile('test2.pdf'),\n        createMockFile('test3.pdf'),\n      ];\n\n      render(<FileUpload {...defaultProps} multiple maxFiles={2} />);\n\n      const input = screen.getByRole('button', { name: /click to upload/i });\n      await user.click(input);\n\n      const fileInput = document.querySelector('input[type=\"file\"]') as HTMLInputElement;\n      Object.defineProperty(fileInput, 'files', {\n        value: files,\n        writable: false,\n      });\n      fireEvent.change(fileInput);\n\n      await waitFor(() => {\n        expect(screen.getByText('Selected Files (2)')).toBeInTheDocument();\n        expect(screen.getByText('test1.pdf')).toBeInTheDocument();\n        expect(screen.getByText('test2.pdf')).toBeInTheDocument();\n        expect(screen.queryByText('test3.pdf')).not.toBeInTheDocument();\n      });\n    });\n  });\n\n  describe('drag and drop', () => {\n    it('handles drag over events', () => {\n      render(<FileUpload {...defaultProps} />);\n\n      const dropZone = screen.getByRole('button', { name: /click to upload/i });\n      \n      fireEvent.dragOver(dropZone);\n      \n      // Should add drag over styling (tested via class changes)\n      expect(dropZone).toHaveClass('border-orange-500', 'bg-orange-50');\n    });\n\n    it('handles drag leave events', () => {\n      render(<FileUpload {...defaultProps} />);\n\n      const dropZone = screen.getByRole('button', { name: /click to upload/i });\n      \n      fireEvent.dragOver(dropZone);\n      fireEvent.dragLeave(dropZone);\n      \n      // Should remove drag over styling\n      expect(dropZone).not.toHaveClass('border-orange-500', 'bg-orange-50');\n    });\n\n    it('handles file drop', async () => {\n      const file = createMockFile('dropped.pdf');\n      \n      render(<FileUpload {...defaultProps} />);\n\n      const dropZone = screen.getByRole('button', { name: /click to upload/i });\n      \n      fireEvent.drop(dropZone, {\n        dataTransfer: {\n          files: [file],\n        },\n      });\n\n      await waitFor(() => {\n        expect(screen.getByText('Selected Files (1)')).toBeInTheDocument();\n        expect(screen.getByText('dropped.pdf')).toBeInTheDocument();\n      });\n    });\n  });\n\n  describe('file removal', () => {\n    it('allows removing selected files', async () => {\n      const user = userEvent.setup();\n      const file = createMockFile('test.pdf');\n\n      render(<FileUpload {...defaultProps} />);\n\n      // Add file\n      const input = screen.getByRole('button', { name: /click to upload/i });\n      await user.click(input);\n\n      const fileInput = document.querySelector('input[type=\"file\"]') as HTMLInputElement;\n      Object.defineProperty(fileInput, 'files', {\n        value: [file],\n        writable: false,\n      });\n      fireEvent.change(fileInput);\n\n      await waitFor(() => {\n        expect(screen.getByText('test.pdf')).toBeInTheDocument();\n      });\n\n      // Remove file\n      const removeButton = screen.getByRole('button', { name: '' }); // X button\n      await user.click(removeButton);\n\n      await waitFor(() => {\n        expect(screen.queryByText('Selected Files (1)')).not.toBeInTheDocument();\n        expect(screen.queryByText('test.pdf')).not.toBeInTheDocument();\n      });\n    });\n  });\n\n  describe('file upload', () => {\n    it('uploads files when upload button is clicked', async () => {\n      const user = userEvent.setup();\n      const file = createMockFile('test.pdf');\n      const onUpload = jest.fn();\n\n      render(<FileUpload {...defaultProps} onUpload={onUpload} />);\n\n      // Add file\n      const input = screen.getByRole('button', { name: /click to upload/i });\n      await user.click(input);\n\n      const fileInput = document.querySelector('input[type=\"file\"]') as HTMLInputElement;\n      Object.defineProperty(fileInput, 'files', {\n        value: [file],\n        writable: false,\n      });\n      fireEvent.change(fileInput);\n\n      await waitFor(() => {\n        expect(screen.getByText('Upload All')).toBeInTheDocument();\n      });\n\n      // Upload files\n      const uploadButton = screen.getByText('Upload All');\n      await user.click(uploadButton);\n\n      await waitFor(() => {\n        expect(mockFileUploadService.uploadFile).toHaveBeenCalledWith(\n          file,\n          '/api/upload',\n          expect.objectContaining({\n            folder: undefined,\n            maxSize: undefined,\n            onProgress: expect.any(Function),\n          })\n        );\n        expect(onUpload).toHaveBeenCalledWith([\n          {\n            success: true,\n            url: 'https://example.com/file.pdf',\n            filename: 'file.pdf',\n            size: 1024,\n          },\n        ]);\n      });\n    });\n\n    it('shows upload progress during upload', async () => {\n      const user = userEvent.setup();\n      const file = createMockFile('test.pdf');\n      let progressCallback: ((progress: any) => void) | undefined;\n\n      mockFileUploadService.uploadFile.mockImplementation((file, endpoint, options) => {\n        progressCallback = options?.onProgress;\n        return new Promise((resolve) => {\n          setTimeout(() => {\n            resolve({\n              success: true,\n              url: 'https://example.com/file.pdf',\n              filename: 'file.pdf',\n              size: 1024,\n            });\n          }, 100);\n        });\n      });\n\n      render(<FileUpload {...defaultProps} />);\n\n      // Add file\n      const input = screen.getByRole('button', { name: /click to upload/i });\n      await user.click(input);\n\n      const fileInput = document.querySelector('input[type=\"file\"]') as HTMLInputElement;\n      Object.defineProperty(fileInput, 'files', {\n        value: [file],\n        writable: false,\n      });\n      fireEvent.change(fileInput);\n\n      // Upload files\n      const uploadButton = screen.getByText('Upload All');\n      await user.click(uploadButton);\n\n      // Simulate progress\n      if (progressCallback) {\n        progressCallback({ loaded: 50, total: 100, percentage: 50 });\n      }\n\n      expect(screen.getByText('50%')).toBeInTheDocument();\n\n      await waitFor(() => {\n        expect(screen.queryByText('50%')).not.toBeInTheDocument();\n      });\n    });\n\n    it('handles upload errors', async () => {\n      const user = userEvent.setup();\n      const file = createMockFile('test.pdf');\n\n      mockFileUploadService.uploadFile.mockResolvedValue({\n        success: false,\n        error: 'Upload failed',\n      });\n\n      render(<FileUpload {...defaultProps} />);\n\n      // Add file\n      const input = screen.getByRole('button', { name: /click to upload/i });\n      await user.click(input);\n\n      const fileInput = document.querySelector('input[type=\"file\"]') as HTMLInputElement;\n      Object.defineProperty(fileInput, 'files', {\n        value: [file],\n        writable: false,\n      });\n      fireEvent.change(fileInput);\n\n      // Upload files\n      const uploadButton = screen.getByText('Upload All');\n      await user.click(uploadButton);\n\n      await waitFor(() => {\n        expect(screen.getByText('Upload failed')).toBeInTheDocument();\n      });\n    });\n  });\n\n  describe('disabled state', () => {\n    it('prevents interaction when disabled', async () => {\n      const user = userEvent.setup();\n\n      render(<FileUpload {...defaultProps} disabled />);\n\n      const dropZone = screen.getByRole('button', { name: /click to upload/i });\n      \n      expect(dropZone).toHaveClass('opacity-50', 'cursor-not-allowed');\n\n      // Should not respond to drag events\n      fireEvent.dragOver(dropZone);\n      expect(dropZone).not.toHaveClass('border-orange-500');\n\n      // Should not open file dialog\n      await user.click(dropZone);\n      // File input should be disabled\n      const fileInput = document.querySelector('input[type=\"file\"]') as HTMLInputElement;\n      expect(fileInput).toBeDisabled();\n    });\n  });\n});\n"], "names": ["jest", "mock", "__esModule", "default", "uploadFile", "fn", "formatFileSize", "size", "isImage", "file", "type", "startsWith", "isPDF", "createPreviewUrl", "revokePreviewUrl", "mockFileUploadService", "fileUploadService", "describe", "defaultProps", "endpoint", "onUpload", "beforeEach", "clearAllMocks", "mockResolvedValue", "success", "url", "filename", "it", "render", "FileUpload", "expect", "screen", "getByText", "toBeInTheDocument", "div", "queryByText", "not", "accept", "maxSize", "user", "userEvent", "setup", "createMockFile", "input", "getByRole", "name", "click", "fileInput", "document", "querySelector", "Object", "defineProperty", "value", "writable", "fireEvent", "change", "waitFor", "files", "multiple", "maxFiles", "dropZone", "dragOver", "toHaveClass", "dragLeave", "drop", "dataTransfer", "removeButton", "uploadButton", "toHaveBeenCalledWith", "objectContaining", "folder", "undefined", "onProgress", "any", "Function", "progressCallback", "mockImplementation", "options", "Promise", "resolve", "setTimeout", "loaded", "total", "percentage", "error", "disabled", "toBeDisabled"], "mappings": ";AAOA,+BAA+B;AAC/BA,KAAKC,IAAI,CAAC,oBAAoB,IAAO,CAAA;QACnCC,YAAY;QACZC,SAAS;YACPC,YAAYJ,KAAKK,EAAE;YACnBC,gBAAgBN,KAAKK,EAAE,CAAC,CAACE,OAAiB,GAAGA,KAAK,MAAM,CAAC;YACzDC,SAASR,KAAKK,EAAE,CAAC,CAACI,OAAeA,KAAKC,IAAI,CAACC,UAAU,CAAC;YACtDC,OAAOZ,KAAKK,EAAE,CAAC,CAACI,OAAeA,KAAKC,IAAI,KAAK;YAC7CG,kBAAkBb,KAAKK,EAAE,CAAC,IAAM;YAChCS,kBAAkBd,KAAKK,EAAE;QAC3B;IACF,CAAA;;;;;8DAlBkB;wBACyB;kEACrB;2BACiB;mEAChB;mEACO;;;;;;AAe9B,MAAMU,wBAAwBC,mBAAiB;AAE/CC,SAAS,cAAc;IACrB,MAAMC,eAAe;QACnBC,UAAU;QACVC,UAAUpB,KAAKK,EAAE;IACnB;IAEAgB,WAAW;QACTrB,KAAKsB,aAAa;QAClBP,sBAAsBX,UAAU,CAACmB,iBAAiB,CAAC;YACjDC,SAAS;YACTC,KAAK;YACLC,UAAU;YACVnB,MAAM;QACR;IACF;IAEAU,SAAS,aAAa;QACpBU,GAAG,4CAA4C;YAC7CC,IAAAA,iBAAM,gBAAC,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;;YAEnCY,OAAOC,cAAM,CAACC,SAAS,CAAC,qCAAqCC,iBAAiB;YAC9EH,OAAOC,cAAM,CAACC,SAAS,CAAC,4BAA4BC,iBAAiB;QACvE;QAEAN,GAAG,yCAAyC;YAC1CC,IAAAA,iBAAM,gBACJ,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;0BAC1B,cAAA,qBAACgB;8BAAI;;;YAITJ,OAAOC,cAAM,CAACC,SAAS,CAAC,uBAAuBC,iBAAiB;YAChEH,OAAOC,cAAM,CAACI,WAAW,CAAC,qCAAqCC,GAAG,CAACH,iBAAiB;QACtF;QAEAN,GAAG,uDAAuD;YACxDC,IAAAA,iBAAM,gBAAC,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;gBAAEmB,QAAO;;YAE5CP,OAAOC,cAAM,CAACC,SAAS,CAAC,gCAAgCC,iBAAiB;QAC3E;QAEAN,GAAG,gDAAgD;YACjDC,IAAAA,iBAAM,gBAAC,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;gBAAEoB,SAAS,IAAI,OAAO;;YAEzDR,OAAOC,cAAM,CAACC,SAAS,CAAC,4BAA4BC,iBAAiB;QACvE;IACF;IAEAhB,SAAS,kBAAkB;QACzBU,GAAG,oCAAoC;YACrC,MAAMY,OAAOC,kBAAS,CAACC,KAAK;YAC5B,MAAMhC,OAAOiC,IAAAA,yBAAc,EAAC;YAE5Bd,IAAAA,iBAAM,gBAAC,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;;YAEnC,MAAMyB,QAAQZ,cAAM,CAACa,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAmB;YACpE,MAAMN,KAAKO,KAAK,CAACH;YAEjB,0BAA0B;YAC1B,MAAMI,YAAYC,SAASC,aAAa,CAAC;YACzCC,OAAOC,cAAc,CAACJ,WAAW,SAAS;gBACxCK,OAAO;oBAAC3C;iBAAK;gBACb4C,UAAU;YACZ;YACAC,iBAAS,CAACC,MAAM,CAACR;YAEjB,MAAMS,IAAAA,eAAO,EAAC;gBACZ1B,OAAOC,cAAM,CAACC,SAAS,CAAC,uBAAuBC,iBAAiB;gBAChEH,OAAOC,cAAM,CAACC,SAAS,CAAC,aAAaC,iBAAiB;YACxD;QACF;QAEAN,GAAG,yDAAyD;YAC1D,MAAMY,OAAOC,kBAAS,CAACC,KAAK;YAC5B,MAAMgB,QAAQ;gBAACf,IAAAA,yBAAc,EAAC;gBAAcA,IAAAA,yBAAc,EAAC;aAAa;YAExEd,IAAAA,iBAAM,gBAAC,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;gBAAEwC,QAAQ;;YAE7C,MAAMf,QAAQZ,cAAM,CAACa,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAmB;YACpE,MAAMN,KAAKO,KAAK,CAACH;YAEjB,MAAMI,YAAYC,SAASC,aAAa,CAAC;YACzCC,OAAOC,cAAc,CAACJ,WAAW,SAAS;gBACxCK,OAAOK;gBACPJ,UAAU;YACZ;YACAC,iBAAS,CAACC,MAAM,CAACR;YAEjB,MAAMS,IAAAA,eAAO,EAAC;gBACZ1B,OAAOC,cAAM,CAACC,SAAS,CAAC,uBAAuBC,iBAAiB;gBAChEH,OAAOC,cAAM,CAACC,SAAS,CAAC,cAAcC,iBAAiB;gBACvDH,OAAOC,cAAM,CAACC,SAAS,CAAC,cAAcC,iBAAiB;YACzD;QACF;QAEAN,GAAG,2CAA2C;YAC5C,MAAMY,OAAOC,kBAAS,CAACC,KAAK;YAC5B,MAAMgB,QAAQ;gBACZf,IAAAA,yBAAc,EAAC;gBACfA,IAAAA,yBAAc,EAAC;gBACfA,IAAAA,yBAAc,EAAC;aAChB;YAEDd,IAAAA,iBAAM,gBAAC,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;gBAAEwC,QAAQ;gBAACC,UAAU;;YAExD,MAAMhB,QAAQZ,cAAM,CAACa,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAmB;YACpE,MAAMN,KAAKO,KAAK,CAACH;YAEjB,MAAMI,YAAYC,SAASC,aAAa,CAAC;YACzCC,OAAOC,cAAc,CAACJ,WAAW,SAAS;gBACxCK,OAAOK;gBACPJ,UAAU;YACZ;YACAC,iBAAS,CAACC,MAAM,CAACR;YAEjB,MAAMS,IAAAA,eAAO,EAAC;gBACZ1B,OAAOC,cAAM,CAACC,SAAS,CAAC,uBAAuBC,iBAAiB;gBAChEH,OAAOC,cAAM,CAACC,SAAS,CAAC,cAAcC,iBAAiB;gBACvDH,OAAOC,cAAM,CAACC,SAAS,CAAC,cAAcC,iBAAiB;gBACvDH,OAAOC,cAAM,CAACI,WAAW,CAAC,cAAcC,GAAG,CAACH,iBAAiB;YAC/D;QACF;IACF;IAEAhB,SAAS,iBAAiB;QACxBU,GAAG,4BAA4B;YAC7BC,IAAAA,iBAAM,gBAAC,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;;YAEnC,MAAM0C,WAAW7B,cAAM,CAACa,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAmB;YAEvES,iBAAS,CAACO,QAAQ,CAACD;YAEnB,0DAA0D;YAC1D9B,OAAO8B,UAAUE,WAAW,CAAC,qBAAqB;QACpD;QAEAnC,GAAG,6BAA6B;YAC9BC,IAAAA,iBAAM,gBAAC,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;;YAEnC,MAAM0C,WAAW7B,cAAM,CAACa,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAmB;YAEvES,iBAAS,CAACO,QAAQ,CAACD;YACnBN,iBAAS,CAACS,SAAS,CAACH;YAEpB,kCAAkC;YAClC9B,OAAO8B,UAAUxB,GAAG,CAAC0B,WAAW,CAAC,qBAAqB;QACxD;QAEAnC,GAAG,qBAAqB;YACtB,MAAMlB,OAAOiC,IAAAA,yBAAc,EAAC;YAE5Bd,IAAAA,iBAAM,gBAAC,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;;YAEnC,MAAM0C,WAAW7B,cAAM,CAACa,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAmB;YAEvES,iBAAS,CAACU,IAAI,CAACJ,UAAU;gBACvBK,cAAc;oBACZR,OAAO;wBAAChD;qBAAK;gBACf;YACF;YAEA,MAAM+C,IAAAA,eAAO,EAAC;gBACZ1B,OAAOC,cAAM,CAACC,SAAS,CAAC,uBAAuBC,iBAAiB;gBAChEH,OAAOC,cAAM,CAACC,SAAS,CAAC,gBAAgBC,iBAAiB;YAC3D;QACF;IACF;IAEAhB,SAAS,gBAAgB;QACvBU,GAAG,kCAAkC;YACnC,MAAMY,OAAOC,kBAAS,CAACC,KAAK;YAC5B,MAAMhC,OAAOiC,IAAAA,yBAAc,EAAC;YAE5Bd,IAAAA,iBAAM,gBAAC,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;;YAEnC,WAAW;YACX,MAAMyB,QAAQZ,cAAM,CAACa,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAmB;YACpE,MAAMN,KAAKO,KAAK,CAACH;YAEjB,MAAMI,YAAYC,SAASC,aAAa,CAAC;YACzCC,OAAOC,cAAc,CAACJ,WAAW,SAAS;gBACxCK,OAAO;oBAAC3C;iBAAK;gBACb4C,UAAU;YACZ;YACAC,iBAAS,CAACC,MAAM,CAACR;YAEjB,MAAMS,IAAAA,eAAO,EAAC;gBACZ1B,OAAOC,cAAM,CAACC,SAAS,CAAC,aAAaC,iBAAiB;YACxD;YAEA,cAAc;YACd,MAAMiC,eAAenC,cAAM,CAACa,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAG,IAAI,WAAW;YAC1E,MAAMN,KAAKO,KAAK,CAACoB;YAEjB,MAAMV,IAAAA,eAAO,EAAC;gBACZ1B,OAAOC,cAAM,CAACI,WAAW,CAAC,uBAAuBC,GAAG,CAACH,iBAAiB;gBACtEH,OAAOC,cAAM,CAACI,WAAW,CAAC,aAAaC,GAAG,CAACH,iBAAiB;YAC9D;QACF;IACF;IAEAhB,SAAS,eAAe;QACtBU,GAAG,+CAA+C;YAChD,MAAMY,OAAOC,kBAAS,CAACC,KAAK;YAC5B,MAAMhC,OAAOiC,IAAAA,yBAAc,EAAC;YAC5B,MAAMtB,WAAWpB,KAAKK,EAAE;YAExBuB,IAAAA,iBAAM,gBAAC,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;gBAAEE,UAAUA;;YAE/C,WAAW;YACX,MAAMuB,QAAQZ,cAAM,CAACa,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAmB;YACpE,MAAMN,KAAKO,KAAK,CAACH;YAEjB,MAAMI,YAAYC,SAASC,aAAa,CAAC;YACzCC,OAAOC,cAAc,CAACJ,WAAW,SAAS;gBACxCK,OAAO;oBAAC3C;iBAAK;gBACb4C,UAAU;YACZ;YACAC,iBAAS,CAACC,MAAM,CAACR;YAEjB,MAAMS,IAAAA,eAAO,EAAC;gBACZ1B,OAAOC,cAAM,CAACC,SAAS,CAAC,eAAeC,iBAAiB;YAC1D;YAEA,eAAe;YACf,MAAMkC,eAAepC,cAAM,CAACC,SAAS,CAAC;YACtC,MAAMO,KAAKO,KAAK,CAACqB;YAEjB,MAAMX,IAAAA,eAAO,EAAC;gBACZ1B,OAAOf,sBAAsBX,UAAU,EAAEgE,oBAAoB,CAC3D3D,MACA,eACAqB,OAAOuC,gBAAgB,CAAC;oBACtBC,QAAQC;oBACRjC,SAASiC;oBACTC,YAAY1C,OAAO2C,GAAG,CAACC;gBACzB;gBAEF5C,OAAOV,UAAUgD,oBAAoB,CAAC;oBACpC;wBACE5C,SAAS;wBACTC,KAAK;wBACLC,UAAU;wBACVnB,MAAM;oBACR;iBACD;YACH;QACF;QAEAoB,GAAG,uCAAuC;YACxC,MAAMY,OAAOC,kBAAS,CAACC,KAAK;YAC5B,MAAMhC,OAAOiC,IAAAA,yBAAc,EAAC;YAC5B,IAAIiC;YAEJ5D,sBAAsBX,UAAU,CAACwE,kBAAkB,CAAC,CAACnE,MAAMU,UAAU0D;gBACnEF,mBAAmBE,SAASL;gBAC5B,OAAO,IAAIM,QAAQ,CAACC;oBAClBC,WAAW;wBACTD,QAAQ;4BACNvD,SAAS;4BACTC,KAAK;4BACLC,UAAU;4BACVnB,MAAM;wBACR;oBACF,GAAG;gBACL;YACF;YAEAqB,IAAAA,iBAAM,gBAAC,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;;YAEnC,WAAW;YACX,MAAMyB,QAAQZ,cAAM,CAACa,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAmB;YACpE,MAAMN,KAAKO,KAAK,CAACH;YAEjB,MAAMI,YAAYC,SAASC,aAAa,CAAC;YACzCC,OAAOC,cAAc,CAACJ,WAAW,SAAS;gBACxCK,OAAO;oBAAC3C;iBAAK;gBACb4C,UAAU;YACZ;YACAC,iBAAS,CAACC,MAAM,CAACR;YAEjB,eAAe;YACf,MAAMoB,eAAepC,cAAM,CAACC,SAAS,CAAC;YACtC,MAAMO,KAAKO,KAAK,CAACqB;YAEjB,oBAAoB;YACpB,IAAIQ,kBAAkB;gBACpBA,iBAAiB;oBAAEM,QAAQ;oBAAIC,OAAO;oBAAKC,YAAY;gBAAG;YAC5D;YAEArD,OAAOC,cAAM,CAACC,SAAS,CAAC,QAAQC,iBAAiB;YAEjD,MAAMuB,IAAAA,eAAO,EAAC;gBACZ1B,OAAOC,cAAM,CAACI,WAAW,CAAC,QAAQC,GAAG,CAACH,iBAAiB;YACzD;QACF;QAEAN,GAAG,yBAAyB;YAC1B,MAAMY,OAAOC,kBAAS,CAACC,KAAK;YAC5B,MAAMhC,OAAOiC,IAAAA,yBAAc,EAAC;YAE5B3B,sBAAsBX,UAAU,CAACmB,iBAAiB,CAAC;gBACjDC,SAAS;gBACT4D,OAAO;YACT;YAEAxD,IAAAA,iBAAM,gBAAC,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;;YAEnC,WAAW;YACX,MAAMyB,QAAQZ,cAAM,CAACa,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAmB;YACpE,MAAMN,KAAKO,KAAK,CAACH;YAEjB,MAAMI,YAAYC,SAASC,aAAa,CAAC;YACzCC,OAAOC,cAAc,CAACJ,WAAW,SAAS;gBACxCK,OAAO;oBAAC3C;iBAAK;gBACb4C,UAAU;YACZ;YACAC,iBAAS,CAACC,MAAM,CAACR;YAEjB,eAAe;YACf,MAAMoB,eAAepC,cAAM,CAACC,SAAS,CAAC;YACtC,MAAMO,KAAKO,KAAK,CAACqB;YAEjB,MAAMX,IAAAA,eAAO,EAAC;gBACZ1B,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC7D;QACF;IACF;IAEAhB,SAAS,kBAAkB;QACzBU,GAAG,sCAAsC;YACvC,MAAMY,OAAOC,kBAAS,CAACC,KAAK;YAE5Bb,IAAAA,iBAAM,gBAAC,qBAACC,mBAAU;gBAAE,GAAGX,YAAY;gBAAEmE,QAAQ;;YAE7C,MAAMzB,WAAW7B,cAAM,CAACa,SAAS,CAAC,UAAU;gBAAEC,MAAM;YAAmB;YAEvEf,OAAO8B,UAAUE,WAAW,CAAC,cAAc;YAE3C,oCAAoC;YACpCR,iBAAS,CAACO,QAAQ,CAACD;YACnB9B,OAAO8B,UAAUxB,GAAG,CAAC0B,WAAW,CAAC;YAEjC,8BAA8B;YAC9B,MAAMvB,KAAKO,KAAK,CAACc;YACjB,gCAAgC;YAChC,MAAMb,YAAYC,SAASC,aAAa,CAAC;YACzCnB,OAAOiB,WAAWuC,YAAY;QAChC;IACF;AACF"}