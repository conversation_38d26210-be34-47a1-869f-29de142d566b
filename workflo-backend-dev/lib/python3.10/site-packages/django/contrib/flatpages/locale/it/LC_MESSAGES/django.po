# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON>, 2014
# <PERSON><PERSON> G<PERSON>o <<EMAIL>>, 2020
# <AUTHOR> <EMAIL>, 2015,2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2021-01-15 16:01+0000\n"
"Last-Translator: palmux <<EMAIL>>\n"
"Language-Team: Italian (http://www.transifex.com/django/django/language/"
"it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: it\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Opzioni avanzate"

msgid "Flat Pages"
msgstr "Flat Pages"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Per esempio: '/about/contact'. Assicurati che inizi e finisca con uno slash."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Questo valore deve contenere solo lettere, numeri, punti, underscore, "
"trattini, barre diagonali o tilde."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "Per esempio: '/about/contact'. Assicurati che inizi con uno slash."

msgid "URL is missing a leading slash."
msgstr "Manca una barra iniziale nella URL."

msgid "URL is missing a trailing slash."
msgstr "Manca una barra finale nella URL."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "La flatpage con url %(url)s esiste già per il sito %(site)s"

msgid "title"
msgstr "titolo"

msgid "content"
msgstr "contenuto"

msgid "enable comments"
msgstr "abilita commenti"

msgid "template name"
msgstr "nome template"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Esempio: \"flatpages/contact_page.html\". Se non specificato, il sistema "
"userà \"flatpages/default.html\"."

msgid "registration required"
msgstr "registrazione obbligatoria"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Se selezionato, solo gli utenti che hanno effettuato l'accesso potranno "
"vedere la pagina."

msgid "sites"
msgstr "siti"

msgid "flat page"
msgstr "flat page"

msgid "flat pages"
msgstr "flat pages"
