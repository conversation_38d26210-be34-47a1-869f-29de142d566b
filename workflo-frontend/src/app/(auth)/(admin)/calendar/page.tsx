'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { ChevronLeft, ChevronRight, Plus, Calendar as CalendarIcon, Users, Clock, ArrowLeft } from 'lucide-react';

import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { formatDate } from '@/lib/utils';

// Mock events data
const mockEvents = [
  {
    id: 1,
    title: 'Team Meeting',
    date: '2024-02-15',
    time: '10:00 AM',
    type: 'meeting',
    attendees: 8,
    color: 'bg-blue-500',
  },
  {
    id: 2,
    title: 'Project Deadline',
    date: '2024-02-20',
    time: 'All Day',
    type: 'deadline',
    color: 'bg-red-500',
  },
  {
    id: 3,
    title: 'Birthday - <PERSON>',
    date: '2024-02-18',
    time: 'All Day',
    type: 'birthday',
    color: 'bg-green-500',
  },
  {
    id: 4,
    title: 'Training Session',
    date: '2024-02-22',
    time: '2:00 PM',
    type: 'training',
    attendees: 15,
    color: 'bg-purple-500',
  },
];

const CalendarPage: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [events] = useState(mockEvents);

  // Get calendar data
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const today = new Date();

  // Get first day of month and number of days
  const firstDayOfMonth = new Date(year, month, 1);
  const lastDayOfMonth = new Date(year, month + 1, 0);
  const firstDayWeekday = firstDayOfMonth.getDay();
  const daysInMonth = lastDayOfMonth.getDate();

  // Generate calendar days
  const calendarDays = [];

  // Add empty cells for days before month starts
  for (let i = 0; i < firstDayWeekday; i++) {
    calendarDays.push(null);
  }

  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push(day);
  }

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(month - 1);
    } else {
      newDate.setMonth(month + 1);
    }
    setCurrentDate(newDate);
  };

  const isToday = (day: number) => {
    return (
      day === today.getDate() &&
      month === today.getMonth() &&
      year === today.getFullYear()
    );
  };

  const isSelected = (day: number) => {
    return (
      day === selectedDate.getDate() &&
      month === selectedDate.getMonth() &&
      year === selectedDate.getFullYear()
    );
  };

  const getEventsForDate = (day: number) => {
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    return events.filter(event => event.date === dateStr);
  };

  const selectedDateEvents = getEventsForDate(selectedDate.getDate());

  return (
    <div className="space-y-6 max-w-full overflow-hidden">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="min-w-0 flex-1">
              <div className="flex items-center space-x-4 mb-2">
                <Link href="/manage">
                  <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Management
                  </Button>
                </Link>
              </div>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900 truncate">Calendar</h1>
              <p className="text-gray-600 mt-1 text-sm sm:text-base">
                Manage events, meetings, and important dates
              </p>
            </div>
            <div className="mt-4 sm:mt-0 flex-shrink-0">
              <Button variant="primary" className="w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                Add Event
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Calendar */}
          <div className="xl:col-span-2 min-w-0">
            <Card>
              <div className="p-4 sm:p-6">
                {/* Calendar Header */}
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg sm:text-xl font-semibold text-gray-900 truncate">
                    {monthNames[month]} {year}
                  </h2>
                  <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => navigateMonth('prev')}
                      className="p-2"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => setCurrentDate(new Date())}
                      className="hidden sm:block"
                    >
                      Today
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => navigateMonth('next')}
                      className="p-2"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Calendar Grid */}
                <div className="grid grid-cols-7 gap-1 min-w-0 overflow-hidden">
                  {/* Day headers */}
                  {dayNames.map((day) => (
                    <div
                      key={day}
                      className="p-1 sm:p-2 text-center text-xs sm:text-sm font-medium text-gray-500 truncate"
                    >
                      {day}
                    </div>
                  ))}

                  {/* Calendar days */}
                  {calendarDays.map((day, index) => {
                    if (day === null) {
                      return <div key={`empty-${index}`} className="p-1 sm:p-2 h-16 sm:h-24 min-w-0"></div>;
                    }

                    const dayEvents = getEventsForDate(day);
                    const isCurrentDay = isToday(day);
                    const isSelectedDay = isSelected(day);

                    return (
                      <div
                        key={`${year}-${month}-${day}`}
                        className={`p-1 sm:p-2 h-16 sm:h-24 border border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors min-w-0 overflow-hidden ${
                          isCurrentDay ? 'bg-orange-50 border-orange-200' : ''
                        } ${isSelectedDay ? 'bg-blue-50 border-blue-200' : ''}`}
                        onClick={() => setSelectedDate(new Date(year, month, day))}
                      >
                        <div
                          className={`text-xs sm:text-sm font-medium mb-1 ${
                            isCurrentDay
                              ? 'text-orange-600'
                              : isSelectedDay
                              ? 'text-blue-600'
                              : 'text-gray-900'
                          }`}
                        >
                          {day}
                        </div>
                        <div className="space-y-1 overflow-hidden">
                          {dayEvents.slice(0, 2).map((event) => (
                            <div
                              key={`${year}-${month}-${day}-${event.id}`}
                              className={`text-xs px-1 py-0.5 rounded text-white truncate ${event.color}`}
                              title={event.title}
                            >
                              {event.title}
                            </div>
                          ))}
                          {dayEvents.length > 2 && (
                            <div className="text-xs text-gray-500 truncate">
                              +{dayEvents.length - 2} more
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6 min-w-0">
            {/* Selected Date Events */}
            <Card>
              <div className="p-4 sm:p-6">
                <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4 truncate">
                  {formatDate(selectedDate.toISOString(), 'EEEE, MMMM dd')}
                </h3>

                {selectedDateEvents.length > 0 ? (
                  <div className="space-y-3">
                    {selectedDateEvents.map((event) => (
                      <div
                        key={event.id}
                        className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg min-w-0"
                      >
                        <div className={`w-3 h-3 rounded-full mt-1 flex-shrink-0 ${event.color}`}></div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-gray-900 truncate">{event.title}</h4>
                          <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 mt-1 space-y-1 sm:space-y-0">
                            <div className="flex items-center text-sm text-gray-600">
                              <Clock className="h-4 w-4 mr-1 flex-shrink-0" />
                              <span className="truncate">{event.time}</span>
                            </div>
                            {event.attendees && (
                              <div className="flex items-center text-sm text-gray-600">
                                <Users className="h-4 w-4 mr-1 flex-shrink-0" />
                                <span className="truncate">{event.attendees}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CalendarIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600 text-sm sm:text-base">No events scheduled</p>
                  </div>
                )}
              </div>
            </Card>

            {/* Upcoming Events */}
            <Card>
              <div className="p-4 sm:p-6">
                <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4">
                  Upcoming Events
                </h3>

                <div className="space-y-3">
                  {events.slice(0, 5).map((event) => (
                    <div key={event.id} className="flex items-center space-x-3 min-w-0">
                      <div className={`w-2 h-2 rounded-full flex-shrink-0 ${event.color}`}></div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {event.title}
                        </p>
                        <p className="text-xs text-gray-600 truncate">
                          {formatDate(event.date)} • {event.time}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            {/* Quick Stats */}
            <Card>
              <div className="p-4 sm:p-6">
                <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4">
                  This Month
                </h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 truncate">Total Events</span>
                    <span className="font-medium text-gray-900 flex-shrink-0">{events.length}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 truncate">Meetings</span>
                    <span className="font-medium text-gray-900 flex-shrink-0">
                      {events.filter(e => e.type === 'meeting').length}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 truncate">Birthdays</span>
                    <span className="font-medium text-gray-900 flex-shrink-0">
                      {events.filter(e => e.type === 'birthday').length}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 truncate">Deadlines</span>
                    <span className="font-medium text-gray-900 flex-shrink-0">
                      {events.filter(e => e.type === 'deadline').length}
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
  );
};

export default CalendarPage;
