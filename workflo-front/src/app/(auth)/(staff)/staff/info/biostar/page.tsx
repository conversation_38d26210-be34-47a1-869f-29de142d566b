'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>gerprint,
  Eye,
  Wifi,
  WifiOff,
  Clock,
  MapPin,
  Activity,
  Shield,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Monitor,
  User,
  Database,
  History,
  Settings
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { cn, formatDate } from '@/lib/utils';
import { useBiostarAttendance } from '@/hooks/useBiostarAttendance';
import { BiometricUser, BiometricEvent, BiometricDevice } from '@/types';

interface BiometricProfile {
  user: BiometricUser | null;
  enrolledFingerprints: number;
  enrolledFaces: number;
  lastSync: string | null;
  accessLevel: string;
  isActive: boolean;
}

const StaffBiostarPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'profile' | 'devices' | 'history' | 'security'>('profile');
  const [biometricProfile, setBiometricProfile] = useState<BiometricProfile | null>(null);
  const [recentEvents, setRecentEvents] = useState<BiometricEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Use BioStar attendance hook for device and connectivity data
  const {
    devices,
    connected: biostarConnected,
    error: connectionError,
    refresh: refreshAttendance
  } = useBiostarAttendance({
    employeeId: user?.employee_id,
    autoRefresh: true,
    enableRealTime: false
  });

  useEffect(() => {
    loadBiometricData();
  }, [user?.employee_id]);

  const loadBiometricData = async () => {
    if (!user?.employee_id) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Use mock data instead of API calls
      const mockBiostarUser = {
        id: 'user-001',
        user_id: user.employee_id,
        name: `${user.first_name} ${user.last_name}`,
        email: user.email,
        department: 'Engineering',
        position: 'Software Engineer',
        disabled: false,
        created: '2024-01-01T00:00:00Z',
        updated: new Date().toISOString()
      };

      // Mock recent access events
      const mockEvents: BiometricEvent[] = Array.from({ length: 8 }, (_, i) => ({
        id: `event-${i}`,
        user_id: user.employee_id,
        device_id: `dev-${(i % 3) + 1}`,
        event_type: (i % 2 === 0 ? 'ENTRY' : 'EXIT') as 'ENTRY' | 'EXIT' | 'DENIED',
        datetime: new Date(Date.now() - i * 2 * 60 * 60 * 1000).toISOString(), // Every 2 hours
        user_name: `${user.first_name} ${user.last_name}`,
        device_name: ['Main Entrance', 'Office Floor', 'Back Entrance'][i % 3]
      }));

      setBiometricProfile({
        user: mockBiostarUser,
        enrolledFingerprints: 2,
        enrolledFaces: 1,
        lastSync: new Date().toISOString(),
        accessLevel: 'Standard Employee',
        isActive: true
      });

      setRecentEvents(mockEvents);

    } catch (error) {
      console.error('Failed to load biometric data:', error);
      // Set default profile even on error
      setBiometricProfile({
        user: null,
        enrolledFingerprints: 2,
        enrolledFaces: 1,
        lastSync: new Date().toISOString(),
        accessLevel: 'Standard Employee',
        isActive: true
      });
      setRecentEvents([]);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      loadBiometricData(),
      refreshAttendance()
    ]);
    setRefreshing(false);
  };



  const tabs = [
    { id: 'profile', label: 'Biometric Profile', icon: User },
    { id: 'devices', label: 'Devices', icon: Monitor },
    { id: 'history', label: 'Access History', icon: History },
    { id: 'security', label: 'Security', icon: Shield }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
        <span className="ml-2 text-gray-600">Loading biometric data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">BioStar Profile</h1>
          <p className="text-gray-600 mt-1">
            Manage your biometric data and view access history
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            {biostarConnected ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <span className="text-sm text-gray-600">
              BioStar {biostarConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          <Button
            variant="secondary"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", refreshing && "animate-spin")} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Connection Error Alert */}
      {connectionError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <div>
              <h3 className="text-sm font-medium text-red-800">Connection Error</h3>
              <p className="text-sm text-red-700 mt-1">{connectionError}</p>
            </div>
          </div>
        </div>
      )}

      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <User className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Profile Status</p>
                <p className="text-2xl font-bold text-gray-900">
                  {biometricProfile?.isActive ? 'Active' : 'Inactive'}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Fingerprint className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Fingerprints</p>
                <p className="text-2xl font-bold text-gray-900">
                  {biometricProfile?.enrolledFingerprints || 0}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Eye className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Face Templates</p>
                <p className="text-2xl font-bold text-gray-900">
                  {biometricProfile?.enrolledFaces || 0}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Monitor className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Devices</p>
                <p className="text-2xl font-bold text-gray-900">
                  {devices.filter(d => d.status === 'ONLINE').length}/{devices.length}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={cn(
                  'flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm',
                  activeTab === tab.id
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'profile' && (
          <BiometricProfileTab
            profile={biometricProfile}
            user={user}
          />
        )}

        {activeTab === 'devices' && (
          <DevicesTab
            devices={devices}
            connected={biostarConnected}
          />
        )}

        {activeTab === 'history' && (
          <AccessHistoryTab
            events={recentEvents}
            employeeId={user?.employee_id}
          />
        )}

        {activeTab === 'security' && (
          <SecurityTab
            profile={biometricProfile}
            user={user}
          />
        )}
      </div>
    </div>
  );
};

// Biometric Profile Tab Component
interface BiometricProfileTabProps {
  profile: BiometricProfile | null;
  user: any;
}

const BiometricProfileTab: React.FC<BiometricProfileTabProps> = ({ profile, user }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Profile Information */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Profile Information</h3>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Employee ID</span>
              <span className="font-medium">{user?.employee_id || 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Full Name</span>
              <span className="font-medium">{profile?.user?.name || `${user?.first_name} ${user?.last_name}`}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Email</span>
              <span className="font-medium">{profile?.user?.email || user?.email}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Department</span>
              <span className="font-medium">{profile?.user?.department || 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Position</span>
              <span className="font-medium">{profile?.user?.position || 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Access Level</span>
              <span className="font-medium">{profile?.accessLevel || 'Standard'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Status</span>
              <span className={cn(
                'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                profile?.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              )}>
                {profile?.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </div>
      </Card>

      {/* Biometric Data */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Biometric Enrollment</h3>
          <div className="space-y-6">
            {/* Fingerprints */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Fingerprint className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Fingerprints</p>
                  <p className="text-sm text-gray-600">{profile?.enrolledFingerprints || 0} enrolled</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">{profile?.enrolledFingerprints || 0}/10</div>
                <div className="text-xs text-gray-500">Max: 10</div>
              </div>
            </div>

            {/* Face Recognition */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Eye className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Face Templates</p>
                  <p className="text-sm text-gray-600">{profile?.enrolledFaces || 0} enrolled</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">{profile?.enrolledFaces || 0}/5</div>
                <div className="text-xs text-gray-500">Max: 5</div>
              </div>
            </div>

            {/* Last Sync */}
            <div className="pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Last Sync</span>
                <span className="font-medium">
                  {profile?.lastSync ?
                    formatDate(profile.lastSync, 'MMM dd, yyyy HH:mm') :
                    'Never'
                  }
                </span>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

// Devices Tab Component
interface DevicesTabProps {
  devices: BiometricDevice[];
  connected: boolean;
}

const DevicesTab: React.FC<DevicesTabProps> = ({ devices, connected }) => {
  return (
    <div className="space-y-6">
      {/* Connection Status */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {connected ? (
                <Wifi className="h-6 w-6 text-green-500" />
              ) : (
                <WifiOff className="h-6 w-6 text-red-500" />
              )}
              <div>
                <h3 className="text-lg font-medium text-gray-900">System Status</h3>
                <p className="text-sm text-gray-600">
                  BioStar system is {connected ? 'online and accessible' : 'offline or unreachable'}
                </p>
              </div>
            </div>
            <span className={cn(
              'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
              connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            )}>
              {connected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>
      </Card>

      {/* Devices List */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Available Devices</h3>
          {devices.length === 0 ? (
            <div className="text-center py-8">
              <Monitor className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Devices Found</h3>
              <p className="text-gray-600">No biometric devices are currently available.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {devices.map((device) => (
                <div key={device.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <Monitor className="h-5 w-5 text-gray-600" />
                      <h4 className="font-medium text-gray-900">{device.name}</h4>
                    </div>
                    <span className={cn(
                      'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                      device.status === 'ONLINE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    )}>
                      {device.status}
                    </span>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Location</span>
                      <span className="font-medium">{device.location || 'Unknown'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">IP Address</span>
                      <span className="font-medium">{device.ip}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Type</span>
                      <span className="font-medium">{device.type}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

// Access History Tab Component
interface AccessHistoryTabProps {
  events: BiometricEvent[];
  employeeId?: string;
}

const AccessHistoryTab: React.FC<AccessHistoryTabProps> = ({ events }) => {
  const [selectedPeriod, setSelectedPeriod] = useState('7_days');
  const [filteredEvents, setFilteredEvents] = useState<BiometricEvent[]>(events);

  useEffect(() => {
    // Filter events based on selected period
    const now = new Date();
    let startDate: Date;

    switch (selectedPeriod) {
      case '1_day':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7_days':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30_days':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    const filtered = events.filter(event =>
      new Date(event.datetime) >= startDate
    );
    setFilteredEvents(filtered);
  }, [events, selectedPeriod]);

  const getEventTypeIcon = (eventType: string) => {
    switch (eventType) {
      case 'ENTRY': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'EXIT': return <Activity className="h-4 w-4 text-blue-500" />;
      case 'DENIED': return <AlertCircle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getEventTypeColor = (eventType: string) => {
    switch (eventType) {
      case 'ENTRY': return 'bg-green-100 text-green-800';
      case 'EXIT': return 'bg-blue-100 text-blue-800';
      case 'DENIED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Filter Controls */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Access History</h3>
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="1_day">Last 24 Hours</option>
              <option value="7_days">Last 7 Days</option>
              <option value="30_days">Last 30 Days</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Events List */}
      <Card>
        <div className="p-6">
          {filteredEvents.length === 0 ? (
            <div className="text-center py-8">
              <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Access Events</h3>
              <p className="text-gray-600">No access events found for the selected period.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredEvents.map((event, index) => (
                <div key={`${event.id}-${index}`} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center space-x-4">
                    {getEventTypeIcon(event.event_type)}
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-gray-900">
                          {event.event_type === 'ENTRY' ? 'Check In' :
                           event.event_type === 'EXIT' ? 'Check Out' : 'Access Denied'}
                        </span>
                        <span className={cn(
                          'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                          getEventTypeColor(event.event_type)
                        )}>
                          {event.event_type}
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>{formatDate(event.datetime, 'MMM dd, yyyy HH:mm:ss')}</span>
                        </div>
                        {event.device_name && (
                          <div className="flex items-center space-x-1">
                            <Monitor className="h-3 w-3" />
                            <span>{event.device_name}</span>
                          </div>
                        )}
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3" />
                          <span>Main Office</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

// Security Tab Component
interface SecurityTabProps {
  profile: BiometricProfile | null;
  user: any;
}

const SecurityTab: React.FC<SecurityTabProps> = ({ profile }) => {
  const [showSecuritySettings, setShowSecuritySettings] = useState(false);

  return (
    <div className="space-y-6">
      {/* Security Overview */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Security Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="p-3 bg-green-100 rounded-full w-12 h-12 mx-auto mb-2">
                <Shield className="h-6 w-6 text-green-600" />
              </div>
              <h4 className="font-medium text-gray-900">Account Status</h4>
              <p className="text-sm text-gray-600 mt-1">
                {profile?.isActive ? 'Active & Secure' : 'Inactive'}
              </p>
            </div>
            <div className="text-center">
              <div className="p-3 bg-blue-100 rounded-full w-12 h-12 mx-auto mb-2">
                <Database className="h-6 w-6 text-blue-600" />
              </div>
              <h4 className="font-medium text-gray-900">Data Sync</h4>
              <p className="text-sm text-gray-600 mt-1">
                {profile?.lastSync ? 'Recently Synced' : 'Not Synced'}
              </p>
            </div>
            <div className="text-center">
              <div className="p-3 bg-purple-100 rounded-full w-12 h-12 mx-auto mb-2">
                <Fingerprint className="h-6 w-6 text-purple-600" />
              </div>
              <h4 className="font-medium text-gray-900">Biometric Security</h4>
              <p className="text-sm text-gray-600 mt-1">
                {(profile?.enrolledFingerprints || 0) + (profile?.enrolledFaces || 0) > 0 ? 'Enrolled' : 'Not Enrolled'}
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* Security Settings */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Security Settings</h3>
            <Button
              variant="secondary"
              onClick={() => setShowSecuritySettings(!showSecuritySettings)}
            >
              <Settings className="h-4 w-4 mr-2" />
              {showSecuritySettings ? 'Hide Settings' : 'Show Settings'}
            </Button>
          </div>

          {showSecuritySettings && (
            <div className="space-y-4 border-t border-gray-200 pt-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Two-Factor Authentication</h4>
                  <p className="text-sm text-gray-600">Add an extra layer of security to your account</p>
                </div>
                <Button variant="secondary" size="sm">
                  Enable
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Access Notifications</h4>
                  <p className="text-sm text-gray-600">Get notified when your biometric data is used</p>
                </div>
                <Button variant="secondary" size="sm">
                  Configure
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Data Privacy</h4>
                  <p className="text-sm text-gray-600">Manage how your biometric data is stored and used</p>
                </div>
                <Button variant="secondary" size="sm">
                  Review
                </Button>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Privacy Information */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Privacy & Data Protection</h3>
          <div className="space-y-4 text-sm text-gray-600">
            <div className="flex items-start space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
              <p>Your biometric data is encrypted and stored securely</p>
            </div>
            <div className="flex items-start space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
              <p>Access logs are maintained for security and compliance purposes</p>
            </div>
            <div className="flex items-start space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
              <p>Your data is only used for authentication and attendance tracking</p>
            </div>
            <div className="flex items-start space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
              <p>You can request data deletion upon employment termination</p>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default StaffBiostarPage;
