'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import StaffHomeSidebar from './sidebars/StaffHomeSidebar';
import StaffDynamicSidebar from './StaffDynamicSidebar'; // Fallback for other pages

interface StaffSidebarRouterProps {
  isOpen: boolean;
  onClose: () => void;
}

const StaffSidebarRouter: React.FC<StaffSidebarRouterProps> = ({ isOpen, onClose }) => {
  const pathname = usePathname();

  // Determine which sidebar to show based on the current path
  const getSidebarComponent = () => {
    // Home/Dashboard page - use specialized sidebar
    if (pathname === '/staff') {
      return <StaffHomeSidebar isOpen={isOpen} onClose={onClose} />;
    }
    
    // All other pages use the dynamic sidebar
    return <StaffDynamicSidebar isOpen={isOpen} onClose={onClose} />;
  };

  return getSidebarComponent();
};

export default StaffSidebarRouter;
