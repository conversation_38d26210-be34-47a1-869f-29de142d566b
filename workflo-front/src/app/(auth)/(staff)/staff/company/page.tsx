'use client';

import React, { useState } from 'react';
import {
  Building,
  Users,
  MapPin,
  Phone,
  Mail,
  Globe,
  FileText,
  Search,
  Filter,
  Eye,
  Download,
  Calendar,
  Award,
  Target,
  TrendingUp
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials } from '@/lib/utils';

const StaffCompanyPage: React.FC = () => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [activeTab, setActiveTab] = useState('overview');

  // Mock company data - in real app, this would come from API
  const companyInfo = {
    name: 'WorkFlow Technologies Ltd',
    logo: '/company-logo.png',
    founded: '2018',
    employees: 150,
    headquarters: 'Nairobi, Kenya',
    industry: 'Technology',
    website: 'https://workflow.co.ke',
    email: '<EMAIL>',
    phone: '+*********** 456',
    description: 'Leading provider of workflow automation and business process management solutions in East Africa.',
    mission: 'To empower businesses with innovative technology solutions that streamline operations and drive growth.',
    vision: 'To be the premier technology partner for businesses across Africa.',
    values: ['Innovation', 'Integrity', 'Excellence', 'Collaboration', 'Customer Focus']
  };

  const departments = [
    { id: 1, name: 'Engineering', head: 'John Smith', employees: 45, budget: 'KSH 12M' },
    { id: 2, name: 'Sales & Marketing', head: 'Jane Doe', employees: 25, budget: 'KSH 8M' },
    { id: 3, name: 'Human Resources', head: 'Mike Johnson', employees: 8, budget: 'KSH 3M' },
    { id: 4, name: 'Finance', head: 'Sarah Wilson', employees: 12, budget: 'KSH 5M' },
    { id: 5, name: 'Operations', head: 'David Brown', employees: 20, budget: 'KSH 6M' },
    { id: 6, name: 'Customer Success', head: 'Lisa Davis', employees: 15, budget: 'KSH 4M' }
  ];

  const employees = [
    {
      id: 1,
      name: 'John Smith',
      position: 'Engineering Manager',
      department: 'Engineering',
      email: '<EMAIL>',
      phone: '+254 700 111 111',
      location: 'Nairobi',
      profile_picture: null
    },
    {
      id: 2,
      name: 'Jane Doe',
      position: 'Sales Director',
      department: 'Sales & Marketing',
      email: '<EMAIL>',
      phone: '+254 700 222 222',
      location: 'Nairobi',
      profile_picture: null
    },
    {
      id: 3,
      name: 'Mike Johnson',
      position: 'HR Manager',
      department: 'Human Resources',
      email: '<EMAIL>',
      phone: '+254 ***********',
      location: 'Nairobi',
      profile_picture: null
    },
    {
      id: 4,
      name: 'Sarah Wilson',
      position: 'Finance Manager',
      department: 'Finance',
      email: '<EMAIL>',
      phone: '+*********** 444',
      location: 'Nairobi',
      profile_picture: null
    }
  ];

  const policies = [
    {
      id: 1,
      title: 'Employee Handbook',
      category: 'General',
      last_updated: '2024-01-15',
      version: '2.1',
      description: 'Comprehensive guide to company policies and procedures'
    },
    {
      id: 2,
      title: 'Code of Conduct',
      category: 'Ethics',
      last_updated: '2024-02-01',
      version: '1.5',
      description: 'Professional conduct and ethical guidelines'
    },
    {
      id: 3,
      title: 'Remote Work Policy',
      category: 'Work Arrangements',
      last_updated: '2024-03-10',
      version: '3.0',
      description: 'Guidelines for remote and hybrid work arrangements'
    },
    {
      id: 4,
      title: 'Leave Policy',
      category: 'Benefits',
      last_updated: '2024-01-20',
      version: '2.0',
      description: 'Annual leave, sick leave, and other time-off policies'
    },
    {
      id: 5,
      title: 'IT Security Policy',
      category: 'Security',
      last_updated: '2024-04-05',
      version: '1.8',
      description: 'Information security and data protection guidelines'
    }
  ];

  const companyStats = [
    { label: 'Total Employees', value: companyInfo.employees, icon: Users, color: 'bg-blue-100 text-blue-600' },
    { label: 'Departments', value: departments.length, icon: Building, color: 'bg-green-100 text-green-600' },
    { label: 'Years in Business', value: new Date().getFullYear() - parseInt(companyInfo.founded), icon: Calendar, color: 'bg-purple-100 text-purple-600' },
    { label: 'Office Locations', value: 3, icon: MapPin, color: 'bg-orange-100 text-orange-600' }
  ];

  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = employee.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         employee.position.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         employee.department.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesDepartment = selectedDepartment === 'all' || employee.department === selectedDepartment;
    return matchesSearch && matchesDepartment;
  });

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Building },
    { id: 'directory', label: 'Employee Directory', icon: Users },
    { id: 'departments', label: 'Departments', icon: Target },
    { id: 'policies', label: 'Policies', icon: FileText }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Company Information</h1>
          <p className="text-gray-600 mt-1">
            Learn about our company, team, and organizational structure
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Download Org Chart
          </Button>
        </div>
      </div>

      {/* Company Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {companyStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index}>
              <div className="p-6">
                <div className="flex items-center">
                  <div className={cn('p-2 rounded-lg', stat.color)}>
                    <Icon className="h-6 w-6" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                </div>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  'flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors',
                  activeTab === tab.id
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Company Overview */}
          <Card>
            <div className="p-6">
              <div className="flex items-center space-x-4 mb-6">
                <div className="w-16 h-16 bg-orange-500 rounded-lg flex items-center justify-center">
                  <Building className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">{companyInfo.name}</h3>
                  <p className="text-gray-600">{companyInfo.industry} • Founded {companyInfo.founded}</p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">About Us</h4>
                  <p className="text-sm text-gray-600">{companyInfo.description}</p>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Mission</h4>
                  <p className="text-sm text-gray-600">{companyInfo.mission}</p>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Vision</h4>
                  <p className="text-sm text-gray-600">{companyInfo.vision}</p>
                </div>
              </div>
            </div>
          </Card>

          {/* Contact Information */}
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <MapPin className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Headquarters</p>
                    <p className="text-sm text-gray-600">{companyInfo.headquarters}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Phone className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Phone</p>
                    <p className="text-sm text-gray-600">{companyInfo.phone}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Email</p>
                    <p className="text-sm text-gray-600">{companyInfo.email}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Globe className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Website</p>
                    <a href={companyInfo.website} className="text-sm text-orange-600 hover:text-orange-700">
                      {companyInfo.website}
                    </a>
                  </div>
                </div>
              </div>

              {/* Company Values */}
              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Our Values</h4>
                <div className="flex flex-wrap gap-2">
                  {companyInfo.values.map((value, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800"
                    >
                      {value}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'directory' && (
        <Card>
          <div className="p-6">
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search employees..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>
              <div className="flex items-center space-x-2">
                <select
                  value={selectedDepartment}
                  onChange={(e) => setSelectedDepartment(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                >
                  <option value="all">All Departments</option>
                  {departments.map(dept => (
                    <option key={dept.id} value={dept.name}>{dept.name}</option>
                  ))}
                </select>
                <Filter className="h-4 w-4 text-gray-400" />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredEmployees.map((employee) => (
                <div key={employee.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-3">
                    {employee.profile_picture ? (
                      <img
                        src={employee.profile_picture}
                        alt={employee.name}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-medium">
                          {getInitials(employee.name.split(' ')[0], employee.name.split(' ')[1] || '')}
                        </span>
                      </div>
                    )}
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">{employee.name}</h4>
                      <p className="text-xs text-gray-600">{employee.position}</p>
                      <p className="text-xs text-gray-500">{employee.department}</p>
                    </div>
                  </div>
                  <div className="mt-3 space-y-1">
                    <div className="flex items-center text-xs text-gray-500">
                      <Mail className="h-3 w-3 mr-1" />
                      {employee.email}
                    </div>
                    <div className="flex items-center text-xs text-gray-500">
                      <Phone className="h-3 w-3 mr-1" />
                      {employee.phone}
                    </div>
                    <div className="flex items-center text-xs text-gray-500">
                      <MapPin className="h-3 w-3 mr-1" />
                      {employee.location}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      )}

      {activeTab === 'departments' && (
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Department Overview</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {departments.map((department) => (
                <div key={department.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-medium text-gray-900">{department.name}</h4>
                    <Building className="h-5 w-5 text-gray-400" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Department Head</span>
                      <span className="font-medium text-gray-900">{department.head}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Employees</span>
                      <span className="font-medium text-gray-900">{department.employees}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Budget</span>
                      <span className="font-medium text-gray-900">{department.budget}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      )}

      {activeTab === 'policies' && (
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Company Policies</h3>
            <div className="space-y-4">
              {policies.map((policy) => (
                <div key={policy.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        <FileText className="h-5 w-5 text-gray-600" />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">{policy.title}</h4>
                        <p className="text-xs text-gray-600">{policy.description}</p>
                        <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                          <span>Category: {policy.category}</span>
                          <span>Version: {policy.version}</span>
                          <span>Updated: {policy.last_updated}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                        <Eye className="h-4 w-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                        <Download className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default StaffCompanyPage;
