'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/providers/AuthProvider';
import { User } from '@/types';
import { useToast } from '@/hooks/useToast';

interface RoleGuardProps {
  children: React.ReactNode;
  allowedRoles: string[];
  fallback?: React.ReactNode;
  redirectTo?: string;
}

// Helper function to check if user has required role
const hasRequiredRole = (user: User | null, allowedRoles: string[]): boolean => {
  if (!user) return false;

  // Admin roles that can access admin routes
  const adminRoles = ['hr', 'supervisor', 'admin'];

  // Check if user's role is directly in the allowed roles
  if (allowedRoles.includes(user.role)) {
    return true;
  }

  // If allowed roles include 'admin', check if user has any admin role
  if (allowedRoles.includes('admin') && adminRoles.includes(user.role)) {
    return true;
  }

  // If allowed roles include 'staff', check if user is employee
  if (allowedRoles.includes('staff') && user.role === 'employee') {
    return true;
  }

  return false;
};

const RoleGuard: React.FC<RoleGuardProps> = ({
  children,
  allowedRoles,
  fallback,
  redirectTo
}) => {
  const { user, isAuthenticated, isLoading, logout } = useAuth();
  const router = useRouter();
  const toast = useToast();
  const [countdown, setCountdown] = useState<number | null>(null);
  const [isAutoLogoutActive, setIsAutoLogoutActive] = useState(false);

  // Auto-logout functionality for unauthorized access
  useEffect(() => {
    // Only trigger if user is authenticated but doesn't have required role
    if (!isAuthenticated || !user || hasRequiredRole(user, allowedRoles) || isAutoLogoutActive) {
      return;
    }

    // Start countdown when user doesn't have required role
    setIsAutoLogoutActive(true);
    setCountdown(60); // 60 seconds = 1 minute

    // Show warning toast
    toast.warning(
      'You will be automatically logged out in 1 minute due to insufficient permissions.',
      {
        title: 'Access Denied',
        duration: 10000,
        persistent: false
      }
    );

    // Countdown timer
    const countdownInterval = setInterval(() => {
      setCountdown(prev => {
        if (prev === null || prev <= 1) {
          clearInterval(countdownInterval);

          // Clear all authentication data and redirect to login
          toast.error('Session expired due to insufficient permissions. Please log in again.', {
            title: 'Session Expired',
            persistent: true
          });

          // Clear authentication data and redirect
          logout();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Cleanup function
    return () => {
      clearInterval(countdownInterval);
    };
  }, [isAuthenticated, user?.role, allowedRoles.join(','), logout, toast]);

  // Cleanup effect when auto-logout is cancelled or component unmounts
  useEffect(() => {
    return () => {
      if (isAutoLogoutActive) {
        setIsAutoLogoutActive(false);
        setCountdown(null);
      }
    };
  }, [isAutoLogoutActive]);

  // Function to cancel auto-logout
  const cancelAutoLogout = useCallback(() => {
    setIsAutoLogoutActive(false);
    setCountdown(null);
    toast.success('Auto-logout cancelled. Redirecting to your dashboard...', {
      duration: 3000
    });

    // Redirect to appropriate dashboard
    setTimeout(() => {
      if (user?.role === 'accountant') {
        router.push('/accountant');
      } else if (user?.role === 'supervisor') {
        router.push('/supervisor');
      } else if (user?.role === 'employee') {
        router.push('/staff');
      } else if (hasRequiredRole(user, ['admin'])) {
        router.push('/dashboard');
      } else {
        router.push('/login');
      }
    }, 1000);
  }, [user?.role, router, toast]);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600 text-sm">Checking permissions...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    if (redirectTo) {
      router.replace(redirectTo);
      return null;
    }
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Authentication Required
          </h2>
          <p className="text-gray-600 mb-4">
            Please log in to access this page.
          </p>
          <button
            onClick={() => router.push('/login')}
            className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  // Check if user has required role
  if (!hasRequiredRole(user, allowedRoles)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    // Default unauthorized access component
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Access Denied
          </h2>
          <p className="text-gray-600 mb-4">
            You don't have permission to access this page. Your current role ({user?.role}) is not authorized for this section.
          </p>

          {/* Auto-logout countdown */}
          {countdown !== null && countdown > 0 && (
            <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-yellow-800 font-medium">Auto-logout in {countdown} seconds</span>
              </div>
              <p className="text-yellow-700 text-sm mb-3">
                You will be automatically logged out and redirected to the login page.
              </p>
              <button
                onClick={cancelAutoLogout}
                className="w-full px-3 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors text-sm font-medium"
              >
                Cancel Auto-logout & Go to Dashboard
              </button>
            </div>
          )}

          <div className="space-y-2">
            <button
              onClick={() => router.back()}
              className="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
            >
              Go Back
            </button>
            <button
              onClick={() => {
                // Cancel auto-logout and redirect
                setIsAutoLogoutActive(false);
                setCountdown(null);

                // Redirect based on user role
                if (user?.role === 'accountant') {
                  router.push('/accountant');
                } else if (user?.role === 'supervisor') {
                  router.push('/supervisor');
                } else if (user?.role === 'employee') {
                  router.push('/staff');
                } else if (hasRequiredRole(user, ['admin'])) {
                  router.push('/dashboard');
                } else {
                  router.push('/login');
                }
              }}
              className="w-full px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  // User has required role, render children
  return <>{children}</>;
};

export default RoleGuard;

// Convenience components for specific roles
export const AdminGuard: React.FC<Omit<RoleGuardProps, 'allowedRoles'>> = (props) => (
  <RoleGuard {...props} allowedRoles={['admin']} />
);

export const StaffGuard: React.FC<Omit<RoleGuardProps, 'allowedRoles'>> = (props) => (
  <RoleGuard {...props} allowedRoles={['staff']} />
);

export const HRGuard: React.FC<Omit<RoleGuardProps, 'allowedRoles'>> = (props) => (
  <RoleGuard {...props} allowedRoles={['hr']} />
);

export const SupervisorGuard: React.FC<Omit<RoleGuardProps, 'allowedRoles'>> = (props) => (
  <RoleGuard {...props} allowedRoles={['supervisor']} />
);

export const AccountantGuard: React.FC<Omit<RoleGuardProps, 'allowedRoles'>> = (props) => (
  <RoleGuard {...props} allowedRoles={['accountant']} />
);

// Hook for checking roles in components
export const useRoleCheck = () => {
  const { user } = useAuth();

  return {
    hasRole: (roles: string[]) => hasRequiredRole(user, roles),
    isAdmin: () => hasRequiredRole(user, ['admin']),
    isStaff: () => hasRequiredRole(user, ['staff']),
    isHR: () => hasRequiredRole(user, ['hr']),
    isSupervisor: () => hasRequiredRole(user, ['supervisor']),
    isAccountant: () => hasRequiredRole(user, ['accountant']),
    userRole: user?.role || null
  };
};
