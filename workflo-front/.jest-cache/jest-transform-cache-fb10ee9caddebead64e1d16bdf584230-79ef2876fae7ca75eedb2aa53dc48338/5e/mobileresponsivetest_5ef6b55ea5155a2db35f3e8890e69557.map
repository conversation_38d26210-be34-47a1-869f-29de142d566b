{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/__tests__/mobile-responsive.test.tsx"], "sourcesContent": ["import React from 'react';\nimport { render, screen } from '@testing-library/react';\nimport '@testing-library/jest-dom';\nimport StaffDashboard from '@/app/(auth)/(staff)/staff/page';\n\n// Mock the hooks and dependencies\njest.mock('@/hooks/useBiostarAttendance', () => ({\n  useBiostarAttendance: () => ({\n    summary: {\n      checkInTime: '08:30 AM',\n      hoursWorked: 8.5,\n      todayStatus: 'PRESENT',\n      weeklyHours: 40.0,\n      monthlyAttendance: 22\n    },\n    realtimeUpdates: [],\n    connected: true,\n    loading: false\n  })\n}));\n\njest.mock('@/providers/AuthProvider', () => ({\n  useAuth: () => ({\n    user: {\n      first_name: '<PERSON>',\n      last_name: '<PERSON><PERSON>',\n      employee_id: 'EMP001'\n    },\n    isAuthenticated: true,\n    isLoading: false\n  })\n}));\n\n// Mock Next.js router\njest.mock('next/navigation', () => ({\n  usePathname: () => '/staff',\n  useRouter: () => ({\n    push: jest.fn(),\n    replace: jest.fn(),\n    back: jest.fn()\n  })\n}));\n\ndescribe('Mobile Responsive Staff Dashboard', () => {\n  beforeEach(() => {\n    // Reset viewport to mobile size\n    Object.defineProperty(window, 'innerWidth', {\n      writable: true,\n      configurable: true,\n      value: 375,\n    });\n    Object.defineProperty(window, 'innerHeight', {\n      writable: true,\n      configurable: true,\n      value: 667,\n    });\n  });\n\n  it('renders staff dashboard with mobile-friendly layout', () => {\n    render(<StaffDashboard />);\n\n    // Check if welcome header is present\n    expect(screen.getByText(/Good/)).toBeInTheDocument();\n    expect(screen.getByText(/John/)).toBeInTheDocument();\n\n    // Check if stats cards are present\n    expect(screen.getByText('Check-in Time')).toBeInTheDocument();\n    expect(screen.getByText('Hours Worked')).toBeInTheDocument();\n    expect(screen.getByText('Tasks Completed')).toBeInTheDocument();\n    expect(screen.getByText('Weekly Hours')).toBeInTheDocument();\n\n    // Check if activities section is present\n    expect(screen.getByText(\"Today's Activities\")).toBeInTheDocument();\n\n    // Check if upcoming events section is present\n    expect(screen.getByText('Upcoming Events')).toBeInTheDocument();\n\n    // Check if quick actions section is present\n    expect(screen.getByText('Quick Actions')).toBeInTheDocument();\n  });\n\n  it('has proper responsive classes for mobile', () => {\n    const { container } = render(<StaffDashboard />);\n\n    // Check for responsive spacing classes\n    const mainContainer = container.querySelector('.space-y-4');\n    expect(mainContainer).toBeInTheDocument();\n\n    // Check for responsive grid classes\n    const statsGrid = container.querySelector('.grid-cols-1');\n    expect(statsGrid).toBeInTheDocument();\n  });\n\n  it('displays BioStar connection status', () => {\n    render(<StaffDashboard />);\n\n    expect(screen.getByText('BioStar Connected')).toBeInTheDocument();\n  });\n\n  it('shows current time', () => {\n    render(<StaffDashboard />);\n\n    expect(screen.getByText('Current Time')).toBeInTheDocument();\n  });\n\n  it('has proper z-index hierarchy to prevent header overlap', () => {\n    const { container } = render(<StaffDashboard />);\n\n    // Check that responsive classes include proper z-index handling\n    const responsiveElements = container.querySelectorAll('[class*=\"z-\"]');\n    expect(responsiveElements.length).toBeGreaterThan(0);\n  });\n});\n"], "names": ["jest", "mock", "useBiostarAttendance", "summary", "checkInTime", "hoursWorked", "todayStatus", "weeklyHours", "monthlyAttendance", "realtimeUpdates", "connected", "loading", "useAuth", "user", "first_name", "last_name", "employee_id", "isAuthenticated", "isLoading", "usePathname", "useRouter", "push", "fn", "replace", "back", "describe", "beforeEach", "Object", "defineProperty", "window", "writable", "configurable", "value", "it", "render", "StaffDashboard", "expect", "screen", "getByText", "toBeInTheDocument", "container", "mainContainer", "querySelector", "statsGrid", "responsiveElements", "querySelectorAll", "length", "toBeGreaterThan"], "mappings": ";AAKA,kCAAkC;AAClCA,KAAKC,IAAI,CAAC,gCAAgC,IAAO,CAAA;QAC/CC,sBAAsB,IAAO,CAAA;gBAC3BC,SAAS;oBACPC,aAAa;oBACbC,aAAa;oBACbC,aAAa;oBACbC,aAAa;oBACbC,mBAAmB;gBACrB;gBACAC,iBAAiB,EAAE;gBACnBC,WAAW;gBACXC,SAAS;YACX,CAAA;IACF,CAAA;AAEAX,KAAKC,IAAI,CAAC,4BAA4B,IAAO,CAAA;QAC3CW,SAAS,IAAO,CAAA;gBACdC,MAAM;oBACJC,YAAY;oBACZC,WAAW;oBACXC,aAAa;gBACf;gBACAC,iBAAiB;gBACjBC,WAAW;YACb,CAAA;IACF,CAAA;AAEA,sBAAsB;AACtBlB,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCkB,aAAa,IAAM;QACnBC,WAAW,IAAO,CAAA;gBAChBC,MAAMrB,KAAKsB,EAAE;gBACbC,SAASvB,KAAKsB,EAAE;gBAChBE,MAAMxB,KAAKsB,EAAE;YACf,CAAA;IACF,CAAA;;;;;8DAzCkB;wBACa;QACxB;6DACoB;;;;;;AAwC3BG,SAAS,qCAAqC;IAC5CC,WAAW;QACT,gCAAgC;QAChCC,OAAOC,cAAc,CAACC,QAAQ,cAAc;YAC1CC,UAAU;YACVC,cAAc;YACdC,OAAO;QACT;QACAL,OAAOC,cAAc,CAACC,QAAQ,eAAe;YAC3CC,UAAU;YACVC,cAAc;YACdC,OAAO;QACT;IACF;IAEAC,GAAG,uDAAuD;QACxDC,IAAAA,cAAM,gBAAC,qBAACC,aAAc;QAEtB,qCAAqC;QACrCC,OAAOC,cAAM,CAACC,SAAS,CAAC,SAASC,iBAAiB;QAClDH,OAAOC,cAAM,CAACC,SAAS,CAAC,SAASC,iBAAiB;QAElD,mCAAmC;QACnCH,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;QAC3DH,OAAOC,cAAM,CAACC,SAAS,CAAC,iBAAiBC,iBAAiB;QAC1DH,OAAOC,cAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;QAC7DH,OAAOC,cAAM,CAACC,SAAS,CAAC,iBAAiBC,iBAAiB;QAE1D,yCAAyC;QACzCH,OAAOC,cAAM,CAACC,SAAS,CAAC,uBAAuBC,iBAAiB;QAEhE,8CAA8C;QAC9CH,OAAOC,cAAM,CAACC,SAAS,CAAC,oBAAoBC,iBAAiB;QAE7D,4CAA4C;QAC5CH,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;IAC7D;IAEAN,GAAG,4CAA4C;QAC7C,MAAM,EAAEO,SAAS,EAAE,GAAGN,IAAAA,cAAM,gBAAC,qBAACC,aAAc;QAE5C,uCAAuC;QACvC,MAAMM,gBAAgBD,UAAUE,aAAa,CAAC;QAC9CN,OAAOK,eAAeF,iBAAiB;QAEvC,oCAAoC;QACpC,MAAMI,YAAYH,UAAUE,aAAa,CAAC;QAC1CN,OAAOO,WAAWJ,iBAAiB;IACrC;IAEAN,GAAG,sCAAsC;QACvCC,IAAAA,cAAM,gBAAC,qBAACC,aAAc;QAEtBC,OAAOC,cAAM,CAACC,SAAS,CAAC,sBAAsBC,iBAAiB;IACjE;IAEAN,GAAG,sBAAsB;QACvBC,IAAAA,cAAM,gBAAC,qBAACC,aAAc;QAEtBC,OAAOC,cAAM,CAACC,SAAS,CAAC,iBAAiBC,iBAAiB;IAC5D;IAEAN,GAAG,0DAA0D;QAC3D,MAAM,EAAEO,SAAS,EAAE,GAAGN,IAAAA,cAAM,gBAAC,qBAACC,aAAc;QAE5C,gEAAgE;QAChE,MAAMS,qBAAqBJ,UAAUK,gBAAgB,CAAC;QACtDT,OAAOQ,mBAAmBE,MAAM,EAAEC,eAAe,CAAC;IACpD;AACF"}