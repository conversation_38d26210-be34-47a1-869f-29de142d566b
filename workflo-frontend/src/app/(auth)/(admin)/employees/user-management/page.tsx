'use client';

import React, { useState, useEffect } from 'react';
import { 
  UserCog, 
  Plus, 
  Search, 
  Filter,
  Users, 
  Shield,
  Key,
  Settings,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Mail,
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle,
  Lock,
  Unlock,
  UserCheck,
  UserX
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials, formatDate } from '@/lib/utils';

interface SystemUser {
  id: number;
  employee_id?: string;
  first_name: string;
  last_name: string;
  email: string;
  username: string;
  role: 'admin' | 'hr' | 'supervisor' | 'accountant' | 'employee';
  department?: string;
  status: 'active' | 'inactive' | 'locked' | 'pending';
  last_login?: string;
  created_at: string;
  permissions: string[];
  two_factor_enabled: boolean;
  password_last_changed: string;
  login_attempts: number;
}

const UserManagementPage: React.FC = () => {
  const { user } = useAuth();
  const [users, setUsers] = useState<SystemUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterRole, setFilterRole] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Mock data
  const mockUsers: SystemUser[] = [
    {
      id: 1,
      employee_id: 'EMP001',
      first_name: 'Admin',
      last_name: 'User',
      email: '<EMAIL>',
      username: 'admin',
      role: 'admin',
      department: 'IT',
      status: 'active',
      last_login: '2024-01-15T10:30:00Z',
      created_at: '2023-01-01T00:00:00Z',
      permissions: ['all'],
      two_factor_enabled: true,
      password_last_changed: '2023-12-01T00:00:00Z',
      login_attempts: 0
    },
    {
      id: 2,
      employee_id: 'EMP003',
      first_name: 'Sarah',
      last_name: 'Johnson',
      email: '<EMAIL>',
      username: 'sarah.johnson',
      role: 'hr',
      department: 'Human Resources',
      status: 'active',
      last_login: '2024-01-14T16:45:00Z',
      created_at: '2022-03-15T00:00:00Z',
      permissions: ['employee_management', 'recruitment', 'training'],
      two_factor_enabled: true,
      password_last_changed: '2023-11-15T00:00:00Z',
      login_attempts: 0
    },
    {
      id: 3,
      employee_id: 'EMP002',
      first_name: 'John',
      last_name: 'Smith',
      email: '<EMAIL>',
      username: 'john.smith',
      role: 'supervisor',
      department: 'Engineering',
      status: 'active',
      last_login: '2024-01-15T09:15:00Z',
      created_at: '2022-06-01T00:00:00Z',
      permissions: ['team_management', 'reports'],
      two_factor_enabled: false,
      password_last_changed: '2023-10-01T00:00:00Z',
      login_attempts: 0
    },
    {
      id: 4,
      employee_id: 'EMP004',
      first_name: 'David',
      last_name: 'Wilson',
      email: '<EMAIL>',
      username: 'david.wilson',
      role: 'accountant',
      department: 'Finance',
      status: 'active',
      last_login: '2024-01-12T14:20:00Z',
      created_at: '2023-02-01T00:00:00Z',
      permissions: ['financial_data', 'payroll'],
      two_factor_enabled: true,
      password_last_changed: '2023-12-15T00:00:00Z',
      login_attempts: 0
    },
    {
      id: 5,
      employee_id: 'EMP006',
      first_name: 'Michael',
      last_name: 'Brown',
      email: '<EMAIL>',
      username: 'michael.brown',
      role: 'employee',
      department: 'Marketing',
      status: 'locked',
      last_login: '2024-01-10T11:30:00Z',
      created_at: '2023-05-01T00:00:00Z',
      permissions: ['basic_access'],
      two_factor_enabled: false,
      password_last_changed: '2023-08-01T00:00:00Z',
      login_attempts: 5
    }
  ];

  useEffect(() => {
    const loadUsers = async () => {
      setLoading(true);
      setTimeout(() => {
        setUsers(mockUsers);
        setLoading(false);
      }, 1000);
    };

    loadUsers();
  }, []);

  const filteredUsers = users.filter(user => {
    const matchesSearch = 
      user.first_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.last_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.username.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesRole = !filterRole || user.role === filterRole;
    const matchesStatus = !filterStatus || user.status === filterStatus;
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  const totalUsers = users.length;
  const activeUsers = users.filter(u => u.status === 'active').length;
  const lockedUsers = users.filter(u => u.status === 'locked').length;
  const usersWithTwoFactor = users.filter(u => u.two_factor_enabled).length;

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800';
      case 'hr': return 'bg-blue-100 text-blue-800';
      case 'supervisor': return 'bg-purple-100 text-purple-800';
      case 'accountant': return 'bg-green-100 text-green-800';
      case 'employee': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'locked': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleCreateUser = () => {
    setShowCreateModal(true);
  };

  const handleLockUser = (userId: number) => {
    setUsers(prev => prev.map(u => 
      u.id === userId ? { ...u, status: 'locked' as const } : u
    ));
  };

  const handleUnlockUser = (userId: number) => {
    setUsers(prev => prev.map(u => 
      u.id === userId ? { ...u, status: 'active' as const, login_attempts: 0 } : u
    ));
  };

  const handleResetPassword = (userId: number) => {
    // Simulate password reset
    console.log('Password reset for user:', userId);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
            <p className="text-gray-600 mt-1">
              Manage system users, roles, permissions, and security settings
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <Button variant="primary" onClick={handleCreateUser}>
              <Plus className="h-4 w-4 mr-2" />
              Create User
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Users</p>
                <p className="text-2xl font-semibold text-gray-900">{totalUsers}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Users</p>
                <p className="text-2xl font-semibold text-gray-900">{activeUsers}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Lock className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Locked Users</p>
                <p className="text-2xl font-semibold text-gray-900">{lockedUsers}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Shield className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">2FA Enabled</p>
                <p className="text-2xl font-semibold text-gray-900">{usersWithTwoFactor}</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <div className="w-full sm:w-80">
              <Input
                placeholder="Search users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={<Search className="h-4 w-4" />}
              />
            </div>
            
            <select
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="">All Roles</option>
              <option value="admin">Admin</option>
              <option value="hr">HR</option>
              <option value="supervisor">Supervisor</option>
              <option value="accountant">Accountant</option>
              <option value="employee">Employee</option>
            </select>
            
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="locked">Locked</option>
              <option value="pending">Pending</option>
            </select>
          </div>
          
          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-600">
              {filteredUsers.length} users
            </span>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="p-6">
            <div className="animate-pulse space-y-4">
              {Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Login
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Security
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredUsers.map((user) => (
                  <UserTableRow 
                    key={user.id} 
                    user={user} 
                    onLock={() => handleLockUser(user.id)}
                    onUnlock={() => handleUnlockUser(user.id)}
                    onResetPassword={() => handleResetPassword(user.id)}
                  />
                ))}
              </tbody>
            </table>
          </div>
        )}

        {!loading && filteredUsers.length === 0 && (
          <div className="text-center py-12">
            <UserCog className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No users found
            </h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || filterRole || filterStatus 
                ? 'Try adjusting your search or filter criteria.' 
                : 'Get started by creating your first user.'
              }
            </p>
            <Button variant="primary" onClick={handleCreateUser}>
              <Plus className="h-4 w-4 mr-2" />
              Create User
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

// User Table Row Component
interface UserTableRowProps {
  user: SystemUser;
  onLock: () => void;
  onUnlock: () => void;
  onResetPassword: () => void;
}

const UserTableRow: React.FC<UserTableRowProps> = ({ user, onLock, onUnlock, onResetPassword }) => {
  const [showActions, setShowActions] = useState(false);
  const userInitials = getInitials(user.first_name, user.last_name);

  return (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center mr-3">
            <span className="text-white text-sm font-medium">{userInitials}</span>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-900">
              {user.first_name} {user.last_name}
            </div>
            <div className="text-sm text-gray-500">{user.email}</div>
            <div className="text-sm text-gray-500">@{user.username}</div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={cn(
          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
          user.role === 'admin' ? 'bg-red-100 text-red-800' :
          user.role === 'hr' ? 'bg-blue-100 text-blue-800' :
          user.role === 'supervisor' ? 'bg-purple-100 text-purple-800' :
          user.role === 'accountant' ? 'bg-green-100 text-green-800' :
          'bg-gray-100 text-gray-800'
        )}>
          {user.role.toUpperCase()}
        </span>
        <div className="text-sm text-gray-500 mt-1">{user.department}</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={cn(
          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
          user.status === 'active' ? 'bg-green-100 text-green-800' :
          user.status === 'inactive' ? 'bg-gray-100 text-gray-800' :
          user.status === 'locked' ? 'bg-red-100 text-red-800' :
          'bg-yellow-100 text-yellow-800'
        )}>
          {user.status}
        </span>
        {user.login_attempts > 0 && (
          <div className="text-xs text-red-600 mt-1">
            {user.login_attempts} failed attempts
          </div>
        )}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {user.last_login ? formatDate(user.last_login) : 'Never'}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center space-x-2">
          {user.two_factor_enabled ? (
            <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
              <Shield className="h-3 w-3 mr-1" />
              2FA
            </span>
          ) : (
            <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
              <AlertCircle className="h-3 w-3 mr-1" />
              No 2FA
            </span>
          )}
        </div>
        <div className="text-xs text-gray-500 mt-1">
          Password: {formatDate(user.password_last_changed)}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="relative">
          <button
            onClick={() => setShowActions(!showActions)}
            className="p-1 rounded-full hover:bg-gray-100"
          >
            <MoreHorizontal className="h-4 w-4 text-gray-500" />
          </button>

          {showActions && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
              <div className="py-1">
                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  <Eye className="h-4 w-4 mr-2" />
                  View Profile
                </button>
                <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit User
                </button>
                <button 
                  onClick={onResetPassword}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <Key className="h-4 w-4 mr-2" />
                  Reset Password
                </button>
                {user.status === 'locked' ? (
                  <button 
                    onClick={onUnlock}
                    className="flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-green-50"
                  >
                    <Unlock className="h-4 w-4 mr-2" />
                    Unlock User
                  </button>
                ) : (
                  <button 
                    onClick={onLock}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                  >
                    <Lock className="h-4 w-4 mr-2" />
                    Lock User
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  );
};

export default UserManagementPage;
