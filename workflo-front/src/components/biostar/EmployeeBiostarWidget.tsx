'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  <PERSON>gerprint,
  Eye,
  Wifi,
  WifiOff,
  Clock,
  CheckCircle,
  AlertCircle,
  Activity,
  Monitor,
  ArrowRight,
  RefreshCw
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { cn, formatDate } from '@/lib/utils';
import { useBiostarAttendance } from '@/hooks/useBiostarAttendance';
import { BiometricEvent } from '@/types';

interface EmployeeBiostarWidgetProps {
  employeeId?: string;
  showFullDetails?: boolean;
  className?: string;
}

const EmployeeBiostarWidget: React.FC<EmployeeBiostarWidgetProps> = ({
  employeeId,
  showFullDetails = false,
  className
}) => {
  const [recentEvents, setRecentEvents] = useState<BiometricEvent[]>([]);
  const [loading, setLoading] = useState(true);

  const {
    todayAttendance,
    devices,
    connected: biostarConnected,
    error: connectionError,
    summary
  } = useBiostarAttendance({
    employeeId,
    autoRefresh: true,
    enableRealTime: true
  });

  useEffect(() => {
    // Simulate loading recent events
    setLoading(false);
    
    // Mock recent events - in real app, this would come from the hook
    setRecentEvents([
      {
        id: '1',
        user_id: employeeId || '',
        device_id: 'dev-001',
        event_type: 'ENTRY',
        datetime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        user_name: 'Current User',
        device_name: 'Main Entrance'
      },
      {
        id: '2',
        user_id: employeeId || '',
        device_id: 'dev-002',
        event_type: 'EXIT',
        datetime: new Date(Date.now() - 10 * 60 * 60 * 1000).toISOString(), // 10 hours ago
        user_name: 'Current User',
        device_name: 'Main Entrance'
      }
    ]);
  }, [employeeId]);

  const getEventTypeIcon = (eventType: string) => {
    switch (eventType) {
      case 'ENTRY': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'EXIT': return <Activity className="h-4 w-4 text-blue-500" />;
      case 'DENIED': return <AlertCircle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getEventTypeColor = (eventType: string) => {
    switch (eventType) {
      case 'ENTRY': return 'bg-green-100 text-green-800';
      case 'EXIT': return 'bg-blue-100 text-blue-800';
      case 'DENIED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <div className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-600">Loading BioStar data...</span>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Fingerprint className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">BioStar Profile</h3>
              <div className="flex items-center space-x-2">
                {biostarConnected ? (
                  <Wifi className="h-3 w-3 text-green-500" />
                ) : (
                  <WifiOff className="h-3 w-3 text-red-500" />
                )}
                <span className="text-xs text-gray-600">
                  {biostarConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
            </div>
          </div>
          <Link href="/staff/info/biostar">
            <Button variant="secondary" size="sm">
              <ArrowRight className="h-4 w-4" />
            </Button>
          </Link>
        </div>

        {/* Connection Error */}
        {connectionError && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
              <span className="text-sm text-red-700">Connection Error</span>
            </div>
          </div>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <Fingerprint className="h-4 w-4 text-blue-600" />
            </div>
            <div className="text-lg font-bold text-gray-900">2</div>
            <div className="text-xs text-gray-600">Fingerprints</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <Eye className="h-4 w-4 text-purple-600" />
            </div>
            <div className="text-lg font-bold text-gray-900">1</div>
            <div className="text-xs text-gray-600">Face Template</div>
          </div>
        </div>

        {/* Today's Status */}
        {todayAttendance && (
          <div className="mb-6 p-3 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-900">Today's Status</h4>
                <p className="text-sm text-gray-600">
                  {todayAttendance.first_in ? 
                    `Checked in at ${new Date(todayAttendance.first_in).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}` :
                    'Not checked in'
                  }
                </p>
              </div>
              <span className={cn(
                'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                todayAttendance.status === 'PRESENT' ? 'bg-green-100 text-green-800' :
                todayAttendance.status === 'LATE' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              )}>
                {todayAttendance.status}
              </span>
            </div>
          </div>
        )}

        {/* Recent Access Events */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">Recent Access</h4>
          {recentEvents.length === 0 ? (
            <p className="text-sm text-gray-600 italic">No recent access events</p>
          ) : (
            <div className="space-y-2">
              {recentEvents.slice(0, showFullDetails ? 5 : 2).map((event, index) => (
                <div key={`${event.id}-${index}`} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div className="flex items-center space-x-2">
                    {getEventTypeIcon(event.event_type)}
                    <div>
                      <span className="text-sm font-medium text-gray-900">
                        {event.event_type === 'ENTRY' ? 'Check In' : 
                         event.event_type === 'EXIT' ? 'Check Out' : 'Access Denied'}
                      </span>
                      <div className="text-xs text-gray-600">
                        {formatDate(event.datetime, 'MMM dd, HH:mm')}
                      </div>
                    </div>
                  </div>
                  <span className={cn(
                    'inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium',
                    getEventTypeColor(event.event_type)
                  )}>
                    {event.event_type}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Device Status */}
        {devices.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Devices Online</span>
              <div className="flex items-center space-x-1">
                <Monitor className="h-3 w-3 text-gray-500" />
                <span className="text-sm font-medium">
                  {devices.filter(d => d.status === 'ONLINE').length}/{devices.length}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* View Full Profile Link */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <Link 
            href="/staff/info/biostar"
            className="block w-full text-center py-2 text-sm text-purple-600 hover:text-purple-700 font-medium"
          >
            View Full BioStar Profile →
          </Link>
        </div>
      </div>
    </Card>
  );
};

export default EmployeeBiostarWidget;
