{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/hooks/useBiostarAttendance.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { attendanceService } from '@/lib/attendanceService';\nimport { AttendanceRecord, RealTimeAttendance, BiometricDevice } from '@/types';\n\ninterface UseBiostarAttendanceOptions {\n  employeeId?: string;\n  autoRefresh?: boolean;\n  refreshInterval?: number;\n  enableRealTime?: boolean;\n}\n\ninterface UseBiostarAttendanceReturn {\n  // Data\n  todayAttendance: AttendanceRecord | null;\n  attendanceRecords: AttendanceRecord[];\n  devices: BiometricDevice[];\n  realtimeUpdates: RealTimeAttendance[];\n\n  // Status\n  loading: boolean;\n  connected: boolean;\n  error: string | null;\n\n  // Actions\n  refresh: () => Promise<void>;\n  getAttendanceRange: (startDate: string, endDate: string) => Promise<AttendanceRecord[]>;\n\n  // Summary data\n  summary: {\n    todayStatus: 'PRESENT' | 'ABSENT' | 'LATE' | 'EARLY_OUT';\n    checkInTime?: string;\n    checkOutTime?: string;\n    hoursWorked: number;\n    breakTime: number;\n    overtime: number;\n    weeklyHours: number;\n    monthlyAttendance: number;\n  } | null;\n}\n\nexport const useBiostarAttendance = (\n  options: UseBiostarAttendanceOptions = {}\n): UseBiostarAttendanceReturn => {\n  const {\n    employeeId,\n    autoRefresh = true,\n    refreshInterval = 300000, // 5 minutes\n    enableRealTime = true\n  } = options;\n\n  // State\n  const [todayAttendance, setTodayAttendance] = useState<AttendanceRecord | null>(null);\n  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);\n  const [devices, setDevices] = useState<BiometricDevice[]>([]);\n  const [realtimeUpdates, setRealtimeUpdates] = useState<RealTimeAttendance[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [connected, setConnected] = useState(true); // Always connected for mock data\n  const [error, setError] = useState<string | null>(null);\n  const [summary, setSummary] = useState<any>(null);\n\n  // Load attendance data (using mock data instead of BioStar API)\n  const loadAttendanceData = useCallback(async () => {\n    if (!employeeId) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n\n      // Mock today's attendance\n      const today: AttendanceRecord = {\n        id: 'att-today',\n        employee_id: employeeId,\n        employee_name: 'Current User',\n        date: new Date().toISOString().split('T')[0],\n        first_in: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago\n        last_out: null,\n        total_hours: 2,\n        break_time: 0,\n        overtime: 0,\n        status: 'PRESENT',\n        events: [\n          {\n            id: 'event-1',\n            user_id: employeeId,\n            device_id: 'dev-001',\n            event_type: 'ENTRY',\n            datetime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n            user_name: 'Current User',\n            device_name: 'Main Entrance'\n          }\n        ],\n        biostar_synced: true\n      };\n      setTodayAttendance(today);\n\n      // Mock attendance summary\n      const summaryData = {\n        todayStatus: 'PRESENT' as const,\n        checkInTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n        hoursWorked: 2,\n        breakTime: 0,\n        overtime: 0,\n        weeklyHours: 32,\n        monthlyAttendance: 20\n      };\n      setSummary(summaryData);\n\n      // Mock recent attendance records\n      const records: AttendanceRecord[] = Array.from({ length: 10 }, (_, i) => ({\n        id: `att-${i}`,\n        employee_id: employeeId,\n        employee_name: 'Current User',\n        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n        first_in: new Date(Date.now() - i * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000).toISOString(),\n        last_out: new Date(Date.now() - i * 24 * 60 * 60 * 1000 + 17 * 60 * 60 * 1000).toISOString(),\n        total_hours: 8,\n        break_time: 1,\n        overtime: 0,\n        status: i === 0 ? 'PRESENT' : 'PRESENT',\n        events: [],\n        biostar_synced: true\n      }));\n      setAttendanceRecords(records);\n\n      // Mock devices\n      const deviceList: BiometricDevice[] = [\n        {\n          id: 'dev-001',\n          name: 'Main Entrance',\n          ip: '*************',\n          port: 8080,\n          status: 'ONLINE',\n          type: 'Fingerprint Scanner',\n          location: 'Main Building'\n        },\n        {\n          id: 'dev-002',\n          name: 'Office Floor',\n          ip: '*************',\n          port: 8080,\n          status: 'ONLINE',\n          type: 'Face Recognition',\n          location: 'Second Floor'\n        },\n        {\n          id: 'dev-003',\n          name: 'Back Entrance',\n          ip: '*************',\n          port: 8080,\n          status: 'OFFLINE',\n          type: 'Fingerprint Scanner',\n          location: 'Back Building'\n        }\n      ];\n      setDevices(deviceList);\n\n      setConnected(true);\n    } catch (err) {\n      console.error('Failed to load attendance data:', err);\n      setError(err instanceof Error ? err.message : 'Failed to load attendance data');\n      setConnected(false);\n    } finally {\n      setLoading(false);\n    }\n  }, [employeeId]);\n\n  // Refresh function\n  const refresh = useCallback(async () => {\n    await loadAttendanceData();\n  }, [loadAttendanceData]);\n\n  // Get attendance range (using mock data)\n  const getAttendanceRange = useCallback(async (startDate: string, endDate: string) => {\n    if (!employeeId) return [];\n\n    try {\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 300));\n\n      // Generate mock records for the date range\n      const start = new Date(startDate);\n      const end = new Date(endDate);\n      const records: AttendanceRecord[] = [];\n\n      for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {\n        // Skip weekends\n        if (d.getDay() === 0 || d.getDay() === 6) continue;\n\n        records.push({\n          id: `att-${d.toISOString().split('T')[0]}`,\n          employee_id: employeeId,\n          employee_name: 'Current User',\n          date: d.toISOString().split('T')[0],\n          first_in: new Date(d.getTime() + 8 * 60 * 60 * 1000).toISOString(),\n          last_out: new Date(d.getTime() + 17 * 60 * 60 * 1000).toISOString(),\n          total_hours: 8,\n          break_time: 1,\n          overtime: 0,\n          status: 'PRESENT',\n          events: [],\n          biostar_synced: true\n        });\n      }\n\n      return records;\n    } catch (err) {\n      console.error('Failed to get attendance range:', err);\n      setError(err instanceof Error ? err.message : 'Failed to get attendance range');\n      return [];\n    }\n  }, [employeeId]);\n\n  // Real-time updates handler\n  const handleRealTimeUpdate = useCallback((update: RealTimeAttendance) => {\n    setRealtimeUpdates(prev => {\n      // Add new update and keep only last 10\n      const newUpdates = [update, ...prev];\n      return newUpdates.slice(0, 10);\n    });\n\n    // If it's for current employee, refresh today's data\n    if (update.employee_id === employeeId) {\n      // Debounce the refresh to avoid too many calls\n      setTimeout(() => {\n        loadAttendanceData();\n      }, 2000);\n    }\n  }, [employeeId, loadAttendanceData]);\n\n  // Initial load\n  useEffect(() => {\n    if (employeeId) {\n      loadAttendanceData();\n    }\n  }, [employeeId, loadAttendanceData]);\n\n  // Auto refresh\n  useEffect(() => {\n    if (!autoRefresh || !employeeId) return;\n\n    const interval = setInterval(() => {\n      loadAttendanceData();\n    }, refreshInterval);\n\n    return () => clearInterval(interval);\n  }, [autoRefresh, refreshInterval, employeeId, loadAttendanceData]);\n\n  // Real-time monitoring (disabled - using mock data)\n  useEffect(() => {\n    if (!enableRealTime) return;\n\n    // Generate mock real-time updates periodically\n    const interval = setInterval(() => {\n      const mockUpdate: RealTimeAttendance = {\n        id: `update-${Date.now()}`,\n        employee_id: `EMP${Math.floor(Math.random() * 100).toString().padStart(3, '0')}`,\n        employee_name: `Employee ${Math.floor(Math.random() * 100)}`,\n        event_type: Math.random() > 0.5 ? 'ENTRY' : 'EXIT',\n        device_name: ['Main Entrance', 'Office Floor', 'Back Entrance'][Math.floor(Math.random() * 3)],\n        timestamp: new Date().toISOString(),\n        status: 'PRESENT'\n      };\n\n      handleRealTimeUpdate(mockUpdate);\n    }, 30000); // Every 30 seconds\n\n    return () => clearInterval(interval);\n  }, [enableRealTime, handleRealTimeUpdate]);\n\n  return {\n    // Data\n    todayAttendance,\n    attendanceRecords,\n    devices,\n    realtimeUpdates,\n\n    // Status\n    loading,\n    connected,\n    error,\n\n    // Actions\n    refresh,\n    getAttendanceRange,\n\n    // Summary\n    summary\n  };\n};\n\n// Hook for device monitoring (using mock data)\nexport const useBiostarDevices = () => {\n  const [devices, setDevices] = useState<BiometricDevice[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const loadDevices = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 300));\n\n      // Mock device list\n      const deviceList: BiometricDevice[] = [\n        {\n          id: 'dev-001',\n          name: 'Main Entrance',\n          ip: '*************',\n          port: 8080,\n          status: 'ONLINE',\n          type: 'Fingerprint Scanner',\n          location: 'Main Building'\n        },\n        {\n          id: 'dev-002',\n          name: 'Office Floor',\n          ip: '*************',\n          port: 8080,\n          status: 'ONLINE',\n          type: 'Face Recognition',\n          location: 'Second Floor'\n        },\n        {\n          id: 'dev-003',\n          name: 'Back Entrance',\n          ip: '*************',\n          port: 8080,\n          status: Math.random() > 0.5 ? 'ONLINE' : 'OFFLINE', // Random status\n          type: 'Fingerprint Scanner',\n          location: 'Back Building'\n        },\n        {\n          id: 'dev-004',\n          name: 'Conference Room',\n          ip: '*************',\n          port: 8080,\n          status: 'ONLINE',\n          type: 'Card Reader',\n          location: 'Third Floor'\n        }\n      ];\n\n      setDevices(deviceList);\n    } catch (err) {\n      console.error('Failed to load devices:', err);\n      setError(err instanceof Error ? err.message : 'Failed to load devices');\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  useEffect(() => {\n    loadDevices();\n\n    // Refresh devices every 2 minutes\n    const interval = setInterval(loadDevices, 120000);\n    return () => clearInterval(interval);\n  }, [loadDevices]);\n\n  return {\n    devices,\n    loading,\n    error,\n    refresh: loadDevices,\n    onlineDevices: devices.filter(d => d.status === 'ONLINE'),\n    offlineDevices: devices.filter(d => d.status === 'OFFLINE')\n  };\n};\n\n// Hook for real-time monitoring only\nexport const useBiostarRealTime = () => {\n  const [updates, setUpdates] = useState<RealTimeAttendance[]>([]);\n  const [isMonitoring, setIsMonitoring] = useState(false);\n\n  const startMonitoring = useCallback(() => {\n    if (isMonitoring) return;\n\n    const handleUpdate = (update: RealTimeAttendance) => {\n      setUpdates(prev => [update, ...prev.slice(0, 19)]); // Keep last 20 updates\n    };\n\n    attendanceService.startRealTimeMonitoring(handleUpdate);\n    setIsMonitoring(true);\n\n    return () => {\n      attendanceService.stopRealTimeMonitoring(handleUpdate);\n      setIsMonitoring(false);\n    };\n  }, [isMonitoring]);\n\n  const stopMonitoring = useCallback(() => {\n    attendanceService.stopRealTimeMonitoring();\n    setIsMonitoring(false);\n  }, []);\n\n  const clearUpdates = useCallback(() => {\n    setUpdates([]);\n  }, []);\n\n  return {\n    updates,\n    isMonitoring,\n    startMonitoring,\n    stopMonitoring,\n    clearUpdates\n  };\n};\n"], "names": ["useBiostarAttendance", "useBiostarDevices", "useBiostarRealTime", "options", "employeeId", "autoRefresh", "refreshInterval", "enableRealTime", "todayAttendance", "setTodayAttendance", "useState", "attendanceRecords", "setAttendanceRecords", "devices", "setDevices", "realtimeUpdates", "setRealtimeUpdates", "loading", "setLoading", "connected", "setConnected", "error", "setError", "summary", "set<PERSON>ummary", "loadAttendanceData", "useCallback", "Promise", "resolve", "setTimeout", "today", "id", "employee_id", "employee_name", "date", "Date", "toISOString", "split", "first_in", "now", "last_out", "total_hours", "break_time", "overtime", "status", "events", "user_id", "device_id", "event_type", "datetime", "user_name", "device_name", "biostar_synced", "summaryData", "todayStatus", "checkInTime", "hoursWorked", "breakTime", "weeklyHours", "monthlyAttendance", "records", "Array", "from", "length", "_", "i", "deviceList", "name", "ip", "port", "type", "location", "err", "console", "Error", "message", "refresh", "getAttendanceRange", "startDate", "endDate", "start", "end", "d", "setDate", "getDate", "getDay", "push", "getTime", "handleRealTimeUpdate", "update", "prev", "newUpdates", "slice", "useEffect", "interval", "setInterval", "clearInterval", "mockUpdate", "Math", "floor", "random", "toString", "padStart", "timestamp", "loadDevices", "onlineDevices", "filter", "offlineDevices", "updates", "setUpdates", "isMonitoring", "setIsMonitoring", "startMonitoring", "handleUpdate", "attendanceService", "startRealTimeMonitoring", "stopRealTimeMonitoring", "stopMonitoring", "clearUpdates"], "mappings": ";;;;;;;;;;;IAwCaA,oBAAoB;eAApBA;;IA8PAC,iBAAiB;eAAjBA;;IAiFAC,kBAAkB;eAAlBA;;;uBAvXoC;mCACf;AAuC3B,MAAMF,uBAAuB,CAClCG,UAAuC,CAAC,CAAC;IAEzC,MAAM,EACJC,UAAU,EACVC,cAAc,IAAI,EAClBC,kBAAkB,MAAM,EACxBC,iBAAiB,IAAI,EACtB,GAAGJ;IAEJ,QAAQ;IACR,MAAM,CAACK,iBAAiBC,mBAAmB,GAAGC,IAAAA,eAAQ,EAA0B;IAChF,MAAM,CAACC,mBAAmBC,qBAAqB,GAAGF,IAAAA,eAAQ,EAAqB,EAAE;IACjF,MAAM,CAACG,SAASC,WAAW,GAAGJ,IAAAA,eAAQ,EAAoB,EAAE;IAC5D,MAAM,CAACK,iBAAiBC,mBAAmB,GAAGN,IAAAA,eAAQ,EAAuB,EAAE;IAC/E,MAAM,CAACO,SAASC,WAAW,GAAGR,IAAAA,eAAQ,EAAC;IACvC,MAAM,CAACS,WAAWC,aAAa,GAAGV,IAAAA,eAAQ,EAAC,OAAO,iCAAiC;IACnF,MAAM,CAACW,OAAOC,SAAS,GAAGZ,IAAAA,eAAQ,EAAgB;IAClD,MAAM,CAACa,SAASC,WAAW,GAAGd,IAAAA,eAAQ,EAAM;IAE5C,gEAAgE;IAChE,MAAMe,qBAAqBC,IAAAA,kBAAW,EAAC;QACrC,IAAI,CAACtB,YAAY;QAEjB,IAAI;YACFc,WAAW;YACXI,SAAS;YAET,qBAAqB;YACrB,MAAM,IAAIK,QAAQC,CAAAA,UAAWC,WAAWD,SAAS;YAEjD,0BAA0B;YAC1B,MAAME,QAA0B;gBAC9BC,IAAI;gBACJC,aAAa5B;gBACb6B,eAAe;gBACfC,MAAM,IAAIC,OAAOC,WAAW,GAAGC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC5CC,UAAU,IAAIH,KAAKA,KAAKI,GAAG,KAAK,IAAI,KAAK,KAAK,MAAMH,WAAW;gBAC/DI,UAAU;gBACVC,aAAa;gBACbC,YAAY;gBACZC,UAAU;gBACVC,QAAQ;gBACRC,QAAQ;oBACN;wBACEd,IAAI;wBACJe,SAAS1C;wBACT2C,WAAW;wBACXC,YAAY;wBACZC,UAAU,IAAId,KAAKA,KAAKI,GAAG,KAAK,IAAI,KAAK,KAAK,MAAMH,WAAW;wBAC/Dc,WAAW;wBACXC,aAAa;oBACf;iBACD;gBACDC,gBAAgB;YAClB;YACA3C,mBAAmBqB;YAEnB,0BAA0B;YAC1B,MAAMuB,cAAc;gBAClBC,aAAa;gBACbC,aAAa,IAAIpB,KAAKA,KAAKI,GAAG,KAAK,IAAI,KAAK,KAAK,MAAMH,WAAW;gBAClEoB,aAAa;gBACbC,WAAW;gBACXd,UAAU;gBACVe,aAAa;gBACbC,mBAAmB;YACrB;YACAnC,WAAW6B;YAEX,iCAAiC;YACjC,MAAMO,UAA8BC,MAAMC,IAAI,CAAC;gBAAEC,QAAQ;YAAG,GAAG,CAACC,GAAGC,IAAO,CAAA;oBACxElC,IAAI,CAAC,IAAI,EAAEkC,GAAG;oBACdjC,aAAa5B;oBACb6B,eAAe;oBACfC,MAAM,IAAIC,KAAKA,KAAKI,GAAG,KAAK0B,IAAI,KAAK,KAAK,KAAK,MAAM7B,WAAW,GAAGC,KAAK,CAAC,IAAI,CAAC,EAAE;oBAChFC,UAAU,IAAIH,KAAKA,KAAKI,GAAG,KAAK0B,IAAI,KAAK,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,MAAM7B,WAAW;oBACzFI,UAAU,IAAIL,KAAKA,KAAKI,GAAG,KAAK0B,IAAI,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,MAAM7B,WAAW;oBAC1FK,aAAa;oBACbC,YAAY;oBACZC,UAAU;oBACVC,QAAQqB,MAAM,IAAI,YAAY;oBAC9BpB,QAAQ,EAAE;oBACVO,gBAAgB;gBAClB,CAAA;YACAxC,qBAAqBgD;YAErB,eAAe;YACf,MAAMM,aAAgC;gBACpC;oBACEnC,IAAI;oBACJoC,MAAM;oBACNC,IAAI;oBACJC,MAAM;oBACNzB,QAAQ;oBACR0B,MAAM;oBACNC,UAAU;gBACZ;gBACA;oBACExC,IAAI;oBACJoC,MAAM;oBACNC,IAAI;oBACJC,MAAM;oBACNzB,QAAQ;oBACR0B,MAAM;oBACNC,UAAU;gBACZ;gBACA;oBACExC,IAAI;oBACJoC,MAAM;oBACNC,IAAI;oBACJC,MAAM;oBACNzB,QAAQ;oBACR0B,MAAM;oBACNC,UAAU;gBACZ;aACD;YACDzD,WAAWoD;YAEX9C,aAAa;QACf,EAAE,OAAOoD,KAAK;YACZC,QAAQpD,KAAK,CAAC,mCAAmCmD;YACjDlD,SAASkD,eAAeE,QAAQF,IAAIG,OAAO,GAAG;YAC9CvD,aAAa;QACf,SAAU;YACRF,WAAW;QACb;IACF,GAAG;QAACd;KAAW;IAEf,mBAAmB;IACnB,MAAMwE,UAAUlD,IAAAA,kBAAW,EAAC;QAC1B,MAAMD;IACR,GAAG;QAACA;KAAmB;IAEvB,yCAAyC;IACzC,MAAMoD,qBAAqBnD,IAAAA,kBAAW,EAAC,OAAOoD,WAAmBC;QAC/D,IAAI,CAAC3E,YAAY,OAAO,EAAE;QAE1B,IAAI;YACF,qBAAqB;YACrB,MAAM,IAAIuB,QAAQC,CAAAA,UAAWC,WAAWD,SAAS;YAEjD,2CAA2C;YAC3C,MAAMoD,QAAQ,IAAI7C,KAAK2C;YACvB,MAAMG,MAAM,IAAI9C,KAAK4C;YACrB,MAAMnB,UAA8B,EAAE;YAEtC,IAAK,IAAIsB,IAAI,IAAI/C,KAAK6C,QAAQE,KAAKD,KAAKC,EAAEC,OAAO,CAACD,EAAEE,OAAO,KAAK,GAAI;gBAClE,gBAAgB;gBAChB,IAAIF,EAAEG,MAAM,OAAO,KAAKH,EAAEG,MAAM,OAAO,GAAG;gBAE1CzB,QAAQ0B,IAAI,CAAC;oBACXvD,IAAI,CAAC,IAAI,EAAEmD,EAAE9C,WAAW,GAAGC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;oBAC1CL,aAAa5B;oBACb6B,eAAe;oBACfC,MAAMgD,EAAE9C,WAAW,GAAGC,KAAK,CAAC,IAAI,CAAC,EAAE;oBACnCC,UAAU,IAAIH,KAAK+C,EAAEK,OAAO,KAAK,IAAI,KAAK,KAAK,MAAMnD,WAAW;oBAChEI,UAAU,IAAIL,KAAK+C,EAAEK,OAAO,KAAK,KAAK,KAAK,KAAK,MAAMnD,WAAW;oBACjEK,aAAa;oBACbC,YAAY;oBACZC,UAAU;oBACVC,QAAQ;oBACRC,QAAQ,EAAE;oBACVO,gBAAgB;gBAClB;YACF;YAEA,OAAOQ;QACT,EAAE,OAAOY,KAAK;YACZC,QAAQpD,KAAK,CAAC,mCAAmCmD;YACjDlD,SAASkD,eAAeE,QAAQF,IAAIG,OAAO,GAAG;YAC9C,OAAO,EAAE;QACX;IACF,GAAG;QAACvE;KAAW;IAEf,4BAA4B;IAC5B,MAAMoF,uBAAuB9D,IAAAA,kBAAW,EAAC,CAAC+D;QACxCzE,mBAAmB0E,CAAAA;YACjB,uCAAuC;YACvC,MAAMC,aAAa;gBAACF;mBAAWC;aAAK;YACpC,OAAOC,WAAWC,KAAK,CAAC,GAAG;QAC7B;QAEA,qDAAqD;QACrD,IAAIH,OAAOzD,WAAW,KAAK5B,YAAY;YACrC,+CAA+C;YAC/CyB,WAAW;gBACTJ;YACF,GAAG;QACL;IACF,GAAG;QAACrB;QAAYqB;KAAmB;IAEnC,eAAe;IACfoE,IAAAA,gBAAS,EAAC;QACR,IAAIzF,YAAY;YACdqB;QACF;IACF,GAAG;QAACrB;QAAYqB;KAAmB;IAEnC,eAAe;IACfoE,IAAAA,gBAAS,EAAC;QACR,IAAI,CAACxF,eAAe,CAACD,YAAY;QAEjC,MAAM0F,WAAWC,YAAY;YAC3BtE;QACF,GAAGnB;QAEH,OAAO,IAAM0F,cAAcF;IAC7B,GAAG;QAACzF;QAAaC;QAAiBF;QAAYqB;KAAmB;IAEjE,oDAAoD;IACpDoE,IAAAA,gBAAS,EAAC;QACR,IAAI,CAACtF,gBAAgB;QAErB,+CAA+C;QAC/C,MAAMuF,WAAWC,YAAY;YAC3B,MAAME,aAAiC;gBACrClE,IAAI,CAAC,OAAO,EAAEI,KAAKI,GAAG,IAAI;gBAC1BP,aAAa,CAAC,GAAG,EAAEkE,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,KAAKC,QAAQ,GAAGC,QAAQ,CAAC,GAAG,MAAM;gBAChFrE,eAAe,CAAC,SAAS,EAAEiE,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAM;gBAC5DpD,YAAYkD,KAAKE,MAAM,KAAK,MAAM,UAAU;gBAC5CjD,aAAa;oBAAC;oBAAiB;oBAAgB;iBAAgB,CAAC+C,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,GAAG;gBAC9FG,WAAW,IAAIpE,OAAOC,WAAW;gBACjCQ,QAAQ;YACV;YAEA4C,qBAAqBS;QACvB,GAAG,QAAQ,mBAAmB;QAE9B,OAAO,IAAMD,cAAcF;IAC7B,GAAG;QAACvF;QAAgBiF;KAAqB;IAEzC,OAAO;QACL,OAAO;QACPhF;QACAG;QACAE;QACAE;QAEA,SAAS;QACTE;QACAE;QACAE;QAEA,UAAU;QACVuD;QACAC;QAEA,UAAU;QACVtD;IACF;AACF;AAGO,MAAMtB,oBAAoB;IAC/B,MAAM,CAACY,SAASC,WAAW,GAAGJ,IAAAA,eAAQ,EAAoB,EAAE;IAC5D,MAAM,CAACO,SAASC,WAAW,GAAGR,IAAAA,eAAQ,EAAC;IACvC,MAAM,CAACW,OAAOC,SAAS,GAAGZ,IAAAA,eAAQ,EAAgB;IAElD,MAAM8F,cAAc9E,IAAAA,kBAAW,EAAC;QAC9B,IAAI;YACFR,WAAW;YACXI,SAAS;YAET,qBAAqB;YACrB,MAAM,IAAIK,QAAQC,CAAAA,UAAWC,WAAWD,SAAS;YAEjD,mBAAmB;YACnB,MAAMsC,aAAgC;gBACpC;oBACEnC,IAAI;oBACJoC,MAAM;oBACNC,IAAI;oBACJC,MAAM;oBACNzB,QAAQ;oBACR0B,MAAM;oBACNC,UAAU;gBACZ;gBACA;oBACExC,IAAI;oBACJoC,MAAM;oBACNC,IAAI;oBACJC,MAAM;oBACNzB,QAAQ;oBACR0B,MAAM;oBACNC,UAAU;gBACZ;gBACA;oBACExC,IAAI;oBACJoC,MAAM;oBACNC,IAAI;oBACJC,MAAM;oBACNzB,QAAQsD,KAAKE,MAAM,KAAK,MAAM,WAAW;oBACzC9B,MAAM;oBACNC,UAAU;gBACZ;gBACA;oBACExC,IAAI;oBACJoC,MAAM;oBACNC,IAAI;oBACJC,MAAM;oBACNzB,QAAQ;oBACR0B,MAAM;oBACNC,UAAU;gBACZ;aACD;YAEDzD,WAAWoD;QACb,EAAE,OAAOM,KAAK;YACZC,QAAQpD,KAAK,CAAC,2BAA2BmD;YACzClD,SAASkD,eAAeE,QAAQF,IAAIG,OAAO,GAAG;QAChD,SAAU;YACRzD,WAAW;QACb;IACF,GAAG,EAAE;IAEL2E,IAAAA,gBAAS,EAAC;QACRW;QAEA,kCAAkC;QAClC,MAAMV,WAAWC,YAAYS,aAAa;QAC1C,OAAO,IAAMR,cAAcF;IAC7B,GAAG;QAACU;KAAY;IAEhB,OAAO;QACL3F;QACAI;QACAI;QACAuD,SAAS4B;QACTC,eAAe5F,QAAQ6F,MAAM,CAACxB,CAAAA,IAAKA,EAAEtC,MAAM,KAAK;QAChD+D,gBAAgB9F,QAAQ6F,MAAM,CAACxB,CAAAA,IAAKA,EAAEtC,MAAM,KAAK;IACnD;AACF;AAGO,MAAM1C,qBAAqB;IAChC,MAAM,CAAC0G,SAASC,WAAW,GAAGnG,IAAAA,eAAQ,EAAuB,EAAE;IAC/D,MAAM,CAACoG,cAAcC,gBAAgB,GAAGrG,IAAAA,eAAQ,EAAC;IAEjD,MAAMsG,kBAAkBtF,IAAAA,kBAAW,EAAC;QAClC,IAAIoF,cAAc;QAElB,MAAMG,eAAe,CAACxB;YACpBoB,WAAWnB,CAAAA,OAAQ;oBAACD;uBAAWC,KAAKE,KAAK,CAAC,GAAG;iBAAI,GAAG,uBAAuB;QAC7E;QAEAsB,oCAAiB,CAACC,uBAAuB,CAACF;QAC1CF,gBAAgB;QAEhB,OAAO;YACLG,oCAAiB,CAACE,sBAAsB,CAACH;YACzCF,gBAAgB;QAClB;IACF,GAAG;QAACD;KAAa;IAEjB,MAAMO,iBAAiB3F,IAAAA,kBAAW,EAAC;QACjCwF,oCAAiB,CAACE,sBAAsB;QACxCL,gBAAgB;IAClB,GAAG,EAAE;IAEL,MAAMO,eAAe5F,IAAAA,kBAAW,EAAC;QAC/BmF,WAAW,EAAE;IACf,GAAG,EAAE;IAEL,OAAO;QACLD;QACAE;QACAE;QACAK;QACAC;IACF;AACF"}