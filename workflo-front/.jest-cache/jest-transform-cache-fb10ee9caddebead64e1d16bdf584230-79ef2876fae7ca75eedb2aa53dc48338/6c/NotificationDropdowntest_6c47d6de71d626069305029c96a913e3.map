{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/__tests__/components/ui/NotificationDropdown.test.tsx"], "sourcesContent": ["import React from 'react';\nimport { screen, waitFor } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\nimport { render } from '@/__tests__/utils/test-utils';\nimport NotificationDropdown from '@/components/ui/NotificationDropdown';\nimport { NotificationData } from '@/lib/websocket';\n\nconst mockNotifications: NotificationData[] = [\n  {\n    id: '1',\n    type: 'leave_update',\n    title: 'Leave Application Approved',\n    message: 'Your leave application for Dec 25-29 has been approved.',\n    timestamp: '2024-12-20T10:00:00Z',\n    read: false,\n    priority: 'medium',\n    user_id: 1,\n  },\n  {\n    id: '2',\n    type: 'payroll_update',\n    title: 'Payslip Available',\n    message: 'Your December payslip is now available for download.',\n    timestamp: '2024-12-19T15:30:00Z',\n    read: true,\n    priority: 'low',\n    user_id: 1,\n  },\n  {\n    id: '3',\n    type: 'system_maintenance',\n    title: 'System Maintenance',\n    message: 'Scheduled maintenance on Dec 22, 2024 from 2:00 AM to 4:00 AM.',\n    timestamp: '2024-12-18T09:00:00Z',\n    read: false,\n    priority: 'high',\n    user_id: 1,\n  },\n];\n\ndescribe('NotificationDropdown', () => {\n  const mockNotificationContext = {\n    notifications: mockNotifications,\n    unreadCount: 2,\n    isConnected: true,\n    connectionState: 'connected',\n    markAsRead: jest.fn(),\n    markAllAsRead: jest.fn(),\n    clearNotifications: jest.fn(),\n    requestPermission: jest.fn().mockResolvedValue('granted' as NotificationPermission),\n  };\n\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('notification bell', () => {\n    it('renders notification bell with unread count', () => {\n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      expect(screen.getByText('2')).toBeInTheDocument(); // Unread count badge\n    });\n\n    it('shows connected status indicator', () => {\n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      const bell = screen.getByRole('button');\n      const statusIndicator = bell.querySelector('.bg-green-500');\n      expect(statusIndicator).toBeInTheDocument();\n    });\n\n    it('shows disconnected status when not connected', () => {\n      render(<NotificationDropdown />, {\n        notificationContextValue: {\n          ...mockNotificationContext,\n          isConnected: false,\n          connectionState: 'disconnected',\n        },\n      });\n\n      const bell = screen.getByRole('button');\n      const statusIndicator = bell.querySelector('.bg-red-500');\n      expect(statusIndicator).toBeInTheDocument();\n    });\n\n    it('does not show unread count when there are no unread notifications', () => {\n      render(<NotificationDropdown />, {\n        notificationContextValue: {\n          ...mockNotificationContext,\n          unreadCount: 0,\n        },\n      });\n\n      expect(screen.queryByText('2')).not.toBeInTheDocument();\n    });\n  });\n\n  describe('dropdown functionality', () => {\n    it('opens dropdown when bell is clicked', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      expect(screen.getByText('Notifications')).toBeInTheDocument();\n      expect(screen.getByText('2 unread notifications')).toBeInTheDocument();\n    });\n\n    it('closes dropdown when close button is clicked', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      // Open dropdown\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      // Close dropdown\n      const closeButton = screen.getByRole('button', { name: '' }); // X button\n      await user.click(closeButton);\n\n      await waitFor(() => {\n        expect(screen.queryByText('Notifications')).not.toBeInTheDocument();\n      });\n    });\n\n    it('closes dropdown when clicking outside', async () => {\n      const user = userEvent.setup();\n      \n      render(\n        <div>\n          <NotificationDropdown />\n          <div data-testid=\"outside\">Outside element</div>\n        </div>,\n        {\n          notificationContextValue: mockNotificationContext,\n        }\n      );\n\n      // Open dropdown\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      expect(screen.getByText('Notifications')).toBeInTheDocument();\n\n      // Click outside\n      const outsideElement = screen.getByTestId('outside');\n      await user.click(outsideElement);\n\n      await waitFor(() => {\n        expect(screen.queryByText('Notifications')).not.toBeInTheDocument();\n      });\n    });\n  });\n\n  describe('notification list', () => {\n    it('displays all notifications', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      expect(screen.getByText('Leave Application Approved')).toBeInTheDocument();\n      expect(screen.getByText('Payslip Available')).toBeInTheDocument();\n      expect(screen.getByText('System Maintenance')).toBeInTheDocument();\n    });\n\n    it('shows empty state when no notifications', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: {\n          ...mockNotificationContext,\n          notifications: [],\n          unreadCount: 0,\n        },\n      });\n\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      expect(screen.getByText('No notifications yet')).toBeInTheDocument();\n    });\n\n    it('marks notification as read when clicked', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      const notification = screen.getByText('Leave Application Approved');\n      await user.click(notification);\n\n      expect(mockNotificationContext.markAsRead).toHaveBeenCalledWith('1');\n    });\n\n    it('marks individual notification as read via button', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      // Find the mark as read button for the first notification\n      const markAsReadButtons = screen.getAllByRole('button', { name: '' });\n      const markAsReadButton = markAsReadButtons.find(button => \n        button.querySelector('svg')?.getAttribute('data-testid') === 'check'\n      );\n\n      if (markAsReadButton) {\n        await user.click(markAsReadButton);\n        expect(mockNotificationContext.markAsRead).toHaveBeenCalledWith('1');\n      }\n    });\n  });\n\n  describe('notification actions', () => {\n    it('marks all notifications as read', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      const markAllButton = screen.getByRole('button', { name: '' }); // CheckCheck icon\n      await user.click(markAllButton);\n\n      expect(mockNotificationContext.markAllAsRead).toHaveBeenCalled();\n    });\n\n    it('opens settings panel', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      const settingsButton = screen.getByRole('button', { name: '' }); // Settings icon\n      await user.click(settingsButton);\n\n      expect(screen.getByText('Notification Settings')).toBeInTheDocument();\n      expect(screen.getByText('Enable Browser Notifications')).toBeInTheDocument();\n      expect(screen.getByText('Clear All Notifications')).toBeInTheDocument();\n    });\n\n    it('requests browser notification permission', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      // Open settings\n      const settingsButton = screen.getByRole('button', { name: '' }); // Settings icon\n      await user.click(settingsButton);\n\n      // Click enable notifications\n      const enableButton = screen.getByText('Enable Browser Notifications');\n      await user.click(enableButton);\n\n      expect(mockNotificationContext.requestPermission).toHaveBeenCalled();\n    });\n\n    it('clears all notifications', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      // Open settings\n      const settingsButton = screen.getByRole('button', { name: '' }); // Settings icon\n      await user.click(settingsButton);\n\n      // Click clear all\n      const clearButton = screen.getByText('Clear All Notifications');\n      await user.click(clearButton);\n\n      expect(mockNotificationContext.clearNotifications).toHaveBeenCalled();\n    });\n  });\n\n  describe('connection status', () => {\n    it('shows connection status in header', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      expect(screen.getByText('connected')).toBeInTheDocument();\n    });\n\n    it('shows disconnected status when not connected', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: {\n          ...mockNotificationContext,\n          isConnected: false,\n          connectionState: 'disconnected',\n        },\n      });\n\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      expect(screen.getByText('disconnected')).toBeInTheDocument();\n    });\n  });\n\n  describe('notification priority styling', () => {\n    it('applies correct styling for high priority notifications', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      const highPriorityNotification = screen.getByText('System Maintenance').closest('div');\n      expect(highPriorityNotification).toHaveClass('border-l-red-500', 'bg-red-50');\n    });\n\n    it('applies correct styling for medium priority notifications', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      const mediumPriorityNotification = screen.getByText('Leave Application Approved').closest('div');\n      expect(mediumPriorityNotification).toHaveClass('border-l-yellow-500', 'bg-yellow-50');\n    });\n\n    it('applies correct styling for low priority notifications', async () => {\n      const user = userEvent.setup();\n      \n      render(<NotificationDropdown />, {\n        notificationContextValue: mockNotificationContext,\n      });\n\n      const bell = screen.getByRole('button');\n      await user.click(bell);\n\n      const lowPriorityNotification = screen.getByText('Payslip Available').closest('div');\n      expect(lowPriorityNotification).toHaveClass('border-l-green-500', 'bg-green-50');\n    });\n  });\n});\n"], "names": ["mockNotifications", "id", "type", "title", "message", "timestamp", "read", "priority", "user_id", "describe", "mockNotificationContext", "notifications", "unreadCount", "isConnected", "connectionState", "mark<PERSON><PERSON><PERSON>", "jest", "fn", "markAllAsRead", "clearNotifications", "requestPermission", "mockResolvedValue", "beforeEach", "clearAllMocks", "it", "render", "NotificationDropdown", "notificationContextValue", "expect", "screen", "getByText", "toBeInTheDocument", "bell", "getByRole", "statusIndicator", "querySelector", "queryByText", "not", "user", "userEvent", "setup", "click", "closeButton", "name", "waitFor", "div", "data-testid", "outsideElement", "getByTestId", "notification", "toHaveBeenCalledWith", "markAsReadButtons", "getAllByRole", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "button", "getAttribute", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toHaveBeenCalled", "settingsButton", "enableButton", "clearButton", "highPriorityNotification", "closest", "toHaveClass", "mediumPriorityNotification", "lowPriorityNotification"], "mappings": ";;;;;8DAAkB;wBACc;kEACV;2BACC;6EACU;;;;;;AAGjC,MAAMA,oBAAwC;IAC5C;QACEC,IAAI;QACJC,MAAM;QACNC,OAAO;QACPC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,UAAU;QACVC,SAAS;IACX;IACA;QACEP,IAAI;QACJC,MAAM;QACNC,OAAO;QACPC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,UAAU;QACVC,SAAS;IACX;IACA;QACEP,IAAI;QACJC,MAAM;QACNC,OAAO;QACPC,SAAS;QACTC,WAAW;QACXC,MAAM;QACNC,UAAU;QACVC,SAAS;IACX;CACD;AAEDC,SAAS,wBAAwB;IAC/B,MAAMC,0BAA0B;QAC9BC,eAAeX;QACfY,aAAa;QACbC,aAAa;QACbC,iBAAiB;QACjBC,YAAYC,KAAKC,EAAE;QACnBC,eAAeF,KAAKC,EAAE;QACtBE,oBAAoBH,KAAKC,EAAE;QAC3BG,mBAAmBJ,KAAKC,EAAE,GAAGI,iBAAiB,CAAC;IACjD;IAEAC,WAAW;QACTN,KAAKO,aAAa;IACpB;IAEAd,SAAS,qBAAqB;QAC5Be,GAAG,+CAA+C;YAChDC,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEAkB,OAAOC,cAAM,CAACC,SAAS,CAAC,MAAMC,iBAAiB,IAAI,qBAAqB;QAC1E;QAEAP,GAAG,oCAAoC;YACrCC,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEA,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMC,kBAAkBF,KAAKG,aAAa,CAAC;YAC3CP,OAAOM,iBAAiBH,iBAAiB;QAC3C;QAEAP,GAAG,gDAAgD;YACjDC,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0B;oBACxB,GAAGjB,uBAAuB;oBAC1BG,aAAa;oBACbC,iBAAiB;gBACnB;YACF;YAEA,MAAMkB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMC,kBAAkBF,KAAKG,aAAa,CAAC;YAC3CP,OAAOM,iBAAiBH,iBAAiB;QAC3C;QAEAP,GAAG,qEAAqE;YACtEC,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0B;oBACxB,GAAGjB,uBAAuB;oBAC1BE,aAAa;gBACf;YACF;YAEAgB,OAAOC,cAAM,CAACO,WAAW,CAAC,MAAMC,GAAG,CAACN,iBAAiB;QACvD;IACF;IAEAtB,SAAS,0BAA0B;QACjCe,GAAG,uCAAuC;YACxC,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEA,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjBJ,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAC3DH,OAAOC,cAAM,CAACC,SAAS,CAAC,2BAA2BC,iBAAiB;QACtE;QAEAP,GAAG,gDAAgD;YACjD,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEA,gBAAgB;YAChB,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjB,iBAAiB;YACjB,MAAMU,cAAcb,cAAM,CAACI,SAAS,CAAC,UAAU;gBAAEU,MAAM;YAAG,IAAI,WAAW;YACzE,MAAML,KAAKG,KAAK,CAACC;YAEjB,MAAME,IAAAA,eAAO,EAAC;gBACZhB,OAAOC,cAAM,CAACO,WAAW,CAAC,kBAAkBC,GAAG,CAACN,iBAAiB;YACnE;QACF;QAEAP,GAAG,yCAAyC;YAC1C,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBACJ,sBAACoB;;kCACC,qBAACnB,6BAAoB;kCACrB,qBAACmB;wBAAIC,eAAY;kCAAU;;;gBAE7B;gBACEnB,0BAA0BjB;YAC5B;YAGF,gBAAgB;YAChB,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjBJ,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;YAE3D,gBAAgB;YAChB,MAAMgB,iBAAiBlB,cAAM,CAACmB,WAAW,CAAC;YAC1C,MAAMV,KAAKG,KAAK,CAACM;YAEjB,MAAMH,IAAAA,eAAO,EAAC;gBACZhB,OAAOC,cAAM,CAACO,WAAW,CAAC,kBAAkBC,GAAG,CAACN,iBAAiB;YACnE;QACF;IACF;IAEAtB,SAAS,qBAAqB;QAC5Be,GAAG,8BAA8B;YAC/B,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEA,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjBJ,OAAOC,cAAM,CAACC,SAAS,CAAC,+BAA+BC,iBAAiB;YACxEH,OAAOC,cAAM,CAACC,SAAS,CAAC,sBAAsBC,iBAAiB;YAC/DH,OAAOC,cAAM,CAACC,SAAS,CAAC,uBAAuBC,iBAAiB;QAClE;QAEAP,GAAG,2CAA2C;YAC5C,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0B;oBACxB,GAAGjB,uBAAuB;oBAC1BC,eAAe,EAAE;oBACjBC,aAAa;gBACf;YACF;YAEA,MAAMoB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjBJ,OAAOC,cAAM,CAACC,SAAS,CAAC,yBAAyBC,iBAAiB;QACpE;QAEAP,GAAG,2CAA2C;YAC5C,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEA,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjB,MAAMiB,eAAepB,cAAM,CAACC,SAAS,CAAC;YACtC,MAAMQ,KAAKG,KAAK,CAACQ;YAEjBrB,OAAOlB,wBAAwBK,UAAU,EAAEmC,oBAAoB,CAAC;QAClE;QAEA1B,GAAG,oDAAoD;YACrD,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEA,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjB,0DAA0D;YAC1D,MAAMmB,oBAAoBtB,cAAM,CAACuB,YAAY,CAAC,UAAU;gBAAET,MAAM;YAAG;YACnE,MAAMU,mBAAmBF,kBAAkBG,IAAI,CAACC,CAAAA,SAC9CA,OAAOpB,aAAa,CAAC,QAAQqB,aAAa,mBAAmB;YAG/D,IAAIH,kBAAkB;gBACpB,MAAMf,KAAKG,KAAK,CAACY;gBACjBzB,OAAOlB,wBAAwBK,UAAU,EAAEmC,oBAAoB,CAAC;YAClE;QACF;IACF;IAEAzC,SAAS,wBAAwB;QAC/Be,GAAG,mCAAmC;YACpC,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEA,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjB,MAAMyB,gBAAgB5B,cAAM,CAACI,SAAS,CAAC,UAAU;gBAAEU,MAAM;YAAG,IAAI,kBAAkB;YAClF,MAAML,KAAKG,KAAK,CAACgB;YAEjB7B,OAAOlB,wBAAwBQ,aAAa,EAAEwC,gBAAgB;QAChE;QAEAlC,GAAG,wBAAwB;YACzB,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEA,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjB,MAAM2B,iBAAiB9B,cAAM,CAACI,SAAS,CAAC,UAAU;gBAAEU,MAAM;YAAG,IAAI,gBAAgB;YACjF,MAAML,KAAKG,KAAK,CAACkB;YAEjB/B,OAAOC,cAAM,CAACC,SAAS,CAAC,0BAA0BC,iBAAiB;YACnEH,OAAOC,cAAM,CAACC,SAAS,CAAC,iCAAiCC,iBAAiB;YAC1EH,OAAOC,cAAM,CAACC,SAAS,CAAC,4BAA4BC,iBAAiB;QACvE;QAEAP,GAAG,4CAA4C;YAC7C,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEA,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjB,gBAAgB;YAChB,MAAM2B,iBAAiB9B,cAAM,CAACI,SAAS,CAAC,UAAU;gBAAEU,MAAM;YAAG,IAAI,gBAAgB;YACjF,MAAML,KAAKG,KAAK,CAACkB;YAEjB,6BAA6B;YAC7B,MAAMC,eAAe/B,cAAM,CAACC,SAAS,CAAC;YACtC,MAAMQ,KAAKG,KAAK,CAACmB;YAEjBhC,OAAOlB,wBAAwBU,iBAAiB,EAAEsC,gBAAgB;QACpE;QAEAlC,GAAG,4BAA4B;YAC7B,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEA,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjB,gBAAgB;YAChB,MAAM2B,iBAAiB9B,cAAM,CAACI,SAAS,CAAC,UAAU;gBAAEU,MAAM;YAAG,IAAI,gBAAgB;YACjF,MAAML,KAAKG,KAAK,CAACkB;YAEjB,kBAAkB;YAClB,MAAME,cAAchC,cAAM,CAACC,SAAS,CAAC;YACrC,MAAMQ,KAAKG,KAAK,CAACoB;YAEjBjC,OAAOlB,wBAAwBS,kBAAkB,EAAEuC,gBAAgB;QACrE;IACF;IAEAjD,SAAS,qBAAqB;QAC5Be,GAAG,qCAAqC;YACtC,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEA,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjBJ,OAAOC,cAAM,CAACC,SAAS,CAAC,cAAcC,iBAAiB;QACzD;QAEAP,GAAG,gDAAgD;YACjD,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0B;oBACxB,GAAGjB,uBAAuB;oBAC1BG,aAAa;oBACbC,iBAAiB;gBACnB;YACF;YAEA,MAAMkB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjBJ,OAAOC,cAAM,CAACC,SAAS,CAAC,iBAAiBC,iBAAiB;QAC5D;IACF;IAEAtB,SAAS,iCAAiC;QACxCe,GAAG,2DAA2D;YAC5D,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEA,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjB,MAAM8B,2BAA2BjC,cAAM,CAACC,SAAS,CAAC,sBAAsBiC,OAAO,CAAC;YAChFnC,OAAOkC,0BAA0BE,WAAW,CAAC,oBAAoB;QACnE;QAEAxC,GAAG,6DAA6D;YAC9D,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEA,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjB,MAAMiC,6BAA6BpC,cAAM,CAACC,SAAS,CAAC,8BAA8BiC,OAAO,CAAC;YAC1FnC,OAAOqC,4BAA4BD,WAAW,CAAC,uBAAuB;QACxE;QAEAxC,GAAG,0DAA0D;YAC3D,MAAMc,OAAOC,kBAAS,CAACC,KAAK;YAE5Bf,IAAAA,iBAAM,gBAAC,qBAACC,6BAAoB,OAAK;gBAC/BC,0BAA0BjB;YAC5B;YAEA,MAAMsB,OAAOH,cAAM,CAACI,SAAS,CAAC;YAC9B,MAAMK,KAAKG,KAAK,CAACT;YAEjB,MAAMkC,0BAA0BrC,cAAM,CAACC,SAAS,CAAC,qBAAqBiC,OAAO,CAAC;YAC9EnC,OAAOsC,yBAAyBF,WAAW,CAAC,sBAAsB;QACpE;IACF;AACF"}