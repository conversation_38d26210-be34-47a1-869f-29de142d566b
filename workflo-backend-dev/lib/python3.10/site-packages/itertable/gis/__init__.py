from .mixins import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WktMapper
from ..base import BaseIter


class MetaSyncIter(BaseIter):
    """
    Custom sync() to handle transfering Fiona metadata (except for driver)
    """

    def sync(self, other, save=True):
        driver = other.meta.get("driver", None)
        other.meta = self.meta.copy()
        if driver:
            other.meta["driver"] = driver
        super(MetaSyncIter, self).sync(other, save)

    def get_field_names(self):
        if self.field_names is None and self.meta is not None:
            return ["id", "geometry"] + list(
                self.meta["schema"]["properties"].keys()
            )
        return super(MetaSyncIter, self).get_field_names()


class GisIter(<PERSON><PERSON>oaderParser, GisMapper, MetaSyncIter):
    pass


class ShapeIter(Fiona<PERSON>oaderParser, <PERSON>hapeMapper, MetaSyncIter):
    pass


class WktIter(<PERSON><PERSON><PERSON>derParser, WktMapper, MetaSyncIter):
    pass
