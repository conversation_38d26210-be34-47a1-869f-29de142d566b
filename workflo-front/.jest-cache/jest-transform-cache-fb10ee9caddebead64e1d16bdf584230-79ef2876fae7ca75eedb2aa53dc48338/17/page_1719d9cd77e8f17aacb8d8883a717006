aa0d47cf0a360334d90dd50488d65ed5
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _lucidereact = require("lucide-react");
const _Card = /*#__PURE__*/ _interop_require_default(require("../../../../components/ui/Card"));
const _AuthProvider = require("../../../../providers/AuthProvider");
const _utils = require("../../../../lib/utils");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const StaffDashboard = ()=>{
    const { user } = (0, _AuthProvider.useAuth)();
    const [currentTime, setCurrentTime] = (0, _react.useState)(new Date());
    (0, _react.useEffect)(()=>{
        const timer = setInterval(()=>{
            setCurrentTime(new Date());
        }, 1000);
        return ()=>clearInterval(timer);
    }, []);
    const todayStats = {
        checkInTime: '08:30 AM',
        hoursWorked: '7.5',
        tasksCompleted: 4,
        meetingsToday: 2,
        leaveBalance: 15,
        pendingApprovals: 1
    };
    const recentActivities = [
        {
            id: 1,
            type: 'check_in',
            description: 'Checked in for the day',
            time: '08:30 AM',
            icon: _lucidereact.Clock,
            color: 'text-green-500'
        },
        {
            id: 2,
            type: 'task',
            description: 'Completed project review',
            time: '10:15 AM',
            icon: _lucidereact.CheckCircle,
            color: 'text-blue-500'
        },
        {
            id: 3,
            type: 'meeting',
            description: 'Team standup meeting',
            time: '11:00 AM',
            icon: _lucidereact.Users,
            color: 'text-purple-500'
        },
        {
            id: 4,
            type: 'document',
            description: 'Updated timesheet',
            time: '02:30 PM',
            icon: _lucidereact.FileText,
            color: 'text-orange-500'
        }
    ];
    const upcomingEvents = [
        {
            id: 1,
            title: 'Project Review Meeting',
            time: '3:00 PM',
            type: 'meeting',
            participants: 5
        },
        {
            id: 2,
            title: 'Training Session: React Best Practices',
            time: 'Tomorrow 10:00 AM',
            type: 'training',
            duration: '2 hours'
        },
        {
            id: 3,
            title: 'Performance Review',
            time: 'Friday 2:00 PM',
            type: 'review',
            with: 'Manager'
        }
    ];
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white",
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "flex items-center justify-between",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("h1", {
                                    className: "text-2xl font-bold",
                                    children: [
                                        "Good ",
                                        currentTime.getHours() < 12 ? 'Morning' : currentTime.getHours() < 18 ? 'Afternoon' : 'Evening',
                                        ", ",
                                        user?.first_name,
                                        "!"
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                    className: "text-orange-100 mt-1",
                                    children: (0, _utils.formatDate)(new Date().toISOString(), 'EEEE, MMMM dd, yyyy')
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "text-right",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "text-3xl font-bold",
                                    children: currentTime.toLocaleTimeString([], {
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    })
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "text-orange-100 text-sm",
                                    children: "Current Time"
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-green-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                                            className: "h-6 w-6 text-green-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Check-in Time"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: todayStats.checkInTime
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-blue-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Activity, {
                                            className: "h-6 w-6 text-blue-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Hours Worked"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: [
                                                    todayStats.hoursWorked,
                                                    "h"
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-purple-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Target, {
                                            className: "h-6 w-6 text-purple-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Tasks Completed"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: todayStats.tasksCompleted
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-orange-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Calendar, {
                                            className: "h-6 w-6 text-orange-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Leave Balance"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: [
                                                    todayStats.leaveBalance,
                                                    " days"
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "grid grid-cols-1 lg:grid-cols-2 gap-6",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "p-6",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                    className: "text-lg font-medium text-gray-900 mb-4",
                                    children: "Today's Activities"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "space-y-4",
                                    children: recentActivities.map((activity)=>{
                                        const Icon = activity.icon;
                                        return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-3",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: (0, _utils.cn)('p-2 rounded-full bg-gray-100', activity.color),
                                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Icon, {
                                                        className: "h-4 w-4"
                                                    })
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex-1",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                            className: "text-sm font-medium text-gray-900",
                                                            children: activity.description
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                            className: "text-xs text-gray-500",
                                                            children: activity.time
                                                        })
                                                    ]
                                                })
                                            ]
                                        }, activity.id);
                                    })
                                })
                            ]
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "p-6",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                    className: "text-lg font-medium text-gray-900 mb-4",
                                    children: "Upcoming Events"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "space-y-4",
                                    children: upcomingEvents.map((event)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "border-l-4 border-orange-500 pl-4",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                    className: "text-sm font-medium text-gray-900",
                                                    children: event.title
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                    className: "text-xs text-gray-500 mt-1",
                                                    children: event.time
                                                }),
                                                event.participants && /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                    className: "text-xs text-gray-500",
                                                    children: [
                                                        event.participants,
                                                        " participants"
                                                    ]
                                                }),
                                                event.duration && /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                    className: "text-xs text-gray-500",
                                                    children: [
                                                        "Duration: ",
                                                        event.duration
                                                    ]
                                                }),
                                                event.with && /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                    className: "text-xs text-gray-500",
                                                    children: [
                                                        "With: ",
                                                        event.with
                                                    ]
                                                })
                                            ]
                                        }, event.id))
                                })
                            ]
                        })
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Quick Actions"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "grid grid-cols-2 md:grid-cols-4 gap-4",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                                    className: "p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.FileText, {
                                            className: "h-6 w-6 text-orange-500 mx-auto mb-2"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm font-medium text-gray-900",
                                            children: "Apply Leave"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                                    className: "p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                                            className: "h-6 w-6 text-blue-500 mx-auto mb-2"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm font-medium text-gray-900",
                                            children: "Check Out"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                                    className: "p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.User, {
                                            className: "h-6 w-6 text-green-500 mx-auto mb-2"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm font-medium text-gray-900",
                                            children: "Update Profile"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                                    className: "p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.TrendingUp, {
                                            className: "h-6 w-6 text-purple-500 mx-auto mb-2"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm font-medium text-gray-900",
                                            children: "View Performance"
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            })
        ]
    });
};
const _default = StaffDashboard;

//# sourceMappingURL=data:application/json;base64,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