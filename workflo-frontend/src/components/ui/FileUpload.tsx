'use client';

import React, { useState, useRef, useCallback } from 'react';
import {
  Upload,
  X,
  File,
  Image,
  FileText,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import fileUploadService, { UploadProgress, UploadResult } from '@/lib/fileUpload';

interface FileUploadProps {
  onUpload?: (results: UploadResult[]) => void;
  onProgress?: (progress: UploadProgress) => void;
  multiple?: boolean;
  accept?: string;
  maxSize?: number;
  maxFiles?: number;
  disabled?: boolean;
  className?: string;
  children?: React.ReactNode;
  endpoint: string;
  folder?: string;
}

interface FileWithPreview extends File {
  preview?: string;
  uploadResult?: UploadResult;
  uploading?: boolean;
  progress?: number;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onUpload,
  onProgress,
  multiple = false,
  accept,
  maxSize,
  maxFiles = 10,
  disabled = false,
  className,
  children,
  endpoint,
  folder
}) => {
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback((selectedFiles: FileList | null) => {
    if (!selectedFiles) return;

    const newFiles: FileWithPreview[] = Array.from(selectedFiles).map(file => {
      const fileWithPreview = file as FileWithPreview;
      if (fileUploadService.isImage(file)) {
        fileWithPreview.preview = fileUploadService.createPreviewUrl(file);
      }
      return fileWithPreview;
    });

    if (multiple) {
      setFiles(prev => [...prev, ...newFiles].slice(0, maxFiles));
    } else {
      // Clean up previous preview URLs
      files.forEach(file => {
        if (file.preview) {
          fileUploadService.revokePreviewUrl(file.preview);
        }
      });
      setFiles(newFiles.slice(0, 1));
    }
  }, [files, multiple, maxFiles]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;
    
    handleFileSelect(e.dataTransfer.files);
  }, [disabled, handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleClick = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [disabled]);

  const removeFile = useCallback((index: number) => {
    setFiles(prev => {
      const newFiles = [...prev];
      const removedFile = newFiles[index];
      
      // Clean up preview URL
      if (removedFile.preview) {
        fileUploadService.revokePreviewUrl(removedFile.preview);
      }
      
      newFiles.splice(index, 1);
      return newFiles;
    });
  }, []);

  const uploadFiles = useCallback(async () => {
    if (files.length === 0 || isUploading) return;

    setIsUploading(true);
    const results: UploadResult[] = [];

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        // Update file state to show uploading
        setFiles(prev => prev.map((f, idx) => 
          idx === i ? { ...f, uploading: true, progress: 0 } : f
        ));

        const result = await fileUploadService.uploadFile(file, endpoint, {
          folder,
          maxSize,
          onProgress: (progress) => {
            // Update individual file progress
            setFiles(prev => prev.map((f, idx) => 
              idx === i ? { ...f, progress: progress.percentage } : f
            ));
            
            // Calculate overall progress
            if (onProgress) {
              const overallProgress = {
                loaded: (i * 100) + progress.percentage,
                total: files.length * 100,
                percentage: Math.round(((i * 100) + progress.percentage) / files.length)
              };
              onProgress(overallProgress);
            }
          }
        });

        // Update file state with result
        setFiles(prev => prev.map((f, idx) => 
          idx === i ? { ...f, uploading: false, uploadResult: result } : f
        ));

        results.push(result);
      }

      if (onUpload) {
        onUpload(results);
      }
    } catch (error) {
      console.error('Upload error:', error);
    } finally {
      setIsUploading(false);
    }
  }, [files, isUploading, endpoint, folder, maxSize, onProgress, onUpload]);

  const getFileIcon = (file: File) => {
    if (fileUploadService.isImage(file)) {
      return <Image className="h-8 w-8 text-blue-500" />;
    } else if (fileUploadService.isPDF(file)) {
      return <FileText className="h-8 w-8 text-red-500" />;
    } else {
      return <File className="h-8 w-8 text-gray-500" />;
    }
  };

  const getUploadStatus = (file: FileWithPreview) => {
    if (file.uploading) {
      return (
        <div className="flex items-center space-x-2">
          <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
          <span className="text-sm text-blue-600">{file.progress || 0}%</span>
        </div>
      );
    } else if (file.uploadResult) {
      if (file.uploadResult.success) {
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      } else {
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      }
    }
    return null;
  };

  return (
    <div className={cn('w-full', className)}>
      {/* Drop Zone */}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={handleClick}
        className={cn(
          'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',
          isDragOver
            ? 'border-orange-500 bg-orange-50'
            : 'border-gray-300 hover:border-gray-400',
          disabled && 'opacity-50 cursor-not-allowed'
        )}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept={accept}
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
          disabled={disabled}
        />

        {children || (
          <div className="space-y-2">
            <Upload className="h-12 w-12 text-gray-400 mx-auto" />
            <div>
              <p className="text-sm font-medium text-gray-900">
                Click to upload or drag and drop
              </p>
              <p className="text-xs text-gray-500">
                {accept ? `Accepted formats: ${accept}` : 'All file types accepted'}
              </p>
              {maxSize && (
                <p className="text-xs text-gray-500">
                  Max size: {fileUploadService.formatFileSize(maxSize)}
                </p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* File List */}
      {files.length > 0 && (
        <div className="mt-4 space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900">
              Selected Files ({files.length})
            </h4>
            {files.length > 0 && !isUploading && (
              <button
                onClick={uploadFiles}
                className="px-3 py-1 bg-orange-500 text-white text-sm rounded-md hover:bg-orange-600 transition-colors"
              >
                Upload All
              </button>
            )}
          </div>

          <div className="space-y-2">
            {files.map((file, index) => (
              <div
                key={`${file.name}-${index}`}
                className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg"
              >
                {/* File Icon/Preview */}
                <div className="flex-shrink-0">
                  {file.preview ? (
                    <img
                      src={file.preview}
                      alt={file.name}
                      className="h-12 w-12 object-cover rounded"
                    />
                  ) : (
                    getFileIcon(file)
                  )}
                </div>

                {/* File Info */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {fileUploadService.formatFileSize(file.size)}
                  </p>
                  {file.uploadResult && !file.uploadResult.success && (
                    <p className="text-xs text-red-500 mt-1">
                      {file.uploadResult.error}
                    </p>
                  )}
                </div>

                {/* Status */}
                <div className="flex items-center space-x-2">
                  {getUploadStatus(file)}
                  {!file.uploading && (
                    <button
                      onClick={() => removeFile(index)}
                      className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
