'use client';

import React, { useState, useEffect } from 'react';
import { 
  Briefcase, 
  Plus, 
  Search, 
  Filter,
  Users, 
  Calendar,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  MapPin,
  DollarSign,
  Building,
  User,
  Mail,
  Phone,
  Download
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useAuth } from '@/providers/AuthProvider';
import { cn, formatDate, formatCurrency } from '@/lib/utils';

interface JobPosting {
  id: number;
  title: string;
  department: string;
  location: string;
  employment_type: 'full_time' | 'part_time' | 'contract' | 'intern';
  salary_min: number;
  salary_max: number;
  description: string;
  requirements: string[];
  posted_date: string;
  closing_date: string;
  status: 'active' | 'closed' | 'draft';
  applications_count: number;
  hired_count: number;
}

interface Application {
  id: number;
  job_id: number;
  job_title: string;
  candidate_name: string;
  candidate_email: string;
  candidate_phone?: string;
  resume_url?: string;
  cover_letter?: string;
  applied_date: string;
  status: 'applied' | 'screening' | 'interview' | 'rejected' | 'hired';
  interview_date?: string;
  interviewer?: string;
  notes?: string;
}

const RecruitmentManagementPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'jobs' | 'applications' | 'interviews'>('jobs');
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Mock data
  const mockJobPostings: JobPosting[] = [
    {
      id: 1,
      title: 'Senior Software Engineer',
      department: 'Engineering',
      location: 'San Francisco, CA',
      employment_type: 'full_time',
      salary_min: 120000,
      salary_max: 160000,
      description: 'We are looking for a senior software engineer to join our growing team...',
      requirements: ['5+ years experience', 'React/Node.js', 'AWS experience'],
      posted_date: '2024-01-15T00:00:00Z',
      closing_date: '2024-02-15T00:00:00Z',
      status: 'active',
      applications_count: 45,
      hired_count: 0
    },
    {
      id: 2,
      title: 'Marketing Manager',
      department: 'Marketing',
      location: 'Remote',
      employment_type: 'full_time',
      salary_min: 80000,
      salary_max: 100000,
      description: 'Lead our marketing initiatives and drive brand growth...',
      requirements: ['3+ years marketing experience', 'Digital marketing', 'Analytics'],
      posted_date: '2024-01-10T00:00:00Z',
      closing_date: '2024-02-10T00:00:00Z',
      status: 'active',
      applications_count: 32,
      hired_count: 1
    },
    {
      id: 3,
      title: 'UX Designer',
      department: 'Design',
      location: 'New York, NY',
      employment_type: 'full_time',
      salary_min: 90000,
      salary_max: 120000,
      description: 'Create amazing user experiences for our products...',
      requirements: ['Portfolio required', 'Figma/Sketch', 'User research'],
      posted_date: '2024-01-05T00:00:00Z',
      closing_date: '2024-02-05T00:00:00Z',
      status: 'closed',
      applications_count: 28,
      hired_count: 1
    }
  ];

  const mockApplications: Application[] = [
    {
      id: 1,
      job_id: 1,
      job_title: 'Senior Software Engineer',
      candidate_name: 'Alice Johnson',
      candidate_email: '<EMAIL>',
      candidate_phone: '******-0123',
      resume_url: '/resumes/alice_johnson.pdf',
      applied_date: '2024-01-20T00:00:00Z',
      status: 'interview',
      interview_date: '2024-01-25T14:00:00Z',
      interviewer: 'John Smith',
      notes: 'Strong technical background, good communication skills'
    },
    {
      id: 2,
      job_id: 1,
      job_title: 'Senior Software Engineer',
      candidate_name: 'Bob Wilson',
      candidate_email: '<EMAIL>',
      applied_date: '2024-01-18T00:00:00Z',
      status: 'screening',
      notes: 'Reviewing portfolio and experience'
    },
    {
      id: 3,
      job_id: 2,
      job_title: 'Marketing Manager',
      candidate_name: 'Carol Davis',
      candidate_email: '<EMAIL>',
      applied_date: '2024-01-15T00:00:00Z',
      status: 'hired',
      notes: 'Excellent fit for the role, hired!'
    }
  ];

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setTimeout(() => {
        setJobPostings(mockJobPostings);
        setApplications(mockApplications);
        setLoading(false);
      }, 1000);
    };

    loadData();
  }, []);

  const filteredJobPostings = jobPostings.filter(job =>
    job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    job.department.toLowerCase().includes(searchQuery.toLowerCase()) ||
    job.location.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredApplications = applications.filter(app =>
    app.candidate_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    app.job_title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    app.candidate_email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalApplications = applications.length;
  const pendingApplications = applications.filter(app => ['applied', 'screening'].includes(app.status)).length;
  const interviewsScheduled = applications.filter(app => app.status === 'interview').length;
  const totalHired = applications.filter(app => app.status === 'hired').length;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'applied': return 'bg-blue-100 text-blue-800';
      case 'screening': return 'bg-yellow-100 text-yellow-800';
      case 'interview': return 'bg-purple-100 text-purple-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'hired': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Recruitment Management</h1>
            <p className="text-gray-600 mt-1">
              Manage job postings, applications, and the complete hiring workflow
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-3">
            <Button variant="secondary">
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </Button>
            <Button variant="primary">
              <Plus className="h-4 w-4 mr-2" />
              Create Job Posting
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Briefcase className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Jobs</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {jobPostings.filter(job => job.status === 'active').length}
                </p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Applications</p>
                <p className="text-2xl font-semibold text-gray-900">{totalApplications}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Interviews</p>
                <p className="text-2xl font-semibold text-gray-900">{interviewsScheduled}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Hired</p>
                <p className="text-2xl font-semibold text-gray-900">{totalHired}</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { key: 'jobs', label: 'Job Postings', icon: Briefcase },
              { key: 'applications', label: 'Applications', icon: Users },
              { key: 'interviews', label: 'Interviews', icon: Calendar },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={cn(
                    'py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2',
                    activeTab === tab.key
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Search */}
        <div className="p-6 border-b border-gray-200">
          <div className="w-full max-w-md">
            <Input
              placeholder={`Search ${activeTab}...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<Search className="h-4 w-4" />}
            />
          </div>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'jobs' && (
            <div className="space-y-4">
              {loading ? (
                <div className="animate-pulse space-y-4">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : (
                filteredJobPostings.map((job) => (
                  <JobPostingCard key={job.id} job={job} />
                ))
              )}
            </div>
          )}

          {activeTab === 'applications' && (
            <div className="space-y-4">
              {loading ? (
                <div className="animate-pulse space-y-4">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : (
                filteredApplications.map((application) => (
                  <ApplicationCard key={application.id} application={application} />
                ))
              )}
            </div>
          )}

          {activeTab === 'interviews' && (
            <div className="space-y-4">
              {loading ? (
                <div className="animate-pulse space-y-4">
                  {Array.from({ length: 2 }).map((_, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : (
                applications
                  .filter(app => app.status === 'interview')
                  .map((application) => (
                    <InterviewCard key={application.id} application={application} />
                  ))
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Job Posting Card Component
interface JobPostingCardProps {
  job: JobPosting;
}

const JobPostingCard: React.FC<JobPostingCardProps> = ({ job }) => {
  const [showActions, setShowActions] = useState(false);

  return (
    <Card className="relative hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-semibold text-gray-900">{job.title}</h3>
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                job.status === 'active' ? 'bg-green-100 text-green-800' :
                job.status === 'closed' ? 'bg-gray-100 text-gray-800' :
                'bg-yellow-100 text-yellow-800'
              )}>
                {job.status}
              </span>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-4">
              <div className="flex items-center">
                <Building className="h-4 w-4 mr-2" />
                {job.department}
              </div>
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-2" />
                {job.location}
              </div>
              <div className="flex items-center">
                <DollarSign className="h-4 w-4 mr-2" />
                {formatCurrency(job.salary_min)} - {formatCurrency(job.salary_max)}
              </div>
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-2" />
                {job.applications_count} applications
              </div>
            </div>
            
            <p className="text-sm text-gray-600 mb-4 line-clamp-2">{job.description}</p>
            
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>Posted: {formatDate(job.posted_date)}</span>
              <span>Closes: {formatDate(job.closing_date)}</span>
            </div>
          </div>
          
          <div className="relative ml-4">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-1 rounded-full hover:bg-gray-100"
            >
              <MoreHorizontal className="h-4 w-4 text-gray-500" />
            </button>

            {showActions && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </button>
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Posting
                  </button>
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <Users className="h-4 w-4 mr-2" />
                    View Applications
                  </button>
                  <button className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Posting
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

// Application Card Component
interface ApplicationCardProps {
  application: Application;
}

const ApplicationCard: React.FC<ApplicationCardProps> = ({ application }) => {
  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-semibold text-gray-900">{application.candidate_name}</h3>
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                application.status === 'applied' ? 'bg-blue-100 text-blue-800' :
                application.status === 'screening' ? 'bg-yellow-100 text-yellow-800' :
                application.status === 'interview' ? 'bg-purple-100 text-purple-800' :
                application.status === 'rejected' ? 'bg-red-100 text-red-800' :
                'bg-green-100 text-green-800'
              )}>
                {application.status}
              </span>
            </div>
            
            <p className="text-sm text-gray-600 mb-2">{application.job_title}</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-4">
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2" />
                {application.candidate_email}
              </div>
              {application.candidate_phone && (
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2" />
                  {application.candidate_phone}
                </div>
              )}
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                Applied: {formatDate(application.applied_date)}
              </div>
            </div>
            
            {application.notes && (
              <p className="text-sm text-gray-600 mb-4">{application.notes}</p>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

// Interview Card Component
interface InterviewCardProps {
  application: Application;
}

const InterviewCard: React.FC<InterviewCardProps> = ({ application }) => {
  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-semibold text-gray-900">{application.candidate_name}</h3>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Interview Scheduled
              </span>
            </div>
            
            <p className="text-sm text-gray-600 mb-4">{application.job_title}</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-4">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                {application.interview_date ? formatDate(application.interview_date) : 'TBD'}
              </div>
              <div className="flex items-center">
                <User className="h-4 w-4 mr-2" />
                Interviewer: {application.interviewer || 'TBD'}
              </div>
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2" />
                {application.candidate_email}
              </div>
            </div>
            
            {application.notes && (
              <p className="text-sm text-gray-600">{application.notes}</p>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default RecruitmentManagementPage;
