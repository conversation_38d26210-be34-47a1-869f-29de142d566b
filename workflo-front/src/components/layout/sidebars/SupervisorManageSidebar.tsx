'use client';

import React, { useMemo, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Users,
  Calendar,
  Clock,
  CheckCircle,
  TrendingUp,
  BarChart3,
  UserCheck,
  CalendarCheck,
  Star,
  FileText,
  Target,
  Award,
  Activity,
  AlertCircle,
  Settings,
  X,
  LogOut,
  ChevronRight,
  UserPlus,
  Filter,
  Download,
  Eye,
  Edit
} from 'lucide-react';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials, formatDate } from '@/lib/utils';

interface SupervisorManageSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const SupervisorManageSidebar: React.FC<SupervisorManageSidebarProps> = ({ isOpen, onClose }) => {
  const { user, logout } = useAuth();
  const pathname = usePathname();

  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  const handleLogout = useCallback(() => {
    logout();
    handleClose();
  }, [logout, handleClose]);

  // Management areas with enhanced details
  const managementAreas = useMemo(() => [
    {
      name: 'Leave Management',
      href: '/supervisor/manage/leave-management',
      icon: Calendar,
      description: 'Approve leave requests and manage holidays',
      stats: { pending: 3, approved: 12, rejected: 1 },
      actions: ['Approve Requests', 'View Calendar', 'Set Holidays']
    },
    {
      name: 'Overtime Management',
      href: '/supervisor/manage/overtime',
      icon: Clock,
      description: 'Manage overtime applications and approvals',
      stats: { pending: 2, approved: 8, total_hours: 45 },
      actions: ['Approve Overtime', 'View Reports', 'Set Limits']
    }
  ], []);

  // Team management quick actions
  const managementActions = useMemo(() => [
    {
      name: 'Approve Leave Requests',
      href: '/supervisor/manage/leave-management?action=approve',
      icon: CalendarCheck,
      description: 'Review pending leave applications',
      badge: '3',
      priority: 'high'
    },
    {
      name: 'Manage Overtime',
      href: '/supervisor/manage/overtime?action=approve',
      icon: Clock,
      description: 'Handle overtime requests',
      badge: '2',
      priority: 'medium'
    },
    {
      name: 'Team Performance Review',
      href: '/supervisor/manage?view=performance',
      icon: BarChart3,
      description: 'Analyze team performance metrics',
      priority: 'low'
    },
    {
      name: 'Team Directory',
      href: '/supervisor/manage?view=team',
      icon: Users,
      description: 'View team member details',
      priority: 'low'
    },
    {
      name: 'Schedule Performance Review',
      href: '/supervisor/manage?action=review',
      icon: Star,
      description: 'Set up team performance reviews',
      priority: 'medium'
    },
    {
      name: 'Generate Team Report',
      href: '/supervisor/manage?action=report',
      icon: FileText,
      description: 'Create comprehensive team reports',
      priority: 'low'
    }
  ], []);

  // Team statistics
  const teamStats = useMemo(() => [
    {
      category: 'Team Overview',
      stats: [
        { label: 'Total Members', value: '12', icon: Users, trend: 'stable' },
        { label: 'Active Today', value: '10', icon: UserCheck, trend: 'up' },
        { label: 'On Leave', value: '2', icon: Calendar, trend: 'down' }
      ]
    },
    {
      category: 'Pending Actions',
      stats: [
        { label: 'Leave Requests', value: '3', icon: CalendarCheck, trend: 'up' },
        { label: 'Overtime Apps', value: '2', icon: Clock, trend: 'stable' },
        { label: 'Reviews Due', value: '1', icon: Star, trend: 'down' }
      ]
    },
    {
      category: 'Performance',
      stats: [
        { label: 'Team Score', value: '87%', icon: TrendingUp, trend: 'up' },
        { label: 'Tasks Complete', value: '45', icon: CheckCircle, trend: 'up' },
        { label: 'Goals Met', value: '92%', icon: Target, trend: 'stable' }
      ]
    }
  ], []);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-4 border-red-500 bg-red-50';
      case 'medium':
        return 'border-l-4 border-yellow-500 bg-yellow-50';
      case 'low':
        return 'border-l-4 border-green-500 bg-green-50';
      default:
        return 'border-l-4 border-gray-500 bg-gray-50';
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
        onClick={handleClose}
      />

      {/* Sidebar */}
      <div
        className={cn(
          "fixed left-0 top-0 h-full bg-white shadow-xl z-30 transform transition-transform duration-300 ease-in-out overflow-y-auto",
          "md:relative md:translate-x-0 md:shadow-lg md:top-0 md:h-full",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
        style={{ width: '368px' }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-green-500 to-green-600">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
              <Users className="h-6 w-6 text-green-500" />
            </div>
            <div>
              <h2 className="text-white font-semibold">Team Management</h2>
              <p className="text-green-100 text-sm">Supervisor Control Center</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 rounded-lg text-white hover:bg-white hover:bg-opacity-20 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* User Info */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
              <span className="text-white font-medium">
                {getInitials(user?.first_name || '', user?.last_name || '')}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="font-medium text-gray-900 truncate">
                {user?.first_name} {user?.last_name}
              </p>
              <p className="text-sm text-gray-500">Team Supervisor</p>
              <p className="text-xs text-gray-400">Managing 12 team members</p>
            </div>
          </div>
        </div>

        {/* Priority Actions */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-4">Priority Actions</h3>
          <div className="space-y-2">
            {managementActions.map((action, index) => {
              const Icon = action.icon;
              return (
                <Link
                  key={index}
                  href={action.href}
                  onClick={handleClose}
                  className={cn(
                    "flex items-center justify-between p-3 rounded-lg transition-colors group",
                    getPriorityColor(action.priority)
                  )}
                >
                  <div className="flex items-center">
                    <Icon className="h-5 w-5 mr-3 text-green-600" />
                    <div>
                      <div className="font-medium text-sm text-gray-900">{action.name}</div>
                      <div className="text-xs text-gray-500">{action.description}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {action.badge && (
                      <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                        {action.badge}
                      </span>
                    )}
                    <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-green-600" />
                  </div>
                </Link>
              );
            })}
          </div>
        </div>

        {/* Team Statistics */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-4">Team Statistics</h3>
          <div className="space-y-4">
            {teamStats.map((category, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">{category.category}</h4>
                <div className="grid grid-cols-3 gap-2">
                  {category.stats.map((stat, statIndex) => {
                    const Icon = stat.icon;
                    return (
                      <div key={statIndex} className="text-center">
                        <Icon className="h-4 w-4 mx-auto mb-1 text-green-600" />
                        <div className="text-sm font-semibold text-gray-900">{stat.value}</div>
                        <div className="text-xs text-gray-500">{stat.label}</div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Management Areas */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-4">Management Areas</h3>
          <div className="space-y-2">
            {managementAreas.map((area, index) => {
              const Icon = area.icon;
              const isCurrentPage = pathname === area.href;
              return (
                <Link
                  key={index}
                  href={area.href}
                  onClick={handleClose}
                  className={cn(
                    "flex items-center justify-between p-3 rounded-lg transition-colors",
                    isCurrentPage
                      ? "bg-green-50 text-green-700 border border-green-200"
                      : "hover:bg-gray-50 text-gray-700"
                  )}
                >
                  <div className="flex items-center">
                    <Icon className={cn(
                      "h-4 w-4 mr-3",
                      isCurrentPage ? "text-green-600" : "text-gray-400"
                    )} />
                    <div>
                      <div className="font-medium text-sm">{area.name}</div>
                      <div className="text-xs text-gray-500">{area.description}</div>
                      <div className="flex space-x-2 mt-1">
                        {Object.entries(area.stats).map(([key, value]) => (
                          <span key={key} className="text-xs bg-gray-100 px-2 py-1 rounded">
                            {key}: {value}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  <ChevronRight className="h-3 w-3 text-gray-400" />
                </Link>
              );
            })}
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 mt-auto">
          <div className="space-y-2">
            <Link
              href="/supervisor/settings"
              onClick={handleClose}
              className="flex items-center p-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Link>
            <button
              onClick={handleLogout}
              className="flex items-center w-full p-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default SupervisorManageSidebar;
