import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { AuthProvider } from '@/providers/AuthProvider';
import StaffBiostarPage from '@/app/(auth)/(staff)/staff/info/biostar/page';
import EmployeeBiostarWidget from '@/components/biostar/EmployeeBiostarWidget';
import { useBiostarAttendance } from '@/hooks/useBiostarAttendance';
import { biostarApi } from '@/lib/biostarApi';

// Mock the BioStar attendance hook
jest.mock('@/hooks/useBiostarAttendance');
const mockUseBiostarAttendance = useBiostarAttendance as jest.MockedFunction<typeof useBiostarAttendance>;

// Mock the BioStar API
jest.mock('@/lib/biostarApi', () => ({
  biostarApi: {
    getUserById: jest.fn(),
    getEvents: jest.fn(),
  },
}));

// Mock the AuthProvider
jest.mock('@/providers/AuthProvider', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useAuth: () => ({
    user: {
      id: 1,
      employee_id: 'EMP001',
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      role: 'employee',
    }
  })
}));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
  }),
  useParams: () => ({}),
}));

const mockUser = {
  id: 1,
  employee_id: 'EMP001',
  first_name: 'John',
  last_name: 'Doe',
  email: '<EMAIL>',
  role: 'employee',
};

const mockBiostarData = {
  todayAttendance: {
    id: 'att-001',
    employee_id: 'EMP001',
    employee_name: 'John Doe',
    date: '2024-12-21',
    first_in: '2024-12-21T08:30:00Z',
    last_out: null,
    total_hours: 0,
    break_time: 0,
    overtime: 0,
    status: 'PRESENT' as const,
    events: [],
    biostar_synced: true,
  },
  devices: [
    {
      id: 'dev-001',
      name: 'Main Entrance',
      ip: '*************',
      port: 8080,
      status: 'ONLINE' as const,
      type: 'Fingerprint Scanner',
      location: 'Main Building',
    },
    {
      id: 'dev-002',
      name: 'Back Entrance',
      ip: '*************',
      port: 8080,
      status: 'OFFLINE' as const,
      type: 'Face Recognition',
      location: 'Back Building',
    },
  ],
  connected: true,
  loading: false,
  error: null,
  summary: {
    todayStatus: 'PRESENT' as const,
    checkInTime: '2024-12-21T08:30:00Z',
    hoursWorked: 0,
    breakTime: 0,
    overtime: 0,
    weeklyHours: 32,
    monthlyAttendance: 20,
  },
};

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <AuthProvider>
    {children}
  </AuthProvider>
);



describe('Employee BioStar View', () => {
  beforeEach(() => {
    // Mock API responses
    biostarApi.getUserById.mockResolvedValue({
      id: 'user-001',
      user_id: 'EMP001',
      name: 'John Doe',
      email: '<EMAIL>',
      department: 'Engineering',
      position: 'Software Engineer',
      disabled: false,
      created: '2024-01-01T00:00:00Z',
      updated: '2024-12-21T00:00:00Z'
    });

    biostarApi.getEvents.mockResolvedValue({
      results: [],
      count: 0
    });

    mockUseBiostarAttendance.mockReturnValue({
      ...mockBiostarData,
      attendanceRecords: [],
      realtimeUpdates: [],
      refresh: jest.fn(),
      getAttendanceRange: jest.fn(),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('StaffBiostarPage', () => {
    it('should render the BioStar profile page', async () => {
      render(
        <TestWrapper>
          <StaffBiostarPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('BioStar Profile')).toBeInTheDocument();
        expect(screen.getByText('Manage your biometric data and view access history')).toBeInTheDocument();
      });
    });

    it('should show connection status', async () => {
      render(
        <TestWrapper>
          <StaffBiostarPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('BioStar Connected')).toBeInTheDocument();
      });
    });

    it('should display profile status cards', async () => {
      render(
        <TestWrapper>
          <StaffBiostarPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Profile Status')).toBeInTheDocument();
        expect(screen.getByText('Fingerprints')).toBeInTheDocument();
        expect(screen.getByText('Face Templates')).toBeInTheDocument();
        expect(screen.getByText('Devices')).toBeInTheDocument();
      });
    });

    it('should show tab navigation', async () => {
      render(
        <TestWrapper>
          <StaffBiostarPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Biometric Profile')).toBeInTheDocument();
        expect(screen.getByText('Devices')).toBeInTheDocument();
        expect(screen.getByText('Access History')).toBeInTheDocument();
        expect(screen.getByText('Security')).toBeInTheDocument();
      });
    });

    it('should switch between tabs', async () => {
      render(
        <TestWrapper>
          <StaffBiostarPage />
        </TestWrapper>
      );

      await waitFor(() => {
        const devicesTab = screen.getByText('Devices');
        fireEvent.click(devicesTab);
        expect(screen.getByText('System Status')).toBeInTheDocument();
      });
    });

    it('should show device information in devices tab', async () => {
      render(
        <TestWrapper>
          <StaffBiostarPage />
        </TestWrapper>
      );

      await waitFor(() => {
        const devicesTab = screen.getByText('Devices');
        fireEvent.click(devicesTab);

        expect(screen.getByText('Available Devices')).toBeInTheDocument();
        expect(screen.getByText('Main Entrance')).toBeInTheDocument();
        expect(screen.getByText('Back Entrance')).toBeInTheDocument();
      });
    });

    it('should handle connection errors', async () => {
      mockUseBiostarAttendance.mockReturnValue({
        ...mockBiostarData,
        connected: false,
        error: 'Connection failed',
        attendanceRecords: [],
        realtimeUpdates: [],
        refresh: jest.fn(),
        getAttendanceRange: jest.fn(),
      });

      render(
        <TestWrapper>
          <StaffBiostarPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Connection Error')).toBeInTheDocument();
        expect(screen.getByText('Connection failed')).toBeInTheDocument();
      });
    });

    it('should show refresh button and handle refresh', async () => {
      const mockRefresh = jest.fn();
      mockUseBiostarAttendance.mockReturnValue({
        ...mockBiostarData,
        attendanceRecords: [],
        realtimeUpdates: [],
        refresh: mockRefresh,
        getAttendanceRange: jest.fn(),
      });

      render(
        <TestWrapper>
          <StaffBiostarPage />
        </TestWrapper>
      );

      await waitFor(() => {
        const refreshButton = screen.getByText('Refresh');
        fireEvent.click(refreshButton);
        expect(mockRefresh).toHaveBeenCalled();
      });
    });
  });

  describe('EmployeeBiostarWidget', () => {
    it('should render the BioStar widget', async () => {
      render(
        <TestWrapper>
          <EmployeeBiostarWidget employeeId="EMP001" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('BioStar Profile')).toBeInTheDocument();
      });
    });

    it('should show connection status in widget', async () => {
      render(
        <TestWrapper>
          <EmployeeBiostarWidget employeeId="EMP001" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Connected')).toBeInTheDocument();
      });
    });

    it('should display biometric stats', async () => {
      render(
        <TestWrapper>
          <EmployeeBiostarWidget employeeId="EMP001" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Fingerprints')).toBeInTheDocument();
        expect(screen.getByText('Face Template')).toBeInTheDocument();
      });
    });

    it('should show today\'s attendance status', async () => {
      render(
        <TestWrapper>
          <EmployeeBiostarWidget employeeId="EMP001" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Today\'s Status')).toBeInTheDocument();
        expect(screen.getByText('PRESENT')).toBeInTheDocument();
      });
    });

    it('should display recent access events', async () => {
      render(
        <TestWrapper>
          <EmployeeBiostarWidget employeeId="EMP001" showFullDetails={true} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Recent Access')).toBeInTheDocument();
      });
    });

    it('should show device status summary', async () => {
      render(
        <TestWrapper>
          <EmployeeBiostarWidget employeeId="EMP001" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Devices Online')).toBeInTheDocument();
        expect(screen.getByText('1/2')).toBeInTheDocument(); // 1 online out of 2 total
      });
    });

    it('should have link to full profile', async () => {
      render(
        <TestWrapper>
          <EmployeeBiostarWidget employeeId="EMP001" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('View Full BioStar Profile →')).toBeInTheDocument();
      });
    });

    it('should handle disconnected state', async () => {
      mockUseBiostarAttendance.mockReturnValue({
        ...mockBiostarData,
        connected: false,
        attendanceRecords: [],
        realtimeUpdates: [],
        refresh: jest.fn(),
        getAttendanceRange: jest.fn(),
      });

      render(
        <TestWrapper>
          <EmployeeBiostarWidget employeeId="EMP001" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Disconnected')).toBeInTheDocument();
      });
    });

    it('should show loading state', () => {
      mockUseBiostarAttendance.mockReturnValue({
        ...mockBiostarData,
        loading: true,
        attendanceRecords: [],
        realtimeUpdates: [],
        refresh: jest.fn(),
        getAttendanceRange: jest.fn(),
      });

      render(
        <TestWrapper>
          <EmployeeBiostarWidget employeeId="EMP001" />
        </TestWrapper>
      );

      expect(screen.getByText('Loading BioStar data...')).toBeInTheDocument();
    });
  });

  describe('Integration Tests', () => {
    it('should integrate with useBiostarAttendance hook correctly', async () => {
      const mockGetAttendanceRange = jest.fn();
      mockUseBiostarAttendance.mockReturnValue({
        ...mockBiostarData,
        attendanceRecords: [],
        realtimeUpdates: [],
        refresh: jest.fn(),
        getAttendanceRange: mockGetAttendanceRange,
      });

      render(
        <TestWrapper>
          <StaffBiostarPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockUseBiostarAttendance).toHaveBeenCalledWith({
          employeeId: undefined, // No user context in test
          autoRefresh: true,
          enableRealTime: false,
        });
      });
    });

    it('should handle empty device list', async () => {
      mockUseBiostarAttendance.mockReturnValue({
        ...mockBiostarData,
        devices: [],
        attendanceRecords: [],
        realtimeUpdates: [],
        refresh: jest.fn(),
        getAttendanceRange: jest.fn(),
      });

      render(
        <TestWrapper>
          <StaffBiostarPage />
        </TestWrapper>
      );

      await waitFor(() => {
        const devicesTab = screen.getByText('Devices');
        fireEvent.click(devicesTab);
        expect(screen.getByText('No Devices Found')).toBeInTheDocument();
      });
    });
  });
});
