'use client';

import React, { useState } from 'react';
import {
  DollarSign,
  Download,
  Eye,
  Calendar,
  TrendingUp,
  <PERSON><PERSON><PERSON>,
  CreditCard,
  Building,
  Shield,
  Plus,
  Minus
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { formatDate } from '@/lib/utils';

const SupervisorSalaryPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  // Mock salary data
  const salaryData = {
    basic_salary: 120000,
    currency: 'KSH',
    pay_frequency: 'Monthly',
    last_increment: '2023-07-01',
    increment_percentage: 8.5,
    next_review: '2024-07-01',
    grade: 'Grade 8',
    step: 'Step 3'
  };

  const currentPayslip = {
    period: '2024-12-01',
    basic_salary: 120000,
    allowances: {
      house_allowance: 25000,
      transport_allowance: 15000,
      supervisory_allowance: 10000,
      medical_allowance: 5000
    },
    deductions: {
      nssf: 7200, // 6% of basic
      nhif: 3300, // 2.75% of basic
      housing_levy: 1800, // 1.5% of basic
      paye: 18500,
      loan_repayment: 8000
    },
    overtime: 12000,
    bonus: 0,
    gross_pay: 187000,
    total_deductions: 38800,
    net_pay: 148200
  };

  const salaryHistory = [
    {
      period: '2024-12',
      gross_pay: 187000,
      net_pay: 148200,
      status: 'Paid'
    },
    {
      period: '2024-11',
      gross_pay: 175000,
      net_pay: 138500,
      status: 'Paid'
    },
    {
      period: '2024-10',
      gross_pay: 175000,
      net_pay: 138500,
      status: 'Paid'
    }
  ];

  const benefits = [
    {
      name: 'Medical Insurance',
      provider: 'AAR Insurance',
      coverage: 'Family',
      value: 'KSH 150,000 annually'
    },
    {
      name: 'Life Insurance',
      provider: 'Jubilee Insurance',
      coverage: '3x Annual Salary',
      value: 'KSH 4,320,000'
    },
    {
      name: 'Retirement Benefits',
      provider: 'Company Pension Scheme',
      coverage: '15% Contribution',
      value: 'KSH 18,000 monthly'
    }
  ];

  const tabs = [
    { id: 'overview', label: 'Overview', icon: DollarSign },
    { id: 'payslip', label: 'Current Payslip', icon: CreditCard },
    { id: 'history', label: 'Salary History', icon: Calendar },
    { id: 'benefits', label: 'Benefits', icon: Shield }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Salary Information</h1>
          <p className="text-gray-600 mt-1">
            View your salary details, payslips, and benefits information
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Download Payslip
          </Button>
          <Button variant="primary">
            <Eye className="h-4 w-4 mr-2" />
            View Statement
          </Button>
        </div>
      </div>

      {/* Salary Overview Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-5 w-5 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Basic Salary</p>
                <p className="text-lg font-semibold text-gray-900">
                  {salaryData.currency} {salaryData.basic_salary.toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <CreditCard className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Net Pay</p>
                <p className="text-lg font-semibold text-gray-900">
                  {salaryData.currency} {currentPayslip.net_pay.toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-5 w-5 text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Last Increment</p>
                <p className="text-lg font-semibold text-gray-900">
                  {salaryData.increment_percentage}%
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Calendar className="h-5 w-5 text-orange-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Next Review</p>
                <p className="text-lg font-semibold text-gray-900">
                  {formatDate(salaryData.next_review, 'MMM yyyy')}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Salary Structure</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Basic Salary</span>
                    <span className="font-medium">KSH {salaryData.basic_salary.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Grade</span>
                    <span className="font-medium">{salaryData.grade}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Step</span>
                    <span className="font-medium">{salaryData.step}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Pay Frequency</span>
                    <span className="font-medium">{salaryData.pay_frequency}</span>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Changes</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center">
                      <Plus className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-sm text-gray-900">Salary Increment</span>
                    </div>
                    <span className="text-sm font-medium text-green-600">+{salaryData.increment_percentage}%</span>
                  </div>
                  <div className="text-xs text-gray-500">
                    Last updated: {formatDate(salaryData.last_increment, 'MMM dd, yyyy')}
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {activeTab === 'payslip' && (
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">
                  Payslip for {formatDate(currentPayslip.period, 'MMMM yyyy')}
                </h3>
                <Button variant="secondary" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Download PDF
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Earnings */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-4">Earnings</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Basic Salary</span>
                      <span className="font-medium">KSH {currentPayslip.basic_salary.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">House Allowance</span>
                      <span className="font-medium">KSH {currentPayslip.allowances.house_allowance.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Transport Allowance</span>
                      <span className="font-medium">KSH {currentPayslip.allowances.transport_allowance.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Supervisory Allowance</span>
                      <span className="font-medium">KSH {currentPayslip.allowances.supervisory_allowance.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Medical Allowance</span>
                      <span className="font-medium">KSH {currentPayslip.allowances.medical_allowance.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Overtime</span>
                      <span className="font-medium">KSH {currentPayslip.overtime.toLocaleString()}</span>
                    </div>
                    <div className="border-t pt-3">
                      <div className="flex justify-between font-semibold">
                        <span>Gross Pay</span>
                        <span>KSH {currentPayslip.gross_pay.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Deductions */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-4">Deductions</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">NSSF (6%)</span>
                      <span className="font-medium text-red-600">-KSH {currentPayslip.deductions.nssf.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">NHIF (2.75%)</span>
                      <span className="font-medium text-red-600">-KSH {currentPayslip.deductions.nhif.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Housing Levy (1.5%)</span>
                      <span className="font-medium text-red-600">-KSH {currentPayslip.deductions.housing_levy.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">PAYE Tax</span>
                      <span className="font-medium text-red-600">-KSH {currentPayslip.deductions.paye.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Loan Repayment</span>
                      <span className="font-medium text-red-600">-KSH {currentPayslip.deductions.loan_repayment.toLocaleString()}</span>
                    </div>
                    <div className="border-t pt-3">
                      <div className="flex justify-between font-semibold">
                        <span>Total Deductions</span>
                        <span className="text-red-600">-KSH {currentPayslip.total_deductions.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-8 pt-6 border-t">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-gray-900">Net Pay</span>
                  <span className="text-2xl font-bold text-green-600">
                    KSH {currentPayslip.net_pay.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          </Card>
        )}

        {activeTab === 'history' && (
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Salary History</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Period
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Gross Pay
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Net Pay
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {salaryHistory.map((record, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(record.period, 'MMM yyyy')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          KSH {record.gross_pay.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          KSH {record.net_pay.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {record.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <Button variant="secondary" size="sm">
                            <Download className="h-3 w-3 mr-1" />
                            Download
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </Card>
        )}

        {activeTab === 'benefits' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {benefits.map((benefit, index) => (
              <Card key={index}>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <Shield className="h-6 w-6 text-blue-500 mr-3" />
                    <h3 className="text-lg font-medium text-gray-900">{benefit.name}</h3>
                  </div>
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm text-gray-600">Provider:</span>
                      <p className="font-medium">{benefit.provider}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Coverage:</span>
                      <p className="font-medium">{benefit.coverage}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Value:</span>
                      <p className="font-medium text-green-600">{benefit.value}</p>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SupervisorSalaryPage;
