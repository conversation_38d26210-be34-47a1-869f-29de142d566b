'use client';

import React, { useState, useEffect } from 'react';
import {
  X,
  Calendar,
  Users,
  DollarSign,
  AlertTriangle,
  Save,
  Loader2
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { cn, formatDate } from '@/lib/utils';
import { PayCycle } from '@/types';

interface PayCycleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (payCycle: PayCycle) => Promise<void>;
  payCycle?: PayCycle | null;
  existingPayCycles: PayCycle[];
  mode: 'create' | 'edit';
}

const PayCycleModal: React.FC<PayCycleModalProps> = ({
  isOpen,
  onClose,
  onSave,
  payCycle,
  existingPayCycles,
  mode
}) => {
  const [formData, setFormData] = useState<PayCycle>({
    pay_period: '',
    start_date: '',
    end_date: '',
    pay_date: '',
    paid: false,
    total_employees: 0,
    total_amount: 0
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && payCycle) {
        setFormData(payCycle);
      } else {
        // Reset form for create mode
        setFormData({
          pay_period: '',
          start_date: '',
          end_date: '',
          pay_date: '',
          paid: false,
          total_employees: 0,
          total_amount: 0
        });
      }
      setErrors({});
    }
  }, [isOpen, mode, payCycle]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required field validation
    if (!formData.pay_period.trim()) {
      newErrors.pay_period = 'Pay period is required';
    }
    if (!formData.start_date) {
      newErrors.start_date = 'Start date is required';
    }
    if (!formData.end_date) {
      newErrors.end_date = 'End date is required';
    }
    if (!formData.pay_date) {
      newErrors.pay_date = 'Pay date is required';
    }

    // Date validation
    if (formData.start_date && formData.end_date) {
      const startDate = new Date(formData.start_date);
      const endDate = new Date(formData.end_date);

      if (endDate <= startDate) {
        newErrors.end_date = 'End date must be after start date';
      }
    }

    if (formData.pay_date && formData.end_date) {
      const payDate = new Date(formData.pay_date);
      const endDate = new Date(formData.end_date);

      if (payDate < endDate) {
        newErrors.pay_date = 'Pay date should be on or after end date';
      }
    }

    // Check for overlapping pay cycles
    if (formData.start_date && formData.end_date) {
      const startDate = new Date(formData.start_date);
      const endDate = new Date(formData.end_date);

      const overlapping = existingPayCycles.find(cycle => {
        // Skip current cycle when editing
        if (mode === 'edit' && cycle.id === payCycle?.id) return false;

        const cycleStart = new Date(cycle.start_date);
        const cycleEnd = new Date(cycle.end_date);

        return (
          (startDate >= cycleStart && startDate <= cycleEnd) ||
          (endDate >= cycleStart && endDate <= cycleEnd) ||
          (startDate <= cycleStart && endDate >= cycleEnd)
        );
      });

      if (overlapping) {
        newErrors.start_date = `Pay cycle overlaps with "${overlapping.pay_period}"`;
        newErrors.end_date = `Pay cycle overlaps with "${overlapping.pay_period}"`;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof PayCycle, value: string | number | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Error saving pay cycle:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Calendar className="h-6 w-6 text-orange-500" />
            <h2 className="text-xl font-semibold text-gray-900">
              {mode === 'create' ? 'Create Pay Cycle' : 'Edit Pay Cycle'}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-md transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Scrollable Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Pay Period */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pay Period *
              </label>
              <Input
                type="text"
                value={formData.pay_period}
                onChange={(e) => handleInputChange('pay_period', e.target.value)}
                placeholder="e.g., January 2024"
                className={errors.pay_period ? 'border-red-500' : ''}
              />
              {errors.pay_period && (
                <p className="mt-1 text-sm text-red-600">{errors.pay_period}</p>
              )}
            </div>

            {/* Date Range */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Start Date *
                </label>
                <Input
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => handleInputChange('start_date', e.target.value)}
                  className={errors.start_date ? 'border-red-500' : ''}
                />
                {errors.start_date && (
                  <p className="mt-1 text-sm text-red-600">{errors.start_date}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  End Date *
                </label>
                <Input
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => handleInputChange('end_date', e.target.value)}
                  className={errors.end_date ? 'border-red-500' : ''}
                />
                {errors.end_date && (
                  <p className="mt-1 text-sm text-red-600">{errors.end_date}</p>
                )}
              </div>
            </div>

            {/* Pay Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pay Date *
              </label>
              <Input
                type="date"
                value={formData.pay_date}
                onChange={(e) => handleInputChange('pay_date', e.target.value)}
                className={errors.pay_date ? 'border-red-500' : ''}
              />
              {errors.pay_date && (
                <p className="mt-1 text-sm text-red-600">{errors.pay_date}</p>
              )}
              <p className="mt-1 text-sm text-gray-500">
                Recommended: On or after the end date
              </p>
            </div>

            {/* Employee Count */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Total Employees
              </label>
              <Input
                type="number"
                value={formData.total_employees}
                onChange={(e) => handleInputChange('total_employees', parseInt(e.target.value) || 0)}
                placeholder="0"
                min="0"
              />
            </div>

            {/* Total Amount */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Total Amount (KSH)
              </label>
              <Input
                type="number"
                value={formData.total_amount}
                onChange={(e) => handleInputChange('total_amount', parseFloat(e.target.value) || 0)}
                placeholder="0.00"
                min="0"
                step="0.01"
              />
            </div>

            {/* Status */}
            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.paid}
                  onChange={(e) => handleInputChange('paid', e.target.checked)}
                  className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                />
                <span className="text-sm font-medium text-gray-700">
                  Mark as Paid
                </span>
              </label>
            </div>

            {/* Warning for overlapping cycles */}
            {(errors.start_date || errors.end_date) && (
              <div className="flex items-start space-x-2 p-3 bg-red-50 border border-red-200 rounded-md">
                <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-red-800">
                    Pay Cycle Overlap Detected
                  </p>
                  <p className="text-sm text-red-700">
                    This pay cycle overlaps with an existing one. Please adjust the dates.
                  </p>
                </div>
              </div>
            )}
          </form>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={() => handleSubmit({} as React.FormEvent)}
            disabled={isLoading}
            className="min-w-[100px]"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {mode === 'create' ? 'Create' : 'Update'}
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PayCycleModal;
