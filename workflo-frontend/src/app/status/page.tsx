'use client';

import React from 'react';

const StatusPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-green-500 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span className="text-white text-2xl">✓</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            WorkFlow Frontend
          </h1>
          <p className="text-gray-600 mb-6">
            Application is running successfully!
          </p>
          
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">Features Available:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>✓ Authentication System</li>
                <li>✓ Dashboard with Statistics</li>
                <li>✓ Employee Management</li>
                <li>✓ Leave Management</li>
                <li>✓ Calendar Integration</li>
                <li>✓ Mock API Integration</li>
              </ul>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">Demo Credentials:</h3>
              <div className="text-sm text-blue-800 space-y-1">
                <p><strong>Admin:</strong> <EMAIL> / admin123</p>
                <p><strong>Employee:</strong> <EMAIL> / employee123</p>
              </div>
            </div>
          </div>
          
          <div className="mt-6 space-y-2">
            <a
              href="/login"
              className="block w-full bg-orange-500 text-white py-2 px-4 rounded-md hover:bg-orange-600 transition-colors"
            >
              Go to Login
            </a>
            <a
              href="/dashboard"
              className="block w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
            >
              Go to Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatusPage;
