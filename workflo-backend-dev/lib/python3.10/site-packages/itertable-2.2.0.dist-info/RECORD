itertable-2.2.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
itertable-2.2.0.dist-info/LICENSE,sha256=XW_PrlHguoUsGC8CC99dZF6MDpI2smvwwlk7TwhZgQI,1083
itertable-2.2.0.dist-info/METADATA,sha256=TVH3nuhohPljN7bPSoIvxSKIJtCBSjUxoR0rEnpQWCU,3722
itertable-2.2.0.dist-info/RECORD,,
itertable-2.2.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
itertable-2.2.0.dist-info/WHEEL,sha256=pkctZYzUS4AYVn6dJ-7367OJZivF2e8RA9b_ZBjif18,92
itertable-2.2.0.dist-info/top_level.txt,sha256=_x6z5PV4nqHAT48AGiTAkAebzbMkgR3GG7yaRcETVCY,10
itertable/__init__.py,sha256=rc5dkFH2S3BXUUSyOyuh_8CVYbrMDQS2yU1x235S1C0,2153
itertable/__main__.py,sha256=gxBN3cK82wU1GQVhdjxR9WtuZFTClPP8Vp_hitv6VAc,65
itertable/__pycache__/__init__.cpython-310.pyc,,
itertable/__pycache__/__main__.cpython-310.pyc,,
itertable/__pycache__/base.cpython-310.pyc,,
itertable/__pycache__/commands.cpython-310.pyc,,
itertable/__pycache__/exceptions.cpython-310.pyc,,
itertable/__pycache__/loaders.cpython-310.pyc,,
itertable/__pycache__/mappers.cpython-310.pyc,,
itertable/__pycache__/util.cpython-310.pyc,,
itertable/__pycache__/version.cpython-310.pyc,,
itertable/base.py,sha256=10-aECkWZICRXNEApPZmubMjUKbSGFQ7CPc5ToyJY6o,6536
itertable/commands.py,sha256=cmjpXq_monssP-ve4NlsoSSZ2VUiwPZBPkPhY5Gjqig,2413
itertable/exceptions.py,sha256=-LPknL2YdMVUrC8OB0fxnNLdwyEn7b2nvWM1X7QHxWM,1265
itertable/gis/__init__.py,sha256=6Fm2Y6a-6QxT5YzQk_6yRsFGZi_d8XFAFIF6VWTeNK8,947
itertable/gis/__pycache__/__init__.cpython-310.pyc,,
itertable/gis/__pycache__/mixins.cpython-310.pyc,,
itertable/gis/mixins.py,sha256=MEB1ca_7gLq-_ppoTo3pXqvRdpOEE-pCTKkB2Q91V2o,4951
itertable/loaders.py,sha256=eJa_CqFJkFmbjfGzYJ_SinTLICyBj8RbVSAYuiDmEhM,5030
itertable/mappers.py,sha256=3Pbwzq2jHxlgGMpDhcn0u-q2alvmUjKYs73FWg-Lra4,5794
itertable/parsers/__init__.py,sha256=5aYtkD7nuw8F_HqX-pJ_DQpOfoGTasdLzrEFKBjSTY0,205
itertable/parsers/__pycache__/__init__.cpython-310.pyc,,
itertable/parsers/__pycache__/base.cpython-310.pyc,,
itertable/parsers/__pycache__/readers.cpython-310.pyc,,
itertable/parsers/__pycache__/text.cpython-310.pyc,,
itertable/parsers/__pycache__/xls.cpython-310.pyc,,
itertable/parsers/base.py,sha256=hrNj2D3SzzuU86UEL5HftQOPeeHwklYbiZr8gGYW6X0,176
itertable/parsers/readers.py,sha256=bS4OyW0pbd4SSRkDlRMq-6rMKmpGtjQNULWdCK7WjJI,2298
itertable/parsers/text.py,sha256=nk8sV9lVE9c4CpMsznnIlbswzhlBiJE8oF6KaNuixas,3755
itertable/parsers/xls.py,sha256=jzO5fytA0l7Qw-miBQFSlW-i4hh3XUWOt8heB6dVH5g,9201
itertable/util.py,sha256=Cp_q3434TO6vqRcD9SOK9KRB-LG5xRreoYNfqDwHnP8,5665
itertable/version.py,sha256=dxXEfJFO-huoMpS-XiGjhF7YooN-RSWqH6Jd4NsgE1Q,160
