'use client';

import React, { useState, useEffect } from 'react';
import {
  X,
  FileText,
  User,
  DollarSign,
  Calculator,
  Save,
  Loader2,
  AlertTriangle
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { cn, formatCurrency } from '@/lib/utils';

interface Employee {
  id: number;
  name: string;
  employee_id: string;
  department: string;
  base_salary: number;
}

interface Payslip {
  id?: number;
  employee_id: number;
  employee_name: string;
  employee_code: string;
  pay_period: string;
  pay_date: string;
  gross_salary: number;
  basic_salary: number;
  allowances: number;
  overtime: number;
  bonuses: number;
  total_deductions: number;
  tax_deduction: number;
  nssf_deduction: number;
  nhif_deduction: number;
  housing_levy: number;
  other_deductions: number;
  net_salary: number;
  status: 'generated' | 'sent' | 'viewed';
  generated_at?: string;
}

interface PayslipModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (payslip: Payslip) => Promise<void>;
  payslip?: Payslip | null;
  employees: Employee[];
  mode: 'create' | 'edit' | 'view';
}

const PayslipModal: React.FC<PayslipModalProps> = ({
  isOpen,
  onClose,
  onSave,
  payslip,
  employees,
  mode
}) => {
  const [formData, setFormData] = useState<Payslip>({
    employee_id: 0,
    employee_name: '',
    employee_code: '',
    pay_period: '',
    pay_date: '',
    gross_salary: 0,
    basic_salary: 0,
    allowances: 0,
    overtime: 0,
    bonuses: 0,
    total_deductions: 0,
    tax_deduction: 0,
    nssf_deduction: 0,
    nhif_deduction: 0,
    housing_levy: 0,
    other_deductions: 0,
    net_salary: 0,
    status: 'generated'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      if ((mode === 'edit' || mode === 'view') && payslip) {
        setFormData(payslip);
      } else {
        // Reset form for create mode
        const currentDate = new Date();
        const currentMonth = currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

        setFormData({
          employee_id: 0,
          employee_name: '',
          employee_code: '',
          pay_period: currentMonth,
          pay_date: currentDate.toISOString().split('T')[0],
          gross_salary: 0,
          basic_salary: 0,
          allowances: 0,
          overtime: 0,
          bonuses: 0,
          total_deductions: 0,
          tax_deduction: 0,
          nssf_deduction: 0,
          nhif_deduction: 0,
          housing_levy: 0,
          other_deductions: 0,
          net_salary: 0,
          status: 'generated'
        });
      }
      setErrors({});
    }
  }, [isOpen, mode, payslip]);

  // Calculate deductions and net salary
  useEffect(() => {
    const grossSalary = formData.basic_salary + formData.allowances + formData.overtime + formData.bonuses;

    // Kenyan tax calculation (simplified)
    let taxDeduction = 0;
    if (grossSalary > 24000) {
      if (grossSalary <= 32333) {
        taxDeduction = (grossSalary - 24000) * 0.25;
      } else if (grossSalary <= 500000) {
        taxDeduction = 8333 * 0.25 + (grossSalary - 32333) * 0.30;
      } else if (grossSalary <= 800000) {
        taxDeduction = 8333 * 0.25 + 467667 * 0.30 + (grossSalary - 500000) * 0.325;
      } else {
        taxDeduction = 8333 * 0.25 + 467667 * 0.30 + 300000 * 0.325 + (grossSalary - 800000) * 0.35;
      }
    }

    // NSSF (6% with tier system)
    const nssfDeduction = Math.min(grossSalary * 0.06, 2160); // Max KSH 2,160

    // NHIF/SHA (2.75%)
    const nhifDeduction = grossSalary * 0.0275;

    // Housing Levy (1.5%)
    const housingLevy = grossSalary * 0.015;

    const totalDeductions = taxDeduction + nssfDeduction + nhifDeduction + housingLevy + formData.other_deductions;
    const netSalary = grossSalary - totalDeductions;

    setFormData(prev => ({
      ...prev,
      gross_salary: grossSalary,
      tax_deduction: Math.round(taxDeduction),
      nssf_deduction: Math.round(nssfDeduction),
      nhif_deduction: Math.round(nhifDeduction),
      housing_levy: Math.round(housingLevy),
      total_deductions: Math.round(totalDeductions),
      net_salary: Math.round(netSalary)
    }));
  }, [formData.basic_salary, formData.allowances, formData.overtime, formData.bonuses, formData.other_deductions]);

  const handleEmployeeChange = (employeeId: number) => {
    const employee = employees.find(emp => emp.id === employeeId);
    if (employee) {
      setFormData(prev => ({
        ...prev,
        employee_id: employeeId,
        employee_name: employee.name,
        employee_code: employee.employee_id,
        basic_salary: employee.base_salary
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.employee_id) {
      newErrors.employee_id = 'Employee is required';
    }
    if (!formData.pay_period.trim()) {
      newErrors.pay_period = 'Pay period is required';
    }
    if (!formData.pay_date) {
      newErrors.pay_date = 'Pay date is required';
    }
    if (formData.basic_salary <= 0) {
      newErrors.basic_salary = 'Basic salary must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof Payslip, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Error saving payslip:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  const isReadOnly = mode === 'view';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <FileText className="h-6 w-6 text-orange-500" />
            <h2 className="text-xl font-semibold text-gray-900">
              {mode === 'create' ? 'Generate Payslip' : mode === 'edit' ? 'Edit Payslip' : 'View Payslip'}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-md transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Scrollable Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Employee Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Employee *
                </label>
                <select
                  value={formData.employee_id}
                  onChange={(e) => handleEmployeeChange(parseInt(e.target.value))}
                  disabled={isReadOnly}
                  className={cn(
                    "w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500",
                    errors.employee_id ? 'border-red-500' : '',
                    isReadOnly ? 'bg-gray-50' : ''
                  )}
                >
                  <option value="">Select Employee</option>
                  {employees.map(employee => (
                    <option key={employee.id} value={employee.id}>
                      {employee.name} ({employee.employee_id})
                    </option>
                  ))}
                </select>
                {errors.employee_id && (
                  <p className="mt-1 text-sm text-red-600">{errors.employee_id}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Employee Code
                </label>
                <Input
                  type="text"
                  value={formData.employee_code}
                  readOnly
                  className="bg-gray-50"
                />
              </div>
            </div>

            {/* Pay Period and Date */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pay Period *
                </label>
                <Input
                  type="text"
                  value={formData.pay_period}
                  onChange={(e) => handleInputChange('pay_period', e.target.value)}
                  placeholder="e.g., January 2024"
                  readOnly={isReadOnly}
                  className={errors.pay_period ? 'border-red-500' : ''}
                />
                {errors.pay_period && (
                  <p className="mt-1 text-sm text-red-600">{errors.pay_period}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pay Date *
                </label>
                <Input
                  type="date"
                  value={formData.pay_date}
                  onChange={(e) => handleInputChange('pay_date', e.target.value)}
                  readOnly={isReadOnly}
                  className={errors.pay_date ? 'border-red-500' : ''}
                />
                {errors.pay_date && (
                  <p className="mt-1 text-sm text-red-600">{errors.pay_date}</p>
                )}
              </div>
            </div>

            {/* Earnings Section */}
            <div className="bg-green-50 rounded-lg p-4">
              <h3 className="text-lg font-medium text-green-800 mb-4 flex items-center">
                <DollarSign className="h-5 w-5 mr-2" />
                Earnings
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Basic Salary (KSH) *
                  </label>
                  <Input
                    type="number"
                    value={formData.basic_salary}
                    onChange={(e) => handleInputChange('basic_salary', parseFloat(e.target.value) || 0)}
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    readOnly={isReadOnly}
                    className={errors.basic_salary ? 'border-red-500' : ''}
                  />
                  {errors.basic_salary && (
                    <p className="mt-1 text-sm text-red-600">{errors.basic_salary}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Allowances (KSH)
                  </label>
                  <Input
                    type="number"
                    value={formData.allowances}
                    onChange={(e) => handleInputChange('allowances', parseFloat(e.target.value) || 0)}
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    readOnly={isReadOnly}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Overtime (KSH)
                  </label>
                  <Input
                    type="number"
                    value={formData.overtime}
                    onChange={(e) => handleInputChange('overtime', parseFloat(e.target.value) || 0)}
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    readOnly={isReadOnly}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bonuses (KSH)
                  </label>
                  <Input
                    type="number"
                    value={formData.bonuses}
                    onChange={(e) => handleInputChange('bonuses', parseFloat(e.target.value) || 0)}
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    readOnly={isReadOnly}
                  />
                </div>
              </div>

              <div className="mt-4 p-3 bg-white rounded border">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-700">Gross Salary:</span>
                  <span className="text-lg font-bold text-green-600">
                    {formatCurrency(formData.gross_salary, 'KSH')}
                  </span>
                </div>
              </div>
            </div>

            {/* Deductions Section */}
            <div className="bg-red-50 rounded-lg p-4">
              <h3 className="text-lg font-medium text-red-800 mb-4 flex items-center">
                <Calculator className="h-5 w-5 mr-2" />
                Deductions
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tax Deduction (KSH)
                  </label>
                  <Input
                    type="number"
                    value={formData.tax_deduction}
                    readOnly
                    className="bg-gray-50"
                  />
                  <p className="mt-1 text-xs text-gray-500">Auto-calculated based on Kenyan tax brackets</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    NSSF (6%) (KSH)
                  </label>
                  <Input
                    type="number"
                    value={formData.nssf_deduction}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    NHIF/SHA (2.75%) (KSH)
                  </label>
                  <Input
                    type="number"
                    value={formData.nhif_deduction}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Housing Levy (1.5%) (KSH)
                  </label>
                  <Input
                    type="number"
                    value={formData.housing_levy}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Other Deductions (KSH)
                  </label>
                  <Input
                    type="number"
                    value={formData.other_deductions}
                    onChange={(e) => handleInputChange('other_deductions', parseFloat(e.target.value) || 0)}
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    readOnly={isReadOnly}
                  />
                </div>
              </div>

              <div className="mt-4 p-3 bg-white rounded border">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-700">Total Deductions:</span>
                  <span className="text-lg font-bold text-red-600">
                    -{formatCurrency(formData.total_deductions, 'KSH')}
                  </span>
                </div>
              </div>
            </div>

            {/* Net Salary */}
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex justify-between items-center">
                <span className="text-xl font-medium text-blue-800">Net Salary:</span>
                <span className="text-2xl font-bold text-blue-600">
                  {formatCurrency(formData.net_salary, 'KSH')}
                </span>
              </div>
            </div>

            {/* Status */}
            {!isReadOnly && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  <option value="generated">Generated</option>
                  <option value="sent">Sent</option>
                  <option value="viewed">Viewed</option>
                </select>
              </div>
            )}
          </form>
        </div>

        {/* Footer */}
        {!isReadOnly && (
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={() => handleSubmit({} as React.FormEvent)}
              disabled={isLoading}
              className="min-w-[100px]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {mode === 'create' ? 'Generate' : 'Update'}
                </>
              )}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default PayslipModal;
