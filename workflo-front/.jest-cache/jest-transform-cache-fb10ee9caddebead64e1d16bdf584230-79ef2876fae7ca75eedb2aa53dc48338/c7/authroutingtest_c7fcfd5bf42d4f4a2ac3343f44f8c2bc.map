{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/__tests__/auth-routing.test.tsx"], "sourcesContent": ["/**\n * Test file for role-based authentication routing\n */\n\nimport { describe, it, expect, beforeEach, jest } from '@jest/globals';\n\n// Mock the next/navigation module\nconst mockPush = jest.fn();\nconst mockReplace = jest.fn();\n\njest.mock('next/navigation', () => ({\n  useRouter: () => ({\n    push: mockPush,\n    replace: mockReplace,\n    back: jest.fn(),\n  }),\n  usePathname: () => '/login',\n}));\n\n// Mock the auth provider\nconst mockLogin = jest.fn();\nconst mockUser = { id: 1, email: '<EMAIL>', role: 'hr', first_name: 'Test', last_name: 'User' };\n\njest.mock('@/providers/AuthProvider', () => ({\n  useAuth: () => ({\n    login: mockLogin,\n    user: mockUser,\n    isAuthenticated: false,\n    isLoading: false,\n    error: null,\n    clearError: jest.fn(),\n  }),\n}));\n\ndescribe('Role-based Authentication Routing', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('Role-based redirect paths', () => {\n    it('should redirect admin roles to /dashboard', () => {\n      const adminRoles = ['hr', 'supervisor', 'admin', 'accountant'];\n      \n      adminRoles.forEach(role => {\n        const user = { ...mockUser, role };\n        const getRedirectPath = (user: any): string => {\n          if (!user) return '/login';\n          \n          const adminRoles = ['hr', 'supervisor', 'admin', 'accountant'];\n          if (adminRoles.includes(user.role)) {\n            return '/dashboard';\n          }\n          \n          if (user.role === 'employee') {\n            return '/staff';\n          }\n          \n          return '/dashboard';\n        };\n\n        expect(getRedirectPath(user)).toBe('/dashboard');\n      });\n    });\n\n    it('should redirect employee role to /staff', () => {\n      const user = { ...mockUser, role: 'employee' };\n      const getRedirectPath = (user: any): string => {\n        if (!user) return '/login';\n        \n        const adminRoles = ['hr', 'supervisor', 'admin', 'accountant'];\n        if (adminRoles.includes(user.role)) {\n          return '/dashboard';\n        }\n        \n        if (user.role === 'employee') {\n          return '/staff';\n        }\n        \n        return '/dashboard';\n      };\n\n      expect(getRedirectPath(user)).toBe('/staff');\n    });\n\n    it('should redirect to /login for null user', () => {\n      const getRedirectPath = (user: any): string => {\n        if (!user) return '/login';\n        \n        const adminRoles = ['hr', 'supervisor', 'admin', 'accountant'];\n        if (adminRoles.includes(user.role)) {\n          return '/dashboard';\n        }\n        \n        if (user.role === 'employee') {\n          return '/staff';\n        }\n        \n        return '/dashboard';\n      };\n\n      expect(getRedirectPath(null)).toBe('/login');\n    });\n  });\n\n  describe('Login credentials', () => {\n    it('should have correct demo credentials', () => {\n      const demoCredentials = [\n        { email: '<EMAIL>', password: 'admin123', role: 'hr' },\n        { email: '<EMAIL>', password: 'employee123', role: 'employee' }\n      ];\n\n      expect(demoCredentials[0].email).toBe('<EMAIL>');\n      expect(demoCredentials[0].password).toBe('admin123');\n      expect(demoCredentials[1].email).toBe('<EMAIL>');\n      expect(demoCredentials[1].password).toBe('employee123');\n    });\n  });\n\n  describe('Password visibility toggle', () => {\n    it('should toggle password visibility state', () => {\n      let showPassword = false;\n      const togglePassword = () => {\n        showPassword = !showPassword;\n      };\n\n      expect(showPassword).toBe(false);\n      togglePassword();\n      expect(showPassword).toBe(true);\n      togglePassword();\n      expect(showPassword).toBe(false);\n    });\n  });\n});\n"], "names": ["mockPush", "jest", "fn", "mockReplace", "mock", "useRouter", "push", "replace", "back", "usePathname", "mockLogin", "mockUser", "id", "email", "role", "first_name", "last_name", "useAuth", "login", "user", "isAuthenticated", "isLoading", "error", "clearError", "describe", "beforeEach", "clearAllMocks", "it", "adminRoles", "for<PERSON>ach", "getRedirectPath", "includes", "expect", "toBe", "demoCredentials", "password", "showPassword", "togglePassword"], "mappings": "AAAA;;CAEC;;;;yBAEsD;AAEvD,kCAAkC;AAClC,MAAMA,WAAWC,aAAI,CAACC,EAAE;AACxB,MAAMC,cAAcF,aAAI,CAACC,EAAE;AAE3BD,aAAI,CAACG,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCC,WAAW,IAAO,CAAA;gBAChBC,MAAMN;gBACNO,SAASJ;gBACTK,MAAMP,aAAI,CAACC,EAAE;YACf,CAAA;QACAO,aAAa,IAAM;IACrB,CAAA;AAEA,yBAAyB;AACzB,MAAMC,YAAYT,aAAI,CAACC,EAAE;AACzB,MAAMS,WAAW;IAAEC,IAAI;IAAGC,OAAO;IAAoBC,MAAM;IAAMC,YAAY;IAAQC,WAAW;AAAO;AAEvGf,aAAI,CAACG,IAAI,CAAC,4BAA4B,IAAO,CAAA;QAC3Ca,SAAS,IAAO,CAAA;gBACdC,OAAOR;gBACPS,MAAMR;gBACNS,iBAAiB;gBACjBC,WAAW;gBACXC,OAAO;gBACPC,YAAYtB,aAAI,CAACC,EAAE;YACrB,CAAA;IACF,CAAA;AAEAsB,IAAAA,iBAAQ,EAAC,qCAAqC;IAC5CC,IAAAA,mBAAU,EAAC;QACTxB,aAAI,CAACyB,aAAa;IACpB;IAEAF,IAAAA,iBAAQ,EAAC,6BAA6B;QACpCG,IAAAA,WAAE,EAAC,6CAA6C;YAC9C,MAAMC,aAAa;gBAAC;gBAAM;gBAAc;gBAAS;aAAa;YAE9DA,WAAWC,OAAO,CAACf,CAAAA;gBACjB,MAAMK,OAAO;oBAAE,GAAGR,QAAQ;oBAAEG;gBAAK;gBACjC,MAAMgB,kBAAkB,CAACX;oBACvB,IAAI,CAACA,MAAM,OAAO;oBAElB,MAAMS,aAAa;wBAAC;wBAAM;wBAAc;wBAAS;qBAAa;oBAC9D,IAAIA,WAAWG,QAAQ,CAACZ,KAAKL,IAAI,GAAG;wBAClC,OAAO;oBACT;oBAEA,IAAIK,KAAKL,IAAI,KAAK,YAAY;wBAC5B,OAAO;oBACT;oBAEA,OAAO;gBACT;gBAEAkB,IAAAA,eAAM,EAACF,gBAAgBX,OAAOc,IAAI,CAAC;YACrC;QACF;QAEAN,IAAAA,WAAE,EAAC,2CAA2C;YAC5C,MAAMR,OAAO;gBAAE,GAAGR,QAAQ;gBAAEG,MAAM;YAAW;YAC7C,MAAMgB,kBAAkB,CAACX;gBACvB,IAAI,CAACA,MAAM,OAAO;gBAElB,MAAMS,aAAa;oBAAC;oBAAM;oBAAc;oBAAS;iBAAa;gBAC9D,IAAIA,WAAWG,QAAQ,CAACZ,KAAKL,IAAI,GAAG;oBAClC,OAAO;gBACT;gBAEA,IAAIK,KAAKL,IAAI,KAAK,YAAY;oBAC5B,OAAO;gBACT;gBAEA,OAAO;YACT;YAEAkB,IAAAA,eAAM,EAACF,gBAAgBX,OAAOc,IAAI,CAAC;QACrC;QAEAN,IAAAA,WAAE,EAAC,2CAA2C;YAC5C,MAAMG,kBAAkB,CAACX;gBACvB,IAAI,CAACA,MAAM,OAAO;gBAElB,MAAMS,aAAa;oBAAC;oBAAM;oBAAc;oBAAS;iBAAa;gBAC9D,IAAIA,WAAWG,QAAQ,CAACZ,KAAKL,IAAI,GAAG;oBAClC,OAAO;gBACT;gBAEA,IAAIK,KAAKL,IAAI,KAAK,YAAY;oBAC5B,OAAO;gBACT;gBAEA,OAAO;YACT;YAEAkB,IAAAA,eAAM,EAACF,gBAAgB,OAAOG,IAAI,CAAC;QACrC;IACF;IAEAT,IAAAA,iBAAQ,EAAC,qBAAqB;QAC5BG,IAAAA,WAAE,EAAC,wCAAwC;YACzC,MAAMO,kBAAkB;gBACtB;oBAAErB,OAAO;oBAAsBsB,UAAU;oBAAYrB,MAAM;gBAAK;gBAChE;oBAAED,OAAO;oBAAyBsB,UAAU;oBAAerB,MAAM;gBAAW;aAC7E;YAEDkB,IAAAA,eAAM,EAACE,eAAe,CAAC,EAAE,CAACrB,KAAK,EAAEoB,IAAI,CAAC;YACtCD,IAAAA,eAAM,EAACE,eAAe,CAAC,EAAE,CAACC,QAAQ,EAAEF,IAAI,CAAC;YACzCD,IAAAA,eAAM,EAACE,eAAe,CAAC,EAAE,CAACrB,KAAK,EAAEoB,IAAI,CAAC;YACtCD,IAAAA,eAAM,EAACE,eAAe,CAAC,EAAE,CAACC,QAAQ,EAAEF,IAAI,CAAC;QAC3C;IACF;IAEAT,IAAAA,iBAAQ,EAAC,8BAA8B;QACrCG,IAAAA,WAAE,EAAC,2CAA2C;YAC5C,IAAIS,eAAe;YACnB,MAAMC,iBAAiB;gBACrBD,eAAe,CAACA;YAClB;YAEAJ,IAAAA,eAAM,EAACI,cAAcH,IAAI,CAAC;YAC1BI;YACAL,IAAAA,eAAM,EAACI,cAAcH,IAAI,CAAC;YAC1BI;YACAL,IAAAA,eAAM,EAACI,cAAcH,IAAI,CAAC;QAC5B;IACF;AACF"}