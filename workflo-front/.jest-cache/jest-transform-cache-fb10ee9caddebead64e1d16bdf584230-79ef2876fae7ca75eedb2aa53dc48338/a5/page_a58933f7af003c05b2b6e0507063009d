2fa8616dff7695a9f64c23a43ce08a53
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _lucidereact = require("lucide-react");
const _Card = /*#__PURE__*/ _interop_require_default(require("../../../../../../components/ui/Card"));
const _Button = /*#__PURE__*/ _interop_require_default(require("../../../../../../components/ui/Button"));
const _AuthProvider = require("../../../../../../providers/AuthProvider");
const _utils = require("../../../../../../lib/utils");
const _useBiostarAttendance = require("../../../../../../hooks/useBiostarAttendance");
const _biostarApi = require("../../../../../../lib/biostarApi");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const StaffBiostarPage = ()=>{
    const { user } = (0, _AuthProvider.useAuth)();
    const [activeTab, setActiveTab] = (0, _react.useState)('profile');
    const [biometricProfile, setBiometricProfile] = (0, _react.useState)(null);
    const [recentEvents, setRecentEvents] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(true);
    const [refreshing, setRefreshing] = (0, _react.useState)(false);
    // Use BioStar attendance hook for device and connectivity data
    const { devices, connected: biostarConnected, error: connectionError, refresh: refreshAttendance } = (0, _useBiostarAttendance.useBiostarAttendance)({
        employeeId: user?.employee_id,
        autoRefresh: true,
        enableRealTime: false
    });
    (0, _react.useEffect)(()=>{
        loadBiometricData();
    }, [
        user?.employee_id
    ]);
    const loadBiometricData = async ()=>{
        if (!user?.employee_id) return;
        try {
            setLoading(true);
            // Get user's biometric profile from BioStar
            const biostarUser = await _biostarApi.biostarApi.getUserById(user.employee_id);
            // Get recent access events for this user
            const endDate = new Date().toISOString();
            const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(); // Last 7 days
            const eventsResponse = await _biostarApi.biostarApi.getEvents(startDate, endDate, user.employee_id);
            setBiometricProfile({
                user: biostarUser,
                enrolledFingerprints: 2,
                enrolledFaces: 1,
                lastSync: new Date().toISOString(),
                accessLevel: 'Standard Employee',
                isActive: !biostarUser.disabled
            });
            setRecentEvents(eventsResponse.results || []);
        } catch (error) {
            console.error('Failed to load biometric data:', error);
        } finally{
            setLoading(false);
        }
    };
    const handleRefresh = async ()=>{
        setRefreshing(true);
        await Promise.all([
            loadBiometricData(),
            refreshAttendance()
        ]);
        setRefreshing(false);
    };
    const getEventTypeIcon = (eventType)=>{
        switch(eventType){
            case 'ENTRY':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                    className: "h-4 w-4 text-green-500"
                });
            case 'EXIT':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Activity, {
                    className: "h-4 w-4 text-blue-500"
                });
            case 'DENIED':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertCircle, {
                    className: "h-4 w-4 text-red-500"
                });
            default:
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                    className: "h-4 w-4 text-gray-500"
                });
        }
    };
    const getEventTypeColor = (eventType)=>{
        switch(eventType){
            case 'ENTRY':
                return 'bg-green-100 text-green-800';
            case 'EXIT':
                return 'bg-blue-100 text-blue-800';
            case 'DENIED':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    const tabs = [
        {
            id: 'profile',
            label: 'Biometric Profile',
            icon: _lucidereact.User
        },
        {
            id: 'devices',
            label: 'Devices',
            icon: _lucidereact.Monitor
        },
        {
            id: 'history',
            label: 'Access History',
            icon: _lucidereact.History
        },
        {
            id: 'security',
            label: 'Security',
            icon: _lucidereact.Shield
        }
    ];
    if (loading) {
        return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
            className: "flex items-center justify-center p-8",
            children: [
                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.RefreshCw, {
                    className: "h-8 w-8 animate-spin text-gray-400"
                }),
                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                    className: "ml-2 text-gray-600",
                    children: "Loading biometric data..."
                })
            ]
        });
    }
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h1", {
                                className: "text-2xl font-bold text-gray-900",
                                children: "BioStar Profile"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                className: "text-gray-600 mt-1",
                                children: "Manage your biometric data and view access history"
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "flex items-center space-x-3",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center space-x-2",
                                children: [
                                    biostarConnected ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Wifi, {
                                        className: "h-4 w-4 text-green-500"
                                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.WifiOff, {
                                        className: "h-4 w-4 text-red-500"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                                        className: "text-sm text-gray-600",
                                        children: [
                                            "BioStar ",
                                            biostarConnected ? 'Connected' : 'Disconnected'
                                        ]
                                    })
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Button.default, {
                                variant: "secondary",
                                onClick: handleRefresh,
                                disabled: refreshing,
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.RefreshCw, {
                                        className: (0, _utils.cn)("h-4 w-4 mr-2", refreshing && "animate-spin")
                                    }),
                                    refreshing ? 'Refreshing...' : 'Refresh'
                                ]
                            })
                        ]
                    })
                ]
            }),
            connectionError && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "bg-red-50 border border-red-200 rounded-lg p-4",
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "flex items-center",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertCircle, {
                            className: "h-5 w-5 text-red-500 mr-2"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                    className: "text-sm font-medium text-red-800",
                                    children: "Connection Error"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                    className: "text-sm text-red-700 mt-1",
                                    children: connectionError
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "grid grid-cols-1 md:grid-cols-4 gap-6",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-blue-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.User, {
                                            className: "h-6 w-6 text-blue-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Profile Status"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: biometricProfile?.isActive ? 'Active' : 'Inactive'
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-green-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Fingerprint, {
                                            className: "h-6 w-6 text-green-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Fingerprints"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: biometricProfile?.enrolledFingerprints || 0
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-purple-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Eye, {
                                            className: "h-6 w-6 text-purple-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Face Templates"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: biometricProfile?.enrolledFaces || 0
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-orange-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Monitor, {
                                            className: "h-6 w-6 text-orange-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Devices"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: [
                                                    devices.filter((d)=>d.status === 'ONLINE').length,
                                                    "/",
                                                    devices.length
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "border-b border-gray-200",
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("nav", {
                    className: "-mb-px flex space-x-8",
                    children: tabs.map((tab)=>{
                        const Icon = tab.icon;
                        return /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                            onClick: ()=>setActiveTab(tab.id),
                            className: (0, _utils.cn)('flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm', activeTab === tab.id ? 'border-orange-500 text-orange-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(Icon, {
                                    className: "h-4 w-4"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                    children: tab.label
                                })
                            ]
                        }, tab.id);
                    })
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "mt-6",
                children: [
                    activeTab === 'profile' && /*#__PURE__*/ (0, _jsxruntime.jsx)(BiometricProfileTab, {
                        profile: biometricProfile,
                        user: user
                    }),
                    activeTab === 'devices' && /*#__PURE__*/ (0, _jsxruntime.jsx)(DevicesTab, {
                        devices: devices,
                        connected: biostarConnected
                    }),
                    activeTab === 'history' && /*#__PURE__*/ (0, _jsxruntime.jsx)(AccessHistoryTab, {
                        events: recentEvents,
                        employeeId: user?.employee_id
                    }),
                    activeTab === 'security' && /*#__PURE__*/ (0, _jsxruntime.jsx)(SecurityTab, {
                        profile: biometricProfile,
                        user: user
                    })
                ]
            })
        ]
    });
};
const BiometricProfileTab = ({ profile, user })=>{
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "grid grid-cols-1 lg:grid-cols-2 gap-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Profile Information"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "space-y-4",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Employee ID"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: user?.employee_id || 'N/A'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Full Name"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.user?.name || `${user?.first_name} ${user?.last_name}`
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Email"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.user?.email || user?.email
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Department"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.user?.department || 'N/A'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Position"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.user?.position || 'N/A'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Access Level"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "font-medium",
                                            children: profile?.accessLevel || 'Standard'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm text-gray-600",
                                            children: "Status"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: (0, _utils.cn)('inline-flex items-center px-2 py-1 rounded-full text-xs font-medium', profile?.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),
                                            children: profile?.isActive ? 'Active' : 'Inactive'
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Biometric Enrollment"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "space-y-6",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-3",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: "p-2 bg-blue-100 rounded-lg",
                                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Fingerprint, {
                                                        className: "h-5 w-5 text-blue-600"
                                                    })
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                            className: "font-medium text-gray-900",
                                                            children: "Fingerprints"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                            className: "text-sm text-gray-600",
                                                            children: [
                                                                profile?.enrolledFingerprints || 0,
                                                                " enrolled"
                                                            ]
                                                        })
                                                    ]
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "text-right",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "text-2xl font-bold text-gray-900",
                                                    children: [
                                                        profile?.enrolledFingerprints || 0,
                                                        "/10"
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: "text-xs text-gray-500",
                                                    children: "Max: 10"
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-3",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: "p-2 bg-purple-100 rounded-lg",
                                                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Eye, {
                                                        className: "h-5 w-5 text-purple-600"
                                                    })
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                            className: "font-medium text-gray-900",
                                                            children: "Face Templates"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                            className: "text-sm text-gray-600",
                                                            children: [
                                                                profile?.enrolledFaces || 0,
                                                                " enrolled"
                                                            ]
                                                        })
                                                    ]
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "text-right",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "text-2xl font-bold text-gray-900",
                                                    children: [
                                                        profile?.enrolledFaces || 0,
                                                        "/5"
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: "text-xs text-gray-500",
                                                    children: "Max: 5"
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "pt-4 border-t border-gray-200",
                                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "flex items-center justify-between",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                className: "text-sm text-gray-600",
                                                children: "Last Sync"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                className: "font-medium",
                                                children: profile?.lastSync ? (0, _utils.formatDate)(profile.lastSync, 'MMM dd, yyyy HH:mm') : 'Never'
                                            })
                                        ]
                                    })
                                })
                            ]
                        })
                    ]
                })
            })
        ]
    });
};
const DevicesTab = ({ devices, connected })=>{
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "p-6",
                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center space-x-3",
                                children: [
                                    connected ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Wifi, {
                                        className: "h-6 w-6 text-green-500"
                                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.WifiOff, {
                                        className: "h-6 w-6 text-red-500"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                                className: "text-lg font-medium text-gray-900",
                                                children: "System Status"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                className: "text-sm text-gray-600",
                                                children: [
                                                    "BioStar system is ",
                                                    connected ? 'online and accessible' : 'offline or unreachable'
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                className: (0, _utils.cn)('inline-flex items-center px-3 py-1 rounded-full text-sm font-medium', connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),
                                children: connected ? 'Connected' : 'Disconnected'
                            })
                        ]
                    })
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Available Devices"
                        }),
                        devices.length === 0 ? /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "text-center py-8",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Monitor, {
                                    className: "h-12 w-12 text-gray-400 mx-auto mb-4"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                    className: "text-lg font-medium text-gray-900 mb-2",
                                    children: "No Devices Found"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                    className: "text-gray-600",
                                    children: "No biometric devices are currently available."
                                })
                            ]
                        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                            children: devices.map((device)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "border border-gray-200 rounded-lg p-4",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center justify-between mb-3",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex items-center space-x-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Monitor, {
                                                            className: "h-5 w-5 text-gray-600"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                            className: "font-medium text-gray-900",
                                                            children: device.name
                                                        })
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                    className: (0, _utils.cn)('inline-flex items-center px-2 py-1 rounded-full text-xs font-medium', device.status === 'ONLINE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),
                                                    children: device.status
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "space-y-2 text-sm",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex justify-between",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "text-gray-600",
                                                            children: "Location"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "font-medium",
                                                            children: device.location || 'Unknown'
                                                        })
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex justify-between",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "text-gray-600",
                                                            children: "IP Address"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "font-medium",
                                                            children: device.ip
                                                        })
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex justify-between",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "text-gray-600",
                                                            children: "Type"
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "font-medium",
                                                            children: device.type
                                                        })
                                                    ]
                                                })
                                            ]
                                        })
                                    ]
                                }, device.id))
                        })
                    ]
                })
            })
        ]
    });
};
const AccessHistoryTab = ({ events, employeeId })=>{
    const [selectedPeriod, setSelectedPeriod] = (0, _react.useState)('7_days');
    const [filteredEvents, setFilteredEvents] = (0, _react.useState)(events);
    (0, _react.useEffect)(()=>{
        // Filter events based on selected period
        const now = new Date();
        let startDate;
        switch(selectedPeriod){
            case '1_day':
                startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case '7_days':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30_days':
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            default:
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        }
        const filtered = events.filter((event)=>new Date(event.datetime) >= startDate);
        setFilteredEvents(filtered);
    }, [
        events,
        selectedPeriod
    ]);
    const getEventTypeIcon = (eventType)=>{
        switch(eventType){
            case 'ENTRY':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                    className: "h-4 w-4 text-green-500"
                });
            case 'EXIT':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Activity, {
                    className: "h-4 w-4 text-blue-500"
                });
            case 'DENIED':
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.AlertCircle, {
                    className: "h-4 w-4 text-red-500"
                });
            default:
                return /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                    className: "h-4 w-4 text-gray-500"
                });
        }
    };
    const getEventTypeColor = (eventType)=>{
        switch(eventType){
            case 'ENTRY':
                return 'bg-green-100 text-green-800';
            case 'EXIT':
                return 'bg-blue-100 text-blue-800';
            case 'DENIED':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "p-6",
                    children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                className: "text-lg font-medium text-gray-900",
                                children: "Access History"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("select", {
                                value: selectedPeriod,
                                onChange: (e)=>setSelectedPeriod(e.target.value),
                                className: "px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                        value: "1_day",
                                        children: "Last 24 Hours"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                        value: "7_days",
                                        children: "Last 7 Days"
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("option", {
                                        value: "30_days",
                                        children: "Last 30 Days"
                                    })
                                ]
                            })
                        ]
                    })
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                    className: "p-6",
                    children: filteredEvents.length === 0 ? /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                        className: "text-center py-8",
                        children: [
                            /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.History, {
                                className: "h-12 w-12 text-gray-400 mx-auto mb-4"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                className: "text-lg font-medium text-gray-900 mb-2",
                                children: "No Access Events"
                            }),
                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                className: "text-gray-600",
                                children: "No access events found for the selected period."
                            })
                        ]
                    }) : /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        className: "space-y-4",
                        children: filteredEvents.map((event, index)=>/*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                className: "flex items-center justify-between p-4 border border-gray-200 rounded-lg",
                                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center space-x-4",
                                    children: [
                                        getEventTypeIcon(event.event_type),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex items-center space-x-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: "font-medium text-gray-900",
                                                            children: event.event_type === 'ENTRY' ? 'Check In' : event.event_type === 'EXIT' ? 'Check Out' : 'Access Denied'
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                            className: (0, _utils.cn)('inline-flex items-center px-2 py-1 rounded-full text-xs font-medium', getEventTypeColor(event.event_type)),
                                                            children: event.event_type
                                                        })
                                                    ]
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                    className: "flex items-center space-x-4 text-sm text-gray-600 mt-1",
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                            className: "flex items-center space-x-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                                                                    className: "h-3 w-3"
                                                                }),
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                                    children: (0, _utils.formatDate)(event.datetime, 'MMM dd, yyyy HH:mm:ss')
                                                                })
                                                            ]
                                                        }),
                                                        event.device_name && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                            className: "flex items-center space-x-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Monitor, {
                                                                    className: "h-3 w-3"
                                                                }),
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                                    children: event.device_name
                                                                })
                                                            ]
                                                        }),
                                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                            className: "flex items-center space-x-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.MapPin, {
                                                                    className: "h-3 w-3"
                                                                }),
                                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                                    children: "Main Office"
                                                                })
                                                            ]
                                                        })
                                                    ]
                                                })
                                            ]
                                        })
                                    ]
                                })
                            }, `${event.id}-${index}`))
                    })
                })
            })
        ]
    });
};
const SecurityTab = ({ profile, user })=>{
    const [showSecuritySettings, setShowSecuritySettings] = (0, _react.useState)(false);
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Security Overview"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "grid grid-cols-1 md:grid-cols-3 gap-6",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                            className: "p-3 bg-green-100 rounded-full w-12 h-12 mx-auto mb-2",
                                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Shield, {
                                                className: "h-6 w-6 text-green-600"
                                            })
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                            className: "font-medium text-gray-900",
                                            children: "Account Status"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            className: "text-sm text-gray-600 mt-1",
                                            children: profile?.isActive ? 'Active & Secure' : 'Inactive'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                            className: "p-3 bg-blue-100 rounded-full w-12 h-12 mx-auto mb-2",
                                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Database, {
                                                className: "h-6 w-6 text-blue-600"
                                            })
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                            className: "font-medium text-gray-900",
                                            children: "Data Sync"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            className: "text-sm text-gray-600 mt-1",
                                            children: profile?.lastSync ? 'Recently Synced' : 'Not Synced'
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                            className: "p-3 bg-purple-100 rounded-full w-12 h-12 mx-auto mb-2",
                                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Fingerprint, {
                                                className: "h-6 w-6 text-purple-600"
                                            })
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                            className: "font-medium text-gray-900",
                                            children: "Biometric Security"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            className: "text-sm text-gray-600 mt-1",
                                            children: (profile?.enrolledFingerprints || 0) + (profile?.enrolledFaces || 0) > 0 ? 'Enrolled' : 'Not Enrolled'
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "flex items-center justify-between mb-4",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                    className: "text-lg font-medium text-gray-900",
                                    children: "Security Settings"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)(_Button.default, {
                                    variant: "secondary",
                                    onClick: ()=>setShowSecuritySettings(!showSecuritySettings),
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Settings, {
                                            className: "h-4 w-4 mr-2"
                                        }),
                                        showSecuritySettings ? 'Hide Settings' : 'Show Settings'
                                    ]
                                })
                            ]
                        }),
                        showSecuritySettings && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "space-y-4 border-t border-gray-200 pt-4",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                    className: "font-medium text-gray-900",
                                                    children: "Two-Factor Authentication"
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                    className: "text-sm text-gray-600",
                                                    children: "Add an extra layer of security to your account"
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_Button.default, {
                                            variant: "secondary",
                                            size: "sm",
                                            children: "Enable"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                    className: "font-medium text-gray-900",
                                                    children: "Access Notifications"
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                    className: "text-sm text-gray-600",
                                                    children: "Get notified when your biometric data is used"
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_Button.default, {
                                            variant: "secondary",
                                            size: "sm",
                                            children: "Configure"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                    className: "font-medium text-gray-900",
                                                    children: "Data Privacy"
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                    className: "text-sm text-gray-600",
                                                    children: "Manage how your biometric data is stored and used"
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_Button.default, {
                                            variant: "secondary",
                                            size: "sm",
                                            children: "Review"
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Privacy & Data Protection"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "space-y-4 text-sm text-gray-600",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-start space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                                            className: "h-4 w-4 text-green-500 mt-0.5"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            children: "Your biometric data is encrypted and stored securely"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-start space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                                            className: "h-4 w-4 text-green-500 mt-0.5"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            children: "Access logs are maintained for security and compliance purposes"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-start space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                                            className: "h-4 w-4 text-green-500 mt-0.5"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            children: "Your data is only used for authentication and attendance tracking"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-start space-x-2",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.CheckCircle, {
                                            className: "h-4 w-4 text-green-500 mt-0.5"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                            children: "You can request data deletion upon employment termination"
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            })
        ]
    });
};
const _default = StaffBiostarPage;

//# sourceMappingURL=data:application/json;base64,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