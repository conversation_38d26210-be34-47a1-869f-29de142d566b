# This file is distributed under the same license as the Django package.
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-03-18 09:16+0100\n"
"PO-Revision-Date: 2015-03-18 08:35+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Khmer (http://www.transifex.com/projects/p/django/language/"
"km/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: km\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "GIS"
msgstr ""

msgid "The base GIS field -- maps to the OpenGIS Specification Geometry type."
msgstr ""

msgid "Point"
msgstr ""

msgid "Line string"
msgstr ""

msgid "Polygon"
msgstr ""

msgid "Multi-point"
msgstr ""

msgid "Multi-line string"
msgstr ""

msgid "Multi polygon"
msgstr ""

msgid "Geometry collection"
msgstr ""

msgid "Extent Aggregate Field"
msgstr ""

msgid "No geometry value provided."
msgstr ""

msgid "Invalid geometry value."
msgstr ""

msgid "Invalid geometry type."
msgstr ""

msgid ""
"An error occurred when transforming the geometry to the SRID of the geometry "
"form field."
msgstr ""

msgid "Delete all Features"
msgstr ""

msgid "WKT debugging window:"
msgstr ""

msgid "Google Maps via GeoDjango"
msgstr ""

msgid "Debugging window (serialized value)"
msgstr ""

msgid "No feeds are registered."
msgstr ""

#, python-format
msgid "Slug %r isn't registered."
msgstr ""
