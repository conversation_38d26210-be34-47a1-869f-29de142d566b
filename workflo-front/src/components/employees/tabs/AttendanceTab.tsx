'use client';

import React, { useState, useEffect } from 'react';
import {
  Clock,
  Calendar,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertCircle,
  Timer,
  Play,
  Pause,
  BarChart3,
  TrendingUp,
  MapPin,
  Fingerprint,
  Download
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { cn, formatDate } from '@/lib/utils';

interface AttendanceRecord {
  id: number;
  employee_id: number;
  date: string;
  check_in_time?: string;
  check_out_time?: string;
  break_start_time?: string;
  break_end_time?: string;
  total_hours: number;
  regular_hours: number;
  overtime_hours: number;
  status: 'present' | 'absent' | 'late' | 'half_day' | 'holiday';
  check_in_method: 'biometric' | 'manual' | 'mobile';
  check_out_method?: 'biometric' | 'manual' | 'mobile';
  location?: string;
  notes?: string;
}

interface BiometricDevice {
  id: number;
  name: string;
  location: string;
  ip_address: string;
  status: 'online' | 'offline' | 'maintenance';
  last_sync: string;
  total_users: number;
}

interface AttendanceTabProps {
  employeeId: number;
  employeeName: string;
  isCurrentUser?: boolean;
}

const AttendanceTab: React.FC<AttendanceTabProps> = ({
  employeeId,
  employeeName,
  isCurrentUser = false
}) => {
  const [activeSubTab, setActiveSubTab] = useState<'records' | 'tracking' | 'devices' | 'analytics'>('records');
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [biometricDevices, setBiometricDevices] = useState<BiometricDevice[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [selectedMonth, setSelectedMonth] = useState(new Date().toISOString().slice(0, 7));
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Mock data
  const mockAttendanceRecords: AttendanceRecord[] = [
    {
      id: 1,
      employee_id: employeeId,
      date: '2024-01-15',
      check_in_time: '09:00:00',
      check_out_time: '18:00:00',
      break_start_time: '12:00:00',
      break_end_time: '13:00:00',
      total_hours: 8,
      regular_hours: 8,
      overtime_hours: 0,
      status: 'present',
      check_in_method: 'biometric',
      check_out_method: 'biometric',
      location: 'Main Office',
      notes: 'Regular working day'
    },
    {
      id: 2,
      employee_id: employeeId,
      date: '2024-01-16',
      check_in_time: '09:15:00',
      check_out_time: '19:30:00',
      break_start_time: '12:30:00',
      break_end_time: '13:30:00',
      total_hours: 9.25,
      regular_hours: 8,
      overtime_hours: 1.25,
      status: 'late',
      check_in_method: 'biometric',
      check_out_method: 'biometric',
      location: 'Main Office',
      notes: 'Late arrival due to traffic'
    },
    {
      id: 3,
      employee_id: employeeId,
      date: '2024-01-17',
      check_in_time: '08:45:00',
      check_out_time: '13:00:00',
      total_hours: 4.25,
      regular_hours: 4.25,
      overtime_hours: 0,
      status: 'half_day',
      check_in_method: 'biometric',
      check_out_method: 'manual',
      location: 'Main Office',
      notes: 'Half day - personal appointment'
    },
    {
      id: 4,
      employee_id: employeeId,
      date: '2024-01-18',
      total_hours: 0,
      regular_hours: 0,
      overtime_hours: 0,
      status: 'absent',
      check_in_method: 'manual',
      notes: 'Sick leave'
    }
  ];

  const mockBiometricDevices: BiometricDevice[] = [
    {
      id: 1,
      name: 'Suprema BioStation 3',
      location: 'Main Entrance',
      ip_address: '*************',
      status: 'online',
      last_sync: '2024-01-15T08:00:00Z',
      total_users: 150
    },
    {
      id: 2,
      name: 'Suprema BioEntry Plus',
      location: 'Floor 2 Entrance',
      ip_address: '*************',
      status: 'online',
      last_sync: '2024-01-15T08:00:00Z',
      total_users: 75
    },
    {
      id: 3,
      name: 'Suprema FaceStation F2',
      location: 'Executive Floor',
      ip_address: '*************',
      status: 'maintenance',
      last_sync: '2024-01-14T18:00:00Z',
      total_users: 25
    }
  ];

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setTimeout(() => {
        setAttendanceRecords(mockAttendanceRecords);
        setBiometricDevices(mockBiometricDevices);
        setLoading(false);
      }, 1000);
    };

    loadData();
  }, [employeeId]);

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const filteredRecords = attendanceRecords.filter(record => {
    const matchesSearch = record.notes?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.location?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = !filterStatus || record.status === filterStatus;
    const matchesMonth = record.date.startsWith(selectedMonth);
    return matchesSearch && matchesStatus && matchesMonth;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present': return 'bg-green-100 text-green-800';
      case 'absent': return 'bg-red-100 text-red-800';
      case 'late': return 'bg-yellow-100 text-yellow-800';
      case 'half_day': return 'bg-blue-100 text-blue-800';
      case 'holiday': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present': return <CheckCircle className="h-4 w-4" />;
      case 'absent': return <XCircle className="h-4 w-4" />;
      case 'late': return <AlertCircle className="h-4 w-4" />;
      case 'half_day': return <Clock className="h-4 w-4" />;
      case 'holiday': return <Calendar className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getDeviceStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-100 text-green-800';
      case 'offline': return 'bg-red-100 text-red-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (time: string) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const handleCheckIn = () => {
    setIsCheckedIn(true);
    // Simulate check-in API call
    console.log('Checking in at:', currentTime.toISOString());
  };

  const handleCheckOut = () => {
    setIsCheckedIn(false);
    // Simulate check-out API call
    console.log('Checking out at:', currentTime.toISOString());
  };

  const calculateMonthlyStats = () => {
    const monthRecords = attendanceRecords.filter(record =>
      record.date.startsWith(selectedMonth)
    );

    const totalDays = monthRecords.length;
    const presentDays = monthRecords.filter(r => r.status === 'present').length;
    const lateDays = monthRecords.filter(r => r.status === 'late').length;
    const absentDays = monthRecords.filter(r => r.status === 'absent').length;
    const totalHours = monthRecords.reduce((sum, r) => sum + r.total_hours, 0);
    const overtimeHours = monthRecords.reduce((sum, r) => sum + r.overtime_hours, 0);

    return {
      totalDays,
      presentDays,
      lateDays,
      absentDays,
      totalHours,
      overtimeHours,
      attendanceRate: totalDays > 0 ? ((presentDays + lateDays) / totalDays * 100) : 0
    };
  };

  const monthlyStats = calculateMonthlyStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Attendance Management</h3>
          <p className="text-sm text-gray-600 mt-1">
            Track attendance and working hours for {employeeName}
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          {isCurrentUser && (
            <div className="text-right">
              <div className="text-lg font-mono font-bold text-gray-900">
                {currentTime.toLocaleTimeString()}
              </div>
              <div className="text-sm text-gray-600">
                {currentTime.toLocaleDateString()}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Sub Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {[
            { key: 'records', label: 'Attendance Records', icon: Calendar },
            { key: 'tracking', label: 'Time Tracking', icon: Timer },
            { key: 'devices', label: 'Biometric Devices', icon: Fingerprint },
            { key: 'analytics', label: 'Analytics', icon: BarChart3 },
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.key}
                onClick={() => setActiveSubTab(tab.key as any)}
                className={cn(
                  'py-2 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2',
                  activeSubTab === tab.key
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeSubTab === 'records' && (
        <AttendanceRecordsView
          records={filteredRecords}
          loading={loading}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          filterStatus={filterStatus}
          setFilterStatus={setFilterStatus}
          selectedMonth={selectedMonth}
          setSelectedMonth={setSelectedMonth}
        />
      )}

      {activeSubTab === 'tracking' && (
        <TimeTrackingView
          isCurrentUser={isCurrentUser}
          isCheckedIn={isCheckedIn}
          currentTime={currentTime}
          onCheckIn={handleCheckIn}
          onCheckOut={handleCheckOut}
        />
      )}

      {activeSubTab === 'devices' && (
        <BiometricDevicesView devices={biometricDevices} loading={loading} />
      )}

      {activeSubTab === 'analytics' && (
        <AttendanceAnalyticsView stats={monthlyStats} selectedMonth={selectedMonth} />
      )}
    </div>
  );
};

// Helper functions
const getStatusColor = (status: string) => {
  switch (status) {
    case 'present': return 'bg-green-100 text-green-800';
    case 'absent': return 'bg-red-100 text-red-800';
    case 'late': return 'bg-yellow-100 text-yellow-800';
    case 'half_day': return 'bg-blue-100 text-blue-800';
    case 'holiday': return 'bg-purple-100 text-purple-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'present': return <CheckCircle className="h-4 w-4" />;
    case 'absent': return <XCircle className="h-4 w-4" />;
    case 'late': return <AlertCircle className="h-4 w-4" />;
    case 'half_day': return <Clock className="h-4 w-4" />;
    case 'holiday': return <Calendar className="h-4 w-4" />;
    default: return <Clock className="h-4 w-4" />;
  }
};

const formatTime = (time: string) => {
  return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

// Attendance Records View Component
interface AttendanceRecordsViewProps {
  records: AttendanceRecord[];
  loading: boolean;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  selectedMonth: string;
  setSelectedMonth: (month: string) => void;
}

const AttendanceRecordsView: React.FC<AttendanceRecordsViewProps> = ({
  records,
  loading,
  searchQuery,
  setSearchQuery,
  filterStatus,
  setFilterStatus,
  selectedMonth,
  setSelectedMonth
}) => {
  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <div className="flex-1">
          <Input
            placeholder="Search records..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="h-4 w-4" />}
          />
        </div>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="">All Status</option>
          <option value="present">Present</option>
          <option value="absent">Absent</option>
          <option value="late">Late</option>
          <option value="half_day">Half Day</option>
          <option value="holiday">Holiday</option>
        </select>
        <input
          type="month"
          value={selectedMonth}
          onChange={(e) => setSelectedMonth(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        />
      </div>

      {/* Records Table */}
      {loading ? (
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      ) : records.length === 0 ? (
        <div className="text-center py-12">
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No attendance records found
          </h3>
          <p className="text-gray-600">
            No records found for the selected criteria.
          </p>
        </div>
      ) : (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Check In/Out
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Hours
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Method
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Notes
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {records.map((record) => (
                  <tr key={record.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatDate(record.date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="space-y-1">
                        {record.check_in_time && (
                          <div className="flex items-center">
                            <Play className="h-3 w-3 text-green-500 mr-1" />
                            {formatTime(record.check_in_time)}
                          </div>
                        )}
                        {record.check_out_time && (
                          <div className="flex items-center">
                            <Pause className="h-3 w-3 text-red-500 mr-1" />
                            {formatTime(record.check_out_time)}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="space-y-1">
                        <div>Total: {record.total_hours}h</div>
                        {record.overtime_hours > 0 && (
                          <div className="text-orange-600">OT: {record.overtime_hours}h</div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={cn(
                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                        getStatusColor(record.status)
                      )}>
                        {getStatusIcon(record.status)}
                        <span className="ml-1 capitalize">{record.status.replace('_', ' ')}</span>
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center">
                        {record.check_in_method === 'biometric' && <Fingerprint className="h-4 w-4 mr-1" />}
                        {record.check_in_method === 'mobile' && <MapPin className="h-4 w-4 mr-1" />}
                        <span className="capitalize">{record.check_in_method}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      <div className="max-w-xs truncate" title={record.notes}>
                        {record.notes}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

// Time Tracking View Component
interface TimeTrackingViewProps {
  isCurrentUser: boolean;
  isCheckedIn: boolean;
  currentTime: Date;
  onCheckIn: () => void;
  onCheckOut: () => void;
}

const TimeTrackingView: React.FC<TimeTrackingViewProps> = ({
  isCurrentUser,
  isCheckedIn,
  currentTime,
  onCheckIn,
  onCheckOut
}) => {
  if (!isCurrentUser) {
    return (
      <div className="text-center py-12">
        <Timer className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Time Tracking
        </h3>
        <p className="text-gray-600">
          Time tracking is only available for your own profile.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <div className="p-8 text-center">
          <div className="mb-6">
            <div className="text-4xl font-mono font-bold text-gray-900 mb-2">
              {currentTime.toLocaleTimeString()}
            </div>
            <div className="text-lg text-gray-600">
              {currentTime.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </div>
          </div>

          <div className="space-y-4">
            {!isCheckedIn ? (
              <Button variant="primary" size="lg" onClick={onCheckIn}>
                <Play className="h-5 w-5 mr-2" />
                Check In
              </Button>
            ) : (
              <Button variant="secondary" size="lg" onClick={onCheckOut}>
                <Pause className="h-5 w-5 mr-2" />
                Check Out
              </Button>
            )}
          </div>

          {isCheckedIn && (
            <div className="mt-6 p-4 bg-green-50 rounded-lg">
              <div className="flex items-center justify-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <span className="text-green-800 font-medium">
                  You are currently checked in
                </span>
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

// Biometric Devices View Component
interface BiometricDevicesViewProps {
  devices: any[];
  loading: boolean;
}

const BiometricDevicesView: React.FC<BiometricDevicesViewProps> = ({ devices, loading }) => {
  const getDeviceStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-100 text-green-800';
      case 'offline': return 'bg-red-100 text-red-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {devices.map((device) => (
        <Card key={device.id}>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900">{device.name}</h4>
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                getDeviceStatusColor(device.status)
              )}>
                {device.status.toUpperCase()}
              </span>
            </div>

            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>Location:</span>
                <span className="font-medium text-gray-900">{device.location}</span>
              </div>
              <div className="flex justify-between">
                <span>IP Address:</span>
                <span className="font-medium text-gray-900">{device.ip_address}</span>
              </div>
              <div className="flex justify-between">
                <span>Users:</span>
                <span className="font-medium text-gray-900">{device.total_users}</span>
              </div>
              <div className="flex justify-between">
                <span>Last Sync:</span>
                <span className="font-medium text-gray-900">{formatDate(device.last_sync)}</span>
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

// Attendance Analytics View Component
interface AttendanceAnalyticsViewProps {
  stats: any;
  selectedMonth: string;
}

const AttendanceAnalyticsView: React.FC<AttendanceAnalyticsViewProps> = ({ stats, selectedMonth }) => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Days</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalDays}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Present Days</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.presentDays}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Hours</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalHours}h</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Attendance Rate</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.attendanceRate.toFixed(1)}%</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Monthly Summary for {selectedMonth}
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">{stats.presentDays}</div>
              <div className="text-sm text-gray-600">Present</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-yellow-600">{stats.lateDays}</div>
              <div className="text-sm text-gray-600">Late</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">{stats.absentDays}</div>
              <div className="text-sm text-gray-600">Absent</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-orange-600">{stats.overtimeHours}h</div>
              <div className="text-sm text-gray-600">Overtime</div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AttendanceTab;
