import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define public routes that don't require authentication
const publicRoutes = ['/login', '/', '/status', '/test'];

// Define role-based route access
const roleBasedRoutes = {
  // Admin routes (HR, Supervisor, Admin, Accountant)
  admin: ['/dashboard', '/employees', '/company', '/calendar', '/leave', '/reviews', '/reports', '/manage', '/settings', '/payroll'],

  // Staff routes (Employee)
  staff: ['/staff'],

  // Supervisor routes (Supervisor role)
  supervisor: ['/supervisor'],

  // Accountant routes (Accountant role)
  accountant: ['/accountant'],

  // Shared routes (accessible by all authenticated users)
  shared: ['/profile']
};

// Define protected routes that require authentication
const protectedRoutes = [
  ...roleBasedRoutes.admin,
  ...roleBasedRoutes.staff,
  ...roleBasedRoutes.supervisor,
  ...roleBasedRoutes.accountant,
  ...roleBasedRoutes.shared
];

// Helper function to determine if user has admin role
const isAdminRole = (role: string): boolean => {
  const adminRoles = ['hr', 'supervisor', 'admin', 'accountant'];
  return adminRoles.includes(role);
};

// Helper function to get default redirect path based on role
const getDefaultRedirectPath = (role: string): string => {
  if (role === 'accountant') {
    return '/accountant';
  }
  if (role === 'supervisor') {
    return '/supervisor';
  }
  if (role === 'employee') {
    return '/staff';
  }
  if (isAdminRole(role)) {
    return '/dashboard';
  }
  return '/login';
};

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route));
  const isPublicRoute = publicRoutes.includes(pathname);

  // Add security headers
  const response = NextResponse.next();

  // Security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // For protected routes, add role information to headers for client-side access control
  if (isProtectedRoute) {
    // Add route type to headers for client-side role checking
    if (roleBasedRoutes.admin.some(route => pathname.startsWith(route))) {
      response.headers.set('X-Required-Role', 'admin');
    } else if (roleBasedRoutes.staff.some(route => pathname.startsWith(route))) {
      response.headers.set('X-Required-Role', 'staff');
    } else if (roleBasedRoutes.supervisor.some(route => pathname.startsWith(route))) {
      response.headers.set('X-Required-Role', 'supervisor');
    } else if (roleBasedRoutes.accountant.some(route => pathname.startsWith(route))) {
      response.headers.set('X-Required-Role', 'accountant');
    } else {
      response.headers.set('X-Required-Role', 'shared');
    }
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
