import React from 'react';
import { cn } from '@/lib/utils';
import { CardProps } from '@/types';

const Card: React.FC<CardProps> = ({
  title,
  children,
  className,
  actions,
  ...props
}) => {
  return (
    <div
      className={cn(
        'bg-white rounded-lg shadow-sm border border-gray-200 min-w-0 overflow-hidden touch-manipulation',
        className
      )}
      {...props}
    >
      {(title || actions) && (
        <div className="px-3 sm:px-4 md:px-6 py-3 sm:py-4 border-b border-gray-200 flex items-center justify-between min-w-0">
          {title && (
            <h3 className="text-sm sm:text-base md:text-lg font-medium text-gray-900 truncate">{title}</h3>
          )}
          {actions && <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">{actions}</div>}
        </div>
      )}
      <div className="p-3 sm:p-4 md:p-6 min-w-0">{children}</div>
    </div>
  );
};

// Card Header Component
export const CardHeader: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <div className={cn('px-3 sm:px-4 md:px-6 py-3 sm:py-4 border-b border-gray-200', className)}>
      {children}
    </div>
  );
};

// Card Body Component
export const CardBody: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return <div className={cn('p-3 sm:p-4 md:p-6', className)}>{children}</div>;
};

// Card Footer Component
export const CardFooter: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <div className={cn('px-3 sm:px-4 md:px-6 py-3 sm:py-4 border-t border-gray-200 bg-gray-50', className)}>
      {children}
    </div>
  );
};

// Stats Card Component
export const StatsCard: React.FC<{
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color?: 'primary' | 'success' | 'warning' | 'danger';
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}> = ({ title, value, icon, color = 'primary', trend, className }) => {
  const colorClasses = {
    primary: 'bg-orange-500',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    danger: 'bg-red-500',
  };

  return (
    <Card className={cn('touch-manipulation', className)}>
      <div className="flex items-center min-w-0">
        <div className={cn('p-2 sm:p-3 rounded-lg flex-shrink-0', colorClasses[color])}>
          <div className="text-white text-lg sm:text-xl">{icon}</div>
        </div>
        <div className="ml-3 sm:ml-4 flex-1 min-w-0">
          <h4 className="text-xs sm:text-sm font-medium text-gray-600 truncate">{title}</h4>
          <p className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 truncate">{value}</p>
          {trend && (
            <div className="flex items-center mt-1">
              <span
                className={cn(
                  'text-xs sm:text-sm font-medium',
                  trend.isPositive ? 'text-green-600' : 'text-red-600'
                )}
              >
                {trend.isPositive ? '+' : '-'}{Math.abs(trend.value)}%
              </span>
              <span className="text-xs sm:text-sm text-gray-500 ml-1 truncate">vs last month</span>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

// Employee Card Component
export const EmployeeCard: React.FC<{
  employee: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    job_title?: string;
    profile_picture?: string;
    department?: { name: string };
  };
  onClick?: () => void;
  className?: string;
}> = ({ employee, onClick, className }) => {
  const initials = `${employee.first_name.charAt(0)}${employee.last_name.charAt(0)}`;

  return (
    <Card
      className={cn(
        'cursor-pointer hover:shadow-md transition-shadow touch-manipulation',
        className
      )}
      onClick={onClick}
    >
      <div className="text-center min-w-0">
        <div className="mb-3 sm:mb-4">
          {employee.profile_picture ? (
            <img
              src={employee.profile_picture}
              alt={`${employee.first_name} ${employee.last_name}`}
              className="w-14 h-14 sm:w-16 sm:h-16 rounded-full mx-auto object-cover"
            />
          ) : (
            <div className="w-14 h-14 sm:w-16 sm:h-16 rounded-full mx-auto bg-orange-500 flex items-center justify-center text-white font-medium text-base sm:text-lg">
              {initials}
            </div>
          )}
        </div>
        <h4 className="text-sm sm:text-base md:text-lg font-medium text-gray-900 truncate">
          {employee.first_name} {employee.last_name}
        </h4>
        {employee.job_title && (
          <p className="text-xs sm:text-sm font-medium text-gray-600 mt-1 truncate" title={employee.job_title}>
            {employee.job_title}
          </p>
        )}
        <p className="text-xs sm:text-sm text-gray-500 mt-1 truncate" title={employee.email}>{employee.email}</p>
        {employee.department && (
          <p className="text-xs text-gray-400 mt-1 truncate" title={employee.department.name}>{employee.department.name}</p>
        )}
      </div>
    </Card>
  );
};

export default Card;
