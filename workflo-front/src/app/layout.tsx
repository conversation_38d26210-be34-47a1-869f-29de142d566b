import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/providers/AuthProvider";
import { NotificationProvider } from "@/providers/NotificationProvider";
import { ToastProvider } from "@/providers/ToastProvider";
import ClientAuthGuard from "@/components/auth/ClientAuthGuard";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "WorkFlow - HR Management System",
  description: "Comprehensive Human Resources Management System for modern organizations",
  keywords: "HR, Human Resources, Employee Management, Payroll, Attendance, Leave Management",
  authors: [{ name: "WorkFlow Team" }],
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <body
        className={`${inter.variable} font-sans antialiased h-full bg-gray-50`}
        suppressHydrationWarning={process.env.NODE_ENV === 'development'}
      >
        <AuthProvider>
          <ToastProvider>
            <NotificationProvider>
              <ClientAuthGuard>
                {children}
              </ClientAuthGuard>
            </NotificationProvider>
          </ToastProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
