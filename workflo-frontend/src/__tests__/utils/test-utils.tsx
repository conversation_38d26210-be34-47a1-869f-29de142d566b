import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { AuthProvider } from '@/providers/AuthProvider';
import { NotificationProvider } from '@/providers/NotificationProvider';
import { User } from '@/types';

// Mock user data for testing
export const mockUser: User = {
  id: 1,
  employee_id: 'EMP001',
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  email: '<EMAIL>',
  phone_number: '+254700123456',
  role: 'employee',
  department: 'Engineering',
  position: 'Software Engineer',
  hire_date: '2022-01-15',
  is_active: true,
  profile_picture: null,
  created_at: '2022-01-15T00:00:00Z',
  updated_at: '2024-01-15T00:00:00Z'
};

export const mockAdminUser: User = {
  ...mockUser,
  id: 2,
  employee_id: 'EMP002',
  first_name: '<PERSON>',
  last_name: '<PERSON>',
  email: '<EMAIL>',
  role: 'hr',
  position: 'HR Manager'
};

// Mock auth context values
export const mockAuthContextValue = {
  user: mockUser,
  isAuthenticated: true,
  isLoading: false,
  login: jest.fn(),
  logout: jest.fn(),
  getCurrentUser: jest.fn(),
  updateProfile: jest.fn(),
  changePassword: jest.fn(),
};

export const mockUnauthenticatedAuthContextValue = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  login: jest.fn(),
  logout: jest.fn(),
  getCurrentUser: jest.fn(),
  updateProfile: jest.fn(),
  changePassword: jest.fn(),
};

export const mockLoadingAuthContextValue = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: jest.fn(),
  logout: jest.fn(),
  getCurrentUser: jest.fn(),
  updateProfile: jest.fn(),
  changePassword: jest.fn(),
};

// Mock notification context values
export const mockNotificationContextValue = {
  notifications: [],
  unreadCount: 0,
  isConnected: true,
  connectionState: 'connected',
  markAsRead: jest.fn(),
  markAllAsRead: jest.fn(),
  clearNotifications: jest.fn(),
  requestPermission: jest.fn().mockResolvedValue('granted' as NotificationPermission),
};

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  authContextValue?: typeof mockAuthContextValue;
  notificationContextValue?: typeof mockNotificationContextValue;
  withAuth?: boolean;
  withNotifications?: boolean;
}

const AllTheProviders: React.FC<{
  children: React.ReactNode;
  authContextValue?: typeof mockAuthContextValue;
  notificationContextValue?: typeof mockNotificationContextValue;
  withAuth?: boolean;
  withNotifications?: boolean;
}> = ({ 
  children, 
  authContextValue = mockAuthContextValue,
  notificationContextValue = mockNotificationContextValue,
  withAuth = true,
  withNotifications = true
}) => {
  let component = <>{children}</>;

  if (withNotifications) {
    // Mock the notification provider
    const MockNotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
      const NotificationContext = React.createContext(notificationContextValue);
      return (
        <NotificationContext.Provider value={notificationContextValue}>
          {children}
        </NotificationContext.Provider>
      );
    };
    component = <MockNotificationProvider>{component}</MockNotificationProvider>;
  }

  if (withAuth) {
    // Mock the auth provider
    const MockAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
      const AuthContext = React.createContext(authContextValue);
      return (
        <AuthContext.Provider value={authContextValue}>
          {children}
        </AuthContext.Provider>
      );
    };
    component = <MockAuthProvider>{component}</MockAuthProvider>;
  }

  return component;
};

const customRender = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
) => {
  const {
    authContextValue,
    notificationContextValue,
    withAuth,
    withNotifications,
    ...renderOptions
  } = options;

  return render(ui, {
    wrapper: ({ children }) => (
      <AllTheProviders
        authContextValue={authContextValue}
        notificationContextValue={notificationContextValue}
        withAuth={withAuth}
        withNotifications={withNotifications}
      >
        {children}
      </AllTheProviders>
    ),
    ...renderOptions,
  });
};

// Helper function to create mock API responses
export const createMockApiResponse = <T,>(data: T, status = 200) => ({
  data,
  status,
  statusText: 'OK',
  headers: {},
  config: {},
});

// Helper function to create mock API error
export const createMockApiError = (message: string, status = 500) => ({
  response: {
    data: { message },
    status,
    statusText: 'Internal Server Error',
  },
  message,
});

// Helper function to wait for async operations
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to create mock file
export const createMockFile = (
  name: string = 'test.pdf',
  size: number = 1024,
  type: string = 'application/pdf'
): File => {
  const file = new File(['test content'], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
};

// Helper function to create mock form data
export const createMockFormData = (data: Record<string, any>): FormData => {
  const formData = new FormData();
  Object.entries(data).forEach(([key, value]) => {
    formData.append(key, value);
  });
  return formData;
};

// Helper function to mock localStorage
export const mockLocalStorage = (data: Record<string, string> = {}) => {
  const localStorageMock = {
    getItem: jest.fn((key: string) => data[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      data[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete data[key];
    }),
    clear: jest.fn(() => {
      Object.keys(data).forEach(key => delete data[key]);
    }),
  };

  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
    writable: true,
  });

  return localStorageMock;
};

// Helper function to mock sessionStorage
export const mockSessionStorage = (data: Record<string, string> = {}) => {
  const sessionStorageMock = {
    getItem: jest.fn((key: string) => data[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      data[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete data[key];
    }),
    clear: jest.fn(() => {
      Object.keys(data).forEach(key => delete data[key]);
    }),
  };

  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
    writable: true,
  });

  return sessionStorageMock;
};

// Re-export everything from React Testing Library
export * from '@testing-library/react';
export { customRender as render };
