'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { Plus, Calendar, Clock, CheckCircle, XCircle, AlertCircle, ArrowLeft } from 'lucide-react';

import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { FormModal } from '@/components/ui/Modal';
import Input, { Select, Textarea } from '@/components/ui/Input';
import { useAuth } from '@/store/authStore';
import { formatDate, getStatusColor } from '@/lib/utils';

// Mock leave data
const mockLeaves = [
  {
    id: 1,
    employee_name: '<PERSON>',
    leave_type: 'annual',
    start_date: '2024-02-15',
    end_date: '2024-02-20',
    days_requested: 5,
    reason: 'Family vacation',
    status: 'approved',
    applied_date: '2024-02-01',
    approved_by_name: '<PERSON>',
  },
  {
    id: 2,
    employee_name: '<PERSON>',
    leave_type: 'sick',
    start_date: '2024-02-10',
    end_date: '2024-02-12',
    days_requested: 3,
    reason: 'Medical appointment',
    status: 'pending',
    applied_date: '2024-02-08',
  },
  {
    id: 3,
    employee_name: '<PERSON>',
    leave_type: 'maternity',
    start_date: '2024-03-01',
    end_date: '2024-05-01',
    days_requested: 60,
    reason: 'Maternity leave',
    status: 'approved',
    applied_date: '2024-01-15',
    approved_by_name: 'Jane Smith',
  },
];

const LeavePage: React.FC = () => {
  const { user } = useAuth();
  const [leaves, setLeaves] = useState(mockLeaves);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [formData, setFormData] = useState({
    leave_type: '',
    start_date: '',
    end_date: '',
    reason: '',
  });

  const leaveTypes = [
    { value: 'annual', label: 'Annual Leave' },
    { value: 'sick', label: 'Sick Leave' },
    { value: 'maternity', label: 'Maternity Leave' },
    { value: 'paternity', label: 'Paternity Leave' },
  ];

  const filteredLeaves = leaves.filter(leave => {
    if (activeTab === 'all') return true;
    return leave.status === activeTab;
  });

  const handleSubmit = () => {
    // Handle form submission
    console.log('Submitting leave application:', formData);
    setIsModalOpen(false);
    setFormData({ leave_type: '', start_date: '', end_date: '', reason: '' });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getLeaveTypeColor = (type: string) => {
    const colors = {
      annual: 'bg-blue-100 text-blue-800',
      sick: 'bg-red-100 text-red-800',
      maternity: 'bg-pink-100 text-pink-800',
      paternity: 'bg-purple-100 text-purple-800',
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <div className="flex items-center space-x-4 mb-2">
                <Link href="/manage">
                  <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Management
                  </Button>
                </Link>
              </div>
              <h1 className="text-2xl font-bold text-gray-900">Leave Management</h1>
              <p className="text-gray-600 mt-1">
                Manage leave applications and requests
              </p>
            </div>
            <div className="mt-4 sm:mt-0">
              <Button variant="primary" onClick={() => setIsModalOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Apply Leave
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-blue-100">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-600">Total Leaves</h3>
                <p className="text-2xl font-bold text-gray-900">{leaves.length}</p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-yellow-100">
                <AlertCircle className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-600">Pending</h3>
                <p className="text-2xl font-bold text-gray-900">
                  {leaves.filter(l => l.status === 'pending').length}
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-green-100">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-600">Approved</h3>
                <p className="text-2xl font-bold text-gray-900">
                  {leaves.filter(l => l.status === 'approved').length}
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-red-100">
                <XCircle className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-600">Rejected</h3>
                <p className="text-2xl font-bold text-gray-900">
                  {leaves.filter(l => l.status === 'rejected').length}
                </p>
              </div>
            </div>
          </Card>
        </div>

        {/* Leave Applications */}
        <Card>
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Leave Applications</h3>

              {/* Tabs */}
              <div className="flex space-x-1">
                {[
                  { key: 'all', label: 'All' },
                  { key: 'pending', label: 'Pending' },
                  { key: 'approved', label: 'Approved' },
                  { key: 'rejected', label: 'Rejected' },
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setActiveTab(tab.key)}
                    className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                      activeTab === tab.key
                        ? 'bg-orange-100 text-orange-700'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>
            </div>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              {filteredLeaves.map((leave) => (
                <div
                  key={leave.id}
                  className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {getStatusIcon(leave.status)}
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {leave.employee_name}
                        </h4>
                        <p className="text-sm text-gray-600">{leave.reason}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${getLeaveTypeColor(
                          leave.leave_type
                        )}`}
                      >
                        {leaveTypes.find(t => t.value === leave.leave_type)?.label}
                      </span>

                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {formatDate(leave.start_date)} - {formatDate(leave.end_date)}
                        </p>
                        <p className="text-xs text-gray-500">
                          {leave.days_requested} days
                        </p>
                      </div>

                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                          leave.status
                        )}`}
                      >
                        {leave.status.charAt(0).toUpperCase() + leave.status.slice(1)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredLeaves.length === 0 && (
              <div className="text-center py-12">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No leave applications found
                </h3>
                <p className="text-gray-600">
                  {activeTab === 'all'
                    ? 'No leave applications have been submitted yet.'
                    : `No ${activeTab} leave applications found.`
                  }
                </p>
              </div>
            )}
          </div>
        </Card>

        {/* Apply Leave Modal */}
        <FormModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title="Apply for Leave"
          onSubmit={handleSubmit}
          submitText="Submit Application"
          size="md"
        >
          <div className="space-y-4">
            <Select
              label="Leave Type"
              required
              value={formData.leave_type}
              onChange={(e) => setFormData({ ...formData, leave_type: e.target.value })}
              options={leaveTypes}
              placeholder="Select leave type"
            />

            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Start Date"
                type="date"
                required
                value={formData.start_date}
                onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
              />

              <Input
                label="End Date"
                type="date"
                required
                value={formData.end_date}
                onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
              />
            </div>

            <Textarea
              label="Reason"
              required
              rows={4}
              value={formData.reason}
              onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
              placeholder="Please provide a reason for your leave application..."
            />
          </div>
        </FormModal>
      </div>
  );
};

export default LeavePage;
