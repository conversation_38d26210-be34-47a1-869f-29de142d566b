{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/lib/biostarApi.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\n\n// BioStar 2 API Configuration\nconst BIOSTAR_API_URL = process.env.NEXT_PUBLIC_BIOSTAR_API_URL || 'https://bs2api.biostar2.com';\nconst BIOSTAR_USERNAME = process.env.NEXT_PUBLIC_BIOSTAR_USERNAME || '';\nconst BIOSTAR_PASSWORD = process.env.NEXT_PUBLIC_BIOSTAR_PASSWORD || '';\n\n// BioStar 2 API Types\nexport interface BiostarUser {\n  id: string;\n  user_id: string;\n  name: string;\n  email?: string;\n  phone?: string;\n  department?: string;\n  position?: string;\n  start_datetime?: string;\n  expiry_datetime?: string;\n  disabled: boolean;\n  created: string;\n  updated: string;\n}\n\nexport interface BiostarEvent {\n  id: string;\n  user_id: string;\n  device_id: string;\n  event_type: 'ENTRY' | 'EXIT' | 'DENIED';\n  datetime: string;\n  user_name?: string;\n  device_name?: string;\n}\n\nexport interface BiostarAttendance {\n  user_id: string;\n  user_name: string;\n  date: string;\n  first_in?: string;\n  last_out?: string;\n  total_hours?: number;\n  break_time?: number;\n  overtime?: number;\n  events: BiostarEvent[];\n}\n\nexport interface BiostarDevice {\n  id: string;\n  name: string;\n  ip: string;\n  port: number;\n  status: 'ONLINE' | 'OFFLINE';\n  type: string;\n  location?: string;\n}\n\nexport interface BiostarAuthResponse {\n  access_token: string;\n  token_type: string;\n  expires_in: number;\n}\n\n// Create axios instance for BioStar 2 API\nconst biostarClient: AxiosInstance = axios.create({\n  baseURL: BIOSTAR_API_URL,\n  timeout: 15000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// BioStar Token Management\nclass BiostarTokenManager {\n  private static readonly BIOSTAR_TOKEN_KEY = 'biostar_access_token';\n  private static readonly BIOSTAR_TOKEN_EXPIRY_KEY = 'biostar_token_expiry';\n\n  static getToken(): string | null {\n    if (typeof window === 'undefined') return null;\n    const token = localStorage.getItem(this.BIOSTAR_TOKEN_KEY);\n    const expiry = localStorage.getItem(this.BIOSTAR_TOKEN_EXPIRY_KEY);\n    \n    if (token && expiry) {\n      const expiryTime = parseInt(expiry);\n      if (Date.now() < expiryTime) {\n        return token;\n      } else {\n        this.clearToken();\n      }\n    }\n    return null;\n  }\n\n  static setToken(token: string, expiresIn: number): void {\n    if (typeof window === 'undefined') return;\n    const expiryTime = Date.now() + (expiresIn * 1000);\n    localStorage.setItem(this.BIOSTAR_TOKEN_KEY, token);\n    localStorage.setItem(this.BIOSTAR_TOKEN_EXPIRY_KEY, expiryTime.toString());\n  }\n\n  static clearToken(): void {\n    if (typeof window === 'undefined') return;\n    localStorage.removeItem(this.BIOSTAR_TOKEN_KEY);\n    localStorage.removeItem(this.BIOSTAR_TOKEN_EXPIRY_KEY);\n  }\n}\n\n// Request interceptor to add auth token\nbiostarClient.interceptors.request.use(\n  async (config) => {\n    let token = BiostarTokenManager.getToken();\n    \n    // If no valid token, authenticate first\n    if (!token) {\n      try {\n        const authResponse = await biostarClient.post<BiostarAuthResponse>('/login', {\n          username: BIOSTAR_USERNAME,\n          password: BIOSTAR_PASSWORD,\n        });\n        \n        token = authResponse.data.access_token;\n        BiostarTokenManager.setToken(token, authResponse.data.expires_in);\n      } catch (error) {\n        console.error('BioStar authentication failed:', error);\n        throw error;\n      }\n    }\n    \n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    \n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle token refresh\nbiostarClient.interceptors.response.use(\n  (response) => response,\n  async (error) => {\n    const originalRequest = error.config;\n\n    if (error.response?.status === 401 && !originalRequest._retry) {\n      originalRequest._retry = true;\n      BiostarTokenManager.clearToken();\n      \n      try {\n        const authResponse = await biostarClient.post<BiostarAuthResponse>('/login', {\n          username: BIOSTAR_USERNAME,\n          password: BIOSTAR_PASSWORD,\n        });\n        \n        const token = authResponse.data.access_token;\n        BiostarTokenManager.setToken(token, authResponse.data.expires_in);\n        \n        // Retry original request with new token\n        originalRequest.headers.Authorization = `Bearer ${token}`;\n        return biostarClient(originalRequest);\n      } catch (authError) {\n        console.error('BioStar re-authentication failed:', authError);\n        return Promise.reject(authError);\n      }\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// BioStar API Service\nclass BiostarApiService {\n  // Generic request method\n  private async request<T>(config: AxiosRequestConfig): Promise<T> {\n    try {\n      const response: AxiosResponse<T> = await biostarClient(config);\n      return response.data;\n    } catch (error) {\n      console.error('BioStar API Request Error:', error);\n      throw error;\n    }\n  }\n\n  // GET request\n  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    return this.request<T>({ ...config, method: 'GET', url });\n  }\n\n  // POST request\n  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    return this.request<T>({ ...config, method: 'POST', url, data });\n  }\n\n  // PUT request\n  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    return this.request<T>({ ...config, method: 'PUT', url, data });\n  }\n\n  // DELETE request\n  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    return this.request<T>({ ...config, method: 'DELETE', url });\n  }\n\n  // Authentication\n  async authenticate(): Promise<BiostarAuthResponse> {\n    return this.post<BiostarAuthResponse>('/login', {\n      username: BIOSTAR_USERNAME,\n      password: BIOSTAR_PASSWORD,\n    });\n  }\n\n  // Users\n  async getUsers(limit = 100, offset = 0): Promise<{ results: BiostarUser[]; count: number }> {\n    return this.get(`/users?limit=${limit}&offset=${offset}`);\n  }\n\n  async getUserById(userId: string): Promise<BiostarUser> {\n    return this.get(`/users/${userId}`);\n  }\n\n  async createUser(userData: Partial<BiostarUser>): Promise<BiostarUser> {\n    return this.post('/users', userData);\n  }\n\n  async updateUser(userId: string, userData: Partial<BiostarUser>): Promise<BiostarUser> {\n    return this.put(`/users/${userId}`, userData);\n  }\n\n  async deleteUser(userId: string): Promise<void> {\n    return this.delete(`/users/${userId}`);\n  }\n\n  // Events (Access logs)\n  async getEvents(\n    startDate?: string,\n    endDate?: string,\n    userId?: string,\n    deviceId?: string,\n    limit = 100,\n    offset = 0\n  ): Promise<{ results: BiostarEvent[]; count: number }> {\n    const params = new URLSearchParams();\n    if (startDate) params.append('start_datetime', startDate);\n    if (endDate) params.append('end_datetime', endDate);\n    if (userId) params.append('user_id', userId);\n    if (deviceId) params.append('device_id', deviceId);\n    params.append('limit', limit.toString());\n    params.append('offset', offset.toString());\n\n    return this.get(`/events?${params.toString()}`);\n  }\n\n  // Devices\n  async getDevices(): Promise<{ results: BiostarDevice[]; count: number }> {\n    return this.get('/devices');\n  }\n\n  async getDeviceById(deviceId: string): Promise<BiostarDevice> {\n    return this.get(`/devices/${deviceId}`);\n  }\n\n  // Attendance Reports\n  async getAttendanceReport(\n    startDate: string,\n    endDate: string,\n    userId?: string\n  ): Promise<BiostarAttendance[]> {\n    const params = new URLSearchParams();\n    params.append('start_date', startDate);\n    params.append('end_date', endDate);\n    if (userId) params.append('user_id', userId);\n\n    return this.get(`/attendance/report?${params.toString()}`);\n  }\n\n  // Real-time monitoring\n  async getRealtimeEvents(limit = 50): Promise<BiostarEvent[]> {\n    return this.get(`/events/realtime?limit=${limit}`);\n  }\n\n  // Health check\n  async healthCheck(): Promise<{ status: string; timestamp: string }> {\n    return this.get('/health');\n  }\n}\n\n// Create and export BioStar API service instance\nexport const biostarApi = new BiostarApiService();\n\n// Export token manager for external use\nexport { BiostarTokenManager };\n\n// Export the configured axios instance for custom requests\nexport { biostarClient };\n"], "names": ["BiostarTokenManager", "biostarApi", "biostarClient", "BIOSTAR_API_URL", "process", "env", "NEXT_PUBLIC_BIOSTAR_API_URL", "BIOSTAR_USERNAME", "NEXT_PUBLIC_BIOSTAR_USERNAME", "BIOSTAR_PASSWORD", "NEXT_PUBLIC_BIOSTAR_PASSWORD", "axios", "create", "baseURL", "timeout", "headers", "BIOSTAR_TOKEN_KEY", "BIOSTAR_TOKEN_EXPIRY_KEY", "getToken", "window", "token", "localStorage", "getItem", "expiry", "expiryTime", "parseInt", "Date", "now", "clearToken", "setToken", "expiresIn", "setItem", "toString", "removeItem", "interceptors", "request", "use", "config", "authResponse", "post", "username", "password", "data", "access_token", "expires_in", "error", "console", "Authorization", "Promise", "reject", "response", "originalRequest", "status", "_retry", "authError", "BiostarApiService", "get", "url", "method", "put", "delete", "authenticate", "getUsers", "limit", "offset", "getUserById", "userId", "createUser", "userData", "updateUser", "deleteUser", "getEvents", "startDate", "endDate", "deviceId", "params", "URLSearchParams", "append", "getDevices", "getDeviceById", "getAttendanceReport", "getRealtimeEvents", "healthCheck"], "mappings": ";;;;;;;;;;;IAiSSA,mBAAmB;eAAnBA;;IAHIC,UAAU;eAAVA;;IAMJC,aAAa;eAAbA;;;8DApS+D;;;;;;AAExE,8BAA8B;AAC9B,MAAMC,kBAAkBC,QAAQC,GAAG,CAACC,2BAA2B,IAAI;AACnE,MAAMC,mBAAmBH,QAAQC,GAAG,CAACG,4BAA4B,IAAI;AACrE,MAAMC,mBAAmBL,QAAQC,GAAG,CAACK,4BAA4B,IAAI;AAwDrE,0CAA0C;AAC1C,MAAMR,gBAA+BS,cAAK,CAACC,MAAM,CAAC;IAChDC,SAASV;IACTW,SAAS;IACTC,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,2BAA2B;AAC3B,MAAMf;;aACoBgB,oBAAoB;;;aACpBC,2BAA2B;;IAEnD,OAAOC,WAA0B;QAC/B,IAAI,OAAOC,WAAW,aAAa,OAAO;QAC1C,MAAMC,QAAQC,aAAaC,OAAO,CAAC,IAAI,CAACN,iBAAiB;QACzD,MAAMO,SAASF,aAAaC,OAAO,CAAC,IAAI,CAACL,wBAAwB;QAEjE,IAAIG,SAASG,QAAQ;YACnB,MAAMC,aAAaC,SAASF;YAC5B,IAAIG,KAAKC,GAAG,KAAKH,YAAY;gBAC3B,OAAOJ;YACT,OAAO;gBACL,IAAI,CAACQ,UAAU;YACjB;QACF;QACA,OAAO;IACT;IAEA,OAAOC,SAAST,KAAa,EAAEU,SAAiB,EAAQ;QACtD,IAAI,OAAOX,WAAW,aAAa;QACnC,MAAMK,aAAaE,KAAKC,GAAG,KAAMG,YAAY;QAC7CT,aAAaU,OAAO,CAAC,IAAI,CAACf,iBAAiB,EAAEI;QAC7CC,aAAaU,OAAO,CAAC,IAAI,CAACd,wBAAwB,EAAEO,WAAWQ,QAAQ;IACzE;IAEA,OAAOJ,aAAmB;QACxB,IAAI,OAAOT,WAAW,aAAa;QACnCE,aAAaY,UAAU,CAAC,IAAI,CAACjB,iBAAiB;QAC9CK,aAAaY,UAAU,CAAC,IAAI,CAAChB,wBAAwB;IACvD;AACF;AAEA,wCAAwC;AACxCf,cAAcgC,YAAY,CAACC,OAAO,CAACC,GAAG,CACpC,OAAOC;IACL,IAAIjB,QAAQpB,oBAAoBkB,QAAQ;IAExC,wCAAwC;IACxC,IAAI,CAACE,OAAO;QACV,IAAI;YACF,MAAMkB,eAAe,MAAMpC,cAAcqC,IAAI,CAAsB,UAAU;gBAC3EC,UAAUjC;gBACVkC,UAAUhC;YACZ;YAEAW,QAAQkB,aAAaI,IAAI,CAACC,YAAY;YACtC3C,oBAAoB6B,QAAQ,CAACT,OAAOkB,aAAaI,IAAI,CAACE,UAAU;QAClE,EAAE,OAAOC,OAAO;YACdC,QAAQD,KAAK,CAAC,kCAAkCA;YAChD,MAAMA;QACR;IACF;IAEA,IAAIzB,OAAO;QACTiB,OAAOtB,OAAO,CAACgC,aAAa,GAAG,CAAC,OAAO,EAAE3B,OAAO;IAClD;IAEA,OAAOiB;AACT,GACA,CAACQ;IACC,OAAOG,QAAQC,MAAM,CAACJ;AACxB;AAGF,+CAA+C;AAC/C3C,cAAcgC,YAAY,CAACgB,QAAQ,CAACd,GAAG,CACrC,CAACc,WAAaA,UACd,OAAOL;IACL,MAAMM,kBAAkBN,MAAMR,MAAM;IAEpC,IAAIQ,MAAMK,QAAQ,EAAEE,WAAW,OAAO,CAACD,gBAAgBE,MAAM,EAAE;QAC7DF,gBAAgBE,MAAM,GAAG;QACzBrD,oBAAoB4B,UAAU;QAE9B,IAAI;YACF,MAAMU,eAAe,MAAMpC,cAAcqC,IAAI,CAAsB,UAAU;gBAC3EC,UAAUjC;gBACVkC,UAAUhC;YACZ;YAEA,MAAMW,QAAQkB,aAAaI,IAAI,CAACC,YAAY;YAC5C3C,oBAAoB6B,QAAQ,CAACT,OAAOkB,aAAaI,IAAI,CAACE,UAAU;YAEhE,wCAAwC;YACxCO,gBAAgBpC,OAAO,CAACgC,aAAa,GAAG,CAAC,OAAO,EAAE3B,OAAO;YACzD,OAAOlB,cAAciD;QACvB,EAAE,OAAOG,WAAW;YAClBR,QAAQD,KAAK,CAAC,qCAAqCS;YACnD,OAAON,QAAQC,MAAM,CAACK;QACxB;IACF;IAEA,OAAON,QAAQC,MAAM,CAACJ;AACxB;AAGF,sBAAsB;AACtB,MAAMU;IACJ,yBAAyB;IACzB,MAAcpB,QAAWE,MAA0B,EAAc;QAC/D,IAAI;YACF,MAAMa,WAA6B,MAAMhD,cAAcmC;YACvD,OAAOa,SAASR,IAAI;QACtB,EAAE,OAAOG,OAAO;YACdC,QAAQD,KAAK,CAAC,8BAA8BA;YAC5C,MAAMA;QACR;IACF;IAEA,cAAc;IACd,MAAMW,IAAOC,GAAW,EAAEpB,MAA2B,EAAc;QACjE,OAAO,IAAI,CAACF,OAAO,CAAI;YAAE,GAAGE,MAAM;YAAEqB,QAAQ;YAAOD;QAAI;IACzD;IAEA,eAAe;IACf,MAAMlB,KAAQkB,GAAW,EAAEf,IAAU,EAAEL,MAA2B,EAAc;QAC9E,OAAO,IAAI,CAACF,OAAO,CAAI;YAAE,GAAGE,MAAM;YAAEqB,QAAQ;YAAQD;YAAKf;QAAK;IAChE;IAEA,cAAc;IACd,MAAMiB,IAAOF,GAAW,EAAEf,IAAU,EAAEL,MAA2B,EAAc;QAC7E,OAAO,IAAI,CAACF,OAAO,CAAI;YAAE,GAAGE,MAAM;YAAEqB,QAAQ;YAAOD;YAAKf;QAAK;IAC/D;IAEA,iBAAiB;IACjB,MAAMkB,OAAUH,GAAW,EAAEpB,MAA2B,EAAc;QACpE,OAAO,IAAI,CAACF,OAAO,CAAI;YAAE,GAAGE,MAAM;YAAEqB,QAAQ;YAAUD;QAAI;IAC5D;IAEA,iBAAiB;IACjB,MAAMI,eAA6C;QACjD,OAAO,IAAI,CAACtB,IAAI,CAAsB,UAAU;YAC9CC,UAAUjC;YACVkC,UAAUhC;QACZ;IACF;IAEA,QAAQ;IACR,MAAMqD,SAASC,QAAQ,GAAG,EAAEC,SAAS,CAAC,EAAsD;QAC1F,OAAO,IAAI,CAACR,GAAG,CAAC,CAAC,aAAa,EAAEO,MAAM,QAAQ,EAAEC,QAAQ;IAC1D;IAEA,MAAMC,YAAYC,MAAc,EAAwB;QACtD,OAAO,IAAI,CAACV,GAAG,CAAC,CAAC,OAAO,EAAEU,QAAQ;IACpC;IAEA,MAAMC,WAAWC,QAA8B,EAAwB;QACrE,OAAO,IAAI,CAAC7B,IAAI,CAAC,UAAU6B;IAC7B;IAEA,MAAMC,WAAWH,MAAc,EAAEE,QAA8B,EAAwB;QACrF,OAAO,IAAI,CAACT,GAAG,CAAC,CAAC,OAAO,EAAEO,QAAQ,EAAEE;IACtC;IAEA,MAAME,WAAWJ,MAAc,EAAiB;QAC9C,OAAO,IAAI,CAACN,MAAM,CAAC,CAAC,OAAO,EAAEM,QAAQ;IACvC;IAEA,uBAAuB;IACvB,MAAMK,UACJC,SAAkB,EAClBC,OAAgB,EAChBP,MAAe,EACfQ,QAAiB,EACjBX,QAAQ,GAAG,EACXC,SAAS,CAAC,EAC2C;QACrD,MAAMW,SAAS,IAAIC;QACnB,IAAIJ,WAAWG,OAAOE,MAAM,CAAC,kBAAkBL;QAC/C,IAAIC,SAASE,OAAOE,MAAM,CAAC,gBAAgBJ;QAC3C,IAAIP,QAAQS,OAAOE,MAAM,CAAC,WAAWX;QACrC,IAAIQ,UAAUC,OAAOE,MAAM,CAAC,aAAaH;QACzCC,OAAOE,MAAM,CAAC,SAASd,MAAM/B,QAAQ;QACrC2C,OAAOE,MAAM,CAAC,UAAUb,OAAOhC,QAAQ;QAEvC,OAAO,IAAI,CAACwB,GAAG,CAAC,CAAC,QAAQ,EAAEmB,OAAO3C,QAAQ,IAAI;IAChD;IAEA,UAAU;IACV,MAAM8C,aAAmE;QACvE,OAAO,IAAI,CAACtB,GAAG,CAAC;IAClB;IAEA,MAAMuB,cAAcL,QAAgB,EAA0B;QAC5D,OAAO,IAAI,CAAClB,GAAG,CAAC,CAAC,SAAS,EAAEkB,UAAU;IACxC;IAEA,qBAAqB;IACrB,MAAMM,oBACJR,SAAiB,EACjBC,OAAe,EACfP,MAAe,EACe;QAC9B,MAAMS,SAAS,IAAIC;QACnBD,OAAOE,MAAM,CAAC,cAAcL;QAC5BG,OAAOE,MAAM,CAAC,YAAYJ;QAC1B,IAAIP,QAAQS,OAAOE,MAAM,CAAC,WAAWX;QAErC,OAAO,IAAI,CAACV,GAAG,CAAC,CAAC,mBAAmB,EAAEmB,OAAO3C,QAAQ,IAAI;IAC3D;IAEA,uBAAuB;IACvB,MAAMiD,kBAAkBlB,QAAQ,EAAE,EAA2B;QAC3D,OAAO,IAAI,CAACP,GAAG,CAAC,CAAC,uBAAuB,EAAEO,OAAO;IACnD;IAEA,eAAe;IACf,MAAMmB,cAA8D;QAClE,OAAO,IAAI,CAAC1B,GAAG,CAAC;IAClB;AACF;AAGO,MAAMvD,aAAa,IAAIsD"}