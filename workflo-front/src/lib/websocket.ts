import { TokenManager } from './api';

export interface NotificationData {
  id: string;
  type: 'leave_update' | 'payroll_update' | 'performance_update' | 'company_announcement' | 'system_maintenance' | 'ticket_update';
  title: string;
  message: string;
  data?: any;
  timestamp: string;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
  user_id: number;
}

export interface WebSocketMessage {
  type: 'notification' | 'status' | 'error' | 'ping' | 'pong';
  data?: any;
  timestamp?: string;
}

type WebSocketEventHandler = (data: any) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 1000; // Start with 1 second
  private maxReconnectInterval = 30000; // Max 30 seconds
  private reconnectTimer: NodeJS.Timeout | null = null;
  private pingTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map();
  private notifications: NotificationData[] = [];

  private getWebSocketUrl(): string {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = process.env.NEXT_PUBLIC_WS_HOST || window.location.host;
    return `${protocol}//${host}/ws/notifications/`;
  }

  private isWebSocketEnabled(): boolean {
    // Check if WebSocket is enabled via environment variable
    const wsEnabled = process.env.NEXT_PUBLIC_ENABLE_WEBSOCKET !== 'false';
    const isDevelopment = process.env.NODE_ENV === 'development';

    // In development, only connect if explicitly enabled
    if (isDevelopment && process.env.NEXT_PUBLIC_WS_HOST === undefined) {
      return false;
    }

    return wsEnabled;
  }

  connect(): void {
    if (!this.isWebSocketEnabled()) {
      console.log('WebSocket connection disabled in current environment');
      return;
    }

    if (this.ws?.readyState === WebSocket.OPEN || this.isConnecting) {
      return;
    }

    this.isConnecting = true;
    const token = TokenManager.getAccessToken();

    if (!token) {
      console.warn('No access token available for WebSocket connection');
      this.isConnecting = false;
      return;
    }

    try {
      const wsUrl = `${this.getWebSocketUrl()}?token=${token}`;
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);
    } catch (error) {
      console.warn('WebSocket connection error (this is normal in development):', error);
      this.isConnecting = false;
      // Don't schedule reconnect in development if WebSocket server is not available
      if (this.isWebSocketEnabled() && process.env.NODE_ENV !== 'development') {
        this.scheduleReconnect();
      }
    }
  }

  disconnect(): void {
    this.clearTimers();

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }

    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  private handleOpen(): void {
    console.log('WebSocket connected');
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.reconnectInterval = 1000;

    // Start ping/pong to keep connection alive
    this.startPing();

    // Emit connection event
    this.emit('connected', { timestamp: new Date().toISOString() });
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);

      switch (message.type) {
        case 'notification':
          this.handleNotification(message.data);
          break;
        case 'status':
          this.emit('status', message.data);
          break;
        case 'error':
          this.emit('error', message.data);
          break;
        case 'pong':
          // Pong received, connection is alive
          break;
        default:
          console.log('Unknown WebSocket message type:', message.type);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  private handleClose(event: CloseEvent): void {
    console.log('WebSocket disconnected:', event.code, event.reason);
    this.isConnecting = false;
    this.clearTimers();

    // Emit disconnection event
    this.emit('disconnected', {
      code: event.code,
      reason: event.reason,
      timestamp: new Date().toISOString()
    });

    // Attempt to reconnect unless it was a clean close
    if (event.code !== 1000) {
      this.scheduleReconnect();
    }
  }

  private handleError(error: Event): void {
    // Only log errors in production or when WebSocket is explicitly enabled
    if (process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_WS_HOST) {
      console.error('WebSocket error:', error);
    } else {
      console.log('WebSocket connection failed (this is normal in development without WebSocket server)');
    }
    this.isConnecting = false;
    this.emit('error', { error, timestamp: new Date().toISOString() });
  }

  private handleNotification(notification: NotificationData): void {
    // Add to notifications list
    this.notifications.unshift(notification);

    // Keep only last 100 notifications
    if (this.notifications.length > 100) {
      this.notifications = this.notifications.slice(0, 100);
    }

    // Emit notification event
    this.emit('notification', notification);

    // Show browser notification if permission granted
    this.showBrowserNotification(notification);
  }

  private showBrowserNotification(notification: NotificationData): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id,
        requireInteraction: notification.priority === 'high'
      });

      browserNotification.onclick = () => {
        window.focus();
        this.emit('notificationClick', notification);
        browserNotification.close();
      };

      // Auto close after 5 seconds for non-high priority
      if (notification.priority !== 'high') {
        setTimeout(() => {
          browserNotification.close();
        }, 5000);
      }
    }
  }

  private scheduleReconnect(): void {
    // Don't attempt reconnection in development without WebSocket server
    if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_WS_HOST) {
      return;
    }

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      if (process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_WS_HOST) {
        console.error('Max reconnection attempts reached');
      }
      this.emit('maxReconnectAttemptsReached', {
        attempts: this.reconnectAttempts,
        timestamp: new Date().toISOString()
      });
      return;
    }

    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++;
      if (process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_WS_HOST) {
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      }

      this.connect();

      // Exponential backoff
      this.reconnectInterval = Math.min(
        this.reconnectInterval * 2,
        this.maxReconnectInterval
      );
    }, this.reconnectInterval);
  }

  private startPing(): void {
    this.pingTimer = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.send({ type: 'ping', timestamp: new Date().toISOString() });
      }
    }, 30000); // Ping every 30 seconds
  }

  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
  }

  private send(message: WebSocketMessage): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  // Event system
  on(event: string, handler: WebSocketEventHandler): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  off(event: string, handler: WebSocketEventHandler): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private emit(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error('Error in WebSocket event handler:', error);
        }
      });
    }
  }

  // Public methods
  getNotifications(): NotificationData[] {
    return [...this.notifications];
  }

  getUnreadNotifications(): NotificationData[] {
    return this.notifications.filter(n => !n.read);
  }

  markNotificationAsRead(notificationId: string): void {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
      this.emit('notificationRead', notification);
    }
  }

  markAllNotificationsAsRead(): void {
    this.notifications.forEach(n => n.read = true);
    this.emit('allNotificationsRead', {});
  }

  clearNotifications(): void {
    this.notifications = [];
    this.emit('notificationsCleared', {});
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  getConnectionState(): string {
    if (!this.ws) return 'disconnected';

    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'connecting';
      case WebSocket.OPEN: return 'connected';
      case WebSocket.CLOSING: return 'closing';
      case WebSocket.CLOSED: return 'disconnected';
      default: return 'unknown';
    }
  }

  // Request browser notification permission
  static async requestNotificationPermission(): Promise<NotificationPermission> {
    if ('Notification' in window) {
      return await Notification.requestPermission();
    }
    return 'denied';
  }
}

// Create singleton instance
export const webSocketService = new WebSocketService();

// Export the class for static method access
export { WebSocketService };

export default webSocketService;
