'use client';

import React, { useState, useEffect } from 'react';
import { Building, Users, MapPin, Phone, Mail, Globe, Edit, Plus, Network } from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import OrganizationalChart from '@/components/ui/OrganizationalChart';
import { useAuth } from '@/store/authStore';

const CompanyPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [employees, setEmployees] = useState<any[]>([]);

  // Mock company data
  const companyData = {
    name: 'WorkFlow Technologies',
    description: 'A leading HR management solutions provider',
    industry: 'Technology',
    founded: '2020',
    employees: 150,
    headquarters: 'San Francisco, CA',
    phone: '+****************',
    email: '<EMAIL>',
    website: 'www.workflow.com',
    logo: '/api/placeholder/100/100'
  };

  const departments = [
    { id: 1, name: '<PERSON>', employees: 45, manager: '<PERSON>' },
    { id: 2, name: '<PERSON>', employees: 12, manager: '<PERSON>' },
    { id: 3, name: 'Marketing', employees: 18, manager: '<PERSON>' },
    { id: 4, name: 'Sales', employees: 25, manager: 'Lisa Brown' },
    { id: 5, name: 'HR', employees: 8, manager: 'Emma Davis' },
    { id: 6, name: 'Finance', employees: 10, manager: 'David Lee' },
  ];

  const offices = [
    { id: 1, name: 'San Francisco HQ', address: '123 Tech Street, San Francisco, CA 94105', employees: 85 },
    { id: 2, name: 'New York Office', address: '456 Business Ave, New York, NY 10001', employees: 35 },
    { id: 3, name: 'Austin Branch', address: '789 Innovation Blvd, Austin, TX 73301', employees: 30 },
  ];

  // Mock employee data for organizational chart
  const mockEmployees = [
    {
      id: 1,
      employee_id: 'EMP001',
      first_name: 'John',
      last_name: 'Smith',
      email: '<EMAIL>',
      position: 'Chief Executive Officer',
      department_name: 'Executive',
      role: 'admin',
      supervisor_id: null,
    },
    {
      id: 2,
      employee_id: 'EMP002',
      first_name: 'Sarah',
      last_name: 'Johnson',
      email: '<EMAIL>',
      position: 'VP of Engineering',
      department_name: 'Engineering',
      role: 'supervisor',
      supervisor_id: 1,
    },
    {
      id: 3,
      employee_id: 'EMP003',
      first_name: 'Mike',
      last_name: 'Wilson',
      email: '<EMAIL>',
      position: 'VP of Marketing',
      department_name: 'Marketing',
      role: 'supervisor',
      supervisor_id: 1,
    },
    {
      id: 4,
      employee_id: 'EMP004',
      first_name: 'Lisa',
      last_name: 'Brown',
      email: '<EMAIL>',
      position: 'VP of Sales',
      department_name: 'Sales',
      role: 'supervisor',
      supervisor_id: 1,
    },
    {
      id: 5,
      employee_id: 'EMP005',
      first_name: 'Emma',
      last_name: 'Davis',
      email: '<EMAIL>',
      position: 'HR Director',
      department_name: 'HR',
      role: 'hr',
      supervisor_id: 1,
    },
    {
      id: 6,
      employee_id: 'EMP006',
      first_name: 'David',
      last_name: 'Lee',
      email: '<EMAIL>',
      position: 'Finance Director',
      department_name: 'Finance',
      role: 'supervisor',
      supervisor_id: 1,
    },
    {
      id: 7,
      employee_id: 'EMP007',
      first_name: 'Alex',
      last_name: 'Chen',
      email: '<EMAIL>',
      position: 'Senior Software Engineer',
      department_name: 'Engineering',
      role: 'employee',
      supervisor_id: 2,
    },
    {
      id: 8,
      employee_id: 'EMP008',
      first_name: 'Maria',
      last_name: 'Garcia',
      email: '<EMAIL>',
      position: 'Marketing Manager',
      department_name: 'Marketing',
      role: 'employee',
      supervisor_id: 3,
    },
    {
      id: 9,
      employee_id: 'EMP009',
      first_name: 'James',
      last_name: 'Taylor',
      email: '<EMAIL>',
      position: 'Sales Manager',
      department_name: 'Sales',
      role: 'employee',
      supervisor_id: 4,
    },
    {
      id: 10,
      employee_id: 'EMP010',
      first_name: 'Anna',
      last_name: 'White',
      email: '<EMAIL>',
      position: 'HR Specialist',
      department_name: 'HR',
      role: 'employee',
      supervisor_id: 5,
    },
  ];

  useEffect(() => {
    // In a real app, this would fetch from an API
    setEmployees(mockEmployees);
  }, []);

  return (
    <div className="space-y-6 max-w-full overflow-hidden">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center space-x-4 min-w-0 flex-1">
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-orange-500 rounded-lg flex items-center justify-center flex-shrink-0">
              <Building className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <h1 className="text-lg sm:text-2xl font-bold text-gray-900 truncate">{companyData.name}</h1>
              <p className="text-gray-600 mt-1 text-sm sm:text-base truncate">{companyData.description}</p>
            </div>
          </div>
          <div className="mt-4 sm:mt-0 flex-shrink-0">
            <Button variant="primary" className="w-full sm:w-auto">
              <Edit className="h-4 w-4 mr-2" />
              Edit Company
            </Button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-4 sm:space-x-8 px-4 sm:px-6 overflow-x-auto">
            {[
              { key: 'overview', label: 'Overview' },
              { key: 'departments', label: 'Departments' },
              { key: 'organization', label: 'Organization' },
              { key: 'offices', label: 'Offices' },
              { key: 'settings', label: 'Settings' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors whitespace-nowrap ${
                  activeTab === tab.key
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-4 sm:p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Company Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <Card className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-lg bg-blue-100">
                      <Users className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-600">Total Employees</h3>
                      <p className="text-2xl font-bold text-gray-900">{companyData.employees}</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-lg bg-green-100">
                      <Building className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-600">Departments</h3>
                      <p className="text-2xl font-bold text-gray-900">{departments.length}</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-lg bg-purple-100">
                      <MapPin className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-600">Offices</h3>
                      <p className="text-2xl font-bold text-gray-900">{offices.length}</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-lg bg-orange-100">
                      <Globe className="h-6 w-6 text-orange-600" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-sm font-medium text-gray-600">Founded</h3>
                      <p className="text-2xl font-bold text-gray-900">{companyData.founded}</p>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Company Information */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Company Information</h3>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <Building className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Industry</p>
                          <p className="font-medium text-gray-900">{companyData.industry}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <MapPin className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Headquarters</p>
                          <p className="font-medium text-gray-900">{companyData.headquarters}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Phone className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Phone</p>
                          <p className="font-medium text-gray-900">{companyData.phone}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Mail className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Email</p>
                          <p className="font-medium text-gray-900">{companyData.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Globe className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-600">Website</p>
                          <p className="font-medium text-gray-900">{companyData.website}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>

                <Card>
                  <div className="p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
                    <div className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">New employee onboarded</p>
                          <p className="text-xs text-gray-500">2 hours ago</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">Department restructured</p>
                          <p className="text-xs text-gray-500">1 day ago</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-orange-400 rounded-full mt-2"></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">Policy updated</p>
                          <p className="text-xs text-gray-500">3 days ago</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}

          {activeTab === 'departments' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Departments</h3>
                <Button variant="primary">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Department
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {departments.map((dept) => (
                  <Card key={dept.id} className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium text-gray-900">{dept.name}</h4>
                      <Button variant="secondary" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Employees</span>
                        <span className="text-sm font-medium text-gray-900">{dept.employees}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Manager</span>
                        <span className="text-sm font-medium text-gray-900">{dept.manager}</span>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'organization' && (
            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                <div className="min-w-0 flex-1">
                  <h3 className="text-base sm:text-lg font-medium text-gray-900 truncate">Organizational Chart</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Visual representation of the company's organizational structure and reporting relationships
                  </p>
                </div>
                <div className="flex items-center space-x-2 mt-4 sm:mt-0 flex-shrink-0">
                  <Button variant="secondary" size="sm" className="hidden sm:flex">
                    <Network className="h-4 w-4 mr-2" />
                    Export Chart
                  </Button>
                  <Button variant="primary" size="sm">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Structure
                  </Button>
                </div>
              </div>

              {/* Organizational Chart */}
              <Card>
                <div className="p-6">
                  <OrganizationalChart employees={employees} />
                </div>
              </Card>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-lg bg-blue-100">
                      <Users className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <h4 className="text-sm font-medium text-gray-600">Total Employees</h4>
                      <p className="text-2xl font-bold text-gray-900">{employees.length}</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-lg bg-green-100">
                      <Building className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <h4 className="text-sm font-medium text-gray-600">Departments</h4>
                      <p className="text-2xl font-bold text-gray-900">
                        {new Set(employees.map(emp => emp.department_name)).size}
                      </p>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-lg bg-purple-100">
                      <Network className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <h4 className="text-sm font-medium text-gray-600">Management Levels</h4>
                      <p className="text-2xl font-bold text-gray-900">
                        {employees.filter(emp => emp.role === 'supervisor' || emp.role === 'admin').length}
                      </p>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          )}

          {activeTab === 'offices' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Office Locations</h3>
                <Button variant="primary">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Office
                </Button>
              </div>

              <div className="space-y-4">
                {offices.map((office) => (
                  <Card key={office.id} className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="p-3 rounded-lg bg-blue-100">
                          <MapPin className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{office.name}</h4>
                          <p className="text-sm text-gray-600">{office.address}</p>
                          <p className="text-sm text-gray-500">{office.employees} employees</p>
                        </div>
                      </div>
                      <Button variant="secondary" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Company Settings</h3>
              <div className="text-center py-12">
                <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Company Settings
                </h3>
                <p className="text-gray-600">
                  Configure company-wide settings and preferences.
                </p>
                <Button variant="primary" className="mt-4">
                  Configure Settings
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CompanyPage;
