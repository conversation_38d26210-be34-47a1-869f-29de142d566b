{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/app/(auth)/(staff)/staff/info/biostar/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Fingerprint,\n  Eye,\n  Wifi,\n  WifiOff,\n  Clock,\n  Calendar,\n  MapPin,\n  Activity,\n  Shield,\n  RefreshCw,\n  CheckCircle,\n  AlertCircle,\n  Monitor,\n  User,\n  Database,\n  History,\n  Settings\n} from 'lucide-react';\nimport Card from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport { useAuth } from '@/providers/AuthProvider';\nimport { cn, formatDate } from '@/lib/utils';\nimport { useBiostarAttendance } from '@/hooks/useBiostarAttendance';\nimport { biostarApi } from '@/lib/biostarApi';\nimport { BiometricUser, BiometricEvent, BiometricDevice } from '@/types';\n\ninterface BiometricProfile {\n  user: BiometricUser | null;\n  enrolledFingerprints: number;\n  enrolledFaces: number;\n  lastSync: string | null;\n  accessLevel: string;\n  isActive: boolean;\n}\n\nconst StaffBiostarPage: React.FC = () => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState<'profile' | 'devices' | 'history' | 'security'>('profile');\n  const [biometricProfile, setBiometricProfile] = useState<BiometricProfile | null>(null);\n  const [recentEvents, setRecentEvents] = useState<BiometricEvent[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // Use BioStar attendance hook for device and connectivity data\n  const {\n    devices,\n    connected: biostarConnected,\n    error: connectionError,\n    refresh: refreshAttendance\n  } = useBiostarAttendance({\n    employeeId: user?.employee_id,\n    autoRefresh: true,\n    enableRealTime: false\n  });\n\n  useEffect(() => {\n    loadBiometricData();\n  }, [user?.employee_id]);\n\n  const loadBiometricData = async () => {\n    if (!user?.employee_id) {\n      setLoading(false);\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      // Get user's biometric profile from BioStar\n      const biostarUser = await biostarApi.getUserById(user.employee_id);\n\n      // Get recent access events for this user\n      const endDate = new Date().toISOString();\n      const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(); // Last 7 days\n      const eventsResponse = await biostarApi.getEvents(startDate, endDate, user.employee_id);\n\n      setBiometricProfile({\n        user: biostarUser,\n        enrolledFingerprints: 2, // Mock data - would come from BioStar\n        enrolledFaces: 1, // Mock data - would come from BioStar\n        lastSync: new Date().toISOString(),\n        accessLevel: 'Standard Employee',\n        isActive: !biostarUser?.disabled\n      });\n\n      setRecentEvents(eventsResponse?.results || []);\n\n    } catch (error) {\n      console.error('Failed to load biometric data:', error);\n      // Set default profile even on error\n      setBiometricProfile({\n        user: null,\n        enrolledFingerprints: 2,\n        enrolledFaces: 1,\n        lastSync: new Date().toISOString(),\n        accessLevel: 'Standard Employee',\n        isActive: true\n      });\n      setRecentEvents([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await Promise.all([\n      loadBiometricData(),\n      refreshAttendance()\n    ]);\n    setRefreshing(false);\n  };\n\n  const getEventTypeIcon = (eventType: string) => {\n    switch (eventType) {\n      case 'ENTRY': return <CheckCircle className=\"h-4 w-4 text-green-500\" />;\n      case 'EXIT': return <Activity className=\"h-4 w-4 text-blue-500\" />;\n      case 'DENIED': return <AlertCircle className=\"h-4 w-4 text-red-500\" />;\n      default: return <Clock className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getEventTypeColor = (eventType: string) => {\n    switch (eventType) {\n      case 'ENTRY': return 'bg-green-100 text-green-800';\n      case 'EXIT': return 'bg-blue-100 text-blue-800';\n      case 'DENIED': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const tabs = [\n    { id: 'profile', label: 'Biometric Profile', icon: User },\n    { id: 'devices', label: 'Devices', icon: Monitor },\n    { id: 'history', label: 'Access History', icon: History },\n    { id: 'security', label: 'Security', icon: Shield }\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center p-8\">\n        <RefreshCw className=\"h-8 w-8 animate-spin text-gray-400\" />\n        <span className=\"ml-2 text-gray-600\">Loading biometric data...</span>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">BioStar Profile</h1>\n          <p className=\"text-gray-600 mt-1\">\n            Manage your biometric data and view access history\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"flex items-center space-x-2\">\n            {biostarConnected ? (\n              <Wifi className=\"h-4 w-4 text-green-500\" />\n            ) : (\n              <WifiOff className=\"h-4 w-4 text-red-500\" />\n            )}\n            <span className=\"text-sm text-gray-600\">\n              BioStar {biostarConnected ? 'Connected' : 'Disconnected'}\n            </span>\n          </div>\n          <Button\n            variant=\"secondary\"\n            onClick={handleRefresh}\n            disabled={refreshing}\n          >\n            <RefreshCw className={cn(\"h-4 w-4 mr-2\", refreshing && \"animate-spin\")} />\n            {refreshing ? 'Refreshing...' : 'Refresh'}\n          </Button>\n        </div>\n      </div>\n\n      {/* Connection Error Alert */}\n      {connectionError && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <AlertCircle className=\"h-5 w-5 text-red-500 mr-2\" />\n            <div>\n              <h3 className=\"text-sm font-medium text-red-800\">Connection Error</h3>\n              <p className=\"text-sm text-red-700 mt-1\">{connectionError}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Status Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <Card>\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <User className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Profile Status</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {biometricProfile?.isActive ? 'Active' : 'Inactive'}\n                </p>\n              </div>\n            </div>\n          </div>\n        </Card>\n\n        <Card>\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <Fingerprint className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Fingerprints</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {biometricProfile?.enrolledFingerprints || 0}\n                </p>\n              </div>\n            </div>\n          </div>\n        </Card>\n\n        <Card>\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <Eye className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Face Templates</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {biometricProfile?.enrolledFaces || 0}\n                </p>\n              </div>\n            </div>\n          </div>\n        </Card>\n\n        <Card>\n          <div className=\"p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-orange-100 rounded-lg\">\n                <Monitor className=\"h-6 w-6 text-orange-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Devices</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {devices.filter(d => d.status === 'ONLINE').length}/{devices.length}\n                </p>\n              </div>\n            </div>\n          </div>\n        </Card>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon;\n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={cn(\n                  'flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm',\n                  activeTab === tab.id\n                    ? 'border-orange-500 text-orange-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                )}\n              >\n                <Icon className=\"h-4 w-4\" />\n                <span>{tab.label}</span>\n              </button>\n            );\n          })}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      <div className=\"mt-6\">\n        {activeTab === 'profile' && (\n          <BiometricProfileTab\n            profile={biometricProfile}\n            user={user}\n          />\n        )}\n\n        {activeTab === 'devices' && (\n          <DevicesTab\n            devices={devices}\n            connected={biostarConnected}\n          />\n        )}\n\n        {activeTab === 'history' && (\n          <AccessHistoryTab\n            events={recentEvents}\n            employeeId={user?.employee_id}\n          />\n        )}\n\n        {activeTab === 'security' && (\n          <SecurityTab\n            profile={biometricProfile}\n            user={user}\n          />\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Biometric Profile Tab Component\ninterface BiometricProfileTabProps {\n  profile: BiometricProfile | null;\n  user: any;\n}\n\nconst BiometricProfileTab: React.FC<BiometricProfileTabProps> = ({ profile, user }) => {\n  return (\n    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      {/* Profile Information */}\n      <Card>\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Profile Information</h3>\n          <div className=\"space-y-4\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">Employee ID</span>\n              <span className=\"font-medium\">{user?.employee_id || 'N/A'}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">Full Name</span>\n              <span className=\"font-medium\">{profile?.user?.name || `${user?.first_name} ${user?.last_name}`}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">Email</span>\n              <span className=\"font-medium\">{profile?.user?.email || user?.email}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">Department</span>\n              <span className=\"font-medium\">{profile?.user?.department || 'N/A'}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">Position</span>\n              <span className=\"font-medium\">{profile?.user?.position || 'N/A'}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">Access Level</span>\n              <span className=\"font-medium\">{profile?.accessLevel || 'Standard'}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600\">Status</span>\n              <span className={cn(\n                'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',\n                profile?.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n              )}>\n                {profile?.isActive ? 'Active' : 'Inactive'}\n              </span>\n            </div>\n          </div>\n        </div>\n      </Card>\n\n      {/* Biometric Data */}\n      <Card>\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Biometric Enrollment</h3>\n          <div className=\"space-y-6\">\n            {/* Fingerprints */}\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <Fingerprint className=\"h-5 w-5 text-blue-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium text-gray-900\">Fingerprints</p>\n                  <p className=\"text-sm text-gray-600\">{profile?.enrolledFingerprints || 0} enrolled</p>\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-gray-900\">{profile?.enrolledFingerprints || 0}/10</div>\n                <div className=\"text-xs text-gray-500\">Max: 10</div>\n              </div>\n            </div>\n\n            {/* Face Recognition */}\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"p-2 bg-purple-100 rounded-lg\">\n                  <Eye className=\"h-5 w-5 text-purple-600\" />\n                </div>\n                <div>\n                  <p className=\"font-medium text-gray-900\">Face Templates</p>\n                  <p className=\"text-sm text-gray-600\">{profile?.enrolledFaces || 0} enrolled</p>\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-gray-900\">{profile?.enrolledFaces || 0}/5</div>\n                <div className=\"text-xs text-gray-500\">Max: 5</div>\n              </div>\n            </div>\n\n            {/* Last Sync */}\n            <div className=\"pt-4 border-t border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Last Sync</span>\n                <span className=\"font-medium\">\n                  {profile?.lastSync ?\n                    formatDate(profile.lastSync, 'MMM dd, yyyy HH:mm') :\n                    'Never'\n                  }\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\n// Devices Tab Component\ninterface DevicesTabProps {\n  devices: BiometricDevice[];\n  connected: boolean;\n}\n\nconst DevicesTab: React.FC<DevicesTabProps> = ({ devices, connected }) => {\n  return (\n    <div className=\"space-y-6\">\n      {/* Connection Status */}\n      <Card>\n        <div className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              {connected ? (\n                <Wifi className=\"h-6 w-6 text-green-500\" />\n              ) : (\n                <WifiOff className=\"h-6 w-6 text-red-500\" />\n              )}\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900\">System Status</h3>\n                <p className=\"text-sm text-gray-600\">\n                  BioStar system is {connected ? 'online and accessible' : 'offline or unreachable'}\n                </p>\n              </div>\n            </div>\n            <span className={cn(\n              'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',\n              connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n            )}>\n              {connected ? 'Connected' : 'Disconnected'}\n            </span>\n          </div>\n        </div>\n      </Card>\n\n      {/* Devices List */}\n      <Card>\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Available Devices</h3>\n          {devices.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <Monitor className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Devices Found</h3>\n              <p className=\"text-gray-600\">No biometric devices are currently available.</p>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {devices.map((device) => (\n                <div key={device.id} className=\"border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Monitor className=\"h-5 w-5 text-gray-600\" />\n                      <h4 className=\"font-medium text-gray-900\">{device.name}</h4>\n                    </div>\n                    <span className={cn(\n                      'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',\n                      device.status === 'ONLINE' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                    )}>\n                      {device.status}\n                    </span>\n                  </div>\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Location</span>\n                      <span className=\"font-medium\">{device.location || 'Unknown'}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">IP Address</span>\n                      <span className=\"font-medium\">{device.ip}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600\">Type</span>\n                      <span className=\"font-medium\">{device.type}</span>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </Card>\n    </div>\n  );\n};\n\n// Access History Tab Component\ninterface AccessHistoryTabProps {\n  events: BiometricEvent[];\n  employeeId?: string;\n}\n\nconst AccessHistoryTab: React.FC<AccessHistoryTabProps> = ({ events, employeeId }) => {\n  const [selectedPeriod, setSelectedPeriod] = useState('7_days');\n  const [filteredEvents, setFilteredEvents] = useState<BiometricEvent[]>(events);\n\n  useEffect(() => {\n    // Filter events based on selected period\n    const now = new Date();\n    let startDate: Date;\n\n    switch (selectedPeriod) {\n      case '1_day':\n        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n        break;\n      case '7_days':\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n        break;\n      case '30_days':\n        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n        break;\n      default:\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n    }\n\n    const filtered = events.filter(event =>\n      new Date(event.datetime) >= startDate\n    );\n    setFilteredEvents(filtered);\n  }, [events, selectedPeriod]);\n\n  const getEventTypeIcon = (eventType: string) => {\n    switch (eventType) {\n      case 'ENTRY': return <CheckCircle className=\"h-4 w-4 text-green-500\" />;\n      case 'EXIT': return <Activity className=\"h-4 w-4 text-blue-500\" />;\n      case 'DENIED': return <AlertCircle className=\"h-4 w-4 text-red-500\" />;\n      default: return <Clock className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getEventTypeColor = (eventType: string) => {\n    switch (eventType) {\n      case 'ENTRY': return 'bg-green-100 text-green-800';\n      case 'EXIT': return 'bg-blue-100 text-blue-800';\n      case 'DENIED': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Filter Controls */}\n      <Card>\n        <div className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-medium text-gray-900\">Access History</h3>\n            <select\n              value={selectedPeriod}\n              onChange={(e) => setSelectedPeriod(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500\"\n            >\n              <option value=\"1_day\">Last 24 Hours</option>\n              <option value=\"7_days\">Last 7 Days</option>\n              <option value=\"30_days\">Last 30 Days</option>\n            </select>\n          </div>\n        </div>\n      </Card>\n\n      {/* Events List */}\n      <Card>\n        <div className=\"p-6\">\n          {filteredEvents.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <History className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Access Events</h3>\n              <p className=\"text-gray-600\">No access events found for the selected period.</p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {filteredEvents.map((event, index) => (\n                <div key={`${event.id}-${index}`} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n                  <div className=\"flex items-center space-x-4\">\n                    {getEventTypeIcon(event.event_type)}\n                    <div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"font-medium text-gray-900\">\n                          {event.event_type === 'ENTRY' ? 'Check In' :\n                           event.event_type === 'EXIT' ? 'Check Out' : 'Access Denied'}\n                        </span>\n                        <span className={cn(\n                          'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',\n                          getEventTypeColor(event.event_type)\n                        )}>\n                          {event.event_type}\n                        </span>\n                      </div>\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-600 mt-1\">\n                        <div className=\"flex items-center space-x-1\">\n                          <Clock className=\"h-3 w-3\" />\n                          <span>{formatDate(event.datetime, 'MMM dd, yyyy HH:mm:ss')}</span>\n                        </div>\n                        {event.device_name && (\n                          <div className=\"flex items-center space-x-1\">\n                            <Monitor className=\"h-3 w-3\" />\n                            <span>{event.device_name}</span>\n                          </div>\n                        )}\n                        <div className=\"flex items-center space-x-1\">\n                          <MapPin className=\"h-3 w-3\" />\n                          <span>Main Office</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </Card>\n    </div>\n  );\n};\n\n// Security Tab Component\ninterface SecurityTabProps {\n  profile: BiometricProfile | null;\n  user: any;\n}\n\nconst SecurityTab: React.FC<SecurityTabProps> = ({ profile, user }) => {\n  const [showSecuritySettings, setShowSecuritySettings] = useState(false);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Security Overview */}\n      <Card>\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Security Overview</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"p-3 bg-green-100 rounded-full w-12 h-12 mx-auto mb-2\">\n                <Shield className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <h4 className=\"font-medium text-gray-900\">Account Status</h4>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                {profile?.isActive ? 'Active & Secure' : 'Inactive'}\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"p-3 bg-blue-100 rounded-full w-12 h-12 mx-auto mb-2\">\n                <Database className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <h4 className=\"font-medium text-gray-900\">Data Sync</h4>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                {profile?.lastSync ? 'Recently Synced' : 'Not Synced'}\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"p-3 bg-purple-100 rounded-full w-12 h-12 mx-auto mb-2\">\n                <Fingerprint className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <h4 className=\"font-medium text-gray-900\">Biometric Security</h4>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                {(profile?.enrolledFingerprints || 0) + (profile?.enrolledFaces || 0) > 0 ? 'Enrolled' : 'Not Enrolled'}\n              </p>\n            </div>\n          </div>\n        </div>\n      </Card>\n\n      {/* Security Settings */}\n      <Card>\n        <div className=\"p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-medium text-gray-900\">Security Settings</h3>\n            <Button\n              variant=\"secondary\"\n              onClick={() => setShowSecuritySettings(!showSecuritySettings)}\n            >\n              <Settings className=\"h-4 w-4 mr-2\" />\n              {showSecuritySettings ? 'Hide Settings' : 'Show Settings'}\n            </Button>\n          </div>\n\n          {showSecuritySettings && (\n            <div className=\"space-y-4 border-t border-gray-200 pt-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Two-Factor Authentication</h4>\n                  <p className=\"text-sm text-gray-600\">Add an extra layer of security to your account</p>\n                </div>\n                <Button variant=\"secondary\" size=\"sm\">\n                  Enable\n                </Button>\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Access Notifications</h4>\n                  <p className=\"text-sm text-gray-600\">Get notified when your biometric data is used</p>\n                </div>\n                <Button variant=\"secondary\" size=\"sm\">\n                  Configure\n                </Button>\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Data Privacy</h4>\n                  <p className=\"text-sm text-gray-600\">Manage how your biometric data is stored and used</p>\n                </div>\n                <Button variant=\"secondary\" size=\"sm\">\n                  Review\n                </Button>\n              </div>\n            </div>\n          )}\n        </div>\n      </Card>\n\n      {/* Privacy Information */}\n      <Card>\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Privacy & Data Protection</h3>\n          <div className=\"space-y-4 text-sm text-gray-600\">\n            <div className=\"flex items-start space-x-2\">\n              <CheckCircle className=\"h-4 w-4 text-green-500 mt-0.5\" />\n              <p>Your biometric data is encrypted and stored securely</p>\n            </div>\n            <div className=\"flex items-start space-x-2\">\n              <CheckCircle className=\"h-4 w-4 text-green-500 mt-0.5\" />\n              <p>Access logs are maintained for security and compliance purposes</p>\n            </div>\n            <div className=\"flex items-start space-x-2\">\n              <CheckCircle className=\"h-4 w-4 text-green-500 mt-0.5\" />\n              <p>Your data is only used for authentication and attendance tracking</p>\n            </div>\n            <div className=\"flex items-start space-x-2\">\n              <CheckCircle className=\"h-4 w-4 text-green-500 mt-0.5\" />\n              <p>You can request data deletion upon employment termination</p>\n            </div>\n          </div>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default StaffBiostarPage;\n"], "names": ["StaffBiostarPage", "user", "useAuth", "activeTab", "setActiveTab", "useState", "biometricProfile", "setBiometricProfile", "recentEvents", "setRecentEvents", "loading", "setLoading", "refreshing", "setRefreshing", "devices", "connected", "biostarConnected", "error", "connectionError", "refresh", "refreshAttendance", "useBiostarAttendance", "employeeId", "employee_id", "autoRefresh", "enableRealTime", "useEffect", "loadBiometricData", "biostarUser", "biostarApi", "getUserById", "endDate", "Date", "toISOString", "startDate", "now", "eventsResponse", "getEvents", "enrolledFingerprints", "enrolledFaces", "lastSync", "accessLevel", "isActive", "disabled", "results", "console", "handleRefresh", "Promise", "all", "getEventTypeIcon", "eventType", "CheckCircle", "className", "Activity", "AlertCircle", "Clock", "getEventTypeColor", "tabs", "id", "label", "icon", "User", "Monitor", "History", "Shield", "div", "RefreshCw", "span", "h1", "p", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "variant", "onClick", "cn", "h3", "Card", "Fingerprint", "Eye", "filter", "d", "status", "length", "nav", "map", "tab", "Icon", "button", "BiometricProfileTab", "profile", "DevicesTab", "AccessHistoryTab", "events", "SecurityTab", "name", "first_name", "last_name", "email", "department", "position", "formatDate", "device", "h4", "location", "ip", "type", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "filteredEvents", "setFilteredEvents", "getTime", "filtered", "event", "datetime", "select", "value", "onChange", "e", "target", "option", "index", "event_type", "device_name", "MapPin", "showSecuritySettings", "setShowSecuritySettings", "Database", "Settings", "size"], "mappings": "AAAA;;;;;+BAkwBA;;;eAAA;;;;+DAhwB2C;6BAmBpC;6DACU;+DACE;8BACK;uBACO;sCACM;4BACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3B,MAAMA,mBAA6B;IACjC,MAAM,EAAEC,IAAI,EAAE,GAAGC,IAAAA,qBAAO;IACxB,MAAM,CAACC,WAAWC,aAAa,GAAGC,IAAAA,eAAQ,EAAiD;IAC3F,MAAM,CAACC,kBAAkBC,oBAAoB,GAAGF,IAAAA,eAAQ,EAA0B;IAClF,MAAM,CAACG,cAAcC,gBAAgB,GAAGJ,IAAAA,eAAQ,EAAmB,EAAE;IACrE,MAAM,CAACK,SAASC,WAAW,GAAGN,IAAAA,eAAQ,EAAC;IACvC,MAAM,CAACO,YAAYC,cAAc,GAAGR,IAAAA,eAAQ,EAAC;IAE7C,+DAA+D;IAC/D,MAAM,EACJS,OAAO,EACPC,WAAWC,gBAAgB,EAC3BC,OAAOC,eAAe,EACtBC,SAASC,iBAAiB,EAC3B,GAAGC,IAAAA,0CAAoB,EAAC;QACvBC,YAAYrB,MAAMsB;QAClBC,aAAa;QACbC,gBAAgB;IAClB;IAEAC,IAAAA,gBAAS,EAAC;QACRC;IACF,GAAG;QAAC1B,MAAMsB;KAAY;IAEtB,MAAMI,oBAAoB;QACxB,IAAI,CAAC1B,MAAMsB,aAAa;YACtBZ,WAAW;YACX;QACF;QAEA,IAAI;YACFA,WAAW;YAEX,4CAA4C;YAC5C,MAAMiB,cAAc,MAAMC,sBAAU,CAACC,WAAW,CAAC7B,KAAKsB,WAAW;YAEjE,yCAAyC;YACzC,MAAMQ,UAAU,IAAIC,OAAOC,WAAW;YACtC,MAAMC,YAAY,IAAIF,KAAKA,KAAKG,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAMF,WAAW,IAAI,cAAc;YAC9F,MAAMG,iBAAiB,MAAMP,sBAAU,CAACQ,SAAS,CAACH,WAAWH,SAAS9B,KAAKsB,WAAW;YAEtFhB,oBAAoB;gBAClBN,MAAM2B;gBACNU,sBAAsB;gBACtBC,eAAe;gBACfC,UAAU,IAAIR,OAAOC,WAAW;gBAChCQ,aAAa;gBACbC,UAAU,CAACd,aAAae;YAC1B;YAEAlC,gBAAgB2B,gBAAgBQ,WAAW,EAAE;QAE/C,EAAE,OAAO3B,OAAO;YACd4B,QAAQ5B,KAAK,CAAC,kCAAkCA;YAChD,oCAAoC;YACpCV,oBAAoB;gBAClBN,MAAM;gBACNqC,sBAAsB;gBACtBC,eAAe;gBACfC,UAAU,IAAIR,OAAOC,WAAW;gBAChCQ,aAAa;gBACbC,UAAU;YACZ;YACAjC,gBAAgB,EAAE;QACpB,SAAU;YACRE,WAAW;QACb;IACF;IAEA,MAAMmC,gBAAgB;QACpBjC,cAAc;QACd,MAAMkC,QAAQC,GAAG,CAAC;YAChBrB;YACAP;SACD;QACDP,cAAc;IAChB;IAEA,MAAMoC,mBAAmB,CAACC;QACxB,OAAQA;YACN,KAAK;gBAAS,qBAAO,qBAACC,wBAAW;oBAACC,WAAU;;YAC5C,KAAK;gBAAQ,qBAAO,qBAACC,qBAAQ;oBAACD,WAAU;;YACxC,KAAK;gBAAU,qBAAO,qBAACE,wBAAW;oBAACF,WAAU;;YAC7C;gBAAS,qBAAO,qBAACG,kBAAK;oBAACH,WAAU;;QACnC;IACF;IAEA,MAAMI,oBAAoB,CAACN;QACzB,OAAQA;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAMO,OAAO;QACX;YAAEC,IAAI;YAAWC,OAAO;YAAqBC,MAAMC,iBAAI;QAAC;QACxD;YAAEH,IAAI;YAAWC,OAAO;YAAWC,MAAME,oBAAO;QAAC;QACjD;YAAEJ,IAAI;YAAWC,OAAO;YAAkBC,MAAMG,oBAAO;QAAC;QACxD;YAAEL,IAAI;YAAYC,OAAO;YAAYC,MAAMI,mBAAM;QAAC;KACnD;IAED,IAAItD,SAAS;QACX,qBACE,sBAACuD;YAAIb,WAAU;;8BACb,qBAACc,sBAAS;oBAACd,WAAU;;8BACrB,qBAACe;oBAAKf,WAAU;8BAAqB;;;;IAG3C;IAEA,qBACE,sBAACa;QAAIb,WAAU;;0BAEb,sBAACa;gBAAIb,WAAU;;kCACb,sBAACa;;0CACC,qBAACG;gCAAGhB,WAAU;0CAAmC;;0CACjD,qBAACiB;gCAAEjB,WAAU;0CAAqB;;;;kCAIpC,sBAACa;wBAAIb,WAAU;;0CACb,sBAACa;gCAAIb,WAAU;;oCACZpC,iCACC,qBAACsD,iBAAI;wCAAClB,WAAU;uDAEhB,qBAACmB,oBAAO;wCAACnB,WAAU;;kDAErB,sBAACe;wCAAKf,WAAU;;4CAAwB;4CAC7BpC,mBAAmB,cAAc;;;;;0CAG9C,sBAACwD,eAAM;gCACLC,SAAQ;gCACRC,SAAS5B;gCACTH,UAAU/B;;kDAEV,qBAACsD,sBAAS;wCAACd,WAAWuB,IAAAA,SAAE,EAAC,gBAAgB/D,cAAc;;oCACtDA,aAAa,kBAAkB;;;;;;;YAMrCM,iCACC,qBAAC+C;gBAAIb,WAAU;0BACb,cAAA,sBAACa;oBAAIb,WAAU;;sCACb,qBAACE,wBAAW;4BAACF,WAAU;;sCACvB,sBAACa;;8CACC,qBAACW;oCAAGxB,WAAU;8CAAmC;;8CACjD,qBAACiB;oCAAEjB,WAAU;8CAA6BlC;;;;;;;0BAOlD,sBAAC+C;gBAAIb,WAAU;;kCACb,qBAACyB,aAAI;kCACH,cAAA,qBAACZ;4BAAIb,WAAU;sCACb,cAAA,sBAACa;gCAAIb,WAAU;;kDACb,qBAACa;wCAAIb,WAAU;kDACb,cAAA,qBAACS,iBAAI;4CAACT,WAAU;;;kDAElB,sBAACa;wCAAIb,WAAU;;0DACb,qBAACiB;gDAAEjB,WAAU;0DAAoC;;0DACjD,qBAACiB;gDAAEjB,WAAU;0DACV9C,kBAAkBoC,WAAW,WAAW;;;;;;;;kCAOnD,qBAACmC,aAAI;kCACH,cAAA,qBAACZ;4BAAIb,WAAU;sCACb,cAAA,sBAACa;gCAAIb,WAAU;;kDACb,qBAACa;wCAAIb,WAAU;kDACb,cAAA,qBAAC0B,wBAAW;4CAAC1B,WAAU;;;kDAEzB,sBAACa;wCAAIb,WAAU;;0DACb,qBAACiB;gDAAEjB,WAAU;0DAAoC;;0DACjD,qBAACiB;gDAAEjB,WAAU;0DACV9C,kBAAkBgC,wBAAwB;;;;;;;;kCAOrD,qBAACuC,aAAI;kCACH,cAAA,qBAACZ;4BAAIb,WAAU;sCACb,cAAA,sBAACa;gCAAIb,WAAU;;kDACb,qBAACa;wCAAIb,WAAU;kDACb,cAAA,qBAAC2B,gBAAG;4CAAC3B,WAAU;;;kDAEjB,sBAACa;wCAAIb,WAAU;;0DACb,qBAACiB;gDAAEjB,WAAU;0DAAoC;;0DACjD,qBAACiB;gDAAEjB,WAAU;0DACV9C,kBAAkBiC,iBAAiB;;;;;;;;kCAO9C,qBAACsC,aAAI;kCACH,cAAA,qBAACZ;4BAAIb,WAAU;sCACb,cAAA,sBAACa;gCAAIb,WAAU;;kDACb,qBAACa;wCAAIb,WAAU;kDACb,cAAA,qBAACU,oBAAO;4CAACV,WAAU;;;kDAErB,sBAACa;wCAAIb,WAAU;;0DACb,qBAACiB;gDAAEjB,WAAU;0DAAoC;;0DACjD,sBAACiB;gDAAEjB,WAAU;;oDACVtC,QAAQkE,MAAM,CAACC,CAAAA,IAAKA,EAAEC,MAAM,KAAK,UAAUC,MAAM;oDAAC;oDAAErE,QAAQqE,MAAM;;;;;;;;;;;0BAS/E,qBAAClB;gBAAIb,WAAU;0BACb,cAAA,qBAACgC;oBAAIhC,WAAU;8BACZK,KAAK4B,GAAG,CAAC,CAACC;wBACT,MAAMC,OAAOD,IAAI1B,IAAI;wBACrB,qBACE,sBAAC4B;4BAECd,SAAS,IAAMtE,aAAakF,IAAI5B,EAAE;4BAClCN,WAAWuB,IAAAA,SAAE,EACX,wEACAxE,cAAcmF,IAAI5B,EAAE,GAChB,sCACA;;8CAGN,qBAAC6B;oCAAKnC,WAAU;;8CAChB,qBAACe;8CAAMmB,IAAI3B,KAAK;;;2BAVX2B,IAAI5B,EAAE;oBAajB;;;0BAKJ,sBAACO;gBAAIb,WAAU;;oBACZjD,cAAc,2BACb,qBAACsF;wBACCC,SAASpF;wBACTL,MAAMA;;oBAITE,cAAc,2BACb,qBAACwF;wBACC7E,SAASA;wBACTC,WAAWC;;oBAIdb,cAAc,2BACb,qBAACyF;wBACCC,QAAQrF;wBACRc,YAAYrB,MAAMsB;;oBAIrBpB,cAAc,4BACb,qBAAC2F;wBACCJ,SAASpF;wBACTL,MAAMA;;;;;;AAMlB;AAQA,MAAMwF,sBAA0D,CAAC,EAAEC,OAAO,EAAEzF,IAAI,EAAE;IAChF,qBACE,sBAACgE;QAAIb,WAAU;;0BAEb,qBAACyB,aAAI;0BACH,cAAA,sBAACZ;oBAAIb,WAAU;;sCACb,qBAACwB;4BAAGxB,WAAU;sCAAyC;;sCACvD,sBAACa;4BAAIb,WAAU;;8CACb,sBAACa;oCAAIb,WAAU;;sDACb,qBAACe;4CAAKf,WAAU;sDAAwB;;sDACxC,qBAACe;4CAAKf,WAAU;sDAAenD,MAAMsB,eAAe;;;;8CAEtD,sBAAC0C;oCAAIb,WAAU;;sDACb,qBAACe;4CAAKf,WAAU;sDAAwB;;sDACxC,qBAACe;4CAAKf,WAAU;sDAAesC,SAASzF,MAAM8F,QAAQ,GAAG9F,MAAM+F,WAAW,CAAC,EAAE/F,MAAMgG,WAAW;;;;8CAEhG,sBAAChC;oCAAIb,WAAU;;sDACb,qBAACe;4CAAKf,WAAU;sDAAwB;;sDACxC,qBAACe;4CAAKf,WAAU;sDAAesC,SAASzF,MAAMiG,SAASjG,MAAMiG;;;;8CAE/D,sBAACjC;oCAAIb,WAAU;;sDACb,qBAACe;4CAAKf,WAAU;sDAAwB;;sDACxC,qBAACe;4CAAKf,WAAU;sDAAesC,SAASzF,MAAMkG,cAAc;;;;8CAE9D,sBAAClC;oCAAIb,WAAU;;sDACb,qBAACe;4CAAKf,WAAU;sDAAwB;;sDACxC,qBAACe;4CAAKf,WAAU;sDAAesC,SAASzF,MAAMmG,YAAY;;;;8CAE5D,sBAACnC;oCAAIb,WAAU;;sDACb,qBAACe;4CAAKf,WAAU;sDAAwB;;sDACxC,qBAACe;4CAAKf,WAAU;sDAAesC,SAASjD,eAAe;;;;8CAEzD,sBAACwB;oCAAIb,WAAU;;sDACb,qBAACe;4CAAKf,WAAU;sDAAwB;;sDACxC,qBAACe;4CAAKf,WAAWuB,IAAAA,SAAE,EACjB,uEACAe,SAAShD,WAAW,gCAAgC;sDAEnDgD,SAAShD,WAAW,WAAW;;;;;;;;;0BAQ1C,qBAACmC,aAAI;0BACH,cAAA,sBAACZ;oBAAIb,WAAU;;sCACb,qBAACwB;4BAAGxB,WAAU;sCAAyC;;sCACvD,sBAACa;4BAAIb,WAAU;;8CAEb,sBAACa;oCAAIb,WAAU;;sDACb,sBAACa;4CAAIb,WAAU;;8DACb,qBAACa;oDAAIb,WAAU;8DACb,cAAA,qBAAC0B,wBAAW;wDAAC1B,WAAU;;;8DAEzB,sBAACa;;sEACC,qBAACI;4DAAEjB,WAAU;sEAA4B;;sEACzC,sBAACiB;4DAAEjB,WAAU;;gEAAyBsC,SAASpD,wBAAwB;gEAAE;;;;;;;sDAG7E,sBAAC2B;4CAAIb,WAAU;;8DACb,sBAACa;oDAAIb,WAAU;;wDAAoCsC,SAASpD,wBAAwB;wDAAE;;;8DACtF,qBAAC2B;oDAAIb,WAAU;8DAAwB;;;;;;8CAK3C,sBAACa;oCAAIb,WAAU;;sDACb,sBAACa;4CAAIb,WAAU;;8DACb,qBAACa;oDAAIb,WAAU;8DACb,cAAA,qBAAC2B,gBAAG;wDAAC3B,WAAU;;;8DAEjB,sBAACa;;sEACC,qBAACI;4DAAEjB,WAAU;sEAA4B;;sEACzC,sBAACiB;4DAAEjB,WAAU;;gEAAyBsC,SAASnD,iBAAiB;gEAAE;;;;;;;sDAGtE,sBAAC0B;4CAAIb,WAAU;;8DACb,sBAACa;oDAAIb,WAAU;;wDAAoCsC,SAASnD,iBAAiB;wDAAE;;;8DAC/E,qBAAC0B;oDAAIb,WAAU;8DAAwB;;;;;;8CAK3C,qBAACa;oCAAIb,WAAU;8CACb,cAAA,sBAACa;wCAAIb,WAAU;;0DACb,qBAACe;gDAAKf,WAAU;0DAAwB;;0DACxC,qBAACe;gDAAKf,WAAU;0DACbsC,SAASlD,WACR6D,IAAAA,iBAAU,EAACX,QAAQlD,QAAQ,EAAE,wBAC7B;;;;;;;;;;;;AAUpB;AAQA,MAAMmD,aAAwC,CAAC,EAAE7E,OAAO,EAAEC,SAAS,EAAE;IACnE,qBACE,sBAACkD;QAAIb,WAAU;;0BAEb,qBAACyB,aAAI;0BACH,cAAA,qBAACZ;oBAAIb,WAAU;8BACb,cAAA,sBAACa;wBAAIb,WAAU;;0CACb,sBAACa;gCAAIb,WAAU;;oCACZrC,0BACC,qBAACuD,iBAAI;wCAAClB,WAAU;uDAEhB,qBAACmB,oBAAO;wCAACnB,WAAU;;kDAErB,sBAACa;;0DACC,qBAACW;gDAAGxB,WAAU;0DAAoC;;0DAClD,sBAACiB;gDAAEjB,WAAU;;oDAAwB;oDAChBrC,YAAY,0BAA0B;;;;;;;0CAI/D,qBAACoD;gCAAKf,WAAWuB,IAAAA,SAAE,EACjB,uEACA5D,YAAY,gCAAgC;0CAE3CA,YAAY,cAAc;;;;;;0BAOnC,qBAAC8D,aAAI;0BACH,cAAA,sBAACZ;oBAAIb,WAAU;;sCACb,qBAACwB;4BAAGxB,WAAU;sCAAyC;;wBACtDtC,QAAQqE,MAAM,KAAK,kBAClB,sBAAClB;4BAAIb,WAAU;;8CACb,qBAACU,oBAAO;oCAACV,WAAU;;8CACnB,qBAACwB;oCAAGxB,WAAU;8CAAyC;;8CACvD,qBAACiB;oCAAEjB,WAAU;8CAAgB;;;2CAG/B,qBAACa;4BAAIb,WAAU;sCACZtC,QAAQuE,GAAG,CAAC,CAACiB,uBACZ,sBAACrC;oCAAoBb,WAAU;;sDAC7B,sBAACa;4CAAIb,WAAU;;8DACb,sBAACa;oDAAIb,WAAU;;sEACb,qBAACU,oBAAO;4DAACV,WAAU;;sEACnB,qBAACmD;4DAAGnD,WAAU;sEAA6BkD,OAAOP,IAAI;;;;8DAExD,qBAAC5B;oDAAKf,WAAWuB,IAAAA,SAAE,EACjB,uEACA2B,OAAOpB,MAAM,KAAK,WAAW,gCAAgC;8DAE5DoB,OAAOpB,MAAM;;;;sDAGlB,sBAACjB;4CAAIb,WAAU;;8DACb,sBAACa;oDAAIb,WAAU;;sEACb,qBAACe;4DAAKf,WAAU;sEAAgB;;sEAChC,qBAACe;4DAAKf,WAAU;sEAAekD,OAAOE,QAAQ,IAAI;;;;8DAEpD,sBAACvC;oDAAIb,WAAU;;sEACb,qBAACe;4DAAKf,WAAU;sEAAgB;;sEAChC,qBAACe;4DAAKf,WAAU;sEAAekD,OAAOG,EAAE;;;;8DAE1C,sBAACxC;oDAAIb,WAAU;;sEACb,qBAACe;4DAAKf,WAAU;sEAAgB;;sEAChC,qBAACe;4DAAKf,WAAU;sEAAekD,OAAOI,IAAI;;;;;;;mCAxBtCJ,OAAO5C,EAAE;;;;;;;AAmCnC;AAQA,MAAMkC,mBAAoD,CAAC,EAAEC,MAAM,EAAEvE,UAAU,EAAE;IAC/E,MAAM,CAACqF,gBAAgBC,kBAAkB,GAAGvG,IAAAA,eAAQ,EAAC;IACrD,MAAM,CAACwG,gBAAgBC,kBAAkB,GAAGzG,IAAAA,eAAQ,EAAmBwF;IAEvEnE,IAAAA,gBAAS,EAAC;QACR,yCAAyC;QACzC,MAAMS,MAAM,IAAIH;QAChB,IAAIE;QAEJ,OAAQyE;YACN,KAAK;gBACHzE,YAAY,IAAIF,KAAKG,IAAI4E,OAAO,KAAK,KAAK,KAAK,KAAK;gBACpD;YACF,KAAK;gBACH7E,YAAY,IAAIF,KAAKG,IAAI4E,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;gBACxD;YACF,KAAK;gBACH7E,YAAY,IAAIF,KAAKG,IAAI4E,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;gBACzD;YACF;gBACE7E,YAAY,IAAIF,KAAKG,IAAI4E,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;QAC5D;QAEA,MAAMC,WAAWnB,OAAOb,MAAM,CAACiC,CAAAA,QAC7B,IAAIjF,KAAKiF,MAAMC,QAAQ,KAAKhF;QAE9B4E,kBAAkBE;IACpB,GAAG;QAACnB;QAAQc;KAAe;IAE3B,MAAM1D,mBAAmB,CAACC;QACxB,OAAQA;YACN,KAAK;gBAAS,qBAAO,qBAACC,wBAAW;oBAACC,WAAU;;YAC5C,KAAK;gBAAQ,qBAAO,qBAACC,qBAAQ;oBAACD,WAAU;;YACxC,KAAK;gBAAU,qBAAO,qBAACE,wBAAW;oBAACF,WAAU;;YAC7C;gBAAS,qBAAO,qBAACG,kBAAK;oBAACH,WAAU;;QACnC;IACF;IAEA,MAAMI,oBAAoB,CAACN;QACzB,OAAQA;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,sBAACe;QAAIb,WAAU;;0BAEb,qBAACyB,aAAI;0BACH,cAAA,qBAACZ;oBAAIb,WAAU;8BACb,cAAA,sBAACa;wBAAIb,WAAU;;0CACb,qBAACwB;gCAAGxB,WAAU;0CAAoC;;0CAClD,sBAAC+D;gCACCC,OAAOT;gCACPU,UAAU,CAACC,IAAMV,kBAAkBU,EAAEC,MAAM,CAACH,KAAK;gCACjDhE,WAAU;;kDAEV,qBAACoE;wCAAOJ,OAAM;kDAAQ;;kDACtB,qBAACI;wCAAOJ,OAAM;kDAAS;;kDACvB,qBAACI;wCAAOJ,OAAM;kDAAU;;;;;;;;0BAOhC,qBAACvC,aAAI;0BACH,cAAA,qBAACZ;oBAAIb,WAAU;8BACZyD,eAAe1B,MAAM,KAAK,kBACzB,sBAAClB;wBAAIb,WAAU;;0CACb,qBAACW,oBAAO;gCAACX,WAAU;;0CACnB,qBAACwB;gCAAGxB,WAAU;0CAAyC;;0CACvD,qBAACiB;gCAAEjB,WAAU;0CAAgB;;;uCAG/B,qBAACa;wBAAIb,WAAU;kCACZyD,eAAexB,GAAG,CAAC,CAAC4B,OAAOQ,sBAC1B,qBAACxD;gCAAiCb,WAAU;0CAC1C,cAAA,sBAACa;oCAAIb,WAAU;;wCACZH,iBAAiBgE,MAAMS,UAAU;sDAClC,sBAACzD;;8DACC,sBAACA;oDAAIb,WAAU;;sEACb,qBAACe;4DAAKf,WAAU;sEACb6D,MAAMS,UAAU,KAAK,UAAU,aAC/BT,MAAMS,UAAU,KAAK,SAAS,cAAc;;sEAE/C,qBAACvD;4DAAKf,WAAWuB,IAAAA,SAAE,EACjB,uEACAnB,kBAAkByD,MAAMS,UAAU;sEAEjCT,MAAMS,UAAU;;;;8DAGrB,sBAACzD;oDAAIb,WAAU;;sEACb,sBAACa;4DAAIb,WAAU;;8EACb,qBAACG,kBAAK;oEAACH,WAAU;;8EACjB,qBAACe;8EAAMkC,IAAAA,iBAAU,EAACY,MAAMC,QAAQ,EAAE;;;;wDAEnCD,MAAMU,WAAW,kBAChB,sBAAC1D;4DAAIb,WAAU;;8EACb,qBAACU,oBAAO;oEAACV,WAAU;;8EACnB,qBAACe;8EAAM8C,MAAMU,WAAW;;;;sEAG5B,sBAAC1D;4DAAIb,WAAU;;8EACb,qBAACwE,mBAAM;oEAACxE,WAAU;;8EAClB,qBAACe;8EAAK;;;;;;;;;;+BA7BN,GAAG8C,MAAMvD,EAAE,CAAC,CAAC,EAAE+D,OAAO;;;;;;AA0ChD;AAQA,MAAM3B,cAA0C,CAAC,EAAEJ,OAAO,EAAEzF,IAAI,EAAE;IAChE,MAAM,CAAC4H,sBAAsBC,wBAAwB,GAAGzH,IAAAA,eAAQ,EAAC;IAEjE,qBACE,sBAAC4D;QAAIb,WAAU;;0BAEb,qBAACyB,aAAI;0BACH,cAAA,sBAACZ;oBAAIb,WAAU;;sCACb,qBAACwB;4BAAGxB,WAAU;sCAAyC;;sCACvD,sBAACa;4BAAIb,WAAU;;8CACb,sBAACa;oCAAIb,WAAU;;sDACb,qBAACa;4CAAIb,WAAU;sDACb,cAAA,qBAACY,mBAAM;gDAACZ,WAAU;;;sDAEpB,qBAACmD;4CAAGnD,WAAU;sDAA4B;;sDAC1C,qBAACiB;4CAAEjB,WAAU;sDACVsC,SAAShD,WAAW,oBAAoB;;;;8CAG7C,sBAACuB;oCAAIb,WAAU;;sDACb,qBAACa;4CAAIb,WAAU;sDACb,cAAA,qBAAC2E,qBAAQ;gDAAC3E,WAAU;;;sDAEtB,qBAACmD;4CAAGnD,WAAU;sDAA4B;;sDAC1C,qBAACiB;4CAAEjB,WAAU;sDACVsC,SAASlD,WAAW,oBAAoB;;;;8CAG7C,sBAACyB;oCAAIb,WAAU;;sDACb,qBAACa;4CAAIb,WAAU;sDACb,cAAA,qBAAC0B,wBAAW;gDAAC1B,WAAU;;;sDAEzB,qBAACmD;4CAAGnD,WAAU;sDAA4B;;sDAC1C,qBAACiB;4CAAEjB,WAAU;sDACV,AAACsC,CAAAA,SAASpD,wBAAwB,CAAA,IAAMoD,CAAAA,SAASnD,iBAAiB,CAAA,IAAK,IAAI,aAAa;;;;;;;;;0BAQnG,qBAACsC,aAAI;0BACH,cAAA,sBAACZ;oBAAIb,WAAU;;sCACb,sBAACa;4BAAIb,WAAU;;8CACb,qBAACwB;oCAAGxB,WAAU;8CAAoC;;8CAClD,sBAACoB,eAAM;oCACLC,SAAQ;oCACRC,SAAS,IAAMoD,wBAAwB,CAACD;;sDAExC,qBAACG,qBAAQ;4CAAC5E,WAAU;;wCACnByE,uBAAuB,kBAAkB;;;;;wBAI7CA,sCACC,sBAAC5D;4BAAIb,WAAU;;8CACb,sBAACa;oCAAIb,WAAU;;sDACb,sBAACa;;8DACC,qBAACsC;oDAAGnD,WAAU;8DAA4B;;8DAC1C,qBAACiB;oDAAEjB,WAAU;8DAAwB;;;;sDAEvC,qBAACoB,eAAM;4CAACC,SAAQ;4CAAYwD,MAAK;sDAAK;;;;8CAKxC,sBAAChE;oCAAIb,WAAU;;sDACb,sBAACa;;8DACC,qBAACsC;oDAAGnD,WAAU;8DAA4B;;8DAC1C,qBAACiB;oDAAEjB,WAAU;8DAAwB;;;;sDAEvC,qBAACoB,eAAM;4CAACC,SAAQ;4CAAYwD,MAAK;sDAAK;;;;8CAKxC,sBAAChE;oCAAIb,WAAU;;sDACb,sBAACa;;8DACC,qBAACsC;oDAAGnD,WAAU;8DAA4B;;8DAC1C,qBAACiB;oDAAEjB,WAAU;8DAAwB;;;;sDAEvC,qBAACoB,eAAM;4CAACC,SAAQ;4CAAYwD,MAAK;sDAAK;;;;;;;;;0BAUhD,qBAACpD,aAAI;0BACH,cAAA,sBAACZ;oBAAIb,WAAU;;sCACb,qBAACwB;4BAAGxB,WAAU;sCAAyC;;sCACvD,sBAACa;4BAAIb,WAAU;;8CACb,sBAACa;oCAAIb,WAAU;;sDACb,qBAACD,wBAAW;4CAACC,WAAU;;sDACvB,qBAACiB;sDAAE;;;;8CAEL,sBAACJ;oCAAIb,WAAU;;sDACb,qBAACD,wBAAW;4CAACC,WAAU;;sDACvB,qBAACiB;sDAAE;;;;8CAEL,sBAACJ;oCAAIb,WAAU;;sDACb,qBAACD,wBAAW;4CAACC,WAAU;;sDACvB,qBAACiB;sDAAE;;;;8CAEL,sBAACJ;oCAAIb,WAAU;;sDACb,qBAACD,wBAAW;4CAACC,WAAU;;sDACvB,qBAACiB;sDAAE;;;;;;;;;;;AAOjB;MAEA,WAAerE"}