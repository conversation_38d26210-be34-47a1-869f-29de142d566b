{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/__tests__/basic.test.tsx"], "sourcesContent": ["import React from 'react';\nimport { render, screen } from '@testing-library/react';\n\n// Simple test to verify Jest and React Testing Library are working\ndescribe('Basic Test Setup', () => {\n  it('should render a simple component', () => {\n    const TestComponent = () => <div>Hello, World!</div>;\n    \n    render(<TestComponent />);\n    \n    expect(screen.getByText('Hello, World!')).toBeInTheDocument();\n  });\n\n  it('should handle basic assertions', () => {\n    expect(1 + 1).toBe(2);\n    expect('hello').toMatch(/hello/);\n    expect([1, 2, 3]).toHaveLength(3);\n  });\n\n  it('should mock functions', () => {\n    const mockFn = jest.fn();\n    mockFn('test');\n    \n    expect(mockFn).toHaveBeenCalledWith('test');\n    expect(mockFn).toHaveBeenCalledTimes(1);\n  });\n});\n"], "names": ["describe", "it", "TestComponent", "div", "render", "expect", "screen", "getByText", "toBeInTheDocument", "toBe", "toMatch", "toHave<PERSON>ength", "mockFn", "jest", "fn", "toHaveBeenCalledWith", "toHaveBeenCalledTimes"], "mappings": ";;;;;8DAAkB;wBACa;;;;;;AAE/B,mEAAmE;AACnEA,SAAS,oBAAoB;IAC3BC,GAAG,oCAAoC;QACrC,MAAMC,gBAAgB,kBAAM,qBAACC;0BAAI;;QAEjCC,IAAAA,cAAM,gBAAC,qBAACF;QAERG,OAAOC,cAAM,CAACC,SAAS,CAAC,kBAAkBC,iBAAiB;IAC7D;IAEAP,GAAG,kCAAkC;QACnCI,OAAO,IAAI,GAAGI,IAAI,CAAC;QACnBJ,OAAO,SAASK,OAAO,CAAC;QACxBL,OAAO;YAAC;YAAG;YAAG;SAAE,EAAEM,YAAY,CAAC;IACjC;IAEAV,GAAG,yBAAyB;QAC1B,MAAMW,SAASC,KAAKC,EAAE;QACtBF,OAAO;QAEPP,OAAOO,QAAQG,oBAAoB,CAAC;QACpCV,OAAOO,QAAQI,qBAAqB,CAAC;IACvC;AACF"}