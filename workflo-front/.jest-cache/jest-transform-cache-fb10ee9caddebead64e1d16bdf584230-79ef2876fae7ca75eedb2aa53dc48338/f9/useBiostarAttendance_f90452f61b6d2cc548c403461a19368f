70cdfd4a66cdd01c97897511578b2408
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    useBiostarAttendance: function() {
        return useBiostarAttendance;
    },
    useBiostarDevices: function() {
        return useBiostarDevices;
    },
    useBiostarRealTime: function() {
        return useBiostarRealTime;
    }
});
const _react = require("react");
const _attendanceService = require("../lib/attendanceService");
const useBiostarAttendance = (options = {})=>{
    const { employeeId, autoRefresh = true, refreshInterval = 300000, enableRealTime = true } = options;
    // State
    const [todayAttendance, setTodayAttendance] = (0, _react.useState)(null);
    const [attendanceRecords, setAttendanceRecords] = (0, _react.useState)([]);
    const [devices, setDevices] = (0, _react.useState)([]);
    const [realtimeUpdates, setRealtimeUpdates] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(true);
    const [connected, setConnected] = (0, _react.useState)(false);
    const [error, setError] = (0, _react.useState)(null);
    const [summary, setSummary] = (0, _react.useState)(null);
    // Load attendance data
    const loadAttendanceData = (0, _react.useCallback)(async ()=>{
        if (!employeeId) return;
        try {
            setLoading(true);
            setError(null);
            // Get today's attendance
            const today = await _attendanceService.attendanceService.getTodayAttendance(employeeId);
            setTodayAttendance(today);
            // Get attendance summary
            const summaryData = await _attendanceService.attendanceService.getAttendanceSummary(employeeId);
            setSummary(summaryData);
            // Get this month's records
            const monthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
            const today_date = new Date().toISOString().split('T')[0];
            const records = await _attendanceService.attendanceService.getAttendanceRange(employeeId, monthStart, today_date);
            setAttendanceRecords(records);
            // Get devices
            const deviceList = await _attendanceService.attendanceService.getDevices();
            setDevices(deviceList);
            setConnected(true);
        } catch (err) {
            console.error('Failed to load attendance data:', err);
            setError(err instanceof Error ? err.message : 'Failed to load attendance data');
            setConnected(false);
        } finally{
            setLoading(false);
        }
    }, [
        employeeId
    ]);
    // Refresh function
    const refresh = (0, _react.useCallback)(async ()=>{
        await loadAttendanceData();
    }, [
        loadAttendanceData
    ]);
    // Get attendance range
    const getAttendanceRange = (0, _react.useCallback)(async (startDate, endDate)=>{
        if (!employeeId) return [];
        try {
            const records = await _attendanceService.attendanceService.getAttendanceRange(employeeId, startDate, endDate);
            return records;
        } catch (err) {
            console.error('Failed to get attendance range:', err);
            setError(err instanceof Error ? err.message : 'Failed to get attendance range');
            return [];
        }
    }, [
        employeeId
    ]);
    // Real-time updates handler
    const handleRealTimeUpdate = (0, _react.useCallback)((update)=>{
        setRealtimeUpdates((prev)=>{
            // Add new update and keep only last 10
            const newUpdates = [
                update,
                ...prev
            ];
            return newUpdates.slice(0, 10);
        });
        // If it's for current employee, refresh today's data
        if (update.employee_id === employeeId) {
            // Debounce the refresh to avoid too many calls
            setTimeout(()=>{
                loadAttendanceData();
            }, 2000);
        }
    }, [
        employeeId,
        loadAttendanceData
    ]);
    // Initial load
    (0, _react.useEffect)(()=>{
        if (employeeId) {
            loadAttendanceData();
        }
    }, [
        employeeId,
        loadAttendanceData
    ]);
    // Auto refresh
    (0, _react.useEffect)(()=>{
        if (!autoRefresh || !employeeId) return;
        const interval = setInterval(()=>{
            loadAttendanceData();
        }, refreshInterval);
        return ()=>clearInterval(interval);
    }, [
        autoRefresh,
        refreshInterval,
        employeeId,
        loadAttendanceData
    ]);
    // Real-time monitoring
    (0, _react.useEffect)(()=>{
        if (!enableRealTime) return;
        _attendanceService.attendanceService.startRealTimeMonitoring(handleRealTimeUpdate);
        return ()=>{
            _attendanceService.attendanceService.stopRealTimeMonitoring(handleRealTimeUpdate);
        };
    }, [
        enableRealTime,
        handleRealTimeUpdate
    ]);
    return {
        // Data
        todayAttendance,
        attendanceRecords,
        devices,
        realtimeUpdates,
        // Status
        loading,
        connected,
        error,
        // Actions
        refresh,
        getAttendanceRange,
        // Summary
        summary
    };
};
const useBiostarDevices = ()=>{
    const [devices, setDevices] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(true);
    const [error, setError] = (0, _react.useState)(null);
    const loadDevices = (0, _react.useCallback)(async ()=>{
        try {
            setLoading(true);
            setError(null);
            const deviceList = await _attendanceService.attendanceService.getDevices();
            setDevices(deviceList);
        } catch (err) {
            console.error('Failed to load devices:', err);
            setError(err instanceof Error ? err.message : 'Failed to load devices');
        } finally{
            setLoading(false);
        }
    }, []);
    (0, _react.useEffect)(()=>{
        loadDevices();
        // Refresh devices every 2 minutes
        const interval = setInterval(loadDevices, 120000);
        return ()=>clearInterval(interval);
    }, [
        loadDevices
    ]);
    return {
        devices,
        loading,
        error,
        refresh: loadDevices,
        onlineDevices: devices.filter((d)=>d.status === 'ONLINE'),
        offlineDevices: devices.filter((d)=>d.status === 'OFFLINE')
    };
};
const useBiostarRealTime = ()=>{
    const [updates, setUpdates] = (0, _react.useState)([]);
    const [isMonitoring, setIsMonitoring] = (0, _react.useState)(false);
    const startMonitoring = (0, _react.useCallback)(()=>{
        if (isMonitoring) return;
        const handleUpdate = (update)=>{
            setUpdates((prev)=>[
                    update,
                    ...prev.slice(0, 19)
                ]); // Keep last 20 updates
        };
        _attendanceService.attendanceService.startRealTimeMonitoring(handleUpdate);
        setIsMonitoring(true);
        return ()=>{
            _attendanceService.attendanceService.stopRealTimeMonitoring(handleUpdate);
            setIsMonitoring(false);
        };
    }, [
        isMonitoring
    ]);
    const stopMonitoring = (0, _react.useCallback)(()=>{
        _attendanceService.attendanceService.stopRealTimeMonitoring();
        setIsMonitoring(false);
    }, []);
    const clearUpdates = (0, _react.useCallback)(()=>{
        setUpdates([]);
    }, []);
    return {
        updates,
        isMonitoring,
        startMonitoring,
        stopMonitoring,
        clearUpdates
    };
};

//# sourceMappingURL=data:application/json;base64,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