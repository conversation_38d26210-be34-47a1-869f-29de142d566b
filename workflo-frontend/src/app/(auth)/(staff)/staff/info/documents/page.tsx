'use client';

import React, { useState } from 'react';
import {
  FileText,
  Upload,
  Download,
  Eye,
  Trash2,
  Plus,
  Search,
  Filter,
  Calendar,
  User,
  Shield,
  AlertCircle,
  CheckCircle,
  Briefcase,
  GraduationCap,
  Heart,
  DollarSign,
  Star,
  XCircle
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import FileUpload from '@/components/ui/FileUpload';
import { DataLoader, ButtonLoader } from '@/components/ui/LoadingStates';
import ErrorBoundary from '@/components/error/ErrorBoundary';
import { useAuth } from '@/providers/AuthProvider';
import { cn, formatDate } from '@/lib/utils';
import { UploadResult } from '@/lib/fileUpload';

const StaffDocumentsPage: React.FC = () => {
  const { user } = useAuth();
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Mock documents data - in real app, this would come from API
  const documents = [
    {
      id: 1,
      name: 'Employment Contract',
      category: 'employment',
      file_type: 'PDF',
      file_size: '2.4 MB',
      uploaded_date: '2022-03-15',
      uploaded_by: 'HR Department',
      status: 'verified',
      description: 'Original employment contract document'
    },
    {
      id: 2,
      name: 'National ID Copy',
      category: 'identification',
      file_type: 'PDF',
      file_size: '1.2 MB',
      uploaded_date: '2024-11-20',
      uploaded_by: 'Self',
      status: 'verified',
      description: 'Updated national ID copy'
    },
    {
      id: 3,
      name: 'Academic Certificates',
      category: 'education',
      file_type: 'PDF',
      file_size: '3.1 MB',
      uploaded_date: '2022-03-10',
      uploaded_by: 'Self',
      status: 'verified',
      description: 'University degree and professional certificates'
    },
    {
      id: 4,
      name: 'Medical Certificate',
      category: 'medical',
      file_type: 'PDF',
      file_size: '0.8 MB',
      uploaded_date: '2024-01-15',
      uploaded_by: 'Self',
      status: 'pending',
      description: 'Annual medical checkup certificate'
    },
    {
      id: 5,
      name: 'Bank Statement',
      category: 'financial',
      file_type: 'PDF',
      file_size: '1.5 MB',
      uploaded_date: '2024-10-01',
      uploaded_by: 'Self',
      status: 'verified',
      description: 'Bank statement for salary account verification'
    },
    {
      id: 6,
      name: 'Performance Review Q3 2024',
      category: 'performance',
      file_type: 'PDF',
      file_size: '0.9 MB',
      uploaded_date: '2024-10-15',
      uploaded_by: 'Manager',
      status: 'verified',
      description: 'Quarterly performance evaluation document'
    }
  ];

  const documentCategories = [
    { value: 'all', label: 'All Documents' },
    { value: 'employment', label: 'Employment' },
    { value: 'identification', label: 'Identification' },
    { value: 'education', label: 'Education' },
    { value: 'medical', label: 'Medical' },
    { value: 'financial', label: 'Financial' },
    { value: 'performance', label: 'Performance' }
  ];

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'employment': return <Briefcase className="h-4 w-4" />;
      case 'identification': return <User className="h-4 w-4" />;
      case 'education': return <GraduationCap className="h-4 w-4" />;
      case 'medical': return <Heart className="h-4 w-4" />;
      case 'financial': return <DollarSign className="h-4 w-4" />;
      case 'performance': return <Star className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'employment': return 'bg-blue-100 text-blue-800';
      case 'identification': return 'bg-green-100 text-green-800';
      case 'education': return 'bg-purple-100 text-purple-800';
      case 'medical': return 'bg-red-100 text-red-800';
      case 'financial': return 'bg-yellow-100 text-yellow-800';
      case 'performance': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'verified': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'rejected': return <XCircle className="h-4 w-4 text-red-500" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = filterCategory === 'all' || doc.category === filterCategory;
    return matchesSearch && matchesCategory;
  });

  const handleUploadDocument = () => {
    setShowUploadForm(true);
  };

  const handleSubmitUpload = (e: React.FormEvent) => {
    e.preventDefault();
    // Form submission is now handled by FileUpload component
    setShowUploadForm(false);
  };

  const handleUploadComplete = (results: UploadResult[]) => {
    console.log('Upload completed:', results);
    setIsUploading(false);

    const hasErrors = results.some(result => !result.success);
    if (hasErrors) {
      setError('Some files failed to upload. Please try again.');
    } else {
      // TODO: Update documents list with new uploads
      // TODO: Show success notification
      setShowUploadForm(false);
    }
  };

  const handleRetryLoad = () => {
    setError(null);
    setIsLoading(true);
    // TODO: Implement actual data fetching
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  const handleDownload = (documentId: number) => {
    // TODO: Implement document download
    console.log('Downloading document:', documentId);
  };

  const handleView = (documentId: number) => {
    // TODO: Implement document view
    console.log('Viewing document:', documentId);
  };

  const handleDelete = (documentId: number) => {
    // TODO: Implement document delete
    if (confirm('Are you sure you want to delete this document?')) {
      console.log('Deleting document:', documentId);
    }
  };

  return (
    <ErrorBoundary>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Documents</h1>
          <p className="text-gray-600 mt-1">
            Manage your personal and employment documents
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Download All
          </Button>
          <Button variant="primary" onClick={handleUploadDocument}>
            <Plus className="h-4 w-4 mr-2" />
            Upload Document
          </Button>
        </div>
      </div>

      {/* Document Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Documents</p>
                <p className="text-2xl font-bold text-gray-900">{documents.length}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Verified</p>
                <p className="text-2xl font-bold text-gray-900">
                  {documents.filter(d => d.status === 'verified').length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <AlertCircle className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-gray-900">
                  {documents.filter(d => d.status === 'pending').length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Upload className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Recent Uploads</p>
                <p className="text-2xl font-bold text-gray-900">2</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filter */}
      <Card>
        <div className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search documents..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
              />
            </div>
            <div className="flex items-center space-x-2">
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
              >
                {documentCategories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
              <Filter className="h-4 w-4 text-gray-400" />
            </div>
          </div>
        </div>
      </Card>

      {/* Documents List */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Document Library</h3>
          <DataLoader
            loading={isLoading}
            error={error}
            onRetry={handleRetryLoad}
            isEmpty={filteredDocuments.length === 0}
            emptyMessage="No documents found. Upload your first document to get started."
          >
            <div className="space-y-4">
              {filteredDocuments.map((document) => (
              <div key={document.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      <FileText className="h-6 w-6 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="text-sm font-medium text-gray-900">{document.name}</h4>
                        <span className={cn(
                          'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                          getCategoryColor(document.category)
                        )}>
                          {document.category}
                        </span>
                        <div className="flex items-center">
                          {getStatusIcon(document.status)}
                          <span className={cn(
                            'ml-1 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                            getStatusColor(document.status)
                          )}>
                            {document.status}
                          </span>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 mb-1">{document.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-400">
                        <span>{document.file_type} • {document.file_size}</span>
                        <span>Uploaded {formatDate(document.uploaded_date)}</span>
                        <span>by {document.uploaded_by}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleView(document.id)}
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                      title="View document"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDownload(document.id)}
                      className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                      title="Download document"
                    >
                      <Download className="h-4 w-4" />
                    </button>
                    {document.uploaded_by === 'Self' && (
                      <button
                        onClick={() => handleDelete(document.id)}
                        className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                        title="Delete document"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
            </div>
          </DataLoader>
        </div>
      </Card>

      {/* Upload Document Form Modal */}
      {showUploadForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Upload Document</h3>
            <form onSubmit={handleSubmitUpload} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Document Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                  placeholder="Enter document name"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                  required
                >
                  <option value="">Select category</option>
                  {documentCategories.slice(1).map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                  placeholder="Brief description of the document"
                ></textarea>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  File Upload
                </label>
                <FileUpload
                  endpoint={`/employees/${user?.id}/documents/`}
                  folder="employee-documents"
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  maxSize={10 * 1024 * 1024} // 10MB
                  onUpload={handleUploadComplete}
                  multiple={false}
                />
              </div>

              <div className="flex items-center justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => setShowUploadForm(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
      </div>
    </ErrorBoundary>
  );
};

export default StaffDocumentsPage;
