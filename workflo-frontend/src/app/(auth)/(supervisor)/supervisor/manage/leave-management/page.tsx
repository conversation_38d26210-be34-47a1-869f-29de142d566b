'use client';

import React, { useState } from 'react';
import {
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  Users,
  Plus,
  Eye,
  Filter,
  Download,
  AlertCircle,
  CalendarDays,
  Gift
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { formatDate } from '@/lib/utils';

const SupervisorLeaveManagementPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('pending');

  // Mock leave requests data
  const leaveRequests = [
    {
      id: 1,
      employee: {
        name: '<PERSON>',
        id: 'EMP001',
        department: 'Operations',
        avatar: null
      },
      type: 'Annual Leave',
      start_date: '2024-12-25',
      end_date: '2024-12-27',
      days: 3,
      reason: 'Christmas holiday with family',
      status: 'Pending',
      applied_date: '2024-12-15',
      covering_employee: '<PERSON>',
      priority: 'normal'
    },
    {
      id: 2,
      employee: {
        name: '<PERSON>',
        id: 'EMP002',
        department: 'Operations',
        avatar: null
      },
      type: 'Sick Leave',
      start_date: '2024-12-20',
      end_date: '2024-12-20',
      days: 1,
      reason: 'Medical appointment',
      status: 'Pending',
      applied_date: '2024-12-18',
      covering_employee: 'Mike Johnson',
      priority: 'urgent'
    },
    {
      id: 3,
      employee: {
        name: 'Mike Johnson',
        id: 'EMP003',
        department: 'Operations',
        avatar: null
      },
      type: 'Annual Leave',
      start_date: '2024-11-28',
      end_date: '2024-11-29',
      days: 2,
      reason: 'Personal matters',
      status: 'Approved',
      applied_date: '2024-11-20',
      approved_date: '2024-11-21',
      covering_employee: 'John Doe',
      priority: 'normal'
    }
  ];

  const holidays = [
    {
      id: 1,
      name: 'Christmas Day',
      date: '2024-12-25',
      type: 'Public Holiday',
      description: 'National holiday celebrating Christmas'
    },
    {
      id: 2,
      name: 'Boxing Day',
      date: '2024-12-26',
      type: 'Public Holiday',
      description: 'Day after Christmas'
    },
    {
      id: 3,
      name: 'New Year\'s Day',
      date: '2025-01-01',
      type: 'Public Holiday',
      description: 'First day of the new year'
    }
  ];

  const teamBirthdays = [
    {
      employee: 'Sarah Wilson',
      date: '2024-12-22',
      department: 'Operations'
    },
    {
      employee: 'Mike Johnson',
      date: '2024-12-28',
      department: 'Operations'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return <CheckCircle className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-4 border-red-500';
      case 'high':
        return 'border-l-4 border-orange-500';
      default:
        return 'border-l-4 border-blue-500';
    }
  };

  const filteredRequests = leaveRequests.filter(request => {
    switch (activeTab) {
      case 'pending':
        return request.status === 'Pending';
      case 'approved':
        return request.status === 'Approved';
      case 'rejected':
        return request.status === 'Rejected';
      default:
        return true;
    }
  });

  const handleApprove = (requestId: number) => {
    console.log('Approving request:', requestId);
    // TODO: Implement approval logic
  };

  const handleReject = (requestId: number) => {
    console.log('Rejecting request:', requestId);
    // TODO: Implement rejection logic
  };

  const tabs = [
    { id: 'pending', label: 'Pending Requests', count: leaveRequests.filter(r => r.status === 'Pending').length },
    { id: 'approved', label: 'Approved', count: leaveRequests.filter(r => r.status === 'Approved').length },
    { id: 'all', label: 'All Requests', count: leaveRequests.length },
    { id: 'calendar', label: 'Team Calendar', count: 0 }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Leave Management</h1>
          <p className="text-gray-600 mt-1">
            Approve leave requests, manage holidays, and track team availability
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button variant="primary">
            <Plus className="h-4 w-4 mr-2" />
            Add Holiday
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="h-5 w-5 text-yellow-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Pending Requests</p>
                <p className="text-lg font-semibold text-gray-900">
                  {leaveRequests.filter(r => r.status === 'Pending').length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Approved This Month</p>
                <p className="text-lg font-semibold text-gray-900">
                  {leaveRequests.filter(r => r.status === 'Approved').length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <CalendarDays className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Upcoming Holidays</p>
                <p className="text-lg font-semibold text-gray-900">{holidays.length}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Gift className="h-5 w-5 text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Team Birthdays</p>
                <p className="text-lg font-semibold text-gray-900">{teamBirthdays.length}</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                activeTab === tab.id
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
              {tab.count > 0 && (
                <span className="ml-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab !== 'calendar' ? (
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-medium text-gray-900">
                  {activeTab === 'pending' ? 'Pending Leave Requests' : 
                   activeTab === 'approved' ? 'Approved Leave Requests' : 'All Leave Requests'}
                </h3>
                <div className="flex items-center space-x-3">
                  <Button variant="secondary" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                {filteredRequests.map((request) => (
                  <div key={request.id} className={`bg-white border rounded-lg p-4 ${getPriorityColor(request.priority)}`}>
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-sm font-medium">
                            {request.employee.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-medium text-gray-900">{request.employee.name}</h4>
                            <span className="text-sm text-gray-500">({request.employee.id})</span>
                            {request.priority === 'urgent' && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Urgent
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{request.type}</p>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-gray-500">Dates:</span>
                              <span className="ml-1 font-medium">
                                {formatDate(request.start_date, 'MMM dd')} - {formatDate(request.end_date, 'MMM dd, yyyy')}
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-500">Duration:</span>
                              <span className="ml-1 font-medium">{request.days} day(s)</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Covering:</span>
                              <span className="ml-1 font-medium">{request.covering_employee}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Applied:</span>
                              <span className="ml-1 font-medium">{formatDate(request.applied_date, 'MMM dd, yyyy')}</span>
                            </div>
                          </div>
                          <div className="mt-2">
                            <span className="text-gray-500 text-sm">Reason:</span>
                            <p className="text-sm text-gray-900 mt-1">{request.reason}</p>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                          {getStatusIcon(request.status)}
                          <span className="ml-1">{request.status}</span>
                        </span>
                        {request.status === 'Pending' && (
                          <div className="flex items-center space-x-2">
                            <Button 
                              variant="primary" 
                              size="sm"
                              onClick={() => handleApprove(request.id)}
                            >
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Approve
                            </Button>
                            <Button 
                              variant="secondary" 
                              size="sm"
                              onClick={() => handleReject(request.id)}
                            >
                              <XCircle className="h-3 w-3 mr-1" />
                              Reject
                            </Button>
                          </div>
                        )}
                        <Button variant="secondary" size="sm">
                          <Eye className="h-3 w-3 mr-1" />
                          Details
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {filteredRequests.length === 0 && (
                <div className="text-center py-12">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No leave requests found</h3>
                  <p className="text-gray-600">
                    {activeTab === 'pending' 
                      ? "No pending leave requests at the moment."
                      : `No ${activeTab} leave requests found.`
                    }
                  </p>
                </div>
              )}
            </div>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Team Calendar would go here */}
            <div className="lg:col-span-2">
              <Card>
                <div className="p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Team Leave Calendar</h3>
                  <div className="text-center py-12">
                    <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Calendar View</h3>
                    <p className="text-gray-600">Interactive calendar showing team leave schedules</p>
                  </div>
                </div>
              </Card>
            </div>

            {/* Holidays and Birthdays */}
            <div className="space-y-6">
              <Card>
                <div className="p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <CalendarDays className="h-5 w-5 mr-2 text-blue-500" />
                    Upcoming Holidays
                  </h3>
                  <div className="space-y-3">
                    {holidays.map((holiday) => (
                      <div key={holiday.id} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div>
                          <p className="font-medium text-gray-900">{holiday.name}</p>
                          <p className="text-sm text-gray-600">{formatDate(holiday.date, 'MMM dd, yyyy')}</p>
                        </div>
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                          {holiday.type}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </Card>

              <Card>
                <div className="p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <Gift className="h-5 w-5 mr-2 text-purple-500" />
                    Team Birthdays
                  </h3>
                  <div className="space-y-3">
                    {teamBirthdays.map((birthday, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                        <div>
                          <p className="font-medium text-gray-900">{birthday.employee}</p>
                          <p className="text-sm text-gray-600">{formatDate(birthday.date, 'MMM dd, yyyy')}</p>
                        </div>
                        <Gift className="h-4 w-4 text-purple-600" />
                      </div>
                    ))}
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SupervisorLeaveManagementPage;
