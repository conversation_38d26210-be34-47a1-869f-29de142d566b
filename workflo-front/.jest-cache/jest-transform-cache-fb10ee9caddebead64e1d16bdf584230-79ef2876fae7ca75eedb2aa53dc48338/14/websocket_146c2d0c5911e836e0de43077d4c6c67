26460c8a4456da96f3be3d0645bd2ecd
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    default: function() {
        return _default;
    },
    webSocketService: function() {
        return webSocketService;
    }
});
const _api = require("./api");
class WebSocketService {
    getWebSocketUrl() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = process.env.NEXT_PUBLIC_WS_HOST || window.location.host;
        return `${protocol}//${host}/ws/notifications/`;
    }
    isWebSocketEnabled() {
        // Check if WebSocket is enabled via environment variable
        const wsEnabled = process.env.NEXT_PUBLIC_ENABLE_WEBSOCKET !== 'false';
        const isDevelopment = process.env.NODE_ENV === 'development';
        // In development, only connect if explicitly enabled
        if (isDevelopment && process.env.NEXT_PUBLIC_WS_HOST === undefined) {
            return false;
        }
        return wsEnabled;
    }
    connect() {
        if (!this.isWebSocketEnabled()) {
            console.log('WebSocket connection disabled in current environment');
            return;
        }
        if (this.ws?.readyState === WebSocket.OPEN || this.isConnecting) {
            return;
        }
        this.isConnecting = true;
        const token = _api.TokenManager.getAccessToken();
        if (!token) {
            console.warn('No access token available for WebSocket connection');
            this.isConnecting = false;
            return;
        }
        try {
            const wsUrl = `${this.getWebSocketUrl()}?token=${token}`;
            this.ws = new WebSocket(wsUrl);
            this.ws.onopen = this.handleOpen.bind(this);
            this.ws.onmessage = this.handleMessage.bind(this);
            this.ws.onclose = this.handleClose.bind(this);
            this.ws.onerror = this.handleError.bind(this);
        } catch (error) {
            console.warn('WebSocket connection error (this is normal in development):', error);
            this.isConnecting = false;
            // Don't schedule reconnect in development if WebSocket server is not available
            if (this.isWebSocketEnabled() && process.env.NODE_ENV !== 'development') {
                this.scheduleReconnect();
            }
        }
    }
    disconnect() {
        this.clearTimers();
        if (this.ws) {
            this.ws.close(1000, 'Client disconnect');
            this.ws = null;
        }
        this.isConnecting = false;
        this.reconnectAttempts = 0;
    }
    handleOpen() {
        console.log('WebSocket connected');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.reconnectInterval = 1000;
        // Start ping/pong to keep connection alive
        this.startPing();
        // Emit connection event
        this.emit('connected', {
            timestamp: new Date().toISOString()
        });
    }
    handleMessage(event) {
        try {
            const message = JSON.parse(event.data);
            switch(message.type){
                case 'notification':
                    this.handleNotification(message.data);
                    break;
                case 'status':
                    this.emit('status', message.data);
                    break;
                case 'error':
                    this.emit('error', message.data);
                    break;
                case 'pong':
                    break;
                default:
                    console.log('Unknown WebSocket message type:', message.type);
            }
        } catch (error) {
            console.error('Error parsing WebSocket message:', error);
        }
    }
    handleClose(event) {
        console.log('WebSocket disconnected:', event.code, event.reason);
        this.isConnecting = false;
        this.clearTimers();
        // Emit disconnection event
        this.emit('disconnected', {
            code: event.code,
            reason: event.reason,
            timestamp: new Date().toISOString()
        });
        // Attempt to reconnect unless it was a clean close
        if (event.code !== 1000) {
            this.scheduleReconnect();
        }
    }
    handleError(error) {
        // Only log errors in production or when WebSocket is explicitly enabled
        if (process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_WS_HOST) {
            console.error('WebSocket error:', error);
        } else {
            console.log('WebSocket connection failed (this is normal in development without WebSocket server)');
        }
        this.isConnecting = false;
        this.emit('error', {
            error,
            timestamp: new Date().toISOString()
        });
    }
    handleNotification(notification) {
        // Add to notifications list
        this.notifications.unshift(notification);
        // Keep only last 100 notifications
        if (this.notifications.length > 100) {
            this.notifications = this.notifications.slice(0, 100);
        }
        // Emit notification event
        this.emit('notification', notification);
        // Show browser notification if permission granted
        this.showBrowserNotification(notification);
    }
    showBrowserNotification(notification) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const browserNotification = new Notification(notification.title, {
                body: notification.message,
                icon: '/favicon.ico',
                tag: notification.id,
                requireInteraction: notification.priority === 'high'
            });
            browserNotification.onclick = ()=>{
                window.focus();
                this.emit('notificationClick', notification);
                browserNotification.close();
            };
            // Auto close after 5 seconds for non-high priority
            if (notification.priority !== 'high') {
                setTimeout(()=>{
                    browserNotification.close();
                }, 5000);
            }
        }
    }
    scheduleReconnect() {
        // Don't attempt reconnection in development without WebSocket server
        if (process.env.NODE_ENV === 'development' && !process.env.NEXT_PUBLIC_WS_HOST) {
            return;
        }
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            if (process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_WS_HOST) {
                console.error('Max reconnection attempts reached');
            }
            this.emit('maxReconnectAttemptsReached', {
                attempts: this.reconnectAttempts,
                timestamp: new Date().toISOString()
            });
            return;
        }
        this.reconnectTimer = setTimeout(()=>{
            this.reconnectAttempts++;
            if (process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_WS_HOST) {
                console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            }
            this.connect();
            // Exponential backoff
            this.reconnectInterval = Math.min(this.reconnectInterval * 2, this.maxReconnectInterval);
        }, this.reconnectInterval);
    }
    startPing() {
        this.pingTimer = setInterval(()=>{
            if (this.ws?.readyState === WebSocket.OPEN) {
                this.send({
                    type: 'ping',
                    timestamp: new Date().toISOString()
                });
            }
        }, 30000); // Ping every 30 seconds
    }
    clearTimers() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        if (this.pingTimer) {
            clearInterval(this.pingTimer);
            this.pingTimer = null;
        }
    }
    send(message) {
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }
    // Event system
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }
    off(event, handler) {
        const handlers = this.eventHandlers.get(event);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }
    emit(event, data) {
        const handlers = this.eventHandlers.get(event);
        if (handlers) {
            handlers.forEach((handler)=>{
                try {
                    handler(data);
                } catch (error) {
                    console.error('Error in WebSocket event handler:', error);
                }
            });
        }
    }
    // Public methods
    getNotifications() {
        return [
            ...this.notifications
        ];
    }
    getUnreadNotifications() {
        return this.notifications.filter((n)=>!n.read);
    }
    markNotificationAsRead(notificationId) {
        const notification = this.notifications.find((n)=>n.id === notificationId);
        if (notification) {
            notification.read = true;
            this.emit('notificationRead', notification);
        }
    }
    markAllNotificationsAsRead() {
        this.notifications.forEach((n)=>n.read = true);
        this.emit('allNotificationsRead', {});
    }
    clearNotifications() {
        this.notifications = [];
        this.emit('notificationsCleared', {});
    }
    isConnected() {
        return this.ws?.readyState === WebSocket.OPEN;
    }
    getConnectionState() {
        if (!this.ws) return 'disconnected';
        switch(this.ws.readyState){
            case WebSocket.CONNECTING:
                return 'connecting';
            case WebSocket.OPEN:
                return 'connected';
            case WebSocket.CLOSING:
                return 'closing';
            case WebSocket.CLOSED:
                return 'disconnected';
            default:
                return 'unknown';
        }
    }
    // Request browser notification permission
    static async requestNotificationPermission() {
        if ('Notification' in window) {
            return await Notification.requestPermission();
        }
        return 'denied';
    }
    constructor(){
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 1000 // Start with 1 second
        ;
        this.maxReconnectInterval = 30000 // Max 30 seconds
        ;
        this.reconnectTimer = null;
        this.pingTimer = null;
        this.isConnecting = false;
        this.eventHandlers = new Map();
        this.notifications = [];
    }
}
const webSocketService = new WebSocketService();
const _default = webSocketService;

//# sourceMappingURL=data:application/json;base64,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