# Generated by Django 2.1 on 2019-07-23 01:26

from django.db import migrations, models
from django.db import connection


def drop_index_if_exists(apps, schema_editor):
    if connection.vendor == 'postgresql':
        schema_editor.execute('DROP INDEX IF EXISTS "easyaudit_requestevent_url_37d1b8c4"')


class Migration(migrations.Migration):

    dependencies = [
        ('easyaudit', '0012_auto_20181018_0012'),
    ]

    operations = [
        migrations.RunPython(drop_index_if_exists, migrations.RunPython.noop),
        migrations.AlterField(
            model_name='requestevent',
            name='url',
            field=models.CharField(db_index=True, max_length=254),
        ),
    ]
