'use client';

import React from 'react';
import {
  Briefcase,
  Calendar,
  MapPin,
  Users,
  Award,
  TrendingUp,
  Clock,
  Building,
  Shield,
  Target,
  Download,
  Eye
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { formatDate } from '@/lib/utils';

const SupervisorEmploymentPage: React.FC = () => {
  const { user } = useAuth();

  // Mock employment data
  const employmentData = {
    employee_id: user?.employee_id || 'SUP001',
    job_title: 'Department Supervisor',
    department: user?.department || 'Operations',
    hire_date: '2021-03-15',
    employment_type: 'Full-time',
    work_location: 'Nairobi Office',
    reporting_manager: '<PERSON>',
    direct_reports: 12,
    salary_grade: 'Grade 8',
    employment_status: 'Active',
    probation_end_date: '2021-09-15',
    contract_end_date: null,
    work_schedule: 'Monday - Friday, 8:00 AM - 5:00 PM'
  };

  const employmentHistory = [
    {
      id: 1,
      position: 'Department Supervisor',
      department: 'Operations',
      start_date: '2021-03-15',
      end_date: null,
      status: 'Current',
      achievements: [
        'Increased team productivity by 25%',
        'Implemented new workflow processes',
        'Reduced employee turnover by 15%'
      ]
    },
    {
      id: 2,
      position: 'Team Lead',
      department: 'Operations',
      start_date: '2019-06-01',
      end_date: '2021-03-14',
      status: 'Promoted',
      achievements: [
        'Led team of 8 employees',
        'Achieved 98% customer satisfaction',
        'Completed leadership training program'
      ]
    },
    {
      id: 3,
      position: 'Senior Associate',
      department: 'Operations',
      start_date: '2018-01-10',
      end_date: '2019-05-31',
      status: 'Promoted',
      achievements: [
        'Exceeded performance targets by 20%',
        'Mentored 3 junior associates',
        'Completed professional certification'
      ]
    }
  ];

  const supervisoryResponsibilities = [
    'Team leadership and development',
    'Performance management and reviews',
    'Budget planning and resource allocation',
    'Process improvement and optimization',
    'Cross-departmental collaboration',
    'Training and mentoring staff',
    'Quality assurance and compliance',
    'Strategic planning and execution'
  ];

  const certifications = [
    {
      name: 'Leadership Excellence Certificate',
      issuer: 'Kenya Management Institute',
      date: '2022-08-15',
      status: 'Active'
    },
    {
      name: 'Project Management Professional (PMP)',
      issuer: 'PMI',
      date: '2021-11-20',
      status: 'Active'
    },
    {
      name: 'Supervisory Skills Development',
      issuer: 'Corporate Training Center',
      date: '2020-05-10',
      status: 'Active'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Employment Information</h1>
          <p className="text-gray-600 mt-1">
            View your job details, employment history, and supervisory responsibilities
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Download className="h-4 w-4 mr-2" />
            Download Certificate
          </Button>
          <Button variant="primary">
            <Eye className="h-4 w-4 mr-2" />
            View Full History
          </Button>
        </div>
      </div>

      {/* Current Employment Overview */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Briefcase className="h-5 w-5 mr-2 text-blue-500" />
            Current Position
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Job Title</label>
              <p className="text-gray-900 font-medium">{employmentData.job_title}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
              <div className="flex items-center">
                <Building className="h-4 w-4 text-gray-400 mr-2" />
                <p className="text-gray-900">
                  {typeof employmentData.department === 'string'
                    ? employmentData.department
                    : employmentData.department?.name || 'N/A'}
                </p>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Employee ID</label>
              <p className="text-gray-900 font-mono">{employmentData.employee_id}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Hire Date</label>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                <p className="text-gray-900">{formatDate(employmentData.hire_date, 'MMM dd, yyyy')}</p>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Employment Type</label>
              <p className="text-gray-900">{employmentData.employment_type}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Work Location</label>
              <div className="flex items-center">
                <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                <p className="text-gray-900">{employmentData.work_location}</p>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Reporting Manager</label>
              <p className="text-gray-900">{employmentData.reporting_manager}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Direct Reports</label>
              <div className="flex items-center">
                <Users className="h-4 w-4 text-gray-400 mr-2" />
                <p className="text-gray-900">{employmentData.direct_reports} employees</p>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Employment Status</label>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                {employmentData.employment_status}
              </span>
            </div>
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Supervisory Responsibilities */}
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Shield className="h-5 w-5 mr-2 text-purple-500" />
              Supervisory Responsibilities
            </h3>
            <div className="space-y-3">
              {supervisoryResponsibilities.map((responsibility, index) => (
                <div key={index} className="flex items-center">
                  <Target className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-700">{responsibility}</span>
                </div>
              ))}
            </div>
          </div>
        </Card>

        {/* Certifications */}
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Award className="h-5 w-5 mr-2 text-yellow-500" />
              Certifications & Training
            </h3>
            <div className="space-y-4">
              {certifications.map((cert, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900">{cert.name}</h4>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {cert.status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">{cert.issuer}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    Issued: {formatDate(cert.date, 'MMM dd, yyyy')}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>

      {/* Employment History */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Clock className="h-5 w-5 mr-2 text-blue-500" />
            Employment History
          </h3>
          <div className="space-y-6">
            {employmentHistory.map((position, index) => (
              <div key={position.id} className="relative">
                {index !== employmentHistory.length - 1 && (
                  <div className="absolute left-4 top-8 bottom-0 w-0.5 bg-gray-200"></div>
                )}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                      <Briefcase className="h-4 w-4 text-white" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-lg font-medium text-gray-900">{position.position}</h4>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        position.status === 'Current' ? 'bg-green-100 text-green-800' :
                        position.status === 'Promoted' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {position.status}
                      </span>
                    </div>
                    <p className="text-gray-600 mb-2">{position.department}</p>
                    <p className="text-sm text-gray-500 mb-3">
                      {formatDate(position.start_date, 'MMM yyyy')} - {
                        position.end_date ? formatDate(position.end_date, 'MMM yyyy') : 'Present'
                      }
                    </p>
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Key Achievements:</h5>
                      <ul className="space-y-1">
                        {position.achievements.map((achievement, achIndex) => (
                          <li key={achIndex} className="flex items-center text-sm text-gray-600">
                            <TrendingUp className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                            {achievement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* Work Schedule */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Clock className="h-5 w-5 mr-2 text-green-500" />
            Work Schedule & Details
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Work Schedule</label>
              <p className="text-gray-900">{employmentData.work_schedule}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Salary Grade</label>
              <p className="text-gray-900">{employmentData.salary_grade}</p>
            </div>
            {employmentData.probation_end_date && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Probation End Date</label>
                <p className="text-gray-900">{formatDate(employmentData.probation_end_date, 'MMM dd, yyyy')}</p>
              </div>
            )}
            {employmentData.contract_end_date && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Contract End Date</label>
                <p className="text-gray-900">{formatDate(employmentData.contract_end_date, 'MMM dd, yyyy')}</p>
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SupervisorEmploymentPage;
