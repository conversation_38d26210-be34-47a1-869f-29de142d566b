'use client';

import React, { useState, useEffect } from 'react';
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Server,
  Wifi,
  WifiOff,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Monitor,
  AlertCircle
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { biostarMonitoring, BiostarMetrics } from '@/lib/biostarMonitoring';
import { performHealthCheck } from '@/lib/biostarConfig';
import { cn } from '@/lib/utils';

interface HealthCheckResult {
  config: { isValid: boolean; errors: string[] };
  connectivity: { success: boolean; responseTime: number; error?: string };
  timestamp: string;
}

const BiostarMonitoringDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<BiostarMetrics | null>(null);
  const [healthCheck, setHealthCheck] = useState<HealthCheckResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    // Initial load
    loadData();

    // Subscribe to metrics updates
    const handleMetricsUpdate = (newMetrics: BiostarMetrics) => {
      setMetrics(newMetrics);
    };

    biostarMonitoring.addListener(handleMetricsUpdate);

    // Start monitoring if not already started
    biostarMonitoring.startMonitoring();

    return () => {
      biostarMonitoring.removeListener(handleMetricsUpdate);
    };
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Get current metrics
      const currentMetrics = biostarMonitoring.getMetrics();
      setMetrics(currentMetrics);

      // Perform health check
      const health = await performHealthCheck();
      setHealthCheck(health);

    } catch (error) {
      console.error('Failed to load monitoring data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'degraded': return 'text-yellow-600 bg-yellow-100';
      case 'unhealthy': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'degraded': return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'unhealthy': return <AlertCircle className="h-5 w-5 text-red-600" />;
      default: return <Monitor className="h-5 w-5 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
        <span className="ml-2 text-gray-600">Loading monitoring data...</span>
      </div>
    );
  }

  const healthStatus = biostarMonitoring.getHealthStatus();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">BioStar Monitoring</h2>
          <p className="text-gray-600 mt-1">
            Real-time monitoring of BioStar 2 API connectivity and performance
          </p>
        </div>
        <Button 
          variant="secondary" 
          onClick={handleRefresh}
          disabled={refreshing}
        >
          <RefreshCw className={cn("h-4 w-4 mr-2", refreshing && "animate-spin")} />
          {refreshing ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      {/* Overall Health Status */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getStatusIcon(healthStatus.status)}
              <div>
                <h3 className="text-lg font-medium text-gray-900">System Health</h3>
                <p className="text-sm text-gray-600">
                  Last updated: {healthStatus.lastCheck ? 
                    new Date(healthStatus.lastCheck).toLocaleString() : 
                    'Never'
                  }
                </p>
              </div>
            </div>
            <div className="text-right">
              <span className={cn(
                'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium',
                getStatusColor(healthStatus.status)
              )}>
                {healthStatus.status.toUpperCase()}
              </span>
              <div className="text-sm text-gray-600 mt-1">
                {healthStatus.uptime.toFixed(2)}% uptime
              </div>
            </div>
          </div>

          {healthStatus.issues.length > 0 && (
            <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
              <h4 className="text-sm font-medium text-yellow-800 mb-2">Issues Detected:</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                {healthStatus.issues.map((issue, index) => (
                  <li key={index}>• {issue}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </Card>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Connection Status */}
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                {healthCheck?.connectivity.success ? (
                  <Wifi className="h-6 w-6 text-blue-600" />
                ) : (
                  <WifiOff className="h-6 w-6 text-red-600" />
                )}
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Connection</p>
                <p className="text-2xl font-bold text-gray-900">
                  {healthCheck?.connectivity.success ? 'Online' : 'Offline'}
                </p>
              </div>
            </div>
          </div>
        </Card>

        {/* Response Time */}
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Clock className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Response Time</p>
                <p className="text-2xl font-bold text-gray-900">
                  {metrics?.averageResponseTime ? 
                    `${Math.round(metrics.averageResponseTime)}ms` : 
                    'N/A'
                  }
                </p>
              </div>
            </div>
          </div>
        </Card>

        {/* Success Rate */}
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {metrics?.uptime ? `${metrics.uptime.toFixed(1)}%` : 'N/A'}
                </p>
              </div>
            </div>
          </div>
        </Card>

        {/* Device Status */}
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Server className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Devices Online</p>
                <p className="text-2xl font-bold text-gray-900">
                  {metrics?.deviceStatus ? 
                    `${metrics.deviceStatus.onlineDevices}/${metrics.deviceStatus.totalDevices}` : 
                    'N/A'
                  }
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Request Statistics */}
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Request Statistics</h3>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Requests</span>
                <span className="font-medium">{metrics?.totalRequests || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Successful</span>
                <span className="font-medium text-green-600">
                  {metrics?.successfulRequests || 0}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Failed</span>
                <span className="font-medium text-red-600">
                  {metrics?.failedRequests || 0}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Last Success</span>
                <span className="font-medium text-xs">
                  {metrics?.lastSuccessfulConnection ? 
                    new Date(metrics.lastSuccessfulConnection).toLocaleString() : 
                    'Never'
                  }
                </span>
              </div>
            </div>
          </div>
        </Card>

        {/* Recent Downtime */}
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Downtime Events</h3>
            <div className="space-y-3">
              {metrics?.downtimeEvents && metrics.downtimeEvents.length > 0 ? (
                metrics.downtimeEvents.slice(0, 5).map((event, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-red-50 rounded">
                    <div>
                      <p className="text-sm font-medium text-red-800">{event.reason}</p>
                      <p className="text-xs text-red-600">
                        {new Date(event.startTime).toLocaleString()}
                      </p>
                    </div>
                    <div className="text-xs text-red-600">
                      {event.duration ? 
                        `${Math.round(event.duration / 1000)}s` : 
                        'Ongoing'
                      }
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500 italic">No recent downtime events</p>
              )}
            </div>
          </div>
        </Card>
      </div>

      {/* Configuration Status */}
      {healthCheck && (
        <Card>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Configuration Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Configuration Validation</h4>
                <div className="flex items-center space-x-2">
                  {healthCheck.config.isValid ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-600" />
                  )}
                  <span className={cn(
                    'text-sm font-medium',
                    healthCheck.config.isValid ? 'text-green-600' : 'text-red-600'
                  )}>
                    {healthCheck.config.isValid ? 'Valid' : 'Invalid'}
                  </span>
                </div>
                {healthCheck.config.errors.length > 0 && (
                  <ul className="mt-2 text-sm text-red-600 space-y-1">
                    {healthCheck.config.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                )}
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Network Connectivity</h4>
                <div className="flex items-center space-x-2">
                  {healthCheck.connectivity.success ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-600" />
                  )}
                  <span className={cn(
                    'text-sm font-medium',
                    healthCheck.connectivity.success ? 'text-green-600' : 'text-red-600'
                  )}>
                    {healthCheck.connectivity.success ? 'Connected' : 'Failed'}
                  </span>
                  <span className="text-xs text-gray-500">
                    ({healthCheck.connectivity.responseTime}ms)
                  </span>
                </div>
                {healthCheck.connectivity.error && (
                  <p className="mt-1 text-sm text-red-600">
                    {healthCheck.connectivity.error}
                  </p>
                )}
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default BiostarMonitoringDashboard;
