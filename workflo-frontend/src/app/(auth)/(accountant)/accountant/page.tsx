'use client';

import React, { useState, useEffect } from 'react';
import {
  DollarSign,
  TrendingUp,
  Users,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock,
  Calculator,
  CreditCard,
  BarChart3,
  PieChart,
  Calendar,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useToast } from '@/hooks/useToast';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

interface DashboardStats {
  totalPayroll: number;
  pendingPayslips: number;
  employeeCount: number;
  monthlyExpenses: number;
  payrollGrowth: number;
  expenseGrowth: number;
}

interface RecentActivity {
  id: string;
  type: 'payroll' | 'expense' | 'report' | 'approval';
  description: string;
  amount?: number;
  timestamp: string;
  status: 'completed' | 'pending' | 'failed';
}

const AccountantDashboard: React.FC = () => {
  const { success, error } = useToast();
  const [stats, setStats] = useState<DashboardStats>({
    totalPayroll: 0,
    pendingPayslips: 0,
    employeeCount: 0,
    monthlyExpenses: 0,
    payrollGrowth: 0,
    expenseGrowth: 0,
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate API call to fetch dashboard data
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);

        // Mock data - replace with actual API calls
        await new Promise(resolve => setTimeout(resolve, 1000));

        setStats({
          totalPayroll: 2450000, // KSH 2,450,000
          pendingPayslips: 12,
          employeeCount: 156,
          monthlyExpenses: 890000, // KSH 890,000
          payrollGrowth: 5.2,
          expenseGrowth: -2.1,
        });

        setRecentActivity([
          {
            id: '1',
            type: 'payroll',
            description: 'Monthly payroll processed for November 2024',
            amount: 2450000,
            timestamp: '2024-11-01T10:30:00Z',
            status: 'completed'
          },
          {
            id: '2',
            type: 'expense',
            description: 'Office supplies expense approved',
            amount: 45000,
            timestamp: '2024-11-01T09:15:00Z',
            status: 'completed'
          },
          {
            id: '3',
            type: 'report',
            description: 'Monthly financial report generated',
            timestamp: '2024-11-01T08:45:00Z',
            status: 'completed'
          },
          {
            id: '4',
            type: 'approval',
            description: 'Overtime payment pending approval',
            amount: 125000,
            timestamp: '2024-11-01T08:00:00Z',
            status: 'pending'
          }
        ]);

        success('Dashboard data loaded successfully');
      } catch (err) {
        error('Failed to load dashboard data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [success, error]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'payroll':
        return DollarSign;
      case 'expense':
        return CreditCard;
      case 'report':
        return FileText;
      case 'approval':
        return Clock;
      default:
        return FileText;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'failed':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-96 bg-gray-200 rounded-lg"></div>
            <div className="h-96 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Accountant Dashboard</h1>
          <p className="text-gray-600">Overview of financial operations and payroll management</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="secondary" size="sm">
            <FileText className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
          <Button variant="primary" size="sm">
            <DollarSign className="h-4 w-4 mr-2" />
            Process Payroll
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Payroll</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalPayroll)}</p>
              <div className="flex items-center mt-2">
                {stats.payrollGrowth > 0 ? (
                  <ArrowUpRight className="h-4 w-4 text-green-500" />
                ) : (
                  <ArrowDownRight className="h-4 w-4 text-red-500" />
                )}
                <span className={cn(
                  'text-sm font-medium ml-1',
                  stats.payrollGrowth > 0 ? 'text-green-600' : 'text-red-600'
                )}>
                  {Math.abs(stats.payrollGrowth)}%
                </span>
                <span className="text-sm text-gray-500 ml-1">vs last month</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Payslips</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pendingPayslips}</p>
              <p className="text-sm text-gray-500 mt-2">Awaiting generation</p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <FileText className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Employees</p>
              <p className="text-2xl font-bold text-gray-900">{stats.employeeCount}</p>
              <p className="text-sm text-gray-500 mt-2">Active employees</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Monthly Expenses</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.monthlyExpenses)}</p>
              <div className="flex items-center mt-2">
                {stats.expenseGrowth > 0 ? (
                  <ArrowUpRight className="h-4 w-4 text-red-500" />
                ) : (
                  <ArrowDownRight className="h-4 w-4 text-green-500" />
                )}
                <span className={cn(
                  'text-sm font-medium ml-1',
                  stats.expenseGrowth > 0 ? 'text-red-600' : 'text-green-600'
                )}>
                  {Math.abs(stats.expenseGrowth)}%
                </span>
                <span className="text-sm text-gray-500 ml-1">vs last month</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
            <Button variant="ghost" size="sm">View All</Button>
          </div>

          <div className="space-y-4">
            {recentActivity.map((activity) => {
              const Icon = getActivityIcon(activity.type);
              return (
                <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50">
                  <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Icon className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                    {activity.amount && (
                      <p className="text-sm text-gray-600">{formatCurrency(activity.amount)}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">
                      {new Date(activity.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <span className={cn(
                    'px-2 py-1 text-xs font-medium rounded-full',
                    getStatusColor(activity.status)
                  )}>
                    {activity.status}
                  </span>
                </div>
              );
            })}
          </div>
        </Card>

        {/* Quick Actions */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>

          <div className="grid grid-cols-2 gap-4">
            <Button variant="outline" className="h-20 flex-col space-y-2">
              <DollarSign className="h-6 w-6" />
              <span className="text-sm">Process Payroll</span>
            </Button>

            <Button variant="outline" className="h-20 flex-col space-y-2">
              <FileText className="h-6 w-6" />
              <span className="text-sm">Generate Payslips</span>
            </Button>

            <Button variant="outline" className="h-20 flex-col space-y-2">
              <BarChart3 className="h-6 w-6" />
              <span className="text-sm">Financial Reports</span>
            </Button>

            <Button variant="outline" className="h-20 flex-col space-y-2">
              <Calculator className="h-6 w-6" />
              <span className="text-sm">Tax Calculations</span>
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AccountantDashboard;
