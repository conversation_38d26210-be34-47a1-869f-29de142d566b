'use client';

import React, { useState, useEffect } from 'react';
import {
  Clock,
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  MoreHorizontal,
  Calendar,
  DollarSign,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle,
  Timer,
  Zap,
  BarChart3,
  X,
  Save
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { cn, formatDate, formatCurrency } from '@/lib/utils';

interface OvertimeRecord {
  id: number;
  employee_id: number;
  date: string;
  start_time: string;
  end_time: string;
  regular_hours: number;
  overtime_hours: number;
  overtime_type: 'daily' | 'weekly' | 'holiday' | 'weekend' | 'emergency' | 'project_deadline';
  overtime_rate: number;
  base_hourly_rate: number;
  overtime_pay: number;
  reason: string;
  project_code?: string;
  department: string;
  supervisor: string;
  status: 'pending' | 'approved' | 'rejected' | 'paid';
  approved_by?: string;
  approved_date?: string;
  rejection_reason?: string;
  is_preapproved: boolean;
  break_time_minutes: number;
  total_hours: number;
  created_date: string;
  updated_date: string;
}

interface OvertimePolicy {
  id: number;
  policy_name: string;
  overtime_threshold_daily: number;
  overtime_threshold_weekly: number;
  overtime_rate_multiplier: number;
  holiday_rate_multiplier: number;
  weekend_rate_multiplier: number;
  max_overtime_daily: number;
  max_overtime_weekly: number;
  requires_approval: boolean;
  auto_approve_threshold: number;
}

interface OvertimeSectionProps {
  employeeId: number;
  employeeName: string;
  isCurrentUser?: boolean;
}

const OvertimeSection: React.FC<OvertimeSectionProps> = ({
  employeeId,
  employeeName,
  isCurrentUser = false
}) => {
  const [overtimeRecords, setOvertimeRecords] = useState<OvertimeRecord[]>([]);
  const [overtimePolicy, setOvertimePolicy] = useState<OvertimePolicy | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [selectedMonth, setSelectedMonth] = useState(new Date().toISOString().slice(0, 7));
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<OvertimeRecord | null>(null);

  // Mock overtime records data
  const mockOvertimeRecords: OvertimeRecord[] = [
    {
      id: 1,
      employee_id: employeeId,
      date: '2024-01-15',
      start_time: '09:00:00',
      end_time: '20:00:00',
      regular_hours: 8,
      overtime_hours: 3,
      overtime_type: 'project_deadline',
      overtime_rate: 37.5,
      base_hourly_rate: 25,
      overtime_pay: 112.5,
      reason: 'Critical project deadline - Customer Portal launch',
      project_code: 'PROJ-2024-001',
      department: 'Engineering',
      supervisor: 'John Smith',
      status: 'approved',
      approved_by: 'John Smith',
      approved_date: '2024-01-16T00:00:00Z',
      is_preapproved: false,
      break_time_minutes: 60,
      total_hours: 11,
      created_date: '2024-01-15T20:30:00Z',
      updated_date: '2024-01-16T09:00:00Z'
    },
    {
      id: 2,
      employee_id: employeeId,
      date: '2024-01-20',
      start_time: '09:00:00',
      end_time: '19:30:00',
      regular_hours: 8,
      overtime_hours: 2.5,
      overtime_type: 'daily',
      overtime_rate: 37.5,
      base_hourly_rate: 25,
      overtime_pay: 93.75,
      reason: 'Bug fixes for production issues',
      department: 'Engineering',
      supervisor: 'John Smith',
      status: 'paid',
      approved_by: 'John Smith',
      approved_date: '2024-01-21T00:00:00Z',
      is_preapproved: true,
      break_time_minutes: 30,
      total_hours: 10.5,
      created_date: '2024-01-20T19:45:00Z',
      updated_date: '2024-01-25T00:00:00Z'
    },
    {
      id: 3,
      employee_id: employeeId,
      date: '2024-01-27',
      start_time: '10:00:00',
      end_time: '16:00:00',
      regular_hours: 0,
      overtime_hours: 6,
      overtime_type: 'weekend',
      overtime_rate: 50,
      base_hourly_rate: 25,
      overtime_pay: 300,
      reason: 'Weekend maintenance and system updates',
      department: 'Engineering',
      supervisor: 'John Smith',
      status: 'approved',
      approved_by: 'John Smith',
      approved_date: '2024-01-28T00:00:00Z',
      is_preapproved: true,
      break_time_minutes: 0,
      total_hours: 6,
      created_date: '2024-01-27T16:15:00Z',
      updated_date: '2024-01-28T09:00:00Z'
    },
    {
      id: 4,
      employee_id: employeeId,
      date: '2024-01-30',
      start_time: '09:00:00',
      end_time: '18:30:00',
      regular_hours: 8,
      overtime_hours: 1.5,
      overtime_type: 'daily',
      overtime_rate: 37.5,
      base_hourly_rate: 25,
      overtime_pay: 56.25,
      reason: 'Client presentation preparation',
      department: 'Engineering',
      supervisor: 'John Smith',
      status: 'pending',
      is_preapproved: false,
      break_time_minutes: 30,
      total_hours: 9.5,
      created_date: '2024-01-30T18:45:00Z',
      updated_date: '2024-01-30T18:45:00Z'
    }
  ];

  // Mock overtime policy
  const mockOvertimePolicy: OvertimePolicy = {
    id: 1,
    policy_name: 'Standard Overtime Policy',
    overtime_threshold_daily: 8,
    overtime_threshold_weekly: 40,
    overtime_rate_multiplier: 1.5,
    holiday_rate_multiplier: 2.0,
    weekend_rate_multiplier: 2.0,
    max_overtime_daily: 4,
    max_overtime_weekly: 20,
    requires_approval: true,
    auto_approve_threshold: 2
  };

  useEffect(() => {
    const loadOvertimeData = async () => {
      setLoading(true);
      setTimeout(() => {
        setOvertimeRecords(mockOvertimeRecords);
        setOvertimePolicy(mockOvertimePolicy);
        setLoading(false);
      }, 1000);
    };

    loadOvertimeData();
  }, [employeeId]);

  const filteredRecords = overtimeRecords.filter(record => {
    const matchesSearch = record.reason.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.project_code?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.supervisor.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = !filterType || record.overtime_type === filterType;
    const matchesStatus = !filterStatus || record.status === filterStatus;
    const matchesMonth = record.date.startsWith(selectedMonth);
    return matchesSearch && matchesType && matchesStatus && matchesMonth;
  });

  const getOvertimeIcon = (type: string) => {
    switch (type) {
      case 'daily': return <Clock className="h-5 w-5 text-blue-600" />;
      case 'weekly': return <Calendar className="h-5 w-5 text-green-600" />;
      case 'holiday': return <Calendar className="h-5 w-5 text-red-600" />;
      case 'weekend': return <Calendar className="h-5 w-5 text-purple-600" />;
      case 'emergency': return <AlertCircle className="h-5 w-5 text-orange-600" />;
      case 'project_deadline': return <Zap className="h-5 w-5 text-yellow-600" />;
      default: return <Timer className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-blue-100 text-blue-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'paid': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <AlertCircle className="h-4 w-4" />;
      case 'approved': return <CheckCircle className="h-4 w-4" />;
      case 'rejected': return <XCircle className="h-4 w-4" />;
      case 'paid': return <CheckCircle className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  const calculateTotalOvertimeHours = () => {
    return filteredRecords.reduce((total, record) => total + record.overtime_hours, 0);
  };

  const calculateTotalOvertimePay = () => {
    return filteredRecords
      .filter(r => r.status === 'approved' || r.status === 'paid')
      .reduce((total, record) => total + record.overtime_pay, 0);
  };

  const calculatePendingPay = () => {
    return filteredRecords
      .filter(r => r.status === 'pending' || r.status === 'approved')
      .reduce((total, record) => total + record.overtime_pay, 0);
  };

  const getAverageOvertimeRate = () => {
    if (filteredRecords.length === 0) return 0;
    const totalRate = filteredRecords.reduce((sum, record) => sum + record.overtime_rate, 0);
    return totalRate / filteredRecords.length;
  };

  const handleCreateRecord = () => {
    setSelectedRecord(null);
    setShowCreateModal(true);
  };

  const handleEditRecord = (record: OvertimeRecord) => {
    setSelectedRecord(record);
    setShowCreateModal(true);
  };

  const handleDeleteRecord = (record: OvertimeRecord) => {
    if (confirm(`Are you sure you want to delete the overtime record for ${formatDate(record.date)}?`)) {
      setOvertimeRecords(prev => prev.filter(r => r.id !== record.id));
    }
  };

  const handleApproveRecord = (record: OvertimeRecord) => {
    setOvertimeRecords(prev => prev.map(r =>
      r.id === record.id
        ? { ...r, status: 'approved', approved_by: 'Current User', approved_date: new Date().toISOString() }
        : r
    ));
  };

  const handleRejectRecord = (record: OvertimeRecord) => {
    const reason = prompt('Please provide a reason for rejection:');
    if (reason) {
      setOvertimeRecords(prev => prev.map(r =>
        r.id === record.id
          ? { ...r, status: 'rejected', rejection_reason: reason }
          : r
      ));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Overtime Management</h3>
          <p className="text-sm text-gray-600 mt-1">
            Track and manage overtime hours for {employeeName}
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <div className="text-right">
            <div className="text-sm text-gray-600">Total Hours ({selectedMonth})</div>
            <div className="text-lg font-bold text-blue-600">
              {calculateTotalOvertimeHours().toFixed(1)}h
            </div>
          </div>
          {!isCurrentUser && (
            <Button variant="primary" onClick={handleCreateRecord}>
              <Plus className="h-4 w-4 mr-2" />
              Add Overtime
            </Button>
          )}
        </div>
      </div>

      {/* Overtime Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Hours</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {calculateTotalOvertimeHours().toFixed(1)}h
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Pay</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(calculateTotalOvertimePay())}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Pending Pay</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(calculatePendingPay())}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BarChart3 className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Avg Rate</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(getAverageOvertimeRate())}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Overtime Policy Info */}
      {overtimePolicy && (
        <Card>
          <div className="p-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">Overtime Policy</h4>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-sm">
              <div>
                <h5 className="font-medium text-gray-900 mb-2">Thresholds</h5>
                <div className="space-y-1 text-gray-600">
                  <div>Daily: {overtimePolicy.overtime_threshold_daily}h</div>
                  <div>Weekly: {overtimePolicy.overtime_threshold_weekly}h</div>
                </div>
              </div>
              <div>
                <h5 className="font-medium text-gray-900 mb-2">Rate Multipliers</h5>
                <div className="space-y-1 text-gray-600">
                  <div>Standard: {overtimePolicy.overtime_rate_multiplier}x</div>
                  <div>Weekend: {overtimePolicy.weekend_rate_multiplier}x</div>
                  <div>Holiday: {overtimePolicy.holiday_rate_multiplier}x</div>
                </div>
              </div>
              <div>
                <h5 className="font-medium text-gray-900 mb-2">Limits</h5>
                <div className="space-y-1 text-gray-600">
                  <div>Max Daily: {overtimePolicy.max_overtime_daily}h</div>
                  <div>Max Weekly: {overtimePolicy.max_overtime_weekly}h</div>
                </div>
              </div>
              <div>
                <h5 className="font-medium text-gray-900 mb-2">Approval</h5>
                <div className="space-y-1 text-gray-600">
                  <div>Required: {overtimePolicy.requires_approval ? 'Yes' : 'No'}</div>
                  <div>Auto-approve: ≤{overtimePolicy.auto_approve_threshold}h</div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <div className="flex-1">
          <Input
            placeholder="Search overtime records..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="h-4 w-4" />}
          />
        </div>
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="">All Types</option>
          <option value="daily">Daily</option>
          <option value="weekly">Weekly</option>
          <option value="weekend">Weekend</option>
          <option value="holiday">Holiday</option>
          <option value="emergency">Emergency</option>
          <option value="project_deadline">Project Deadline</option>
        </select>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="">All Status</option>
          <option value="pending">Pending</option>
          <option value="approved">Approved</option>
          <option value="rejected">Rejected</option>
          <option value="paid">Paid</option>
        </select>
        <input
          type="month"
          value={selectedMonth}
          onChange={(e) => setSelectedMonth(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        />
      </div>

      {/* Overtime Records List */}
      {loading ? (
        <div className="space-y-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      ) : filteredRecords.length === 0 ? (
        <div className="text-center py-12">
          <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No overtime records found
          </h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || filterType || filterStatus
              ? 'Try adjusting your search criteria.'
              : 'No overtime records have been created yet.'
            }
          </p>
          {!isCurrentUser && (
            <Button variant="primary" onClick={handleCreateRecord}>
              <Plus className="h-4 w-4 mr-2" />
              Add Overtime Record
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredRecords.map((record) => (
            <OvertimeRecordCard
              key={record.id}
              record={record}
              onEdit={() => handleEditRecord(record)}
              onDelete={() => handleDeleteRecord(record)}
              onApprove={() => handleApproveRecord(record)}
              onReject={() => handleRejectRecord(record)}
              isCurrentUser={isCurrentUser}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Overtime Record Card Component
interface OvertimeRecordCardProps {
  record: OvertimeRecord;
  onEdit: () => void;
  onDelete: () => void;
  onApprove: () => void;
  onReject: () => void;
  isCurrentUser: boolean;
}

const OvertimeRecordCard: React.FC<OvertimeRecordCardProps> = ({
  record,
  onEdit,
  onDelete,
  onApprove,
  onReject,
  isCurrentUser
}) => {
  const [showActions, setShowActions] = useState(false);

  const getOvertimeIcon = (type: string) => {
    switch (type) {
      case 'daily': return <Clock className="h-5 w-5 text-blue-600" />;
      case 'weekly': return <Calendar className="h-5 w-5 text-green-600" />;
      case 'holiday': return <Calendar className="h-5 w-5 text-red-600" />;
      case 'weekend': return <Calendar className="h-5 w-5 text-purple-600" />;
      case 'emergency': return <AlertCircle className="h-5 w-5 text-orange-600" />;
      case 'project_deadline': return <Zap className="h-5 w-5 text-yellow-600" />;
      default: return <Timer className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-blue-100 text-blue-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'paid': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <AlertCircle className="h-4 w-4" />;
      case 'approved': return <CheckCircle className="h-4 w-4" />;
      case 'rejected': return <XCircle className="h-4 w-4" />;
      case 'paid': return <CheckCircle className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  const formatTime = (time: string) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <Card className="relative hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        {/* Actions Menu */}
        {!isCurrentUser && (
          <div className="absolute top-4 right-4">
            <div className="relative">
              <button
                onClick={() => setShowActions(!showActions)}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <MoreHorizontal className="h-4 w-4 text-gray-500" />
              </button>

              {showActions && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                  <div className="py-1">
                    <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </button>
                    <button
                      onClick={() => {
                        onEdit();
                        setShowActions(false);
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Record
                    </button>
                    {record.status === 'pending' && (
                      <>
                        <button
                          onClick={() => {
                            onApprove();
                            setShowActions(false);
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-green-600 hover:bg-green-50"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Approve
                        </button>
                        <button
                          onClick={() => {
                            onReject();
                            setShowActions(false);
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                        >
                          <XCircle className="h-4 w-4 mr-2" />
                          Reject
                        </button>
                      </>
                    )}
                    <button
                      onClick={() => {
                        onDelete();
                        setShowActions(false);
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Record
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Record Info */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <div>
            <div className="flex items-center mb-3">
              {getOvertimeIcon(record.overtime_type)}
              <div className="ml-3">
                <h4 className="text-lg font-medium text-gray-900">
                  {formatDate(record.date)}
                </h4>
                <p className="text-sm text-gray-600 capitalize">
                  {record.overtime_type.replace('_', ' ')}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2 mb-2">
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                getStatusColor(record.status)
              )}>
                {getStatusIcon(record.status)}
                <span className="ml-1">{record.status.toUpperCase()}</span>
              </span>
              {record.is_preapproved && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Pre-approved
                </span>
              )}
            </div>
          </div>

          <div>
            <h5 className="text-sm font-medium text-gray-900 mb-2">Time Details</h5>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Start:</span>
                <span className="font-medium">{formatTime(record.start_time)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">End:</span>
                <span className="font-medium">{formatTime(record.end_time)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Break:</span>
                <span className="font-medium">{record.break_time_minutes}min</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total:</span>
                <span className="font-medium text-blue-600">{record.total_hours}h</span>
              </div>
            </div>
          </div>

          <div>
            <h5 className="text-sm font-medium text-gray-900 mb-2">Hours Breakdown</h5>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Regular:</span>
                <span className="font-medium">{record.regular_hours}h</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Overtime:</span>
                <span className="font-medium text-orange-600">{record.overtime_hours}h</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Base Rate:</span>
                <span className="font-medium">{formatCurrency(record.base_hourly_rate)}/h</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">OT Rate:</span>
                <span className="font-medium">{formatCurrency(record.overtime_rate)}/h</span>
              </div>
            </div>
          </div>

          <div>
            <h5 className="text-sm font-medium text-gray-900 mb-2">Payment & Approval</h5>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Overtime Pay:</span>
                <span className="text-lg font-bold text-green-600">
                  {formatCurrency(record.overtime_pay)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Supervisor:</span>
                <span className="font-medium">{record.supervisor}</span>
              </div>
              {record.approved_by && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Approved by:</span>
                  <span className="font-medium">{record.approved_by}</span>
                </div>
              )}
              {record.approved_date && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Approved:</span>
                  <span className="font-medium">{formatDate(record.approved_date)}</span>
                </div>
              )}
            </div>
          </div>

          <div>
            <h5 className="text-sm font-medium text-gray-900 mb-2">Project & Reason</h5>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Department:</span>
                <span className="font-medium">{record.department}</span>
              </div>
              {record.project_code && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Project:</span>
                  <span className="font-medium">{record.project_code}</span>
                </div>
              )}
            </div>
            <div className="mt-3 p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-700">{record.reason}</p>
            </div>
            {record.rejection_reason && (
              <div className="mt-2 p-3 bg-red-50 rounded-lg">
                <p className="text-sm text-red-700">
                  <strong>Rejection Reason:</strong> {record.rejection_reason}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default OvertimeSection;
