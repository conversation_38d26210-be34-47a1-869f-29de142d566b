# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Welsh (http://www.transifex.com/django/django/language/cy/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: cy\n"
"Plural-Forms: nplurals=4; plural=(n==1) ? 0 : (n==2) ? 1 : (n != 8 && n != "
"11) ? 2 : 3;\n"

msgid "Administrative Documentation"
msgstr "Dogfennaeth Gweinyddol"

msgid "Home"
msgstr "Hafan"

msgid "Documentation"
msgstr "Dogfennaeth"

msgid "Bookmarklets"
msgstr "Nodau tudalen"

msgid "Documentation bookmarklets"
msgstr "Nodau tudalen dogfennaeth"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""

msgid "Documentation for this page"
msgstr "Dogfennaeth y dudalen hon"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Cymerir chi o unrhyw dudalen i'r dogfennaeth am yr olwg sy'n creu'r dudalen "
"hwnnw."

msgid "Tags"
msgstr "Tagiau"

msgid "List of all the template tags and their functions."
msgstr "Rhestr o holl dagiau'r templad a'u swyddogaeth."

msgid "Filters"
msgstr "Hidlau"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Gweithredoedd yw hidlau sy'n gallu cael eu gosod ar newidynnau mewn templad "
"i newid yr allbwm."

msgid "Models"
msgstr "Modelau"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Mae modelau yn disgrifio pob gwrthrych yn y system a'u meysydd "
"cysylltiedig.  Mae gan bob model restr o feysydd y gellir eu defnyddio fel "
"newidynnau templad"

msgid "Views"
msgstr "Golygon"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Mae pob tudalen ar y wefan gyhoeddus yn cael eu creu gan olwg.  Mae'r golwg "
"yn diffunio pa templad i'w ddefnyddio i gynhyrchu'r dudalen a pa wrthrychau "
"sydd ar gael i'r templad."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Offer ar gyfer eich porwr i gael mynediad cyflym i swyddogaethau gweinyddwr."

msgid "Please install docutils"
msgstr "Gosodwch docutils"

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Mae'r system dogfennaeth gweinyddol angen llyfrgell <a href=\"%(link)s"
"\">docutils</a> Python."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr "Gofynnwch i'r gweinyddwr i osod <a href=\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr ""

msgid "Field"
msgstr "Maes"

msgid "Type"
msgstr "Math"

msgid "Description"
msgstr "Disgrifiad"

msgid "Methods with arguments"
msgstr ""

msgid "Method"
msgstr ""

msgid "Arguments"
msgstr ""

msgid "Back to Model documentation"
msgstr ""

msgid "Model documentation"
msgstr "Dogfennaeth model"

msgid "Model groups"
msgstr "Grwpiau model"

msgid "Templates"
msgstr "Templadau"

#, python-format
msgid "Template: %(name)s"
msgstr "Templad: %(name)s"

#, python-format
msgid "Template: \"%(name)s\""
msgstr "Templad: \"%(name)s\""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template \"%(name)s\":"
msgstr "Chwilio'r llwybr am dempled \"%(name)s\":"

msgid "(does not exist)"
msgstr "(nid yw'n bodoli)"

msgid "Back to Documentation"
msgstr "Nôl i Dogfennaeth"

msgid "Template filters"
msgstr "Hidlau templadau"

msgid "Template filter documentation"
msgstr "Dogfennaeth hidlau templadau"

msgid "Built-in filters"
msgstr "Hidlau mewnol"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"I ddefnyddio'r hildau, rhowch <code>%(code)s</code> yn eich templad cyn "
"defnyddio'r hidl."

msgid "Template tags"
msgstr "Tagiau templadau"

msgid "Template tag documentation"
msgstr "Dogfennaeth tag templad"

msgid "Built-in tags"
msgstr "Tagiau mewnol"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"I ddefnyddio'r tagiau rhowch <code>%(code)s</code> yn eich templad cyn "
"defnyddio'r tag."

#, python-format
msgid "View: %(name)s"
msgstr "Golwg: %(name)s"

msgid "Context:"
msgstr "Cyd-destun:"

msgid "Templates:"
msgstr "Templadau:"

msgid "Back to View documentation"
msgstr ""

msgid "View documentation"
msgstr "Dogfennaeth golygon"

msgid "Jump to namespace"
msgstr ""

msgid "Empty namespace"
msgstr ""

#, python-format
msgid "Views by namespace %(name)s"
msgstr ""

msgid "Views by empty namespace"
msgstr ""

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Gweld ffwythiant: <code>%(full_name)s</code>. Enw: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "tag:"

msgid "filter:"
msgstr "hidl:"

msgid "view:"
msgstr "golwg:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Ni ddarganfuwyd ap %(app_label)r"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Ni darganfyddwyd model %(model_name)r yn ap %(app_label)r"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "y gwrthrych `%(app_label)s.%(data_type)s`  cysylltiedig"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "gwrthrychau `%(app_label)s.%(object_name)s` cysylltiedig"

#, python-format
msgid "all %s"
msgstr "pob %s"

#, python-format
msgid "number of %s"
msgstr "nifer o %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "nid yw %s yn ymweld i fod yn wrthrych urlpattern"
