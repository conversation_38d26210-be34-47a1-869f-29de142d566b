# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON>, 2012,2014-2015,2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-10-01 10:21+0000\n"
"Last-Translator: <PERSON><PERSON>\n"
"Language-Team: Spanish (Argentina) (http://www.transifex.com/django/django/"
"language/es_AR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es_AR\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Tipos de Contenido"

msgid "python model class name"
msgstr "nombre de la clase Python del modelo"

msgid "content type"
msgstr "tipo de contenido"

msgid "content types"
msgstr "tipos de contenido"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "El objeto Tipo de contenido %(ct_id)s no tiene un modelo asociado"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "El objeto Tipo de contenido %(ct_id)s %(obj_id)s no existe"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Los objetos %(ct_name)s no tienen un método get_absolute_url()"
