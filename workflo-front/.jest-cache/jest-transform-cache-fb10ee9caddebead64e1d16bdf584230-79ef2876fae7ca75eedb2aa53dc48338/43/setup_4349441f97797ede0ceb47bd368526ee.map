{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/__tests__/setup.ts"], "sourcesContent": ["import '@testing-library/jest-dom';\nimport React from 'react';\n\n// Mock Next.js router\njest.mock('next/navigation', () => ({\n  useRouter: () => ({\n    push: jest.fn(),\n    replace: jest.fn(),\n    back: jest.fn(),\n    forward: jest.fn(),\n    refresh: jest.fn(),\n    prefetch: jest.fn(),\n  }),\n  usePathname: () => '/staff',\n  useSearchParams: () => new URLSearchParams(),\n}));\n\n// Mock Next.js image\njest.mock('next/image', () => ({\n  __esModule: true,\n  default: (props: any) => {\n    const { src, alt, ...rest } = props;\n    // eslint-disable-next-line @next/next/no-img-element\n    return React.createElement('img', { src, alt, ...rest });\n  },\n}));\n\n// Mock IntersectionObserver\nglobal.IntersectionObserver = jest.fn().mockImplementation(() => ({\n  observe: jest.fn(),\n  unobserve: jest.fn(),\n  disconnect: jest.fn(),\n}));\n\n// Mock ResizeObserver\nglobal.ResizeObserver = jest.fn().mockImplementation(() => ({\n  observe: jest.fn(),\n  unobserve: jest.fn(),\n  disconnect: jest.fn(),\n}));\n\n// Mock WebSocket\nglobal.WebSocket = jest.fn().mockImplementation(() => ({\n  close: jest.fn(),\n  send: jest.fn(),\n  addEventListener: jest.fn(),\n  removeEventListener: jest.fn(),\n  readyState: 1,\n  CONNECTING: 0,\n  OPEN: 1,\n  CLOSING: 2,\n  CLOSED: 3,\n}));\n\n// Mock Notification API\nglobal.Notification = jest.fn().mockImplementation(() => ({\n  close: jest.fn(),\n  onclick: jest.fn(),\n}));\n\nObject.defineProperty(global.Notification, 'permission', {\n  value: 'granted',\n  writable: true,\n});\n\nObject.defineProperty(global.Notification, 'requestPermission', {\n  value: jest.fn().mockResolvedValue('granted'),\n  writable: true,\n});\n\n// Mock localStorage\nconst localStorageMock = {\n  getItem: jest.fn(),\n  setItem: jest.fn(),\n  removeItem: jest.fn(),\n  clear: jest.fn(),\n};\nglobal.localStorage = localStorageMock as any;\n\n// Mock sessionStorage\nconst sessionStorageMock = {\n  getItem: jest.fn(),\n  setItem: jest.fn(),\n  removeItem: jest.fn(),\n  clear: jest.fn(),\n};\nglobal.sessionStorage = sessionStorageMock as any;\n\n// Mock URL.createObjectURL and URL.revokeObjectURL\nglobal.URL.createObjectURL = jest.fn(() => 'mocked-url');\nglobal.URL.revokeObjectURL = jest.fn();\n\n// Mock fetch\nglobal.fetch = jest.fn();\n\n// Mock console methods to reduce noise in tests\nconst originalError = console.error;\nconst originalWarn = console.warn;\n\nbeforeAll(() => {\n  console.error = (...args: any[]) => {\n    if (\n      typeof args[0] === 'string' &&\n      args[0].includes('Warning: ReactDOM.render is no longer supported')\n    ) {\n      return;\n    }\n    originalError.call(console, ...args);\n  };\n\n  console.warn = (...args: any[]) => {\n    if (\n      typeof args[0] === 'string' &&\n      (args[0].includes('componentWillReceiveProps') ||\n        args[0].includes('componentWillUpdate'))\n    ) {\n      return;\n    }\n    originalWarn.call(console, ...args);\n  };\n});\n\nafterAll(() => {\n  console.error = originalError;\n  console.warn = originalWarn;\n});\n\n// Reset mocks after each test\nafterEach(() => {\n  jest.clearAllMocks();\n  localStorageMock.getItem.mockClear();\n  localStorageMock.setItem.mockClear();\n  localStorageMock.removeItem.mockClear();\n  localStorageMock.clear.mockClear();\n  sessionStorageMock.getItem.mockClear();\n  sessionStorageMock.setItem.mockClear();\n  sessionStorageMock.removeItem.mockClear();\n  sessionStorageMock.clear.mockClear();\n});\n"], "names": ["jest", "mock", "useRouter", "push", "fn", "replace", "back", "forward", "refresh", "prefetch", "usePathname", "useSearchParams", "URLSearchParams", "__esModule", "default", "props", "src", "alt", "rest", "React", "createElement", "global", "IntersectionObserver", "mockImplementation", "observe", "unobserve", "disconnect", "ResizeObserver", "WebSocket", "close", "send", "addEventListener", "removeEventListener", "readyState", "CONNECTING", "OPEN", "CLOSING", "CLOSED", "Notification", "onclick", "Object", "defineProperty", "value", "writable", "mockResolvedValue", "localStorageMock", "getItem", "setItem", "removeItem", "clear", "localStorage", "sessionStorageMock", "sessionStorage", "URL", "createObjectURL", "revokeObjectURL", "fetch", "originalError", "console", "error", "originalWarn", "warn", "beforeAll", "args", "includes", "call", "afterAll", "after<PERSON>ach", "clearAllMocks", "mockClear"], "mappings": ";AAGA,sBAAsB;AACtBA,KAAKC,IAAI,CAAC,mBAAmB,IAAO,CAAA;QAClCC,WAAW,IAAO,CAAA;gBAChBC,MAAMH,KAAKI,EAAE;gBACbC,SAASL,KAAKI,EAAE;gBAChBE,MAAMN,KAAKI,EAAE;gBACbG,SAASP,KAAKI,EAAE;gBAChBI,SAASR,KAAKI,EAAE;gBAChBK,UAAUT,KAAKI,EAAE;YACnB,CAAA;QACAM,aAAa,IAAM;QACnBC,iBAAiB,IAAM,IAAIC;IAC7B,CAAA;AAEA,qBAAqB;AACrBZ,KAAKC,IAAI,CAAC,cAAc,IAAO,CAAA;QAC7BY,YAAY;QACZC,SAAS,CAACC;YACR,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAE,GAAGC,MAAM,GAAGH;YAC9B,qDAAqD;YACrD,OAAOI,cAAK,CAACC,aAAa,CAAC,OAAO;gBAAEJ;gBAAKC;gBAAK,GAAGC,IAAI;YAAC;QACxD;IACF,CAAA;;;;QAzBO;8DACW;;;;;;AA0BlB,4BAA4B;AAC5BG,OAAOC,oBAAoB,GAAGtB,KAAKI,EAAE,GAAGmB,kBAAkB,CAAC,IAAO,CAAA;QAChEC,SAASxB,KAAKI,EAAE;QAChBqB,WAAWzB,KAAKI,EAAE;QAClBsB,YAAY1B,KAAKI,EAAE;IACrB,CAAA;AAEA,sBAAsB;AACtBiB,OAAOM,cAAc,GAAG3B,KAAKI,EAAE,GAAGmB,kBAAkB,CAAC,IAAO,CAAA;QAC1DC,SAASxB,KAAKI,EAAE;QAChBqB,WAAWzB,KAAKI,EAAE;QAClBsB,YAAY1B,KAAKI,EAAE;IACrB,CAAA;AAEA,iBAAiB;AACjBiB,OAAOO,SAAS,GAAG5B,KAAKI,EAAE,GAAGmB,kBAAkB,CAAC,IAAO,CAAA;QACrDM,OAAO7B,KAAKI,EAAE;QACd0B,MAAM9B,KAAKI,EAAE;QACb2B,kBAAkB/B,KAAKI,EAAE;QACzB4B,qBAAqBhC,KAAKI,EAAE;QAC5B6B,YAAY;QACZC,YAAY;QACZC,MAAM;QACNC,SAAS;QACTC,QAAQ;IACV,CAAA;AAEA,wBAAwB;AACxBhB,OAAOiB,YAAY,GAAGtC,KAAKI,EAAE,GAAGmB,kBAAkB,CAAC,IAAO,CAAA;QACxDM,OAAO7B,KAAKI,EAAE;QACdmC,SAASvC,KAAKI,EAAE;IAClB,CAAA;AAEAoC,OAAOC,cAAc,CAACpB,OAAOiB,YAAY,EAAE,cAAc;IACvDI,OAAO;IACPC,UAAU;AACZ;AAEAH,OAAOC,cAAc,CAACpB,OAAOiB,YAAY,EAAE,qBAAqB;IAC9DI,OAAO1C,KAAKI,EAAE,GAAGwC,iBAAiB,CAAC;IACnCD,UAAU;AACZ;AAEA,oBAAoB;AACpB,MAAME,mBAAmB;IACvBC,SAAS9C,KAAKI,EAAE;IAChB2C,SAAS/C,KAAKI,EAAE;IAChB4C,YAAYhD,KAAKI,EAAE;IACnB6C,OAAOjD,KAAKI,EAAE;AAChB;AACAiB,OAAO6B,YAAY,GAAGL;AAEtB,sBAAsB;AACtB,MAAMM,qBAAqB;IACzBL,SAAS9C,KAAKI,EAAE;IAChB2C,SAAS/C,KAAKI,EAAE;IAChB4C,YAAYhD,KAAKI,EAAE;IACnB6C,OAAOjD,KAAKI,EAAE;AAChB;AACAiB,OAAO+B,cAAc,GAAGD;AAExB,mDAAmD;AACnD9B,OAAOgC,GAAG,CAACC,eAAe,GAAGtD,KAAKI,EAAE,CAAC,IAAM;AAC3CiB,OAAOgC,GAAG,CAACE,eAAe,GAAGvD,KAAKI,EAAE;AAEpC,aAAa;AACbiB,OAAOmC,KAAK,GAAGxD,KAAKI,EAAE;AAEtB,gDAAgD;AAChD,MAAMqD,gBAAgBC,QAAQC,KAAK;AACnC,MAAMC,eAAeF,QAAQG,IAAI;AAEjCC,UAAU;IACRJ,QAAQC,KAAK,GAAG,CAAC,GAAGI;QAClB,IACE,OAAOA,IAAI,CAAC,EAAE,KAAK,YACnBA,IAAI,CAAC,EAAE,CAACC,QAAQ,CAAC,oDACjB;YACA;QACF;QACAP,cAAcQ,IAAI,CAACP,YAAYK;IACjC;IAEAL,QAAQG,IAAI,GAAG,CAAC,GAAGE;QACjB,IACE,OAAOA,IAAI,CAAC,EAAE,KAAK,YAClBA,CAAAA,IAAI,CAAC,EAAE,CAACC,QAAQ,CAAC,gCAChBD,IAAI,CAAC,EAAE,CAACC,QAAQ,CAAC,sBAAqB,GACxC;YACA;QACF;QACAJ,aAAaK,IAAI,CAACP,YAAYK;IAChC;AACF;AAEAG,SAAS;IACPR,QAAQC,KAAK,GAAGF;IAChBC,QAAQG,IAAI,GAAGD;AACjB;AAEA,8BAA8B;AAC9BO,UAAU;IACRnE,KAAKoE,aAAa;IAClBvB,iBAAiBC,OAAO,CAACuB,SAAS;IAClCxB,iBAAiBE,OAAO,CAACsB,SAAS;IAClCxB,iBAAiBG,UAAU,CAACqB,SAAS;IACrCxB,iBAAiBI,KAAK,CAACoB,SAAS;IAChClB,mBAAmBL,OAAO,CAACuB,SAAS;IACpClB,mBAAmBJ,OAAO,CAACsB,SAAS;IACpClB,mBAAmBH,UAAU,CAACqB,SAAS;IACvClB,mBAAmBF,KAAK,CAACoB,SAAS;AACpC"}