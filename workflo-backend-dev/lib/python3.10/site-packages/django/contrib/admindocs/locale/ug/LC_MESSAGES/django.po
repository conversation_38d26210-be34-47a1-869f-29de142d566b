# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Azat, 2023
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2023-12-04 20:19+0000\n"
"Last-Translator: Azat, 2023\n"
"Language-Team: Uyghur (http://app.transifex.com/django/django/language/ug/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ug\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "باشقۇرۇش قوللانمىسى"

msgid "Home"
msgstr "باش بەت"

msgid "Documentation"
msgstr "قوللانمىلار"

msgid "Bookmarklets"
msgstr "خەتكۈچلەر"

msgid "Documentation bookmarklets"
msgstr "قوللانما خەتكۈچلىرى"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"خەتكۈچنى ئورنىتىش ئۈچۈن، ئۇلانمىنى خەتكۈچ ئىشارىتىڭىزگە سۆرۈڭ ياكى ئۇلانمىنى "
"ئوڭ تېكشۈرۈپ خەتكۈچكە قوشۇڭ. ھازىر سىز تور بېكەتتىكى ھەرقايسى بەتتىن "
"خەتكۈچنى تاللىيالايسىز."

msgid "Documentation for this page"
msgstr "بۇ بەتنىڭ قوللانما ھۆججىتى"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "ھەرقايسى بەتتىن سىزنى ئۇ بەتنىڭ قوللانما كۆرۈنۈش ھۆججىتىغا ئاتلىدۇ."

msgid "Tags"
msgstr "خەتكۈچلەر"

msgid "List of all the template tags and their functions."
msgstr "بارلىق قېلىپ خەتكۈچلىرى ۋە ئۇلارنىڭ ئىقتىدارلىرى تىزىملىكى."

msgid "Filters"
msgstr "سۈزگۈچ"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"سۈزگۈچ نەتىجىنى ئۆزگەرتىش ئۈچۈن قېلىپدىكى ئۆزگەرگۈچى مىقدارلارغا "
"قوللىنىلىدىغان ھەرىكەتلەر."

msgid "Models"
msgstr "ئەندىزەلەر"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"مودېللار سىستېمىدىكى بارلىق جىسىملارنىڭ ۋە ئۇلارنىڭ مۇناسىۋەتلىك ساھەلىرىنىڭ "
"تەسۋىرى. ھەر بىر مودېلنىڭ قېلىپ ئۆزگەرگۈچى مىقدار سۈپىتىدە زىيارەت قىلغىلى "
"بولىدىغان ساھە تىزىملىكى بار"

msgid "Views"
msgstr "كۆرۈنۈشلەر"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"ئاممىۋى تور بېكەتتىكى ھەر بىر بەت كۆرۈنۈش ئارقىلىق ھاسىل بولىدۇ. كۆرۈنۈشتە "
"قايسى قېلىپنىڭ بەت ھاسىل قىلىشتا ئىشلىتىلىدىغانلىقى ۋە قايسى قېلىپلارنىڭ شۇ "
"قېلىپقا ئىشلىتىلىدىغانلىقىنى بەلگىلەيدۇ."

msgid "Tools for your browser to quickly access admin functionality."
msgstr "تور كۆرگۈچىڭىزنىڭ باشقۇرۇش ئىقتىدارىنى تېز زىيارەت قىلىدىغان قوراللار."

msgid "Please install docutils"
msgstr "docutils نى قاچىلاڭ"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a "
"href=\"%(link)s\">docutils</a> library."
msgstr ""
"باشقۇرغۇچى ھۆججەت سىستېمىسى Python نىڭ<a href=\"%(link)s\"></a>docutils1 "
"كۈتۈپخانىسىنى تەلەپ قىلىدۇ."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"باشقۇرغۇچىلىرىڭىزدىن <a href=\"%(link)s\">docutils</a> نى قاچىلاشنى تەلەپ "
"قىلىڭ."

#, python-format
msgid "Model: %(name)s"
msgstr "مودېل: %(name)s"

msgid "Fields"
msgstr "سۆز بۆلىكى"

msgid "Field"
msgstr "سۆز بۆلىكى"

msgid "Type"
msgstr "تىپى"

msgid "Description"
msgstr "چۈشەندۈرۈش"

msgid "Methods with arguments"
msgstr "ئارگۇمېنىتى بار مېتودلار"

msgid "Method"
msgstr "چارە"

msgid "Arguments"
msgstr "ئۆزگەرگۈچىلەر"

msgid "Back to Model documentation"
msgstr "مودېل ھۆججىتىگە قايتىڭ"

msgid "Model documentation"
msgstr "مودېل ھۆججىتى"

msgid "Model groups"
msgstr "ئەندىزە گۇرۇپپىلىرى"

msgid "Templates"
msgstr "قېلىپلار"

#, python-format
msgid "Template: %(name)s"
msgstr "قېلىپ: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "قېلىپ:  <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "قېلىپنىڭ ئىزدەش يولى: <q>%(name)s</q>"

msgid "(does not exist)"
msgstr "(مەۋجۇت ئەمەس)"

msgid "Back to Documentation"
msgstr "ھۆججەتكە قايتىش"

msgid "Template filters"
msgstr "قېلىپ سۈزگۈچلىرى"

msgid "Template filter documentation"
msgstr "قېلىپ سۈزگۈچ ھۆججىتى"

msgid "Built-in filters"
msgstr "ئىچكى سۈزگۈچ"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"بۇ سۈزگۈچلەرنى ئىشلىتىش ئۈچۈن سۈزگۈچنى ئىشلىتىشتىن بۇرۇن قېلىپقا "
"<code>%(code)s</code>   قويۇڭ."

msgid "Template tags"
msgstr "قېلىپ خەتكۈچلىرى"

msgid "Template tag documentation"
msgstr "قېلىپ بەلگىلەر ھۆججىتى"

msgid "Built-in tags"
msgstr "ئىچكى بەلگىلەر"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"بۇ بەلگىلەرنى ئىشلىتىش ئۈچۈن ، خەتكۈچنى ئىشلىتىشتىن بۇرۇن قېلىپقا  "
"<code>%(code)s</code> قويۇڭ."

#, python-format
msgid "View: %(name)s"
msgstr "كۆرۈنۈش: %(name)s"

msgid "Context:"
msgstr "تىل مۇھىتى:"

msgid "Templates:"
msgstr "قېلىپلار:"

msgid "Back to View documentation"
msgstr "ھۆججەتلەرنى كۆرۈش"

msgid "View documentation"
msgstr "ھۆججەتلەرنى كۆرۈش"

msgid "Jump to namespace"
msgstr "ئىسىم بوشلۇقىغا ئاتلاڭ"

msgid "Empty namespace"
msgstr "ئىسىم بوشلۇقى بوش"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "ئىسىم بوشلۇقىدىكى كۆرۈنۈش %(name)s"

msgid "Views by empty namespace"
msgstr "قۇرۇق ئىسىم بوشلۇقىدىكى كۆرۈنۈشلەر"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "بەلگە:"

msgid "filter:"
msgstr "سۈزگۈچ:"

msgid "view:"
msgstr "كۆرۈنۈش:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "ئەپ %(app_label)r تېپىلمىدى"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Model %(model_name)r not found in app %(app_label)r"

msgid "model:"
msgstr "ئەندىزە:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "the related `%(app_label)s.%(data_type)s` object"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "related `%(app_label)s.%(object_name)s` objects"

#, python-format
msgid "all %s"
msgstr "ھەممە %s"

#, python-format
msgid "number of %s"
msgstr "%sنىڭ سانى"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s urlpattern ئوبىيكتى ئەمەستەك كۆرۈنىدۇ."
