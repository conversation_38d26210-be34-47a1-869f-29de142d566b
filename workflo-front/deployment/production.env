# Production Environment Configuration for BioStar Integration
# Copy this file to .env.local and update with your actual values

# =============================================================================
# BIOSTAR 2 API CONFIGURATION
# =============================================================================

# BioStar 2 Server URL
# Replace with your actual BioStar 2 server URL
NEXT_PUBLIC_BIOSTAR_API_URL=https://your-biostar-server.com:8080

# BioStar 2 Authentication
# Replace with your actual BioStar 2 credentials
NEXT_PUBLIC_BIOSTAR_USERNAME=your_biostar_admin_username
NEXT_PUBLIC_BIOSTAR_PASSWORD=your_biostar_admin_password

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================

# API Timeouts (in milliseconds)
BIOSTAR_TIMEOUT=20000
BIOSTAR_RETRY_ATTEMPTS=5
BIOSTAR_RETRY_DELAY=2000

# Polling Configuration
BIOSTAR_POLLING_INTERVAL=60000
BIOSTAR_MAX_CONCURRENT_REQUESTS=10

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Enable/Disable Features
BIOSTAR_ENABLE_MONITORING=true
BIOSTAR_ENABLE_METRICS=true
BIOSTAR_ENABLE_LOGGING=false

# Health Check Configuration
BIOSTAR_HEALTH_CHECK_INTERVAL=300000
BIOSTAR_HEALTH_CHECK_TIMEOUT=10000

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# SSL/TLS Configuration
BIOSTAR_SSL_VERIFY=true
BIOSTAR_SSL_CERT_PATH=/path/to/certificate.pem

# Rate Limiting
BIOSTAR_RATE_LIMIT_REQUESTS=100
BIOSTAR_RATE_LIMIT_WINDOW=60000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log Levels: error, warn, info, debug
BIOSTAR_LOG_LEVEL=warn
BIOSTAR_LOG_FILE=/var/log/workflo/biostar.log

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Fallback Mode (when BioStar is unavailable)
BIOSTAR_ENABLE_FALLBACK=true
BIOSTAR_FALLBACK_MODE=manual

# Data Backup
BIOSTAR_BACKUP_ENABLED=true
BIOSTAR_BACKUP_INTERVAL=3600000
BIOSTAR_BACKUP_RETENTION_DAYS=30

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Cache Configuration
BIOSTAR_CACHE_ENABLED=true
BIOSTAR_CACHE_TTL=300000
BIOSTAR_CACHE_MAX_SIZE=1000

# Connection Pool
BIOSTAR_CONNECTION_POOL_SIZE=10
BIOSTAR_CONNECTION_POOL_TIMEOUT=30000

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================

# Alert Configuration
BIOSTAR_ALERTS_ENABLED=true
BIOSTAR_ALERT_EMAIL=<EMAIL>
BIOSTAR_ALERT_WEBHOOK=https://your-webhook-url.com/alerts

# Notification Thresholds
BIOSTAR_ALERT_UPTIME_THRESHOLD=95
BIOSTAR_ALERT_RESPONSE_TIME_THRESHOLD=5000
BIOSTAR_ALERT_FAILURE_COUNT_THRESHOLD=5

# =============================================================================
# INTEGRATION CONFIGURATION
# =============================================================================

# Employee Sync Configuration
BIOSTAR_SYNC_ENABLED=true
BIOSTAR_SYNC_INTERVAL=86400000
BIOSTAR_SYNC_BATCH_SIZE=100

# Device Configuration
BIOSTAR_DEVICE_SYNC_ENABLED=true
BIOSTAR_DEVICE_SYNC_INTERVAL=300000

# =============================================================================
# DEVELOPMENT/TESTING OVERRIDES
# =============================================================================

# Test Mode (for staging/testing environments)
BIOSTAR_TEST_MODE=false
BIOSTAR_TEST_DATA_ENABLED=false
BIOSTAR_MOCK_RESPONSES=false
