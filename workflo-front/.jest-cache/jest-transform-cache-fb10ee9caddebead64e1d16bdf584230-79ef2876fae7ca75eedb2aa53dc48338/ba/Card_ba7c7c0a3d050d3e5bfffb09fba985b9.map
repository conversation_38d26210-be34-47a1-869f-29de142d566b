{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\nimport { CardProps } from '@/types';\n\nconst Card: React.FC<CardProps> = ({\n  title,\n  children,\n  className,\n  actions,\n  ...props\n}) => {\n  return (\n    <div\n      className={cn(\n        'bg-white rounded-lg shadow-sm border border-gray-200 min-w-0 overflow-hidden',\n        className\n      )}\n      {...props}\n    >\n      {(title || actions) && (\n        <div className=\"px-4 sm:px-6 py-4 border-b border-gray-200 flex items-center justify-between min-w-0\">\n          {title && (\n            <h3 className=\"text-base sm:text-lg font-medium text-gray-900 truncate\">{title}</h3>\n          )}\n          {actions && <div className=\"flex items-center space-x-2 flex-shrink-0\">{actions}</div>}\n        </div>\n      )}\n      <div className=\"p-4 sm:p-6 min-w-0\">{children}</div>\n    </div>\n  );\n};\n\n// Card Header Component\nexport const CardHeader: React.FC<{\n  children: React.ReactNode;\n  className?: string;\n}> = ({ children, className }) => {\n  return (\n    <div className={cn('px-6 py-4 border-b border-gray-200', className)}>\n      {children}\n    </div>\n  );\n};\n\n// Card Body Component\nexport const CardBody: React.FC<{\n  children: React.ReactNode;\n  className?: string;\n}> = ({ children, className }) => {\n  return <div className={cn('p-6', className)}>{children}</div>;\n};\n\n// Card Footer Component\nexport const CardFooter: React.FC<{\n  children: React.ReactNode;\n  className?: string;\n}> = ({ children, className }) => {\n  return (\n    <div className={cn('px-6 py-4 border-t border-gray-200 bg-gray-50', className)}>\n      {children}\n    </div>\n  );\n};\n\n// Stats Card Component\nexport const StatsCard: React.FC<{\n  title: string;\n  value: string | number;\n  icon: React.ReactNode;\n  color?: 'primary' | 'success' | 'warning' | 'danger';\n  trend?: {\n    value: number;\n    isPositive: boolean;\n  };\n  className?: string;\n}> = ({ title, value, icon, color = 'primary', trend, className }) => {\n  const colorClasses = {\n    primary: 'bg-orange-500',\n    success: 'bg-green-500',\n    warning: 'bg-yellow-500',\n    danger: 'bg-red-500',\n  };\n\n  return (\n    <Card className={cn('', className)}>\n      <div className=\"flex items-center min-w-0\">\n        <div className={cn('p-3 rounded-lg flex-shrink-0', colorClasses[color])}>\n          <div className=\"text-white text-xl\">{icon}</div>\n        </div>\n        <div className=\"ml-4 flex-1 min-w-0\">\n          <h4 className=\"text-sm font-medium text-gray-600 truncate\">{title}</h4>\n          <p className=\"text-xl sm:text-2xl font-bold text-gray-900 truncate\">{value}</p>\n          {trend && (\n            <div className=\"flex items-center mt-1\">\n              <span\n                className={cn(\n                  'text-sm font-medium',\n                  trend.isPositive ? 'text-green-600' : 'text-red-600'\n                )}\n              >\n                {trend.isPositive ? '+' : '-'}{Math.abs(trend.value)}%\n              </span>\n              <span className=\"text-sm text-gray-500 ml-1 truncate\">vs last month</span>\n            </div>\n          )}\n        </div>\n      </div>\n    </Card>\n  );\n};\n\n// Employee Card Component\nexport const EmployeeCard: React.FC<{\n  employee: {\n    id: number;\n    first_name: string;\n    last_name: string;\n    email: string;\n    job_title?: string;\n    profile_picture?: string;\n    department?: { name: string };\n  };\n  onClick?: () => void;\n  className?: string;\n}> = ({ employee, onClick, className }) => {\n  const initials = `${employee.first_name.charAt(0)}${employee.last_name.charAt(0)}`;\n\n  return (\n    <Card\n      className={cn(\n        'cursor-pointer hover:shadow-md transition-shadow',\n        className\n      )}\n      onClick={onClick}\n    >\n      <div className=\"text-center min-w-0\">\n        <div className=\"mb-4\">\n          {employee.profile_picture ? (\n            <img\n              src={employee.profile_picture}\n              alt={`${employee.first_name} ${employee.last_name}`}\n              className=\"w-16 h-16 rounded-full mx-auto object-cover\"\n            />\n          ) : (\n            <div className=\"w-16 h-16 rounded-full mx-auto bg-orange-500 flex items-center justify-center text-white font-medium text-lg\">\n              {initials}\n            </div>\n          )}\n        </div>\n        <h4 className=\"text-base sm:text-lg font-medium text-gray-900 truncate\">\n          {employee.first_name} {employee.last_name}\n        </h4>\n        {employee.job_title && (\n          <p className=\"text-sm font-medium text-gray-600 mt-1 truncate\" title={employee.job_title}>\n            {employee.job_title}\n          </p>\n        )}\n        <p className=\"text-sm text-gray-500 mt-1 truncate\" title={employee.email}>{employee.email}</p>\n        {employee.department && (\n          <p className=\"text-xs text-gray-400 mt-1 truncate\" title={employee.department.name}>{employee.department.name}</p>\n        )}\n      </div>\n    </Card>\n  );\n};\n\nexport default Card;\n"], "names": ["CardBody", "<PERSON><PERSON><PERSON>er", "<PERSON><PERSON><PERSON><PERSON>", "EmployeeCard", "StatsCard", "Card", "title", "children", "className", "actions", "props", "div", "cn", "h3", "value", "icon", "color", "trend", "colorClasses", "primary", "success", "warning", "danger", "h4", "p", "span", "isPositive", "Math", "abs", "employee", "onClick", "initials", "first_name", "char<PERSON>t", "last_name", "profile_picture", "img", "src", "alt", "job_title", "email", "department", "name"], "mappings": ";;;;;;;;;;;IA6CaA,QAAQ;eAARA;;IAQAC,UAAU;eAAVA;;IApBAC,UAAU;eAAVA;;IA+EAC,YAAY;eAAZA;;IA/CAC,SAAS;eAATA;;IAqGb,OAAoB;eAApB;;;;8DAtKkB;uBACC;;;;;;AAGnB,MAAMC,OAA4B,CAAC,EACjCC,KAAK,EACLC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACP,GAAGC,OACJ;IACC,qBACE,sBAACC;QACCH,WAAWI,IAAAA,SAAE,EACX,gFACAJ;QAED,GAAGE,KAAK;;YAEPJ,CAAAA,SAASG,OAAM,mBACf,sBAACE;gBAAIH,WAAU;;oBACZF,uBACC,qBAACO;wBAAGL,WAAU;kCAA2DF;;oBAE1EG,yBAAW,qBAACE;wBAAIH,WAAU;kCAA6CC;;;;0BAG5E,qBAACE;gBAAIH,WAAU;0BAAsBD;;;;AAG3C;AAGO,MAAML,aAGR,CAAC,EAAEK,QAAQ,EAAEC,SAAS,EAAE;IAC3B,qBACE,qBAACG;QAAIH,WAAWI,IAAAA,SAAE,EAAC,sCAAsCJ;kBACtDD;;AAGP;AAGO,MAAMP,WAGR,CAAC,EAAEO,QAAQ,EAAEC,SAAS,EAAE;IAC3B,qBAAO,qBAACG;QAAIH,WAAWI,IAAAA,SAAE,EAAC,OAAOJ;kBAAaD;;AAChD;AAGO,MAAMN,aAGR,CAAC,EAAEM,QAAQ,EAAEC,SAAS,EAAE;IAC3B,qBACE,qBAACG;QAAIH,WAAWI,IAAAA,SAAE,EAAC,iDAAiDJ;kBACjED;;AAGP;AAGO,MAAMH,YAUR,CAAC,EAAEE,KAAK,EAAEQ,KAAK,EAAEC,IAAI,EAAEC,QAAQ,SAAS,EAAEC,KAAK,EAAET,SAAS,EAAE;IAC/D,MAAMU,eAAe;QACnBC,SAAS;QACTC,SAAS;QACTC,SAAS;QACTC,QAAQ;IACV;IAEA,qBACE,qBAACjB;QAAKG,WAAWI,IAAAA,SAAE,EAAC,IAAIJ;kBACtB,cAAA,sBAACG;YAAIH,WAAU;;8BACb,qBAACG;oBAAIH,WAAWI,IAAAA,SAAE,EAAC,gCAAgCM,YAAY,CAACF,MAAM;8BACpE,cAAA,qBAACL;wBAAIH,WAAU;kCAAsBO;;;8BAEvC,sBAACJ;oBAAIH,WAAU;;sCACb,qBAACe;4BAAGf,WAAU;sCAA8CF;;sCAC5D,qBAACkB;4BAAEhB,WAAU;sCAAwDM;;wBACpEG,uBACC,sBAACN;4BAAIH,WAAU;;8CACb,sBAACiB;oCACCjB,WAAWI,IAAAA,SAAE,EACX,uBACAK,MAAMS,UAAU,GAAG,mBAAmB;;wCAGvCT,MAAMS,UAAU,GAAG,MAAM;wCAAKC,KAAKC,GAAG,CAACX,MAAMH,KAAK;wCAAE;;;8CAEvD,qBAACW;oCAAKjB,WAAU;8CAAsC;;;;;;;;;AAOpE;AAGO,MAAML,eAYR,CAAC,EAAE0B,QAAQ,EAAEC,OAAO,EAAEtB,SAAS,EAAE;IACpC,MAAMuB,WAAW,GAAGF,SAASG,UAAU,CAACC,MAAM,CAAC,KAAKJ,SAASK,SAAS,CAACD,MAAM,CAAC,IAAI;IAElF,qBACE,qBAAC5B;QACCG,WAAWI,IAAAA,SAAE,EACX,oDACAJ;QAEFsB,SAASA;kBAET,cAAA,sBAACnB;YAAIH,WAAU;;8BACb,qBAACG;oBAAIH,WAAU;8BACZqB,SAASM,eAAe,iBACvB,qBAACC;wBACCC,KAAKR,SAASM,eAAe;wBAC7BG,KAAK,GAAGT,SAASG,UAAU,CAAC,CAAC,EAAEH,SAASK,SAAS,EAAE;wBACnD1B,WAAU;uCAGZ,qBAACG;wBAAIH,WAAU;kCACZuB;;;8BAIP,sBAACR;oBAAGf,WAAU;;wBACXqB,SAASG,UAAU;wBAAC;wBAAEH,SAASK,SAAS;;;gBAE1CL,SAASU,SAAS,kBACjB,qBAACf;oBAAEhB,WAAU;oBAAkDF,OAAOuB,SAASU,SAAS;8BACrFV,SAASU,SAAS;;8BAGvB,qBAACf;oBAAEhB,WAAU;oBAAsCF,OAAOuB,SAASW,KAAK;8BAAGX,SAASW,KAAK;;gBACxFX,SAASY,UAAU,kBAClB,qBAACjB;oBAAEhB,WAAU;oBAAsCF,OAAOuB,SAASY,UAAU,CAACC,IAAI;8BAAGb,SAASY,UAAU,CAACC,IAAI;;;;;AAKvH;MAEA,WAAerC"}