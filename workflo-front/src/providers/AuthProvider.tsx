'use client';

import React, { createContext, useContext, useEffect, useState, useCallback, useMemo } from 'react';
import { User, LoginCredentials } from '@/types';
import { authApi, TokenManager } from '@/lib/api';
import { AuthPersistence } from '@/lib/authPersistence';
import { mockApi } from '@/lib/mockApi';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean;
  isHydrated: boolean;
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  getCurrentUser: () => Promise<void>;
  clearError: () => void;
  initialize: () => Promise<void>;
}

type AuthContextType = AuthState & AuthActions;

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, setState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
    isInitialized: false,
    isHydrated: false,
  });

  const updateState = (updates: Partial<AuthState>) => {
    setState(prev => ({ ...prev, ...updates }));
  };

  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      updateState({ isLoading: true, error: null });

      const tokens = await authApi.login(credentials);
      TokenManager.setTokens(tokens);
      const user = await authApi.getCurrentUser() as User;

      updateState({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });

      AuthPersistence.save(user, true);

      // Log successful login with role information
      console.log(`Login successful for user: ${user.email}, role: ${user.role}`);

    } catch (error: any) {
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.detail ||
                          error.message ||
                          'Login failed. Please check your credentials.';

      updateState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: errorMessage,
      });

      AuthPersistence.clear();
      throw error;
    }
  }, []);

  const logout = useCallback(() => {
    // Clear mock API session
    mockApi.logout();

    // Clear tokens and persistence
    TokenManager.clearTokens();
    AuthPersistence.clear();

    updateState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      isInitialized: false,
    });

    console.log('User logged out successfully');

    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
  }, []);

  const getCurrentUser = useCallback(async () => {
    try {
      // Only show loading if we don't have a user already
      if (!state.user) {
        updateState({ isLoading: true, error: null });
      }

      const token = TokenManager.getAccessToken();
      if (!token) {
        updateState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
        AuthPersistence.clear();
        return;
      }

      const user = await authApi.getCurrentUser() as User;
      updateState({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });

      AuthPersistence.save(user, true);
    } catch (error: any) {
      TokenManager.clearTokens();
      AuthPersistence.clear();

      updateState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    }
  }, [state.user]);

  const clearError = useCallback(() => {
    updateState({ error: null });
  }, []);

  const initialize = useCallback(async () => {
    if (state.isInitialized) return;

    try {
      // First, try to load from persistence without showing loading
      const persisted = AuthPersistence.load();
      if (persisted && persisted.isAuthenticated && persisted.user) {
        updateState({
          user: persisted.user,
          isAuthenticated: persisted.isAuthenticated,
          isInitialized: true, // Mark as initialized immediately
        });

        // Validate in background without loading state
        try {
          await getCurrentUser();
        } catch (error) {
          // If validation fails, clear everything
          AuthPersistence.clear();
          updateState({
            user: null,
            isAuthenticated: false,
            error: null,
          });
        }
      } else {
        // No persisted data, validate with server
        await getCurrentUser();
        updateState({ isInitialized: true });
      }
    } catch (error) {
      // If validation fails, clear everything
      AuthPersistence.clear();
      updateState({
        user: null,
        isAuthenticated: false,
        error: null,
        isInitialized: true,
      });
    }
  }, [state.isInitialized, getCurrentUser]);

  // Initialize on mount
  useEffect(() => {
    updateState({ isHydrated: true });
    initialize();
  }, []);

  const contextValue: AuthContextType = useMemo(() => ({
    ...state,
    login,
    logout,
    getCurrentUser,
    clearError,
    initialize,
  }), [state, login, logout, getCurrentUser, clearError, initialize]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
