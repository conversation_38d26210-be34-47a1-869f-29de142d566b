# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Ossetic (http://www.transifex.com/django/django/language/"
"os/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: os\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Флдӕр фадӕттӕ"

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""
"Цӕвиттон: '/about/contact/'. Дӕ хъус дар цӕмӕ дзы сӕйраг ӕмӕ хицӕнгӕнаг "
"слӕштӕ уой."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Ам хъуамӕ ӕрмӕст дамгъӕтӕ, стъӕлфытӕ, бынылхӕххытӕ, дефистӕ, слӕштӕ ӕмӕ "
"тильдӕтӕ уой."

msgid "URL is missing a leading slash."
msgstr "URL-ы сӕйраг слӕш нӕй."

msgid "URL is missing a trailing slash."
msgstr "URL-ы хицӕнгӕнаг слӕш нӕй."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "%(url)s url-имӕ тъӕпӕнфарс нырид ис %(site)s сайтӕн"

msgid "title"
msgstr "сӕр"

msgid "content"
msgstr "мидис"

msgid "enable comments"
msgstr "хъуыдытӕ баиу кӕнын"

msgid "template name"
msgstr "хуызӕгы ном"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"Цӕвиттон: 'flatpages/contact_page.html'. Кӕд ай лӕвӕрд нӕу, уӕд системӕ "
"сӕвӕрдзӕн 'flatpages/default.html'."

msgid "registration required"
msgstr "регистраци хъӕуы"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Кӕд ай нысангонд у, уӕд ацы фарс ӕрмӕст системӕмӕ хызт архайджытӕн уыдзӕнис "
"бар уынын."

msgid "sites"
msgstr ""

msgid "flat page"
msgstr "тъӕпӕн фарс"

msgid "flat pages"
msgstr "тъӕпӕн фӕрстӕ"
