'use client';

import React, { useMemo, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  User,
  Briefcase,
  DollarSign,
  Calendar,
  FileText,
  Star,
  Clock,
  Edit,
  Eye,
  Download,
  Upload,
  Settings,
  X,
  LogOut,
  ChevronRight,
  Info,
  Shield,
  Award,
  Target,
  Activity
} from 'lucide-react';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials, formatDate } from '@/lib/utils';

interface SupervisorInfoSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const SupervisorInfoSidebar: React.FC<SupervisorInfoSidebarProps> = ({ isOpen, onClose }) => {
  const { user, logout } = useAuth();
  const pathname = usePathname();

  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  const handleLogout = useCallback(() => {
    logout();
    handleClose();
  }, [logout, handleClose]);

  // Info section areas with enhanced details
  const infoSectionAreas = useMemo(() => [
    {
      name: 'Profile',
      href: '/supervisor/info/profile',
      icon: User,
      description: 'Personal information and contact details',
      actions: ['Edit Profile', 'Update Photo', 'Change Password'],
      status: 'complete'
    },
    {
      name: 'Employment',
      href: '/supervisor/info/employment',
      icon: Briefcase,
      description: 'Job details and employment history',
      actions: ['View Contract', 'Employment History', 'Job Description'],
      status: 'complete'
    },
    {
      name: 'Salary',
      href: '/supervisor/info/salary',
      icon: DollarSign,
      description: 'Salary information and payslips',
      actions: ['View Payslips', 'Tax Information', 'Benefits'],
      status: 'complete'
    },
    {
      name: 'Leave Management',
      href: '/supervisor/info/leave-management',
      icon: Calendar,
      description: 'Leave applications and balance',
      actions: ['Apply Leave', 'View Balance', 'Leave History'],
      status: 'pending',
      badge: '2'
    },
    {
      name: 'Documents',
      href: '/supervisor/info/documents',
      icon: FileText,
      description: 'Personal and employment documents',
      actions: ['Upload Documents', 'Download Files', 'Document History'],
      status: 'incomplete'
    },
    {
      name: 'Performance',
      href: '/supervisor/info/performance',
      icon: Star,
      description: 'Performance reviews and goals',
      actions: ['View Reviews', 'Set Goals', 'Performance History'],
      status: 'complete'
    },
    {
      name: 'Time Off',
      href: '/supervisor/info/timeoff',
      icon: Clock,
      description: 'Time off requests and history',
      actions: ['Request Time Off', 'View History', 'Approval Status'],
      status: 'complete'
    },
  ], []);

  // Quick actions for info pages
  const infoQuickActions = useMemo(() => [
    {
      name: 'Edit Profile',
      href: '/supervisor/info/profile?action=edit',
      icon: Edit,
      description: 'Update personal information'
    },
    {
      name: 'View Documents',
      href: '/supervisor/info/documents',
      icon: FileText,
      description: 'Access personal documents'
    },
    {
      name: 'Check Leave Balance',
      href: '/supervisor/info/leave-management',
      icon: Calendar,
      description: 'View available leave days',
      badge: '15 days'
    },
    {
      name: 'Download Payslip',
      href: '/supervisor/info/salary?action=download',
      icon: Download,
      description: 'Get latest payslip'
    }
  ], []);

  // Personal stats
  const personalStats = useMemo(() => [
    { label: 'Years of Service', value: '2.5', icon: Award },
    { label: 'Leave Balance', value: '15', icon: Calendar },
    { label: 'Performance Score', value: '4.8', icon: Star },
    { label: 'Team Size', value: '12', icon: User }
  ], []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'complete':
        return 'text-green-600 bg-green-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'incomplete':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
        onClick={handleClose}
      />

      {/* Sidebar */}
      <div
        className={cn(
          "fixed left-0 top-0 h-full bg-white shadow-xl z-30 transform transition-transform duration-300 ease-in-out overflow-y-auto",
          "md:relative md:translate-x-0 md:shadow-lg md:top-0 md:h-full",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
        style={{ width: '368px' }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-blue-600">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
              <Info className="h-6 w-6 text-blue-500" />
            </div>
            <div>
              <h2 className="text-white font-semibold">Personal Information</h2>
              <p className="text-blue-100 text-sm">Supervisor Profile Hub</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 rounded-lg text-white hover:bg-white hover:bg-opacity-20 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* User Info */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white font-medium">
                {getInitials(user?.first_name || '', user?.last_name || '')}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="font-medium text-gray-900 truncate">
                {user?.first_name} {user?.last_name}
              </p>
              <p className="text-sm text-gray-500">{user?.employee_id}</p>
              <p className="text-xs text-gray-400 capitalize">{user?.role} • Operations</p>
            </div>
          </div>
        </div>

        {/* Personal Stats */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-4">Quick Stats</h3>
          <div className="grid grid-cols-2 gap-3">
            {personalStats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className="bg-gray-50 rounded-lg p-3 text-center">
                  <Icon className="h-5 w-5 mx-auto mb-2 text-blue-600" />
                  <div className="text-lg font-semibold text-gray-900">{stat.value}</div>
                  <div className="text-xs text-gray-500">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-2">
            {infoQuickActions.map((action, index) => {
              const Icon = action.icon;
              return (
                <Link
                  key={index}
                  href={action.href}
                  onClick={handleClose}
                  className="flex items-center justify-between p-3 rounded-lg hover:bg-blue-50 transition-colors group"
                >
                  <div className="flex items-center">
                    <Icon className="h-4 w-4 mr-3 text-blue-600" />
                    <div>
                      <div className="font-medium text-sm text-gray-900">{action.name}</div>
                      <div className="text-xs text-gray-500">{action.description}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {action.badge && (
                      <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                        {action.badge}
                      </span>
                    )}
                    <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-blue-600" />
                  </div>
                </Link>
              );
            })}
          </div>
        </div>

        {/* Info Section Areas */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900 mb-4">Information Sections</h3>
          <div className="space-y-2">
            {infoSectionAreas.map((area, index) => {
              const Icon = area.icon;
              const isCurrentPage = pathname === area.href;
              return (
                <Link
                  key={index}
                  href={area.href}
                  onClick={handleClose}
                  className={cn(
                    "flex items-center justify-between p-3 rounded-lg transition-colors",
                    isCurrentPage
                      ? "bg-blue-50 text-blue-700 border border-blue-200"
                      : "hover:bg-gray-50 text-gray-700"
                  )}
                >
                  <div className="flex items-center">
                    <Icon className={cn(
                      "h-4 w-4 mr-3",
                      isCurrentPage ? "text-blue-600" : "text-gray-400"
                    )} />
                    <div>
                      <div className="font-medium text-sm">{area.name}</div>
                      <div className="text-xs text-gray-500">{area.description}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {area.badge && (
                      <span className="bg-orange-100 text-orange-800 text-xs font-medium px-2 py-1 rounded-full">
                        {area.badge}
                      </span>
                    )}
                    <div className={cn(
                      "w-2 h-2 rounded-full",
                      getStatusColor(area.status)
                    )} />
                    <ChevronRight className="h-3 w-3 text-gray-400" />
                  </div>
                </Link>
              );
            })}
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 mt-auto">
          <div className="space-y-2">
            <Link
              href="/supervisor/settings"
              onClick={handleClose}
              className="flex items-center p-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Link>
            <button
              onClick={handleLogout}
              className="flex items-center w-full p-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default SupervisorInfoSidebar;
