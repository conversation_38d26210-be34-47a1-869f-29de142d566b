'use client';

import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Plus,
  Search,
  Filter,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  MessageSquare,
  Mail,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Coffee,
  Sun,
  MapPin,
  Gift
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { cn, formatDate } from '@/lib/utils';

interface LeaveType {
  id: number;
  name: string;
  days_allowed: number;
  carry_forward: boolean;
  requires_approval: boolean;
  color: string;
}

interface LeaveApplication {
  id: number;
  employee_id: number;
  employee_name: string;
  leave_type_id: number;
  leave_type_name: string;
  start_date: string;
  end_date: string;
  total_days: number;
  business_days: number;
  reason: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  applied_date: string;
  reviewer_id?: number;
  reviewer_name?: string;
  reviewer_comments?: string;
  reviewed_date?: string;
}

interface LeaveBalance {
  leave_type_id: number;
  leave_type_name: string;
  total_allocated: number;
  used: number;
  pending: number;
  remaining: number;
}

interface TimeOffRecord {
  id: number;
  employee_id: number;
  employee_name: string;
  time_off_type: 'half_day_morning' | 'half_day_afternoon' | 'few_hours' | 'late_arrival' | 'early_departure';
  date: string;
  start_time?: string;
  end_time?: string;
  hours: number;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  approved_by?: string;
  approved_date?: string;
  created_date: string;
  updated_date: string;
}

interface Holiday {
  id: number;
  name: string;
  date: string;
  type: 'public' | 'company' | 'religious' | 'national';
  is_mandatory: boolean;
  description?: string;
  created_date: string;
}

interface LeaveManagementTabProps {
  employeeId: number;
  employeeName: string;
  isCurrentUser?: boolean;
}

const LeaveManagementTab: React.FC<LeaveManagementTabProps> = ({
  employeeId,
  employeeName,
  isCurrentUser = false
}) => {
  const [activeSubTab, setActiveSubTab] = useState<'applications' | 'balance' | 'types' | 'timeoff' | 'holidays'>('applications');
  const [leaveApplications, setLeaveApplications] = useState<LeaveApplication[]>([]);
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([]);
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalance[]>([]);
  const [timeOffRecords, setTimeOffRecords] = useState<TimeOffRecord[]>([]);
  const [holidays, setHolidays] = useState<Holiday[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [showApplicationModal, setShowApplicationModal] = useState(false);
  const [showTimeOffModal, setShowTimeOffModal] = useState(false);

  // Mock data
  const mockLeaveTypes: LeaveType[] = [
    {
      id: 1,
      name: 'Annual Leave',
      days_allowed: 25,
      carry_forward: true,
      requires_approval: true,
      color: 'blue'
    },
    {
      id: 2,
      name: 'Sick Leave',
      days_allowed: 10,
      carry_forward: false,
      requires_approval: false,
      color: 'red'
    },
    {
      id: 3,
      name: 'Maternity Leave',
      days_allowed: 90,
      carry_forward: false,
      requires_approval: true,
      color: 'pink'
    },
    {
      id: 4,
      name: 'Paternity Leave',
      days_allowed: 14,
      carry_forward: false,
      requires_approval: true,
      color: 'green'
    }
  ];

  const mockLeaveApplications: LeaveApplication[] = [
    {
      id: 1,
      employee_id: employeeId,
      employee_name: employeeName,
      leave_type_id: 1,
      leave_type_name: 'Annual Leave',
      start_date: '2024-02-15',
      end_date: '2024-02-19',
      total_days: 5,
      business_days: 5,
      reason: 'Family vacation',
      status: 'approved',
      applied_date: '2024-01-20T00:00:00Z',
      reviewer_id: 2,
      reviewer_name: 'John Smith',
      reviewer_comments: 'Approved. Enjoy your vacation!',
      reviewed_date: '2024-01-21T00:00:00Z'
    },
    {
      id: 2,
      employee_id: employeeId,
      employee_name: employeeName,
      leave_type_id: 2,
      leave_type_name: 'Sick Leave',
      start_date: '2024-01-10',
      end_date: '2024-01-12',
      total_days: 3,
      business_days: 3,
      reason: 'Flu symptoms',
      status: 'approved',
      applied_date: '2024-01-10T00:00:00Z',
      reviewer_id: 2,
      reviewer_name: 'John Smith',
      reviewer_comments: 'Get well soon!',
      reviewed_date: '2024-01-10T00:00:00Z'
    },
    {
      id: 3,
      employee_id: employeeId,
      employee_name: employeeName,
      leave_type_id: 1,
      leave_type_name: 'Annual Leave',
      start_date: '2024-03-01',
      end_date: '2024-03-05',
      total_days: 5,
      business_days: 5,
      reason: 'Personal time off',
      status: 'pending',
      applied_date: '2024-01-25T00:00:00Z'
    }
  ];

  const mockLeaveBalances: LeaveBalance[] = [
    {
      leave_type_id: 1,
      leave_type_name: 'Annual Leave',
      total_allocated: 25,
      used: 5,
      pending: 5,
      remaining: 15
    },
    {
      leave_type_id: 2,
      leave_type_name: 'Sick Leave',
      total_allocated: 10,
      used: 3,
      pending: 0,
      remaining: 7
    },
    {
      leave_type_id: 3,
      leave_type_name: 'Maternity Leave',
      total_allocated: 90,
      used: 0,
      pending: 0,
      remaining: 90
    },
    {
      leave_type_id: 4,
      leave_type_name: 'Paternity Leave',
      total_allocated: 14,
      used: 0,
      pending: 0,
      remaining: 14
    }
  ];

  const mockTimeOffRecords: TimeOffRecord[] = [
    {
      id: 1,
      employee_id: employeeId,
      employee_name: employeeName,
      time_off_type: 'half_day_morning',
      date: '2024-01-15',
      start_time: '09:00:00',
      end_time: '13:00:00',
      hours: 4,
      reason: 'Doctor appointment',
      status: 'approved',
      approved_by: 'John Smith',
      approved_date: '2024-01-14T00:00:00Z',
      created_date: '2024-01-13T00:00:00Z',
      updated_date: '2024-01-14T00:00:00Z'
    },
    {
      id: 2,
      employee_id: employeeId,
      employee_name: employeeName,
      time_off_type: 'few_hours',
      date: '2024-01-20',
      start_time: '14:00:00',
      end_time: '16:00:00',
      hours: 2,
      reason: 'Personal errands',
      status: 'pending',
      created_date: '2024-01-19T00:00:00Z',
      updated_date: '2024-01-19T00:00:00Z'
    },
    {
      id: 3,
      employee_id: employeeId,
      employee_name: employeeName,
      time_off_type: 'late_arrival',
      date: '2024-01-25',
      start_time: '11:00:00',
      hours: 2,
      reason: 'Car trouble',
      status: 'approved',
      approved_by: 'John Smith',
      approved_date: '2024-01-25T00:00:00Z',
      created_date: '2024-01-25T00:00:00Z',
      updated_date: '2024-01-25T00:00:00Z'
    }
  ];

  const mockHolidays: Holiday[] = [
    {
      id: 1,
      name: 'New Year\'s Day',
      date: '2024-01-01',
      type: 'public',
      is_mandatory: true,
      description: 'Public holiday celebrating the start of the new year',
      created_date: '2023-12-01T00:00:00Z'
    },
    {
      id: 2,
      name: 'Martin Luther King Jr. Day',
      date: '2024-01-15',
      type: 'public',
      is_mandatory: true,
      description: 'Federal holiday honoring civil rights leader Martin Luther King Jr.',
      created_date: '2023-12-01T00:00:00Z'
    },
    {
      id: 3,
      name: 'Presidents\' Day',
      date: '2024-02-19',
      type: 'public',
      is_mandatory: true,
      description: 'Federal holiday honoring all U.S. presidents',
      created_date: '2023-12-01T00:00:00Z'
    },
    {
      id: 4,
      name: 'Company Founding Day',
      date: '2024-03-15',
      type: 'company',
      is_mandatory: false,
      description: 'Celebrating the anniversary of our company founding',
      created_date: '2023-12-01T00:00:00Z'
    },
    {
      id: 5,
      name: 'Memorial Day',
      date: '2024-05-27',
      type: 'public',
      is_mandatory: true,
      description: 'Federal holiday honoring military personnel who died in service',
      created_date: '2023-12-01T00:00:00Z'
    },
    {
      id: 6,
      name: 'Independence Day',
      date: '2024-07-04',
      type: 'national',
      is_mandatory: true,
      description: 'Celebrating the independence of the United States',
      created_date: '2023-12-01T00:00:00Z'
    },
    {
      id: 7,
      name: 'Labor Day',
      date: '2024-09-02',
      type: 'public',
      is_mandatory: true,
      description: 'Federal holiday celebrating the contributions of workers',
      created_date: '2023-12-01T00:00:00Z'
    },
    {
      id: 8,
      name: 'Thanksgiving Day',
      date: '2024-11-28',
      type: 'public',
      is_mandatory: true,
      description: 'Federal holiday for giving thanks',
      created_date: '2023-12-01T00:00:00Z'
    },
    {
      id: 9,
      name: 'Christmas Day',
      date: '2024-12-25',
      type: 'public',
      is_mandatory: true,
      description: 'Christian holiday celebrating the birth of Jesus Christ',
      created_date: '2023-12-01T00:00:00Z'
    }
  ];

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setTimeout(() => {
        setLeaveTypes(mockLeaveTypes);
        setLeaveApplications(mockLeaveApplications);
        setLeaveBalances(mockLeaveBalances);
        setTimeOffRecords(mockTimeOffRecords);
        setHolidays(mockHolidays);
        setLoading(false);
      }, 1000);
    };

    loadData();
  }, [employeeId]);

  const filteredApplications = leaveApplications.filter(app => {
    const matchesSearch = app.leave_type_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         app.reason.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = !filterStatus || app.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4" />;
      case 'rejected': return <XCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'cancelled': return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const calculateBusinessDays = (startDate: string, endDate: string): number => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    let businessDays = 0;

    const currentDate = new Date(start);
    while (currentDate <= end) {
      const dayOfWeek = currentDate.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not Sunday (0) or Saturday (6)
        businessDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return businessDays;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Leave Management</h3>
          <p className="text-sm text-gray-600 mt-1">
            Manage leave applications and balances for {employeeName}
          </p>
        </div>
        {isCurrentUser && (
          <div className="mt-4 sm:mt-0">
            <Button variant="primary" onClick={() => setShowApplicationModal(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Apply for Leave
            </Button>
          </div>
        )}
      </div>

      {/* Sub Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {[
            { key: 'applications', label: 'Leave Applications', icon: Calendar },
            { key: 'timeoff', label: 'Time Off', icon: Coffee },
            { key: 'holidays', label: 'Holidays', icon: Gift },
            { key: 'balance', label: 'Leave Balance', icon: Clock },
            { key: 'types', label: 'Leave Types', icon: Filter },
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.key}
                onClick={() => setActiveSubTab(tab.key as any)}
                className={cn(
                  'py-2 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2',
                  activeSubTab === tab.key
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeSubTab === 'applications' && (
        <div className="space-y-4">
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <div className="flex-1">
              <Input
                placeholder="Search applications..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={<Search className="h-4 w-4" />}
              />
            </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          {/* Applications List */}
          {loading ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="animate-pulse">
                  <div className="bg-white border border-gray-200 rounded-lg p-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredApplications.length === 0 ? (
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No leave applications found
              </h3>
              <p className="text-gray-600 mb-4">
                {searchQuery || filterStatus
                  ? 'Try adjusting your search criteria.'
                  : 'No leave applications have been submitted yet.'
                }
              </p>
              {isCurrentUser && (
                <Button variant="primary" onClick={() => setShowApplicationModal(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Apply for Leave
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredApplications.map((application) => (
                <LeaveApplicationCard
                  key={application.id}
                  application={application}
                  isCurrentUser={isCurrentUser}
                />
              ))}
            </div>
          )}
        </div>
      )}

      {activeSubTab === 'balance' && (
        <LeaveBalanceView balances={leaveBalances} loading={loading} />
      )}

      {activeSubTab === 'timeoff' && (
        <TimeOffView
          timeOffRecords={timeOffRecords}
          loading={loading}
          isCurrentUser={isCurrentUser}
          onRequestTimeOff={() => setShowTimeOffModal(true)}
        />
      )}

      {activeSubTab === 'holidays' && (
        <HolidaysView holidays={holidays} loading={loading} />
      )}

      {activeSubTab === 'types' && (
        <LeaveTypesView types={leaveTypes} loading={loading} />
      )}
    </div>
  );
};

// Helper functions
const getStatusColor = (status: string) => {
  switch (status) {
    case 'approved': return 'bg-green-100 text-green-800';
    case 'rejected': return 'bg-red-100 text-red-800';
    case 'pending': return 'bg-yellow-100 text-yellow-800';
    case 'cancelled': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'approved': return <CheckCircle className="h-4 w-4" />;
    case 'rejected': return <XCircle className="h-4 w-4" />;
    case 'pending': return <Clock className="h-4 w-4" />;
    case 'cancelled': return <AlertCircle className="h-4 w-4" />;
    default: return <Clock className="h-4 w-4" />;
  }
};

// Leave Application Card Component
interface LeaveApplicationCardProps {
  application: LeaveApplication;
  isCurrentUser: boolean;
}

const LeaveApplicationCard: React.FC<LeaveApplicationCardProps> = ({ application, isCurrentUser }) => {
  const [showActions, setShowActions] = useState(false);

  return (
    <Card className="relative hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        {/* Actions Menu */}
        <div className="absolute top-4 right-4">
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-1 rounded-full hover:bg-gray-100"
            >
              <MoreHorizontal className="h-4 w-4 text-gray-500" />
            </button>

            {showActions && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </button>
                  {isCurrentUser && application.status === 'pending' && (
                    <>
                      <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Application
                      </button>
                      <button className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Cancel Application
                      </button>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Application Info */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <h4 className="text-lg font-medium text-gray-900">{application.leave_type_name}</h4>
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                getStatusColor(application.status)
              )}>
                {getStatusIcon(application.status)}
                <span className="ml-1 capitalize">{application.status}</span>
              </span>
            </div>
            <p className="text-sm text-gray-600 mb-2">{application.reason}</p>
            <div className="text-xs text-gray-500">
              Applied: {formatDate(application.applied_date)}
            </div>
          </div>

          <div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Start Date:</span>
                <span className="font-medium">{formatDate(application.start_date)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">End Date:</span>
                <span className="font-medium">{formatDate(application.end_date)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Business Days:</span>
                <span className="font-medium">{application.business_days}</span>
              </div>
            </div>
          </div>

          <div>
            {application.reviewer_name && (
              <div className="space-y-2">
                <div className="text-sm">
                  <span className="text-gray-600">Reviewed by:</span>
                  <span className="font-medium ml-1">{application.reviewer_name}</span>
                </div>
                {application.reviewed_date && (
                  <div className="text-xs text-gray-500">
                    {formatDate(application.reviewed_date)}
                  </div>
                )}
                {application.reviewer_comments && (
                  <div className="bg-gray-50 rounded-lg p-3 mt-2">
                    <div className="flex items-start">
                      <MessageSquare className="h-4 w-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
                      <p className="text-sm text-gray-700">{application.reviewer_comments}</p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

// Leave Balance View Component
interface LeaveBalanceViewProps {
  balances: LeaveBalance[];
  loading: boolean;
}

const LeaveBalanceView: React.FC<LeaveBalanceViewProps> = ({ balances, loading }) => {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {balances.map((balance) => (
        <Card key={balance.leave_type_id}>
          <div className="p-6">
            <h4 className="text-lg font-medium text-gray-900 mb-4">{balance.leave_type_name}</h4>

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Allocated:</span>
                <span className="text-lg font-semibold text-gray-900">{balance.total_allocated}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Used:</span>
                <span className="text-sm font-medium text-red-600">{balance.used}</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Pending:</span>
                <span className="text-sm font-medium text-yellow-600">{balance.pending}</span>
              </div>

              <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                <span className="text-sm font-medium text-gray-900">Remaining:</span>
                <span className="text-lg font-bold text-green-600">{balance.remaining}</span>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-orange-600 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${(balance.used / balance.total_allocated) * 100}%`
                  }}
                ></div>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {((balance.used / balance.total_allocated) * 100).toFixed(1)}% used
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

// Leave Types View Component
interface LeaveTypesViewProps {
  types: LeaveType[];
  loading: boolean;
}

const LeaveTypesView: React.FC<LeaveTypesViewProps> = ({ types, loading }) => {
  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {types.map((type) => (
        <Card key={type.id}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={cn(
                  "w-4 h-4 rounded-full",
                  type.color === 'blue' && "bg-blue-500",
                  type.color === 'red' && "bg-red-500",
                  type.color === 'green' && "bg-green-500",
                  type.color === 'pink' && "bg-pink-500"
                )}></div>
                <h4 className="text-lg font-medium text-gray-900">{type.name}</h4>
              </div>

              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <div>
                  <span className="font-medium">{type.days_allowed}</span> days/year
                </div>
                {type.carry_forward && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Carry Forward
                  </span>
                )}
                {type.requires_approval && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Requires Approval
                  </span>
                )}
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

// Time Off View Component
interface TimeOffViewProps {
  timeOffRecords: TimeOffRecord[];
  loading: boolean;
  isCurrentUser: boolean;
  onRequestTimeOff: () => void;
}

const TimeOffView: React.FC<TimeOffViewProps> = ({ timeOffRecords, loading, isCurrentUser, onRequestTimeOff }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('');

  const filteredRecords = timeOffRecords.filter(record => {
    const matchesSearch = record.reason.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.time_off_type.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = !filterStatus || record.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const getTimeOffIcon = (type: string) => {
    switch (type) {
      case 'half_day_morning':
      case 'half_day_afternoon': return <Sun className="h-5 w-5 text-yellow-600" />;
      case 'few_hours': return <Clock className="h-5 w-5 text-blue-600" />;
      case 'late_arrival': return <Coffee className="h-5 w-5 text-orange-600" />;
      case 'early_departure': return <MapPin className="h-5 w-5 text-green-600" />;
      default: return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const formatTimeOffType = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatTime = (time?: string) => {
    if (!time) return 'N/A';
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h4 className="text-lg font-medium text-gray-900">Time Off Requests</h4>
          <p className="text-sm text-gray-600">Manage partial day time off and flexible hours</p>
        </div>
        {isCurrentUser && (
          <Button variant="primary" onClick={onRequestTimeOff}>
            <Plus className="h-4 w-4 mr-2" />
            Request Time Off
          </Button>
        )}
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <div className="flex-1">
          <Input
            placeholder="Search time off requests..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="h-4 w-4" />}
          />
        </div>
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="">All Status</option>
          <option value="pending">Pending</option>
          <option value="approved">Approved</option>
          <option value="rejected">Rejected</option>
        </select>
      </div>

      {/* Time Off Records */}
      {loading ? (
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      ) : filteredRecords.length === 0 ? (
        <div className="text-center py-12">
          <Coffee className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No time off requests found
          </h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || filterStatus
              ? 'Try adjusting your search criteria.'
              : 'No time off requests have been submitted yet.'
            }
          </p>
          {isCurrentUser && (
            <Button variant="primary" onClick={onRequestTimeOff}>
              <Plus className="h-4 w-4 mr-2" />
              Request Time Off
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredRecords.map((record) => (
            <Card key={record.id} className="hover:shadow-md transition-shadow duration-200">
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <div className="flex items-center space-x-3 mb-2">
                      {getTimeOffIcon(record.time_off_type)}
                      <h5 className="text-lg font-medium text-gray-900">
                        {formatTimeOffType(record.time_off_type)}
                      </h5>
                    </div>
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={cn(
                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                        getStatusColor(record.status)
                      )}>
                        {getStatusIcon(record.status)}
                        <span className="ml-1 capitalize">{record.status}</span>
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">{record.reason}</p>
                  </div>

                  <div>
                    <h6 className="text-sm font-medium text-gray-900 mb-2">Date & Time</h6>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Date:</span>
                        <span className="font-medium">{formatDate(record.date)}</span>
                      </div>
                      {record.start_time && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Start:</span>
                          <span className="font-medium">{formatTime(record.start_time)}</span>
                        </div>
                      )}
                      {record.end_time && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">End:</span>
                          <span className="font-medium">{formatTime(record.end_time)}</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span className="text-gray-600">Hours:</span>
                        <span className="font-medium text-blue-600">{record.hours}h</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h6 className="text-sm font-medium text-gray-900 mb-2">Approval</h6>
                    <div className="space-y-1 text-sm">
                      {record.approved_by && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Approved by:</span>
                          <span className="font-medium">{record.approved_by}</span>
                        </div>
                      )}
                      {record.approved_date && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Approved:</span>
                          <span className="font-medium">{formatDate(record.approved_date)}</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span className="text-gray-600">Requested:</span>
                        <span className="font-medium">{formatDate(record.created_date)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-end">
                    {record.status === 'pending' && !isCurrentUser && (
                      <div className="space-x-2">
                        <Button variant="secondary" size="sm">
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Approve
                        </Button>
                        <Button variant="secondary" size="sm">
                          <XCircle className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

// Holidays View Component
interface HolidaysViewProps {
  holidays: Holiday[];
  loading: boolean;
}

const HolidaysView: React.FC<HolidaysViewProps> = ({ holidays, loading }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('');

  const filteredHolidays = holidays.filter(holiday => {
    const matchesSearch = holiday.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         holiday.description?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = !filterType || holiday.type === filterType;
    return matchesSearch && matchesType;
  });

  const getHolidayIcon = (type: string) => {
    switch (type) {
      case 'public': return <Gift className="h-5 w-5 text-blue-600" />;
      case 'company': return <Gift className="h-5 w-5 text-orange-600" />;
      case 'religious': return <Gift className="h-5 w-5 text-purple-600" />;
      case 'national': return <Gift className="h-5 w-5 text-red-600" />;
      default: return <Gift className="h-5 w-5 text-gray-600" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'public': return 'bg-blue-100 text-blue-800';
      case 'company': return 'bg-orange-100 text-orange-800';
      case 'religious': return 'bg-purple-100 text-purple-800';
      case 'national': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const sortedHolidays = filteredHolidays.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  return (
    <div className="space-y-4">
      {/* Header */}
      <div>
        <h4 className="text-lg font-medium text-gray-900">Company Holidays</h4>
        <p className="text-sm text-gray-600">View company holidays and public holidays</p>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <div className="flex-1">
          <Input
            placeholder="Search holidays..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="h-4 w-4" />}
          />
        </div>
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="">All Types</option>
          <option value="public">Public</option>
          <option value="company">Company</option>
          <option value="religious">Religious</option>
          <option value="national">National</option>
        </select>
      </div>

      {/* Holidays List */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      ) : sortedHolidays.length === 0 ? (
        <div className="text-center py-12">
          <Gift className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No holidays found
          </h3>
          <p className="text-gray-600">
            {searchQuery || filterType
              ? 'Try adjusting your search criteria.'
              : 'No holidays have been configured yet.'
            }
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {sortedHolidays.map((holiday) => (
            <Card key={holiday.id} className="hover:shadow-md transition-shadow duration-200">
              <div className="p-6">
                <div className="flex items-center space-x-3 mb-3">
                  {getHolidayIcon(holiday.type)}
                  <h5 className="text-lg font-medium text-gray-900">{holiday.name}</h5>
                </div>

                <div className="flex items-center space-x-2 mb-3">
                  <span className={cn(
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    getTypeColor(holiday.type)
                  )}>
                    {holiday.type.toUpperCase()}
                  </span>
                  {holiday.is_mandatory && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Mandatory
                    </span>
                  )}
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Date:</span>
                    <span className="font-medium">{formatDate(holiday.date)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Day:</span>
                    <span className="font-medium">
                      {new Date(holiday.date).toLocaleDateString('en-US', { weekday: 'long' })}
                    </span>
                  </div>
                </div>

                {holiday.description && (
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-700">{holiday.description}</p>
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default LeaveManagementTab;
