# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2021
# <PERSON> <<EMAIL>>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2021-06-23 07:20+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: English (Australia) (http://www.transifex.com/django/django/"
"language/en_AU/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: en_AU\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Advanced options"

msgid "Flat Pages"
msgstr "Flat Pages"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "Example: “/about/contact”. Make sure to have a leading slash."

msgid "URL is missing a leading slash."
msgstr "URL is missing a leading slash."

msgid "URL is missing a trailing slash."
msgstr "URL is missing a trailing slash."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Flatpage with url %(url)s already exists for site %(site)s"

msgid "title"
msgstr "title"

msgid "content"
msgstr "content"

msgid "enable comments"
msgstr "enable comments"

msgid "template name"
msgstr "template name"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."

msgid "registration required"
msgstr "registration required"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"If this is checked, only logged-in users will be able to view the page."

msgid "sites"
msgstr "sites"

msgid "flat page"
msgstr "flat page"

msgid "flat pages"
msgstr "flat pages"
