'use client';

import React, { useState } from 'react';
import {
  Bell,
  CheckCircle,
  AlertCircle,
  Clock,
  Users,
  Calendar,
  FileText,
  Settings,
  Filter,
  Search,
  Check,
  Trash2,
  Plus
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { formatDate } from '@/lib/utils';

const SupervisorNotificationsPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('all');
  const [selectedNotifications, setSelectedNotifications] = useState<number[]>([]);

  // Mock notifications data
  const notifications = [
    {
      id: 1,
      type: 'leave_request',
      title: 'Leave Request Pending Approval',
      message: '<PERSON> has submitted a leave request for Dec 25-27, 2024',
      timestamp: '2024-12-18T10:30:00Z',
      read: false,
      priority: 'high',
      category: 'approval',
      employee: '<PERSON>',
      action_required: true
    },
    {
      id: 2,
      type: 'overtime',
      title: 'Overtime Request Submitted',
      message: '<PERSON> has applied for overtime work this weekend',
      timestamp: '2024-12-18T09:15:00Z',
      read: false,
      priority: 'medium',
      category: 'approval',
      employee: '<PERSON> <PERSON>',
      action_required: true
    },
    {
      id: 3,
      type: 'system',
      title: 'Monthly Report Due',
      message: 'Your monthly team performance report is due in 3 days',
      timestamp: '2024-12-18T08:00:00Z',
      read: true,
      priority: 'medium',
      category: 'reminder',
      action_required: false
    },
    {
      id: 4,
      type: 'team_update',
      title: 'Team Meeting Scheduled',
      message: 'Weekly team meeting scheduled for Friday at 2:00 PM',
      timestamp: '2024-12-17T16:45:00Z',
      read: true,
      priority: 'low',
      category: 'information',
      action_required: false
    },
    {
      id: 5,
      type: 'performance',
      title: 'Performance Review Reminder',
      message: 'Performance reviews for Q4 are due next week',
      timestamp: '2024-12-17T14:20:00Z',
      read: false,
      priority: 'high',
      category: 'reminder',
      action_required: true
    }
  ];

  const notificationStats = {
    total: notifications.length,
    unread: notifications.filter(n => !n.read).length,
    pending_approvals: notifications.filter(n => n.action_required && n.category === 'approval').length,
    high_priority: notifications.filter(n => n.priority === 'high').length
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'leave_request':
        return <Calendar className="h-5 w-5" />;
      case 'overtime':
        return <Clock className="h-5 w-5" />;
      case 'system':
        return <Settings className="h-5 w-5" />;
      case 'team_update':
        return <Users className="h-5 w-5" />;
      case 'performance':
        return <FileText className="h-5 w-5" />;
      default:
        return <Bell className="h-5 w-5" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 bg-red-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    switch (activeTab) {
      case 'unread':
        return !notification.read;
      case 'approvals':
        return notification.category === 'approval';
      case 'reminders':
        return notification.category === 'reminder';
      default:
        return true;
    }
  });

  const handleSelectNotification = (id: number) => {
    setSelectedNotifications(prev =>
      prev.includes(id)
        ? prev.filter(nId => nId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(filteredNotifications.map(n => n.id));
    }
  };

  const tabs = [
    { id: 'all', label: 'All Notifications', count: notifications.length },
    { id: 'unread', label: 'Unread', count: notificationStats.unread },
    { id: 'approvals', label: 'Pending Approvals', count: notificationStats.pending_approvals },
    { id: 'reminders', label: 'Reminders', count: notifications.filter(n => n.category === 'reminder').length }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Notifications</h1>
          <p className="text-gray-600 mt-1">
            Manage your notifications and stay updated on team activities
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
          <Button variant="primary">
            <Plus className="h-4 w-4 mr-2" />
            Send Announcement
          </Button>
        </div>
      </div>

      {/* Notification Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Bell className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-lg font-semibold text-gray-900">{notificationStats.total}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <AlertCircle className="h-5 w-5 text-orange-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Unread</p>
                <p className="text-lg font-semibold text-gray-900">{notificationStats.unread}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <Clock className="h-5 w-5 text-red-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Pending Approvals</p>
                <p className="text-lg font-semibold text-gray-900">{notificationStats.pending_approvals}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <AlertCircle className="h-5 w-5 text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">High Priority</p>
                <p className="text-lg font-semibold text-gray-900">{notificationStats.high_priority}</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                activeTab === tab.id
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
              {tab.count > 0 && (
                <span className="ml-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Notifications List */}
      <Card>
        <div className="p-6">
          {/* Search and Actions */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search notifications..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"
                />
              </div>
              <Button variant="secondary" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {selectedNotifications.length > 0 && (
              <div className="flex items-center space-x-2">
                <Button variant="secondary" size="sm">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Mark as Read
                </Button>
                <Button variant="secondary" size="sm">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            )}
          </div>

          {/* Select All */}
          <div className="flex items-center justify-between mb-4 pb-4 border-b border-gray-200">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={selectedNotifications.length === filteredNotifications.length && filteredNotifications.length > 0}
                onChange={handleSelectAll}
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">
                Select all ({filteredNotifications.length})
              </span>
            </label>

            <Button variant="secondary" size="sm">
              <CheckCircle className="h-4 w-4 mr-2" />
              Mark All as Read
            </Button>
          </div>

          {/* Notifications */}
          <div className="space-y-4">
            {filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`flex items-start space-x-4 p-4 rounded-lg border transition-colors ${
                  notification.read ? 'bg-white border-gray-200' : 'bg-blue-50 border-blue-200'
                } hover:shadow-md`}
              >
                <input
                  type="checkbox"
                  checked={selectedNotifications.includes(notification.id)}
                  onChange={() => handleSelectNotification(notification.id)}
                  className="mt-1 rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                />

                <div className={`p-2 rounded-lg ${getPriorityColor(notification.priority)}`}>
                  {getNotificationIcon(notification.type)}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className={`font-medium ${notification.read ? 'text-gray-900' : 'text-gray-900 font-semibold'}`}>
                      {notification.title}
                    </h4>
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(notification.priority)}`}>
                        {notification.priority}
                      </span>
                      <span className="text-xs text-gray-500">
                        {formatDate(notification.timestamp, 'MMM dd, HH:mm')}
                      </span>
                    </div>
                  </div>
                  <p className="text-gray-600 text-sm mb-2">{notification.message}</p>

                  {notification.employee && (
                    <p className="text-xs text-gray-500 mb-2">Employee: {notification.employee}</p>
                  )}

                  {notification.action_required && (
                    <div className="flex items-center space-x-2">
                      <Button variant="primary" size="sm">
                        Take Action
                      </Button>
                      <Button variant="secondary" size="sm">
                        View Details
                      </Button>
                    </div>
                  )}
                </div>

                {!notification.read && (
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                )}
              </div>
            ))}
          </div>

          {filteredNotifications.length === 0 && (
            <div className="text-center py-12">
              <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
              <p className="text-gray-600">
                {activeTab === 'all'
                  ? "You're all caught up! No new notifications."
                  : `No ${activeTab} notifications at the moment.`
                }
              </p>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default SupervisorNotificationsPage;
