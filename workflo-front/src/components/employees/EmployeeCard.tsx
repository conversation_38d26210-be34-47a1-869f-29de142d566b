'use client';

import React, { useState } from 'react';
import { 
  Eye, 
  Edit, 
  Trash2, 
  Mail, 
  Phone, 
  Building, 
  Calendar, 
  DollarSign,
  MoreHorizontal,
  CheckSquare,
  Square,
  Crown,
  Shield,
  User
} from 'lucide-react';
import Link from 'next/link';
import { cn, getInitials, formatDate, formatCurrency } from '@/lib/utils';

interface Employee {
  id: number;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  profile_picture?: string;
  department_name: string;
  position: string;
  hire_date: string;
  salary: number;
  employment_type: 'full_time' | 'part_time' | 'contract' | 'intern';
  status: 'active' | 'inactive' | 'on_leave' | 'terminated';
  role: 'employee' | 'supervisor' | 'hr' | 'accountant' | 'admin';
}

interface EmployeeCardProps {
  employee: Employee;
  selected: boolean;
  onSelect: (selected: boolean) => void;
  onView: () => void;
  onEdit: () => void;
  onDelete: () => void;
  viewMode: 'grid' | 'list';
}

const EmployeeCard: React.FC<EmployeeCardProps> = ({
  employee,
  selected,
  onSelect,
  onView,
  onEdit,
  onDelete,
  viewMode
}) => {
  const [showActions, setShowActions] = useState(false);
  const userInitials = getInitials(employee.first_name, employee.last_name);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'on_leave': return 'bg-yellow-100 text-yellow-800';
      case 'terminated': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return <Shield className="h-3 w-3" />;
      case 'supervisor': return <Crown className="h-3 w-3" />;
      default: return <User className="h-3 w-3" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800';
      case 'supervisor': return 'bg-purple-100 text-purple-800';
      case 'hr': return 'bg-blue-100 text-blue-800';
      case 'accountant': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (viewMode === 'list') {
    return (
      <div className={cn(
        "flex items-center p-4 border border-gray-200 rounded-lg hover:shadow-md transition-all duration-200",
        selected && "ring-2 ring-orange-500 border-orange-500"
      )}>
        {/* Selection Checkbox */}
        <button
          onClick={() => onSelect(!selected)}
          className="mr-4 p-1 hover:bg-gray-100 rounded"
        >
          {selected ? (
            <CheckSquare className="h-4 w-4 text-orange-600" />
          ) : (
            <Square className="h-4 w-4 text-gray-400" />
          )}
        </button>

        {/* Profile Picture */}
        <div className="flex-shrink-0 mr-4">
          {employee.profile_picture ? (
            <img
              src={employee.profile_picture}
              alt={`${employee.first_name} ${employee.last_name}`}
              className="w-12 h-12 rounded-full object-cover"
            />
          ) : (
            <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-medium">{userInitials}</span>
            </div>
          )}
        </div>

        {/* Employee Info */}
        <div className="flex-1 grid grid-cols-1 md:grid-cols-5 gap-4 items-center">
          <div>
            <Link href={`/employees/${employee.id}`} className="hover:text-orange-600">
              <h3 className="text-sm font-semibold text-gray-900">
                {employee.first_name} {employee.last_name}
              </h3>
            </Link>
            <p className="text-xs text-gray-600">{employee.employee_id}</p>
          </div>

          <div>
            <p className="text-sm text-gray-900">{employee.position}</p>
            <p className="text-xs text-gray-600">{employee.department_name}</p>
          </div>

          <div>
            <p className="text-sm text-gray-900">{employee.email}</p>
            {employee.phone_number && (
              <p className="text-xs text-gray-600">{employee.phone_number}</p>
            )}
          </div>

          <div className="flex flex-col space-y-1">
            <span className={cn(
              'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium w-fit',
              getStatusColor(employee.status)
            )}>
              {employee.status.replace('_', ' ').toUpperCase()}
            </span>
            <span className={cn(
              'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium w-fit',
              getRoleColor(employee.role)
            )}>
              {getRoleIcon(employee.role)}
              <span className="ml-1 capitalize">{employee.role}</span>
            </span>
          </div>

          <div className="text-sm text-gray-900">
            {formatCurrency(employee.salary)}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2 ml-4">
          <Link href={`/employees/${employee.id}`}>
            <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors">
              <Eye className="h-4 w-4" />
            </button>
          </Link>
          <button 
            onClick={onEdit}
            className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-full transition-colors"
          >
            <Edit className="h-4 w-4" />
          </button>
          <button 
            onClick={onDelete}
            className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </div>
    );
  }

  // Grid view
  return (
    <div className={cn(
      "bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-all duration-200 relative",
      selected && "ring-2 ring-orange-500 border-orange-500"
    )}>
      {/* Selection Checkbox */}
      <button
        onClick={() => onSelect(!selected)}
        className="absolute top-4 left-4 p-1 hover:bg-gray-100 rounded"
      >
        {selected ? (
          <CheckSquare className="h-4 w-4 text-orange-600" />
        ) : (
          <Square className="h-4 w-4 text-gray-400" />
        )}
      </button>

      {/* Actions Menu */}
      <div className="absolute top-4 right-4">
        <div className="relative">
          <button
            onClick={() => setShowActions(!showActions)}
            className="p-1 rounded-full hover:bg-gray-100"
          >
            <MoreHorizontal className="h-4 w-4 text-gray-500" />
          </button>

          {showActions && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
              <div className="py-1">
                <Link href={`/employees/${employee.id}`}>
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <Eye className="h-4 w-4 mr-2" />
                    View Profile
                  </button>
                </Link>
                <button 
                  onClick={() => {
                    onEdit();
                    setShowActions(false);
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Employee
                </button>
                <button 
                  onClick={() => {
                    onDelete();
                    setShowActions(false);
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Employee
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Employee Info */}
      <div className="mt-8">
        {/* Profile Picture */}
        <div className="flex items-center mb-4">
          {employee.profile_picture ? (
            <img
              src={employee.profile_picture}
              alt={`${employee.first_name} ${employee.last_name}`}
              className="w-16 h-16 rounded-full object-cover mr-4"
            />
          ) : (
            <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mr-4">
              <span className="text-white text-lg font-medium">{userInitials}</span>
            </div>
          )}
          <div className="flex-1">
            <Link href={`/employees/${employee.id}`} className="hover:text-orange-600">
              <h3 className="text-lg font-semibold text-gray-900">
                {employee.first_name} {employee.last_name}
              </h3>
            </Link>
            <p className="text-sm text-gray-600">{employee.employee_id}</p>
          </div>
        </div>

        {/* Position & Department */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-900">{employee.position}</h4>
          <p className="text-sm text-gray-600 flex items-center mt-1">
            <Building className="h-3 w-3 mr-1" />
            {employee.department_name}
          </p>
        </div>

        {/* Contact Info */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-600">
            <Mail className="h-3 w-3 mr-2" />
            <span className="truncate">{employee.email}</span>
          </div>
          {employee.phone_number && (
            <div className="flex items-center text-sm text-gray-600">
              <Phone className="h-3 w-3 mr-2" />
              <span>{employee.phone_number}</span>
            </div>
          )}
        </div>

        {/* Status & Role */}
        <div className="flex items-center space-x-2 mb-4">
          <span className={cn(
            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
            getStatusColor(employee.status)
          )}>
            {employee.status.replace('_', ' ').toUpperCase()}
          </span>
          <span className={cn(
            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
            getRoleColor(employee.role)
          )}>
            {getRoleIcon(employee.role)}
            <span className="ml-1 capitalize">{employee.role}</span>
          </span>
        </div>

        {/* Additional Info */}
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center justify-between">
            <span>Salary:</span>
            <span className="font-medium text-gray-900">{formatCurrency(employee.salary)}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Hire Date:</span>
            <span className="font-medium text-gray-900">{formatDate(employee.hire_date)}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Type:</span>
            <span className="font-medium text-gray-900 capitalize">
              {employee.employment_type.replace('_', ' ')}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeCard;
