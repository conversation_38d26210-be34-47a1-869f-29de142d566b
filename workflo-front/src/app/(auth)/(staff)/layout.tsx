'use client';

import React from 'react';
import StaffLayout from '@/components/layout/StaffLayout';
import RouteTransition from '@/components/RouteTransition';
import { StaffGuard } from '@/components/auth/RoleGuard';

interface StaffLayoutProps {
  children: React.ReactNode;
}

const StaffLayoutWrapper: React.FC<StaffLayoutProps> = ({ children }) => {
  return (
    <StaffGuard>
      <StaffLayout>
        <RouteTransition>
          {children}
        </RouteTransition>
      </StaffLayout>
    </StaffGuard>
  );
};

export default StaffLayoutWrapper;
