'use client';

import React, { useMemo, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Home,
  User,
  Building,
  Calendar,
  FileText,
  Star,
  Settings,
  X,
  LogOut,
  Plus,
  Clock,
  DollarSign,
  Award,
  Activity,
  Bell,
  Shield,
  Edit,
  Eye,
  Download,
  Briefcase,
  Coffee,
  Target,
  ChevronRight,
  Info,
  Ticket,
  Cog
} from 'lucide-react';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials, formatDate } from '@/lib/utils';

interface StaffSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

// Define page-specific features and actions for staff
const getStaffPageFeatures = (pathname: string) => {
  switch (pathname) {
    case '/staff':
      return {
        title: 'Dashboard',
        breadcrumb: 'Staff Dashboard',
        quickActions: [
          { name: 'Apply Leave', icon: Calendar, href: '/staff/info/leave-management?action=apply', badge: '15 days', description: 'Submit leave request' },
          { name: 'Check In/Out', icon: Clock, href: '/staff/info/attendance?action=checkin', description: 'Mark attendance' },
          { name: 'View Payslip', icon: DollarSign, href: '/staff/info/salary?view=payslip', description: 'Download latest payslip' },
          { name: 'Update Profile', icon: User, href: '/staff/info/profile?action=edit', description: 'Edit personal information' },
          { name: 'Submit Timesheet', icon: FileText, href: '/staff/info/attendance?action=timesheet', description: 'Log work hours' },
          { name: 'View Performance', icon: Star, href: '/staff/info/performance', description: 'Check performance metrics' },
        ],
        features: [
          { name: 'Today\'s Overview', icon: Activity, description: 'Current day summary and stats' },
          { name: 'Recent Activities', icon: Clock, description: 'Latest actions and updates' },
          { name: 'Quick Actions', icon: Plus, description: 'Fast access to common tasks' },
          { name: 'Upcoming Events', icon: Calendar, description: 'Scheduled meetings and deadlines' },
          { name: 'Attendance Status', icon: Target, description: 'Real-time attendance tracking' },
          { name: 'Leave Balance', icon: Coffee, description: 'Available leave days' },
        ]
      };

    case '/staff/info':
      return {
        title: 'Info',
        breadcrumb: 'Personal Information Hub',
        quickActions: [
          { name: 'Edit Profile', icon: Edit, href: '/staff/info/profile?action=edit' },
          { name: 'View Documents', icon: FileText, href: '/staff/info/documents' },
          { name: 'Check Leave Balance', icon: Calendar, href: '/staff/info/leave-management' },
          { name: 'View Performance', icon: Star, href: '/staff/info/performance' },
        ],
        features: [
          { name: 'Profile Management', icon: User },
          { name: 'Employment Details', icon: Briefcase },
          { name: 'Salary Information', icon: DollarSign },
          { name: 'Leave Management', icon: Calendar },
          { name: 'Document Center', icon: FileText },
          { name: 'Performance Reviews', icon: Star },
          { name: 'Time & Attendance', icon: Clock },
        ]
      };

    case '/staff/company':
      return {
        title: 'Company',
        breadcrumb: 'Company Information',
        quickActions: [
          { name: 'View Directory', icon: User, href: '/staff/company?view=directory' },
          { name: 'Company Policies', icon: FileText, href: '/staff/company?view=policies' },
          { name: 'Organization Chart', icon: Building, href: '/staff/company?view=org-chart' },
          { name: 'Contact Info', icon: Bell, href: '/staff/company?view=contact' },
        ],
        features: [
          { name: 'Company Overview', icon: Building },
          { name: 'Employee Directory', icon: User },
          { name: 'Policies & Procedures', icon: FileText },
          { name: 'Organization Structure', icon: Target },
        ]
      };

    case '/staff/ticket':
      return {
        title: 'Ticket',
        breadcrumb: 'Support & Requests',
        quickActions: [
          { name: 'Create Ticket', icon: Plus, href: '/staff/ticket?action=create' },
          { name: 'View My Tickets', icon: Eye, href: '/staff/ticket?view=my-tickets' },
          { name: 'IT Support', icon: Settings, href: '/staff/ticket?category=it' },
          { name: 'HR Request', icon: User, href: '/staff/ticket?category=hr' },
        ],
        features: [
          { name: 'Support Tickets', icon: Ticket },
          { name: 'Request Tracking', icon: Activity },
          { name: 'Knowledge Base', icon: FileText },
          { name: 'FAQ & Help', icon: Info },
        ]
      };

    case '/staff/settings':
      return {
        title: 'Settings',
        breadcrumb: 'Personal Settings',
        quickActions: [
          { name: 'Account Settings', icon: User, href: '/staff/settings?tab=account' },
          { name: 'Notifications', icon: Bell, href: '/staff/settings?tab=notifications' },
          { name: 'Privacy', icon: Shield, href: '/staff/settings?tab=privacy' },
          { name: 'Change Password', icon: Settings, href: '/staff/settings?tab=password' },
        ],
        features: [
          { name: 'Account Management', icon: User },
          { name: 'Notification Preferences', icon: Bell },
          { name: 'Privacy Settings', icon: Shield },
          { name: 'Security Options', icon: Settings },
        ]
      };

    default:
      return {
        title: 'Dashboard',
        breadcrumb: 'Staff Dashboard',
        quickActions: [],
        features: []
      };
  }
};

const StaffDynamicSidebar: React.FC<StaffSidebarProps> = ({ isOpen, onClose }) => {
  const pathname = usePathname();
  const { user, logout } = useAuth();

  // Memoize page features to prevent recalculation
  const pageFeatures = useMemo(() => getStaffPageFeatures(pathname), [pathname]);

  // Memoize user-related data
  const userInitials = useMemo(() =>
    user ? getInitials(user.first_name, user.last_name) : 'U',
    [user?.first_name, user?.last_name]
  );

  const currentDate = useMemo(() =>
    formatDate(new Date().toISOString(), 'EEE, MMM dd, yyyy'),
    []
  );

  // Memoize close handler
  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  // Memoize logout handler
  const handleLogout = useCallback(() => {
    logout();
    onClose();
  }, [logout, onClose]);

  // Info section areas - Always available on info pages
  const infoSectionAreas = useMemo(() => [
    {
      name: 'Profile',
      href: '/staff/info/profile',
      icon: User,
      description: 'Personal information and contact details'
    },
    {
      name: 'Employment',
      href: '/staff/info/employment',
      icon: Briefcase,
      description: 'Job details and employment history'
    },
    {
      name: 'Salary',
      href: '/staff/info/salary',
      icon: DollarSign,
      description: 'Salary information and payslips'
    },
    {
      name: 'Leave Management',
      href: '/staff/info/leave-management',
      icon: Calendar,
      description: 'Leave applications and balance'
    },
    {
      name: 'Documents',
      href: '/staff/info/documents',
      icon: FileText,
      description: 'Personal and employment documents'
    },
    {
      name: 'Performance',
      href: '/staff/info/performance',
      icon: Star,
      description: 'Performance reviews and goals'
    },
    {
      name: 'Attendance',
      href: '/staff/info/attendance',
      icon: Clock,
      description: 'Attendance and time tracking'
    }
  ], []);

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={handleClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-50 bg-white shadow-lg transform transition-transform duration-300 ease-in-out md:relative md:translate-x-0 md:z-10 md:top-0 md:h-full',
          isOpen ? 'translate-x-0' : '-translate-x-full'
        )}
        style={{ width: '368px' }}
      >
        {/* Mobile close button */}
        <div className="md:hidden absolute top-3 right-3 z-20">
          <button
            onClick={handleClose}
            className="p-2 rounded-md text-gray-400 hover:text-gray-600 bg-white shadow-sm border border-gray-200 touch-manipulation"
            aria-label="Close sidebar"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Sidebar content */}
        <div className="flex flex-col h-full overflow-y-auto">
          {/* Dynamic Breadcrumb and Title */}
          <div className="p-4 sm:p-6 border-b border-gray-200 pt-12 md:pt-6">
            <nav className="text-xs sm:text-sm text-gray-500 mb-2">
              <Link href="/staff" className="hover:text-gray-700 touch-manipulation">
                Staff Home
              </Link>
              <span className="mx-2">/</span>
              <span className="text-gray-900">{pageFeatures.title}</span>
            </nav>
            <h1 className="text-lg sm:text-xl font-semibold text-gray-900 leading-tight">
              {pageFeatures.breadcrumb}
            </h1>
          </div>

          {/* User Card */}
          <div className="p-4 sm:p-6 border-b border-gray-200">
            <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg border border-orange-200 p-3 sm:p-4 text-center">
              <div className="mb-3 sm:mb-4">
                {user?.profile_picture ? (
                  <img
                    src={user.profile_picture}
                    alt="Profile"
                    className="w-14 h-14 sm:w-16 sm:h-16 rounded-full mx-auto object-cover border-2 border-orange-300"
                  />
                ) : (
                  <div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full mx-auto flex items-center justify-center border-2 border-orange-300 shadow-md">
                    <span className="text-white text-lg sm:text-xl font-medium">
                      {userInitials}
                    </span>
                  </div>
                )}
              </div>
              <div>
                <h3 className="font-semibold text-orange-900 text-sm sm:text-base">
                  Welcome {user?.first_name || 'Staff'}
                </h3>
                <p className="text-xs sm:text-sm text-orange-700 mt-1">{currentDate}</p>
                {user?.employee_id && (
                  <p className="text-xs text-orange-600 mt-1">ID: {user.employee_id}</p>
                )}
                <div className="flex items-center justify-center mt-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-xs text-green-700 font-medium">Active</span>
                </div>
              </div>
            </div>
          </div>

          {/* Page-Specific Quick Actions */}
          {pageFeatures.quickActions.length > 0 && (
            <div className="p-4 sm:p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
              <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg border border-orange-200">
                <div className="p-3 sm:p-4">
                  <div className="flex items-center mb-3">
                    <Plus className="h-4 w-4 text-orange-600 mr-2" />
                    <h3 className="font-medium text-orange-900 text-sm sm:text-base">Quick Actions</h3>
                  </div>
                  <div className="space-y-2">
                    {pageFeatures.quickActions.map((action, index) => {
                      const Icon = action.icon;
                      return (
                        <Link
                          key={`${pathname}-${index}`}
                          href={action.href}
                          onClick={handleClose}
                          className="flex items-center justify-between w-full text-left py-2.5 sm:py-2 px-3 rounded-md text-sm font-medium text-gray-700 hover:bg-white hover:shadow-sm transition-all duration-150 ease-in-out touch-manipulation group"
                        >
                          <div className="flex items-center min-w-0 flex-1">
                            <Icon className="h-4 w-4 mr-2 flex-shrink-0 text-orange-500 group-hover:text-orange-600" />
                            <div className="min-w-0 flex-1">
                              <span className="truncate block">{action.name}</span>
                              {'description' in action && action.description && (
                                <span className="text-xs text-gray-500 truncate block">{action.description}</span>
                              )}
                            </div>
                          </div>
                          {'badge' in action && action.badge && (
                            <span className="ml-2 px-2 py-1 text-xs bg-orange-200 text-orange-800 rounded-full flex-shrink-0">
                              {action.badge}
                            </span>
                          )}
                          <ChevronRight className="h-3 w-3 text-gray-400 ml-2 flex-shrink-0 group-hover:text-gray-600" />
                        </Link>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Info Section Areas - Show on all info pages */}
          {pathname.startsWith('/staff/info') && (
            <div className="p-4 sm:p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
              <div className="bg-white rounded-lg border border-gray-200">
                <div className="p-3 sm:p-4">
                  <h3 className="font-medium text-gray-900 mb-3 text-sm sm:text-base">Information Sections</h3>
                  <div className="space-y-1">
                    {infoSectionAreas.map((area, index) => {
                      const Icon = area.icon;
                      const isCurrentPage = pathname === area.href;

                      return (
                        <Link
                          key={`info-area-${index}`}
                          href={area.href}
                          onClick={handleClose}
                          className={cn(
                            "flex items-center py-2.5 sm:py-2 px-3 rounded-md text-sm transition-all duration-150 ease-in-out touch-manipulation",
                            isCurrentPage
                              ? "bg-orange-50 text-orange-700 border-l-2 border-orange-500"
                              : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                          )}
                        >
                          <Icon className={cn(
                            "h-4 w-4 mr-2 flex-shrink-0",
                            isCurrentPage ? "text-orange-500" : "text-gray-400"
                          )} />
                          <span className="truncate">{area.name}</span>
                        </Link>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Page Features - Show for non-info pages */}
          {!pathname.startsWith('/staff/info') && pageFeatures.features.length > 0 && (
            <div className="p-4 sm:p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
              <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="p-3 sm:p-4">
                  <div className="flex items-center mb-3">
                    <Activity className="h-4 w-4 text-blue-600 mr-2" />
                    <h3 className="font-medium text-gray-900 text-sm sm:text-base">Dashboard Features</h3>
                  </div>
                  <div className="grid grid-cols-1 gap-2">
                    {pageFeatures.features.map((feature, index) => {
                      const Icon = feature.icon;
                      return (
                        <div
                          key={`${pathname}-feature-${index}`}
                          className="flex items-start py-2.5 px-3 rounded-md text-sm transition-all duration-150 ease-in-out hover:bg-blue-50 group"
                        >
                          <Icon className="h-4 w-4 mr-3 text-blue-500 flex-shrink-0 mt-0.5 group-hover:text-blue-600" />
                          <div className="min-w-0 flex-1">
                            <span className="text-gray-900 font-medium block">{feature.name}</span>
                            {'description' in feature && feature.description && (
                              <span className="text-xs text-gray-500 block mt-0.5">{feature.description}</span>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Dashboard Stats - Show only on home page */}
          {pathname === '/staff' && (
            <div className="p-4 sm:p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                <div className="p-3 sm:p-4">
                  <div className="flex items-center mb-3">
                    <Target className="h-4 w-4 text-blue-600 mr-2" />
                    <h3 className="font-medium text-blue-900 text-sm sm:text-base">Today's Summary</h3>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between py-2 px-3 bg-white rounded-md">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm text-gray-700">Check-in Status</span>
                      </div>
                      <span className="text-sm font-medium text-green-600">08:30 AM</span>
                    </div>
                    <div className="flex items-center justify-between py-2 px-3 bg-white rounded-md">
                      <div className="flex items-center">
                        <Activity className="h-4 w-4 text-blue-500 mr-2" />
                        <span className="text-sm text-gray-700">Hours Today</span>
                      </div>
                      <span className="text-sm font-medium text-blue-600">6.5h</span>
                    </div>
                    <div className="flex items-center justify-between py-2 px-3 bg-white rounded-md">
                      <div className="flex items-center">
                        <Coffee className="h-4 w-4 text-orange-500 mr-2" />
                        <span className="text-sm text-gray-700">Leave Balance</span>
                      </div>
                      <span className="text-sm font-medium text-orange-600">15 days</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Dashboard Stats - Show only on home page */}
          {pathname === '/staff' && (
            <div className="p-4 sm:p-6 border-b border-gray-200 transition-all duration-200 ease-in-out">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                <div className="p-3 sm:p-4">
                  <div className="flex items-center mb-3">
                    <Target className="h-4 w-4 text-blue-600 mr-2" />
                    <h3 className="font-medium text-blue-900 text-sm sm:text-base">Today's Summary</h3>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between py-2 px-3 bg-white rounded-md">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm text-gray-700">Check-in Status</span>
                      </div>
                      <span className="text-sm font-medium text-green-600">08:30 AM</span>
                    </div>
                    <div className="flex items-center justify-between py-2 px-3 bg-white rounded-md">
                      <div className="flex items-center">
                        <Activity className="h-4 w-4 text-blue-500 mr-2" />
                        <span className="text-sm text-gray-700">Hours Today</span>
                      </div>
                      <span className="text-sm font-medium text-blue-600">6.5h</span>
                    </div>
                    <div className="flex items-center justify-between py-2 px-3 bg-white rounded-md">
                      <div className="flex items-center">
                        <Coffee className="h-4 w-4 text-orange-500 mr-2" />
                        <span className="text-sm text-gray-700">Leave Balance</span>
                      </div>
                      <span className="text-sm font-medium text-orange-600">15 days</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Spacer for better layout */}
          <div className="flex-1"></div>

          {/* User Actions */}
          <div className="p-4 sm:p-6 border-t border-gray-200 space-y-1 mt-auto">
            <Link
              href="/staff/info/profile"
              onClick={handleClose}
              className="flex items-center px-3 py-2.5 sm:py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors touch-manipulation"
            >
              <User className="h-5 w-5 mr-3 flex-shrink-0" />
              <span>Profile</span>
            </Link>
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-3 py-2.5 sm:py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors touch-manipulation"
            >
              <LogOut className="h-5 w-5 mr-3 flex-shrink-0" />
              <span>Logout</span>
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default StaffDynamicSidebar;
