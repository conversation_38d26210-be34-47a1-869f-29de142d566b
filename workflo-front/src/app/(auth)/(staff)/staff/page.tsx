'use client';

import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Clock,
  User,
  FileText,
  TrendingUp,
  CheckCircle,
  Target,
  Activity,
  Users,
  Wifi,
  WifiOff
} from 'lucide-react';
import Card from '@/components/ui/Card';
import { useAuth } from '@/providers/AuthProvider';
import { cn, formatDate } from '@/lib/utils';
import { useBiostarAttendance } from '@/hooks/useBiostarAttendance';

const StaffDashboard: React.FC = () => {
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());

  // Use BioStar attendance hook
  const {
    summary: attendanceSummary,
    realtimeUpdates,
    connected: biostarConnected,
    loading
  } = useBiostarAttendance({
    employeeId: user?.employee_id,
    autoRefresh: true,
    enableRealTime: true
  });

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const todayStats = {
    checkInTime: attendanceSummary?.checkInTime ?
      new Date(attendanceSummary.checkInTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) :
      'Not checked in',
    hoursWorked: attendanceSummary?.hoursWorked?.toFixed(1) || '0.0',
    tasksCompleted: 4,
    meetingsToday: 2,
    leaveBalance: 15,
    pendingApprovals: 1,
    attendanceStatus: attendanceSummary?.todayStatus || 'ABSENT',
    weeklyHours: attendanceSummary?.weeklyHours?.toFixed(1) || '0.0',
    monthlyAttendance: attendanceSummary?.monthlyAttendance || 0
  };

  const recentActivities = [
    {
      id: 1,
      type: 'check_in',
      description: 'Checked in for the day',
      time: '08:30 AM',
      icon: Clock,
      color: 'text-green-500'
    },
    {
      id: 2,
      type: 'task',
      description: 'Completed project review',
      time: '10:15 AM',
      icon: CheckCircle,
      color: 'text-blue-500'
    },
    {
      id: 3,
      type: 'meeting',
      description: 'Team standup meeting',
      time: '11:00 AM',
      icon: Users,
      color: 'text-purple-500'
    },
    {
      id: 4,
      type: 'document',
      description: 'Updated timesheet',
      time: '02:30 PM',
      icon: FileText,
      color: 'text-orange-500'
    }
  ];

  const upcomingEvents = [
    {
      id: 1,
      title: 'Project Review Meeting',
      time: '3:00 PM',
      type: 'meeting',
      participants: 5
    },
    {
      id: 2,
      title: 'Training Session: React Best Practices',
      time: 'Tomorrow 10:00 AM',
      type: 'training',
      duration: '2 hours'
    },
    {
      id: 3,
      title: 'Performance Review',
      time: 'Friday 2:00 PM',
      type: 'review',
      with: 'Manager'
    }
  ];

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-4 sm:p-6 text-white">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex-1">
            <h1 className="text-xl sm:text-2xl font-bold leading-tight">
              Good {currentTime.getHours() < 12 ? 'Morning' : currentTime.getHours() < 18 ? 'Afternoon' : 'Evening'}, {user?.first_name}!
            </h1>
            <p className="text-orange-100 mt-1 text-sm sm:text-base">
              {formatDate(new Date().toISOString(), 'EEEE, MMMM dd, yyyy')}
            </p>
            <div className="flex flex-col sm:flex-row sm:items-center mt-3 sm:mt-2 space-y-2 sm:space-y-0 sm:space-x-4">
              <div className="flex items-center space-x-1">
                {biostarConnected ? (
                  <Wifi className="h-4 w-4 text-green-300" />
                ) : (
                  <WifiOff className="h-4 w-4 text-red-300" />
                )}
                <span className="text-sm text-orange-100">
                  BioStar {biostarConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  todayStats.attendanceStatus === 'PRESENT' ? 'bg-green-300' :
                  todayStats.attendanceStatus === 'LATE' ? 'bg-yellow-300' :
                  todayStats.attendanceStatus === 'EARLY_OUT' ? 'bg-orange-300' :
                  'bg-red-300'
                )} />
                <span className="text-sm text-orange-100">
                  {todayStats.attendanceStatus}
                </span>
              </div>
            </div>
          </div>
          <div className="text-center sm:text-right">
            <div className="text-2xl sm:text-3xl font-bold">
              {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </div>
            <div className="text-orange-100 text-sm">
              Current Time
            </div>
            {loading && (
              <div className="text-orange-100 text-xs mt-1">
                Loading attendance...
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Today's Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <Card>
          <div className="p-4 sm:p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg flex-shrink-0">
                <Clock className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
              </div>
              <div className="ml-3 sm:ml-4 min-w-0 flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-600 truncate">Check-in Time</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900 truncate">{todayStats.checkInTime}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4 sm:p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                <Activity className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
              </div>
              <div className="ml-3 sm:ml-4 min-w-0 flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-600 truncate">Hours Worked</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900 truncate">{todayStats.hoursWorked}h</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4 sm:p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg flex-shrink-0">
                <Target className="h-5 w-5 sm:h-6 sm:w-6 text-purple-600" />
              </div>
              <div className="ml-3 sm:ml-4 min-w-0 flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-600 truncate">Tasks Completed</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900 truncate">{todayStats.tasksCompleted}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4 sm:p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg flex-shrink-0">
                <Calendar className="h-5 w-5 sm:h-6 sm:w-6 text-orange-600" />
              </div>
              <div className="ml-3 sm:ml-4 min-w-0 flex-1">
                <p className="text-xs sm:text-sm font-medium text-gray-600 truncate">Weekly Hours</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900 truncate">{todayStats.weeklyHours}h</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        {/* Recent Activities */}
        <Card>
          <div className="p-4 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-base sm:text-lg font-medium text-gray-900">Today's Activities</h3>
              {biostarConnected && (
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-gray-500">Live</span>
                </div>
              )}
            </div>
            <div className="space-y-3 sm:space-y-4">
              {/* Real-time BioStar updates */}
              {realtimeUpdates.slice(0, 2).map((update, index) => (
                <div key={`realtime-${index}`} className="flex items-start space-x-3">
                  <div className="p-2 rounded-full bg-blue-100 flex-shrink-0">
                    <Clock className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {update.employee_name} - {update.event_type.replace('_', ' ')}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {new Date(update.timestamp).toLocaleTimeString()} at {update.device_name}
                    </p>
                  </div>
                </div>
              ))}

              {/* Static activities */}
              {recentActivities.map((activity) => {
                const Icon = activity.icon;
                return (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className={cn('p-2 rounded-full bg-gray-100 flex-shrink-0', activity.color)}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{activity.description}</p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </Card>

        {/* Upcoming Events */}
        <Card>
          <div className="p-4 sm:p-6">
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4">Upcoming Events</h3>
            <div className="space-y-3 sm:space-y-4">
              {upcomingEvents.map((event) => (
                <div key={event.id} className="border-l-4 border-orange-500 pl-3 sm:pl-4">
                  <h4 className="text-sm font-medium text-gray-900 leading-tight">{event.title}</h4>
                  <p className="text-xs text-gray-500 mt-1">{event.time}</p>
                  {event.participants && (
                    <p className="text-xs text-gray-500">{event.participants} participants</p>
                  )}
                  {event.duration && (
                    <p className="text-xs text-gray-500">Duration: {event.duration}</p>
                  )}
                  {event.with && (
                    <p className="text-xs text-gray-500">With: {event.with}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <div className="p-4 sm:p-6">
          <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
            <button className="p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center touch-manipulation">
              <FileText className="h-5 w-5 sm:h-6 sm:w-6 text-orange-500 mx-auto mb-2" />
              <span className="text-xs sm:text-sm font-medium text-gray-900 leading-tight">Apply Leave</span>
            </button>
            <button className="p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center touch-manipulation">
              <Clock className="h-5 w-5 sm:h-6 sm:w-6 text-blue-500 mx-auto mb-2" />
              <span className="text-xs sm:text-sm font-medium text-gray-900 leading-tight">Check Out</span>
            </button>
            <button className="p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center touch-manipulation">
              <User className="h-5 w-5 sm:h-6 sm:w-6 text-green-500 mx-auto mb-2" />
              <span className="text-xs sm:text-sm font-medium text-gray-900 leading-tight">Update Profile</span>
            </button>
            <button className="p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center touch-manipulation">
              <TrendingUp className="h-5 w-5 sm:h-6 sm:w-6 text-purple-500 mx-auto mb-2" />
              <span className="text-xs sm:text-sm font-medium text-gray-900 leading-tight">View Performance</span>
            </button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default StaffDashboard;
