import { apiClient } from './api';

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface UploadResult {
  success: boolean;
  url?: string;
  filename?: string;
  size?: number;
  error?: string;
}

export interface FileUploadOptions {
  onProgress?: (progress: UploadProgress) => void;
  maxSize?: number; // in bytes
  allowedTypes?: string[];
  folder?: string;
}

class FileUploadService {
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB default
  private readonly ALLOWED_TYPES = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv'
  ];

  /**
   * Validate file before upload
   */
  validateFile(file: File, options?: FileUploadOptions): { valid: boolean; error?: string } {
    const maxSize = options?.maxSize || this.MAX_FILE_SIZE;
    const allowedTypes = options?.allowedTypes || this.ALLOWED_TYPES;

    // Check file size
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size (${this.formatFileSize(file.size)}) exceeds maximum allowed size (${this.formatFileSize(maxSize)})`
      };
    }

    // Check file type
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: `File type "${file.type}" is not allowed. Allowed types: ${allowedTypes.join(', ')}`
      };
    }

    return { valid: true };
  }

  /**
   * Upload a single file
   */
  async uploadFile(
    file: File,
    endpoint: string,
    options?: FileUploadOptions
  ): Promise<UploadResult> {
    try {
      // Validate file
      const validation = this.validateFile(file, options);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error
        };
      }

      // Create form data
      const formData = new FormData();
      formData.append('file', file);
      
      if (options?.folder) {
        formData.append('folder', options.folder);
      }

      // Upload with progress tracking
      const response = await apiClient.post(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (options?.onProgress && progressEvent.total) {
            const progress: UploadProgress = {
              loaded: progressEvent.loaded,
              total: progressEvent.total,
              percentage: Math.round((progressEvent.loaded * 100) / progressEvent.total)
            };
            options.onProgress(progress);
          }
        }
      });

      return {
        success: true,
        url: response.data.url,
        filename: response.data.filename,
        size: file.size
      };
    } catch (error: any) {
      console.error('File upload error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Upload failed'
      };
    }
  }

  /**
   * Upload multiple files
   */
  async uploadMultipleFiles(
    files: File[],
    endpoint: string,
    options?: FileUploadOptions
  ): Promise<UploadResult[]> {
    const results: UploadResult[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const result = await this.uploadFile(file, endpoint, {
        ...options,
        onProgress: (progress) => {
          if (options?.onProgress) {
            // Calculate overall progress across all files
            const overallProgress: UploadProgress = {
              loaded: (i * 100) + progress.percentage,
              total: files.length * 100,
              percentage: Math.round(((i * 100) + progress.percentage) / files.length)
            };
            options.onProgress(overallProgress);
          }
        }
      });
      results.push(result);
    }

    return results;
  }

  /**
   * Upload profile picture
   */
  async uploadProfilePicture(file: File, userId: number, onProgress?: (progress: UploadProgress) => void): Promise<UploadResult> {
    return this.uploadFile(file, `/employees/${userId}/profile-picture/`, {
      onProgress,
      maxSize: 5 * 1024 * 1024, // 5MB for profile pictures
      allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      folder: 'profile-pictures'
    });
  }

  /**
   * Upload employee document
   */
  async uploadEmployeeDocument(
    file: File,
    userId: number,
    documentType: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    return this.uploadFile(file, `/employees/${userId}/documents/`, {
      onProgress,
      folder: `documents/${documentType}`
    });
  }

  /**
   * Upload payslip
   */
  async uploadPayslip(file: File, employeeId: number, period: string, onProgress?: (progress: UploadProgress) => void): Promise<UploadResult> {
    return this.uploadFile(file, `/payroll/${employeeId}/payslips/`, {
      onProgress,
      allowedTypes: ['application/pdf'],
      folder: `payslips/${period}`
    });
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get file extension from filename
   */
  getFileExtension(filename: string): string {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
  }

  /**
   * Generate unique filename
   */
  generateUniqueFilename(originalName: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 15);
    const extension = this.getFileExtension(originalName);
    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
    
    return `${nameWithoutExt}_${timestamp}_${random}.${extension}`;
  }

  /**
   * Create file preview URL
   */
  createPreviewUrl(file: File): string {
    return URL.createObjectURL(file);
  }

  /**
   * Revoke file preview URL
   */
  revokePreviewUrl(url: string): void {
    URL.revokeObjectURL(url);
  }

  /**
   * Check if file is an image
   */
  isImage(file: File): boolean {
    return file.type.startsWith('image/');
  }

  /**
   * Check if file is a PDF
   */
  isPDF(file: File): boolean {
    return file.type === 'application/pdf';
  }

  /**
   * Check if file is a document
   */
  isDocument(file: File): boolean {
    const documentTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'text/csv'
    ];
    return documentTypes.includes(file.type);
  }
}

// Create and export service instance
export const fileUploadService = new FileUploadService();

// Export types and service
export default fileUploadService;
