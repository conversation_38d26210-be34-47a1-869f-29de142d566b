# BioStar 2 Production Deployment Guide

This guide provides step-by-step instructions for deploying the BioStar 2 integration to production.

## 📋 Pre-Deployment Checklist

### ✅ Environment Setup

- [ ] **BioStar 2 Server Access**
  - [ ] BioStar 2 server is accessible from production environment
  - [ ] Admin credentials are available
  - [ ] API endpoints are enabled
  - [ ] SSL certificates are configured (if using HTTPS)

- [ ] **Network Configuration**
  - [ ] Firewall rules allow outbound connections to BioStar server
  - [ ] DNS resolution is working for BioStar server
  - [ ] Network latency is acceptable (<500ms)
  - [ ] Load balancer configuration (if applicable)

- [ ] **Environment Variables**
  - [ ] Production environment variables are set
  - [ ] Credentials are stored securely
  - [ ] Configuration is validated
  - [ ] Backup configuration is available

### ✅ Testing Requirements

- [ ] **Connection Testing**
  - [ ] Run connection test script
  - [ ] Verify authentication works
  - [ ] Test all API endpoints
  - [ ] Validate device connectivity

- [ ] **Integration Testing**
  - [ ] Test with actual BioStar devices
  - [ ] Verify attendance data synchronization
  - [ ] Test real-time monitoring
  - [ ] Validate error handling

- [ ] **Performance Testing**
  - [ ] Load testing with expected user volume
  - [ ] Response time validation
  - [ ] Memory usage monitoring
  - [ ] Concurrent request handling

## 🚀 Deployment Steps

### Step 1: Environment Configuration

1. **Copy production environment file:**
   ```bash
   cp deployment/production.env .env.local
   ```

2. **Update configuration values:**
   ```bash
   # Edit .env.local with your actual values
   nano .env.local
   ```

3. **Validate configuration:**
   ```bash
   node scripts/test-biostar-connection.js --config production
   ```

### Step 2: Network Configuration

1. **Firewall Rules:**
   ```bash
   # Allow outbound HTTPS to BioStar server
   sudo ufw allow out 443/tcp
   sudo ufw allow out 8080/tcp  # If using custom port
   ```

2. **DNS Configuration:**
   ```bash
   # Test DNS resolution
   nslookup your-biostar-server.com
   ping your-biostar-server.com
   ```

3. **SSL Certificate Verification:**
   ```bash
   # Test SSL connection
   openssl s_client -connect your-biostar-server.com:443
   ```

### Step 3: Application Deployment

1. **Build the application:**
   ```bash
   npm run build
   ```

2. **Start the application:**
   ```bash
   npm start
   ```

3. **Verify deployment:**
   ```bash
   curl http://localhost:3000/health
   ```

### Step 4: Monitoring Setup

1. **Start BioStar monitoring:**
   - Monitoring starts automatically in production
   - Access monitoring dashboard at `/admin/biostar-monitoring`

2. **Configure alerts:**
   ```bash
   # Set up webhook for alerts (optional)
   export BIOSTAR_ALERT_WEBHOOK=https://your-webhook-url.com/alerts
   ```

3. **Verify monitoring:**
   - Check monitoring dashboard
   - Verify metrics collection
   - Test alert notifications

## 🔧 Configuration Details

### Environment Variables

```env
# Required Configuration
NEXT_PUBLIC_BIOSTAR_API_URL=https://your-biostar-server.com:8080
NEXT_PUBLIC_BIOSTAR_USERNAME=your_admin_username
NEXT_PUBLIC_BIOSTAR_PASSWORD=your_admin_password

# Performance Tuning
BIOSTAR_TIMEOUT=20000
BIOSTAR_RETRY_ATTEMPTS=5
BIOSTAR_POLLING_INTERVAL=60000

# Monitoring
BIOSTAR_ENABLE_MONITORING=true
BIOSTAR_ENABLE_METRICS=true
BIOSTAR_ALERT_EMAIL=<EMAIL>
```

### Network Requirements

| Component | Port | Protocol | Direction | Purpose |
|-----------|------|----------|-----------|---------|
| BioStar API | 8080 | HTTPS | Outbound | API Communication |
| BioStar Web | 443 | HTTPS | Outbound | Web Interface |
| Database | 5432 | TCP | Outbound | Data Sync (optional) |

### Security Considerations

1. **Credential Management:**
   - Store credentials in secure environment variables
   - Use secrets management service (AWS Secrets Manager, etc.)
   - Rotate credentials regularly

2. **Network Security:**
   - Use HTTPS for all communications
   - Implement IP whitelisting if possible
   - Monitor for suspicious activity

3. **Access Control:**
   - Limit BioStar admin access
   - Use least privilege principle
   - Audit access logs regularly

## 🧪 Testing with Actual Devices

### Device Setup

1. **Configure BioStar Devices:**
   ```bash
   # Connect devices to BioStar server
   # Configure device settings
   # Test device connectivity
   ```

2. **Test Employee Registration:**
   - Register test employees in BioStar
   - Enroll biometric data
   - Test authentication

3. **Verify Data Flow:**
   - Test check-in/check-out events
   - Verify data appears in WorkFlo
   - Check real-time updates

### Test Scenarios

1. **Normal Operations:**
   - Employee check-in/check-out
   - Multiple device access
   - Concurrent user access

2. **Error Conditions:**
   - Network disconnection
   - Device offline
   - Invalid credentials

3. **Performance Testing:**
   - High volume of events
   - Multiple simultaneous users
   - Extended operation periods

## 📊 Monitoring and Maintenance

### Health Monitoring

1. **Automated Health Checks:**
   ```bash
   # Run health check every 5 minutes
   */5 * * * * /usr/local/bin/node /path/to/scripts/test-biostar-connection.js
   ```

2. **Key Metrics to Monitor:**
   - Connection uptime
   - Response times
   - Error rates
   - Device status
   - Data synchronization lag

3. **Alert Thresholds:**
   - Uptime < 95%
   - Response time > 5 seconds
   - Error rate > 5%
   - Device offline > 10 minutes

### Maintenance Tasks

1. **Daily:**
   - Check monitoring dashboard
   - Review error logs
   - Verify data synchronization

2. **Weekly:**
   - Review performance metrics
   - Check device status
   - Update employee data

3. **Monthly:**
   - Rotate credentials
   - Review security logs
   - Performance optimization

## 🚨 Troubleshooting

### Common Issues

1. **Connection Failures:**
   ```bash
   # Check network connectivity
   ping your-biostar-server.com
   
   # Test API endpoint
   curl -k https://your-biostar-server.com:8080/health
   
   # Check firewall rules
   sudo ufw status
   ```

2. **Authentication Errors:**
   ```bash
   # Verify credentials
   node scripts/test-biostar-connection.js --verbose
   
   # Check BioStar user permissions
   # Verify API access is enabled
   ```

3. **Performance Issues:**
   ```bash
   # Check response times
   curl -w "@curl-format.txt" -o /dev/null -s https://your-biostar-server.com:8080/health
   
   # Monitor memory usage
   top -p $(pgrep node)
   
   # Check network latency
   ping -c 10 your-biostar-server.com
   ```

### Log Analysis

1. **Application Logs:**
   ```bash
   # Check application logs
   tail -f /var/log/workflo/application.log
   
   # Filter BioStar related logs
   grep "BioStar" /var/log/workflo/application.log
   ```

2. **System Logs:**
   ```bash
   # Check system logs
   journalctl -u workflo-app -f
   
   # Check network logs
   tail -f /var/log/syslog | grep -i network
   ```

## 📞 Support and Escalation

### Support Contacts

- **BioStar Technical Support:** [Contact Information]
- **Network Team:** [Contact Information]
- **Development Team:** [Contact Information]

### Escalation Procedures

1. **Level 1:** Application restart, basic troubleshooting
2. **Level 2:** Network diagnostics, configuration review
3. **Level 3:** BioStar vendor support, advanced debugging

### Emergency Procedures

1. **Complete Outage:**
   - Enable fallback mode
   - Notify stakeholders
   - Implement manual processes

2. **Data Corruption:**
   - Stop synchronization
   - Restore from backup
   - Verify data integrity

## 📈 Performance Optimization

### Recommended Settings

```env
# Production Optimized Settings
BIOSTAR_TIMEOUT=20000
BIOSTAR_RETRY_ATTEMPTS=5
BIOSTAR_POLLING_INTERVAL=60000
BIOSTAR_MAX_CONCURRENT_REQUESTS=10
BIOSTAR_CACHE_TTL=300000
```

### Monitoring Metrics

- **Response Time:** < 2 seconds average
- **Uptime:** > 99% monthly
- **Error Rate:** < 1% of requests
- **Memory Usage:** < 80% of available
- **CPU Usage:** < 70% average

## ✅ Post-Deployment Verification

1. **Functional Testing:**
   - [ ] All API endpoints accessible
   - [ ] Authentication working
   - [ ] Real-time updates functioning
   - [ ] Device status monitoring active

2. **Performance Verification:**
   - [ ] Response times within acceptable range
   - [ ] No memory leaks detected
   - [ ] Monitoring dashboard operational
   - [ ] Alerts configured and tested

3. **Security Validation:**
   - [ ] Credentials properly secured
   - [ ] Network access restricted
   - [ ] Audit logging enabled
   - [ ] SSL certificates valid

## 📚 Additional Resources

- [BioStar 2 API Documentation](https://bs2api.biostar2.com/docs)
- [WorkFlo BioStar Integration Guide](./BIOSTAR_INTEGRATION.md)
- [Monitoring Dashboard Guide](./docs/monitoring-guide.md)
- [Troubleshooting Guide](./docs/troubleshooting.md)
