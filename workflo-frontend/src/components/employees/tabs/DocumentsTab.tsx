'use client';

import React, { useState, useEffect } from 'react';
import {
  FileText,
  Upload,
  Download,
  Eye,
  Trash2,
  Plus,
  Search,
  Filter,
  Calendar,
  User,
  File,
  Image,
  MoreHorizontal,
  X,
  Save
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { cn, formatDate } from '@/lib/utils';

interface Document {
  id: number;
  name: string;
  type: 'contract' | 'id_document' | 'certificate' | 'resume' | 'other';
  file_type: 'pdf' | 'doc' | 'docx' | 'jpg' | 'png' | 'xlsx';
  file_size: number;
  upload_date: string;
  uploaded_by: string;
  description?: string;
  file_url: string;
  is_confidential: boolean;
}

interface DocumentsTabProps {
  employeeId: number;
  employeeName: string;
}

const DocumentsTab: React.FC<DocumentsTabProps> = ({ employeeId, employeeName }) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('');
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);

  // Mock documents data
  const mockDocuments: Document[] = [
    {
      id: 1,
      name: 'Employment Contract',
      type: 'contract',
      file_type: 'pdf',
      file_size: 245760,
      upload_date: '2022-03-15T00:00:00Z',
      uploaded_by: 'HR Department',
      description: 'Initial employment contract',
      file_url: '/documents/contract_001.pdf',
      is_confidential: true
    },
    {
      id: 2,
      name: 'Driver License',
      type: 'id_document',
      file_type: 'jpg',
      file_size: 156432,
      upload_date: '2022-03-16T00:00:00Z',
      uploaded_by: 'Maria Cotton',
      description: 'Government issued ID',
      file_url: '/documents/id_001.jpg',
      is_confidential: true
    },
    {
      id: 3,
      name: 'Software Engineering Certificate',
      type: 'certificate',
      file_type: 'pdf',
      file_size: 189234,
      upload_date: '2022-04-01T00:00:00Z',
      uploaded_by: 'Maria Cotton',
      description: 'Professional certification',
      file_url: '/documents/cert_001.pdf',
      is_confidential: false
    }
  ];

  useEffect(() => {
    const loadDocuments = async () => {
      setLoading(true);
      // Simulate API call
      setTimeout(() => {
        setDocuments(mockDocuments);
        setLoading(false);
      }, 1000);
    };

    loadDocuments();
  }, [employeeId]);

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.description?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = !filterType || doc.type === filterType;
    return matchesSearch && matchesType;
  });

  const handleUpload = () => {
    setShowUploadModal(true);
  };

  const handleDownload = (document: Document) => {
    // Simulate download
    console.log('Downloading:', document.name);
  };

  const handleDelete = (document: Document) => {
    if (confirm(`Are you sure you want to delete "${document.name}"?`)) {
      setDocuments(prev => prev.filter(d => d.id !== document.id));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Employee Documents</h3>
          <p className="text-sm text-gray-600 mt-1">
            Manage personal documents for {employeeName}
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Button variant="primary" onClick={handleUpload}>
            <Upload className="h-4 w-4 mr-2" />
            Upload Document
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <div className="flex-1">
          <Input
            placeholder="Search documents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="h-4 w-4" />}
          />
        </div>
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="">All Types</option>
          <option value="contract">Contract</option>
          <option value="id_document">ID Document</option>
          <option value="certificate">Certificate</option>
          <option value="resume">Resume</option>
          <option value="other">Other</option>
        </select>
      </div>

      {/* Documents Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      ) : filteredDocuments.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No documents found
          </h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || filterType
              ? 'Try adjusting your search criteria.'
              : 'Upload the first document for this employee.'
            }
          </p>
          <Button variant="primary" onClick={handleUpload}>
            <Upload className="h-4 w-4 mr-2" />
            Upload Document
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredDocuments.map((document) => (
            <DocumentCard
              key={document.id}
              document={document}
              onDownload={() => handleDownload(document)}
              onDelete={() => handleDelete(document)}
            />
          ))}
        </div>
      )}

      {/* Upload Modal */}
      {showUploadModal && (
        <UploadDocumentModal
          isOpen={showUploadModal}
          onClose={() => setShowUploadModal(false)}
          employeeId={employeeId}
          employeeName={employeeName}
          onUpload={(newDoc) => {
            setDocuments(prev => [...prev, newDoc]);
            setShowUploadModal(false);
          }}
        />
      )}
    </div>
  );
};

// Helper functions
const getFileIcon = (fileType: string) => {
  switch (fileType) {
    case 'pdf': return <FileText className="h-5 w-5 text-red-500" />;
    case 'doc':
    case 'docx': return <FileText className="h-5 w-5 text-blue-500" />;
    case 'xlsx': return <FileText className="h-5 w-5 text-green-500" />;
    case 'jpg':
    case 'png': return <Image className="h-5 w-5 text-purple-500" />;
    default: return <File className="h-5 w-5 text-gray-500" />;
  }
};

const getTypeColor = (type: string) => {
  switch (type) {
    case 'contract': return 'bg-blue-100 text-blue-800';
    case 'id_document': return 'bg-green-100 text-green-800';
    case 'certificate': return 'bg-purple-100 text-purple-800';
    case 'resume': return 'bg-orange-100 text-orange-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Document Card Component
interface DocumentCardProps {
  document: Document;
  onDownload: () => void;
  onDelete: () => void;
}

const DocumentCard: React.FC<DocumentCardProps> = ({ document, onDownload, onDelete }) => {
  const [showActions, setShowActions] = useState(false);

  return (
    <Card className="relative hover:shadow-md transition-shadow duration-200">
      <div className="p-4">
        {/* Actions Menu */}
        <div className="absolute top-3 right-3">
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-1 rounded-full hover:bg-gray-100"
            >
              <MoreHorizontal className="h-4 w-4 text-gray-500" />
            </button>

            {showActions && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <Eye className="h-4 w-4 mr-2" />
                    View Document
                  </button>
                  <button
                    onClick={() => {
                      onDownload();
                      setShowActions(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </button>
                  <button
                    onClick={() => {
                      onDelete();
                      setShowActions(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Document Info */}
        <div className="mb-3">
          <div className="flex items-center mb-2">
            {getFileIcon(document.file_type)}
            <h4 className="text-sm font-medium text-gray-900 ml-2 truncate">
              {document.name}
            </h4>
          </div>

          <div className="flex items-center space-x-2 mb-2">
            <span className={cn(
              'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
              getTypeColor(document.type)
            )}>
              {document.type.replace('_', ' ').toUpperCase()}
            </span>
            {document.is_confidential && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Confidential
              </span>
            )}
          </div>

          {document.description && (
            <p className="text-xs text-gray-600 mb-2 line-clamp-2">
              {document.description}
            </p>
          )}
        </div>

        {/* Document Details */}
        <div className="space-y-1 text-xs text-gray-500">
          <div className="flex justify-between">
            <span>Size:</span>
            <span>{formatFileSize(document.file_size)}</span>
          </div>
          <div className="flex justify-between">
            <span>Uploaded:</span>
            <span>{formatDate(document.upload_date)}</span>
          </div>
          <div className="flex justify-between">
            <span>By:</span>
            <span className="truncate ml-2">{document.uploaded_by}</span>
          </div>
        </div>
      </div>
    </Card>
  );
};

// Upload Document Modal Component
interface UploadDocumentModalProps {
  isOpen: boolean;
  onClose: () => void;
  employeeId: number;
  employeeName: string;
  onUpload: (document: Document) => void;
}

const UploadDocumentModal: React.FC<UploadDocumentModalProps> = ({
  isOpen,
  onClose,
  employeeId,
  employeeName,
  onUpload
}) => {
  const [formData, setFormData] = useState({
    name: '',
    type: 'other' as Document['type'],
    description: '',
    is_confidential: false
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedFile) return;

    setUploading(true);

    // Simulate upload
    setTimeout(() => {
      const newDocument: Document = {
        id: Date.now(),
        name: formData.name || selectedFile.name,
        type: formData.type,
        file_type: selectedFile.name.split('.').pop()?.toLowerCase() as Document['file_type'],
        file_size: selectedFile.size,
        upload_date: new Date().toISOString(),
        uploaded_by: 'Current User',
        description: formData.description,
        file_url: `/documents/${selectedFile.name}`,
        is_confidential: formData.is_confidential
      };

      onUpload(newDocument);
      setUploading(false);
    }, 2000);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Upload Document</h3>
          <button onClick={onClose} className="p-2 hover:bg-gray-100 rounded-full">
            <X className="h-4 w-4 text-gray-500" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Document Name
            </label>
            <Input
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter document name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Document Type
            </label>
            <select
              value={formData.type}
              onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as Document['type'] }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="contract">Contract</option>
              <option value="id_document">ID Document</option>
              <option value="certificate">Certificate</option>
              <option value="resume">Resume</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Enter description (optional)"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select File
            </label>
            <input
              type="file"
              onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              accept=".pdf,.doc,.docx,.jpg,.png,.xlsx"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="confidential"
              checked={formData.is_confidential}
              onChange={(e) => setFormData(prev => ({ ...prev, is_confidential: e.target.checked }))}
              className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
            />
            <label htmlFor="confidential" className="ml-2 text-sm text-gray-700">
              Mark as confidential
            </label>
          </div>

          <div className="flex items-center justify-end space-x-3 pt-4">
            <Button type="button" variant="secondary" onClick={onClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={!selectedFile || uploading}
            >
              {uploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Uploading...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Upload
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DocumentsTab;
