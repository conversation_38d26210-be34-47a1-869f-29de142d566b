'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Home,
  Users,
  Building,
  Calendar,
  FileText,
  Star,
  BarChart3,
  Settings,
  X,
  User,
  LogOut,
  DollarSign
} from 'lucide-react';
import { useAuth } from '@/store/authStore';
import { cn, getInitials, formatDate } from '@/lib/utils';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const pathname = usePathname();
  const { user, logout } = useAuth();

  const navigation = [
    { name: 'Home', href: '/dashboard', icon: Home },
    { name: 'Admin', href: '/employees', icon: Users },
    { name: 'Info', href: '/info', icon: User },
    { name: 'Company', href: '/company', icon: Building },
    { name: 'Manage', href: '/manage', icon: Settings },
    { name: 'Settings', href: '/settings', icon: Settings },
  ];

  const userInitials = user ? getInitials(user.first_name, user.last_name) : 'U';
  const currentDate = formatDate(new Date().toISOString(), 'EEE, MMM dd, yyyy');

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out md:relative md:translate-x-0',
          isOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        {/* Mobile close button */}
        <div className="md:hidden absolute top-4 right-4">
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Sidebar content */}
        <div className="flex flex-col h-full">
          {/* Breadcrumb and Title */}
          <div className="p-6 border-b border-gray-200">
            <nav className="text-sm text-gray-500 mb-2">
              <Link href="/dashboard" className="hover:text-gray-700">
                Home
              </Link>
              <span className="mx-2">/</span>
              <span className="text-gray-900">Dashboard</span>
            </nav>
            <h1 className="text-xl font-semibold text-gray-900">
              Admin Dashboard
            </h1>
          </div>

          {/* User Card */}
          <div className="p-6 border-b border-gray-200">
            <div className="bg-white rounded-lg border border-gray-200 p-4 text-center">
              <div className="mb-4">
                {user?.profile_picture ? (
                  <img
                    src={user.profile_picture}
                    alt="Profile"
                    className="w-16 h-16 rounded-full mx-auto object-cover"
                  />
                ) : (
                  <div className="w-16 h-16 bg-orange-500 rounded-full mx-auto flex items-center justify-center">
                    <span className="text-white text-xl font-medium">
                      {userInitials}
                    </span>
                  </div>
                )}
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">
                  Welcome {user?.first_name || 'Admin'}
                </h3>
                <p className="text-sm text-gray-500 mt-1">{currentDate}</p>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="p-6 border-b border-gray-200">
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="p-4">
                <h3 className="font-medium text-gray-900 mb-3">Quick Links</h3>
                <div className="space-y-1">
                  <Link
                    href="/dashboard"
                    className={cn(
                      'block w-full text-center py-2 px-3 rounded-md text-sm font-medium transition-colors',
                      pathname === '/dashboard'
                        ? 'bg-orange-500 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    )}
                  >
                    Admin Dashboard
                  </Link>
                  <Link
                    href="/employees/dashboard"
                    className={cn(
                      'block w-full text-center py-2 px-3 rounded-md text-sm font-medium transition-colors',
                      pathname === '/employees/dashboard'
                        ? 'bg-orange-500 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    )}
                  >
                    Employees Dashboard
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-6 space-y-1">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href || pathname.startsWith(item.href + '/');

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={onClose}
                  className={cn(
                    'flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors',
                    isActive
                      ? 'bg-orange-50 text-orange-700 border-r-2 border-orange-500'
                      : 'text-gray-700 hover:bg-gray-100'
                  )}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* User Actions */}
          <div className="p-6 border-t border-gray-200 space-y-1">
            <Link
              href="/profile"
              onClick={onClose}
              className="flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <User className="h-5 w-5 mr-3" />
              Profile
            </Link>
            <button
              onClick={() => {
                logout();
                onClose();
              }}
              className="flex items-center w-full px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <LogOut className="h-5 w-5 mr-3" />
              Logout
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
