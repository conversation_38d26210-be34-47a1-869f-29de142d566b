'use client';

import React, { useEffect, useState } from 'react';
import AuthGuard from './AuthGuard';

interface ClientAuthGuardProps {
  children: React.ReactNode;
}

const ClientAuthGuard: React.FC<ClientAuthGuardProps> = ({ children }) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // During SSR or before hydration, show a stable loading state
  if (!isClient) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600 text-sm">Loading WorkFlow...</p>
        </div>
      </div>
    );
  }

  // After hydration, render the actual AuthGuard
  return <AuthGuard>{children}</AuthGuard>;
};

export default ClientAuthGuard;
