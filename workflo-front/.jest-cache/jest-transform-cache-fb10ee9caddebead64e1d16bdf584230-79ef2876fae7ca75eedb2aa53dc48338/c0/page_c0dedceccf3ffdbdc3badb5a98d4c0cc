8b1d79f57b7570707ff6e9c2fa0d5cf6
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _lucidereact = require("lucide-react");
const _Card = /*#__PURE__*/ _interop_require_default(require("../../../../components/ui/Card"));
const _AuthProvider = require("../../../../providers/AuthProvider");
const _utils = require("../../../../lib/utils");
const _useBiostarAttendance = require("../../../../hooks/useBiostarAttendance");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const StaffDashboard = ()=>{
    const { user } = (0, _AuthProvider.useAuth)();
    const [currentTime, setCurrentTime] = (0, _react.useState)(new Date());
    // Use BioStar attendance hook
    const { summary: attendanceSummary, realtimeUpdates, connected: biostarConnected, loading } = (0, _useBiostarAttendance.useBiostarAttendance)({
        employeeId: user?.employee_id,
        autoRefresh: true,
        enableRealTime: true
    });
    (0, _react.useEffect)(()=>{
        const timer = setInterval(()=>{
            setCurrentTime(new Date());
        }, 1000);
        return ()=>clearInterval(timer);
    }, []);
    const todayStats = {
        checkInTime: attendanceSummary?.checkInTime ? new Date(attendanceSummary.checkInTime).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
        }) : 'Not checked in',
        hoursWorked: attendanceSummary?.hoursWorked?.toFixed(1) || '0.0',
        tasksCompleted: 4,
        meetingsToday: 2,
        leaveBalance: 15,
        pendingApprovals: 1,
        attendanceStatus: attendanceSummary?.todayStatus || 'ABSENT',
        weeklyHours: attendanceSummary?.weeklyHours?.toFixed(1) || '0.0',
        monthlyAttendance: attendanceSummary?.monthlyAttendance || 0
    };
    const recentActivities = [
        {
            id: 1,
            type: 'check_in',
            description: 'Checked in for the day',
            time: '08:30 AM',
            icon: _lucidereact.Clock,
            color: 'text-green-500'
        },
        {
            id: 2,
            type: 'task',
            description: 'Completed project review',
            time: '10:15 AM',
            icon: _lucidereact.CheckCircle,
            color: 'text-blue-500'
        },
        {
            id: 3,
            type: 'meeting',
            description: 'Team standup meeting',
            time: '11:00 AM',
            icon: _lucidereact.Users,
            color: 'text-purple-500'
        },
        {
            id: 4,
            type: 'document',
            description: 'Updated timesheet',
            time: '02:30 PM',
            icon: _lucidereact.FileText,
            color: 'text-orange-500'
        }
    ];
    const upcomingEvents = [
        {
            id: 1,
            title: 'Project Review Meeting',
            time: '3:00 PM',
            type: 'meeting',
            participants: 5
        },
        {
            id: 2,
            title: 'Training Session: React Best Practices',
            time: 'Tomorrow 10:00 AM',
            type: 'training',
            duration: '2 hours'
        },
        {
            id: 3,
            title: 'Performance Review',
            time: 'Friday 2:00 PM',
            type: 'review',
            with: 'Manager'
        }
    ];
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white",
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "flex items-center justify-between",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("h1", {
                                    className: "text-2xl font-bold",
                                    children: [
                                        "Good ",
                                        currentTime.getHours() < 12 ? 'Morning' : currentTime.getHours() < 18 ? 'Afternoon' : 'Evening',
                                        ", ",
                                        user?.first_name,
                                        "!"
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                    className: "text-orange-100 mt-1",
                                    children: (0, _utils.formatDate)(new Date().toISOString(), 'EEEE, MMMM dd, yyyy')
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center mt-2 space-x-4",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-1",
                                            children: [
                                                biostarConnected ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Wifi, {
                                                    className: "h-4 w-4 text-green-300"
                                                }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.WifiOff, {
                                                    className: "h-4 w-4 text-red-300"
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                                                    className: "text-sm text-orange-100",
                                                    children: [
                                                        "BioStar ",
                                                        biostarConnected ? 'Connected' : 'Disconnected'
                                                    ]
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-1",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: (0, _utils.cn)("w-2 h-2 rounded-full", todayStats.attendanceStatus === 'PRESENT' ? 'bg-green-300' : todayStats.attendanceStatus === 'LATE' ? 'bg-yellow-300' : todayStats.attendanceStatus === 'EARLY_OUT' ? 'bg-orange-300' : 'bg-red-300')
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                    className: "text-sm text-orange-100",
                                                    children: todayStats.attendanceStatus
                                                })
                                            ]
                                        })
                                    ]
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "text-right",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "text-3xl font-bold",
                                    children: currentTime.toLocaleTimeString([], {
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    })
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "text-orange-100 text-sm",
                                    children: "Current Time"
                                }),
                                loading && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "text-orange-100 text-xs mt-1",
                                    children: "Loading attendance..."
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-green-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                                            className: "h-6 w-6 text-green-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Check-in Time"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: todayStats.checkInTime
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-blue-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Activity, {
                                            className: "h-6 w-6 text-blue-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Hours Worked"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: [
                                                    todayStats.hoursWorked,
                                                    "h"
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-purple-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Target, {
                                            className: "h-6 w-6 text-purple-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Tasks Completed"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: todayStats.tasksCompleted
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-orange-100 rounded-lg",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Calendar, {
                                            className: "h-6 w-6 text-orange-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-4",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-sm font-medium text-gray-600",
                                                children: "Weekly Hours"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                className: "text-2xl font-bold text-gray-900",
                                                children: [
                                                    todayStats.weeklyHours,
                                                    "h"
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "grid grid-cols-1 lg:grid-cols-2 gap-6",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "p-6",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                            className: "text-lg font-medium text-gray-900",
                                            children: "Today's Activities"
                                        }),
                                        biostarConnected && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-1",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: "w-2 h-2 bg-green-500 rounded-full animate-pulse"
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                    className: "text-xs text-gray-500",
                                                    children: "Live"
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "space-y-4",
                                    children: [
                                        realtimeUpdates.slice(0, 2).map((update, index)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex items-center space-x-3",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                        className: "p-2 rounded-full bg-blue-100",
                                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                                                            className: "h-4 w-4 text-blue-600"
                                                        })
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                        className: "flex-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                                className: "text-sm font-medium text-gray-900",
                                                                children: [
                                                                    update.employee_name,
                                                                    " - ",
                                                                    update.event_type.replace('_', ' ')
                                                                ]
                                                            }),
                                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                                className: "text-xs text-gray-500",
                                                                children: [
                                                                    new Date(update.timestamp).toLocaleTimeString(),
                                                                    " at ",
                                                                    update.device_name
                                                                ]
                                                            })
                                                        ]
                                                    })
                                                ]
                                            }, `realtime-${index}`)),
                                        recentActivities.map((activity)=>{
                                            const Icon = activity.icon;
                                            return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex items-center space-x-3",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                        className: (0, _utils.cn)('p-2 rounded-full bg-gray-100', activity.color),
                                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Icon, {
                                                            className: "h-4 w-4"
                                                        })
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                        className: "flex-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                                className: "text-sm font-medium text-gray-900",
                                                                children: activity.description
                                                            }),
                                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                                className: "text-xs text-gray-500",
                                                                children: activity.time
                                                            })
                                                        ]
                                                    })
                                                ]
                                            }, activity.id);
                                        })
                                    ]
                                })
                            ]
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "p-6",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                    className: "text-lg font-medium text-gray-900 mb-4",
                                    children: "Upcoming Events"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "space-y-4",
                                    children: upcomingEvents.map((event)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "border-l-4 border-orange-500 pl-4",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                    className: "text-sm font-medium text-gray-900",
                                                    children: event.title
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                    className: "text-xs text-gray-500 mt-1",
                                                    children: event.time
                                                }),
                                                event.participants && /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                    className: "text-xs text-gray-500",
                                                    children: [
                                                        event.participants,
                                                        " participants"
                                                    ]
                                                }),
                                                event.duration && /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                    className: "text-xs text-gray-500",
                                                    children: [
                                                        "Duration: ",
                                                        event.duration
                                                    ]
                                                }),
                                                event.with && /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                    className: "text-xs text-gray-500",
                                                    children: [
                                                        "With: ",
                                                        event.with
                                                    ]
                                                })
                                            ]
                                        }, event.id))
                                })
                            ]
                        })
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-lg font-medium text-gray-900 mb-4",
                            children: "Quick Actions"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "grid grid-cols-2 md:grid-cols-4 gap-4",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                                    className: "p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.FileText, {
                                            className: "h-6 w-6 text-orange-500 mx-auto mb-2"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm font-medium text-gray-900",
                                            children: "Apply Leave"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                                    className: "p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                                            className: "h-6 w-6 text-blue-500 mx-auto mb-2"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm font-medium text-gray-900",
                                            children: "Check Out"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                                    className: "p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.User, {
                                            className: "h-6 w-6 text-green-500 mx-auto mb-2"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm font-medium text-gray-900",
                                            children: "Update Profile"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                                    className: "p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.TrendingUp, {
                                            className: "h-6 w-6 text-purple-500 mx-auto mb-2"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-sm font-medium text-gray-900",
                                            children: "View Performance"
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            })
        ]
    });
};
const _default = StaffDashboard;

//# sourceMappingURL=data:application/json;base64,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