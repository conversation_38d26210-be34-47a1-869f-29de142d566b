# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2012,2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2021-12-24 19:22+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Interlingua (http://www.transifex.com/django/django/language/"
"ia/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ia\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Typos de contento"

msgid "python model class name"
msgstr "nomine del classe del modello Python"

msgid "content type"
msgstr "typo de contento"

msgid "content types"
msgstr "typos de contento"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Le objecto del typo de contento %(ct_id)s non ha un modello associate"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Le objecto %(obj_id)s del typo de contento %(ct_id)s non existe"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "Objectos %(ct_name)s non ha un methodo get_absolute_url()"
