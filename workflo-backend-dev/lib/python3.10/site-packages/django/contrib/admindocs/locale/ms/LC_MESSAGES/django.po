# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-11-16 14:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>\n"
"Language-Team: Malay (http://www.transifex.com/django/django/language/ms/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ms\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Administrative Documentation"
msgstr "Dokumentasi Pentadbiran"

msgid "Home"
msgstr "Utama"

msgid "Documentation"
msgstr "Dokumentasi"

msgid "Bookmarklets"
msgstr "Penanda"

msgid "Documentation bookmarklets"
msgstr "Dokumentasi penanda"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Untuk memasang penanda, heret pautan ke palang-alat penanda-buku, atau klik-"
"kanan pada pautan dan tambahkan ke penanda-buku anda. Kini anda boleh "
"memilih penanda itu daripada mana-mana ruangan di laman."

msgid "Documentation for this page"
msgstr "Dokumentasi untuk ruangan ini"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Membawa anda daripada mana-mana ruangan kepada dokumentasi bagi pemandangan "
"yang menjana ruang itu."

msgid "Tags"
msgstr "Tag-tag"

msgid "List of all the template tags and their functions."
msgstr "Senarai kesemua tag templat dan fungsiannya."

msgid "Filters"
msgstr "Tapisan"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Tapisan adalah tindakan yang boleh digunakan pada pembolehubah didalam "
"templat untuk mengubah outputnya."

msgid "Models"
msgstr "Model-model"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Model-model adalah penerangan bagi semua objek-objek didalam sistem dan "
"medan-medan yang berkaitan. Setiap model mempunyai senarai medan-medan yang "
"boleh diakses sebagai tempat pembolehubah."

msgid "Views"
msgstr "Pemandangan"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Setiap ruangan pada laman awan dihasilkan daripada pemandangan. Pemandangan "
"menakrifkan templat mana yang diguna-pakai untuk menghasilkan ruangan itu "
"dan objek mana yang boleh digunakan bersama templat itu."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Alat-alat untuk pelayar anda untuk mengakses fungsian pentadbiran secara "
"pantas."

msgid "Please install docutils"
msgstr "Sila pasang docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Sistem dokumentasi pentadbiran memerlukan perpustakaan <a href=\"%(link)s"
"\">docutils</a> Python."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Sila mohon pada pentadbir-pentadbir anda untuk memasang <a href=\"%(link)s"
"\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Medan-medan"

msgid "Field"
msgstr "Medan"

msgid "Type"
msgstr "Jenis"

msgid "Description"
msgstr "Penerangan"

msgid "Methods with arguments"
msgstr "Kaedah dengan argumen"

msgid "Method"
msgstr "Kaedah"

msgid "Arguments"
msgstr "Argumen"

msgid "Back to Model documentation"
msgstr "Patah balik ke dokumentasi Model"

msgid "Model documentation"
msgstr "Dokumentasi Model"

msgid "Model groups"
msgstr "Kumpulan-kumpulan Model"

msgid "Templates"
msgstr "Templat-templat"

#, python-format
msgid "Template: %(name)s"
msgstr "Templat: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Templat: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Cari laluan untuk templat <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(tidak wujud)"

msgid "Back to Documentation"
msgstr "Patah balik ke Dokumentasi"

msgid "Template filters"
msgstr "Penapisan templat"

msgid "Template filter documentation"
msgstr "Dokumentasi penapisan templat"

msgid "Built-in filters"
msgstr "Penapisan terbina-dalam"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Untuk menggunakan tapisan-tapisan ini, letakkan <code>%(code)s</code> "
"didalam templat anda sebelum menggunakan tapisan itu."

msgid "Template tags"
msgstr "Tag-tag templat"

msgid "Template tag documentation"
msgstr "Dokumentasi tag templat"

msgid "Built-in tags"
msgstr "Tag-tag terbina-dalam"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Untuk menggunakan tag-tag ini, letakkan <code>%(code)s</code> didalam "
"templat anda sebelum menggunakan tag itu."

#, python-format
msgid "View: %(name)s"
msgstr "Pemandagan: %(name)s"

msgid "Context:"
msgstr "Konteks:"

msgid "Templates:"
msgstr "Templat-templat:"

msgid "Back to View documentation"
msgstr "Patah balik ke Dokumentasi Pemandangan"

msgid "View documentation"
msgstr "Dokumentasi Pemandangan"

msgid "Jump to namespace"
msgstr "Lompat ke ruangnama"

msgid "Empty namespace"
msgstr "Ruangnama koson"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Pemandangan berdasakran ruangnama %(name)s"

msgid "Views by empty namespace"
msgstr "Pemandangan berdasarkan ruangnama kosong"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Fungsi Pemandangan: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "tag:"

msgid "filter:"
msgstr "tapis:"

msgid "view:"
msgstr "lihat:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "App %(app_label)r tidak dijumpai"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Model %(model_name)r tidak dijumpai didalam app %(app_label)r"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "objek `%(app_label)s.%(data_type)s` berkaitan"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "objek-objek `%(app_label)s.%(object_name)s` berkaitan"

#, python-format
msgid "all %s"
msgstr "semua %s"

#, python-format
msgid "number of %s"
msgstr "bilangan %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s nampaknya bukan objek urlpattern"
