import {
  User,
  AuthTokens,
  LoginCredentials,
  Employee,
  EmployeeFormData,
  EmployeeFilters,
  EmployeeBulkAction,
  Department,
  PaginatedResponse
} from '@/types';

// Mock data storage
let mockEmployees: Employee[] = [
  {
    id: 1,
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    employee_id: 'EMP001',
    phone_number: '******-0101',
    profile_picture: '/api/placeholder/100/100',
    role: 'employee',
    department: { id: 1, name: 'Engineering', description: 'Software Development', created_at: '2023-01-01', updated_at: '2023-01-01' },
    is_active: true,
    date_joined: '2023-01-15',
    job_title: 'Senior Software Engineer',
    position: 'Senior Software Engineer',
    department_id: 1,
    department_name: 'Engineering',
    hire_date: '2023-01-15',
    employment_type: 'full_time',
    work_location: 'hybrid',
    supervisor_id: 2,
    supervisor_name: '<PERSON>',
    salary: 95000,
    hourly_rate: 45,
    currency: 'USD',
    pay_frequency: 'monthly',
    bank_name: 'Chase Bank',
    bank_account: '****1234',
    nssf_number: 'NSSF001',
    nhif_number: 'NHIF001',
    kra_pin: 'KRA001',
    national_id: '********',
    date_of_birth: '1990-05-15',
    gender: 'female',
    marital_status: 'single',
    nationality: 'American',
    address: '123 Main St',
    city: 'San Francisco',
    state: 'CA',
    postal_code: '94105',
    country: 'USA',
    emergency_contact_name: 'Jane Cotton',
    emergency_contact_phone: '******-0102',
    emergency_contact_relationship: 'Sister',
    status: 'active',
    is_deleted: false,
    created_by: 1,
    updated_by: 1,
  },
  {
    id: 2,
    email: '<EMAIL>',
    first_name: 'John',
    last_name: 'Smith',
    employee_id: 'EMP002',
    phone_number: '******-0201',
    role: 'supervisor',
    department: { id: 1, name: 'Engineering', description: 'Software Development', created_at: '2023-01-01', updated_at: '2023-01-01' },
    is_active: true,
    date_joined: '2022-06-01',
    job_title: 'Engineering Manager',
    position: 'Engineering Manager',
    department_id: 1,
    department_name: 'Engineering',
    hire_date: '2022-06-01',
    employment_type: 'full_time',
    work_location: 'office',
    salary: 120000,
    hourly_rate: 58,
    currency: 'USD',
    pay_frequency: 'monthly',
    bank_name: 'Bank of America',
    bank_account: '****5678',
    nssf_number: 'NSSF002',
    nhif_number: 'NHIF002',
    kra_pin: 'KRA002',
    national_id: '********',
    date_of_birth: '1985-03-20',
    gender: 'male',
    marital_status: 'married',
    nationality: 'American',
    address: '456 Oak Ave',
    city: 'San Francisco',
    state: 'CA',
    postal_code: '94107',
    country: 'USA',
    emergency_contact_name: 'Sarah Smith',
    emergency_contact_phone: '******-0202',
    emergency_contact_relationship: 'Spouse',
    status: 'active',
    is_deleted: false,
    created_by: 1,
    updated_by: 1,
  },
  {
    id: 3,
    email: '<EMAIL>',
    first_name: 'Sarah',
    last_name: 'Johnson',
    employee_id: 'EMP003',
    phone_number: '******-0301',
    role: 'hr',
    department: { id: 2, name: 'Human Resources', description: 'HR Operations', created_at: '2023-01-01', updated_at: '2023-01-01' },
    is_active: true,
    date_joined: '2022-03-15',
    job_title: 'HR Manager',
    position: 'HR Manager',
    department_id: 2,
    department_name: 'Human Resources',
    hire_date: '2022-03-15',
    employment_type: 'full_time',
    work_location: 'office',
    salary: 85000,
    hourly_rate: 41,
    currency: 'USD',
    pay_frequency: 'monthly',
    bank_name: 'Wells Fargo',
    bank_account: '****9012',
    nssf_number: 'NSSF003',
    nhif_number: 'NHIF003',
    kra_pin: 'KRA003',
    national_id: '********',
    date_of_birth: '1988-11-10',
    gender: 'female',
    marital_status: 'married',
    nationality: 'American',
    address: '789 Pine St',
    city: 'San Francisco',
    state: 'CA',
    postal_code: '94108',
    country: 'USA',
    emergency_contact_name: 'Mike Johnson',
    emergency_contact_phone: '******-0302',
    emergency_contact_relationship: 'Spouse',
    status: 'active',
    is_deleted: false,
    created_by: 1,
    updated_by: 1,
  },
  {
    id: 4,
    email: '<EMAIL>',
    first_name: 'David',
    last_name: 'Wilson',
    employee_id: 'EMP004',
    phone_number: '******-0401',
    role: 'accountant',
    department: { id: 3, name: 'Finance', description: 'Financial Operations', created_at: '2023-01-01', updated_at: '2023-01-01' },
    is_active: true,
    date_joined: '2023-02-01',
    job_title: 'Senior Accountant',
    position: 'Senior Accountant',
    department_id: 3,
    department_name: 'Finance',
    hire_date: '2023-02-01',
    employment_type: 'full_time',
    work_location: 'remote',
    salary: 75000,
    hourly_rate: 36,
    currency: 'USD',
    pay_frequency: 'monthly',
    bank_name: 'Citibank',
    bank_account: '****3456',
    nssf_number: 'NSSF004',
    nhif_number: 'NHIF004',
    kra_pin: 'KRA004',
    national_id: '********',
    date_of_birth: '1992-07-25',
    gender: 'male',
    marital_status: 'single',
    nationality: 'American',
    address: '321 Elm St',
    city: 'Oakland',
    state: 'CA',
    postal_code: '94601',
    country: 'USA',
    emergency_contact_name: 'Linda Wilson',
    emergency_contact_phone: '******-0402',
    emergency_contact_relationship: 'Mother',
    status: 'active',
    is_deleted: false,
    created_by: 1,
    updated_by: 1,
  },
  {
    id: 5,
    email: '<EMAIL>',
    first_name: 'Emily',
    last_name: 'Davis',
    employee_id: 'EMP005',
    phone_number: '******-0501',
    role: 'employee',
    department: { id: 4, name: 'Marketing', description: 'Marketing and Sales', created_at: '2023-01-01', updated_at: '2023-01-01' },
    is_active: true,
    date_joined: '2023-04-01',
    job_title: 'Marketing Specialist',
    position: 'Marketing Specialist',
    department_id: 4,
    department_name: 'Marketing',
    hire_date: '2023-04-01',
    employment_type: 'part_time',
    work_location: 'hybrid',
    salary: 55000,
    hourly_rate: 26,
    currency: 'USD',
    pay_frequency: 'monthly',
    bank_name: 'US Bank',
    bank_account: '****7890',
    nssf_number: 'NSSF005',
    nhif_number: 'NHIF005',
    kra_pin: 'KRA005',
    national_id: '********',
    date_of_birth: '1995-12-03',
    gender: 'female',
    marital_status: 'single',
    nationality: 'American',
    address: '654 Maple Ave',
    city: 'Berkeley',
    state: 'CA',
    postal_code: '94702',
    country: 'USA',
    emergency_contact_name: 'Robert Davis',
    emergency_contact_phone: '******-0502',
    emergency_contact_relationship: 'Father',
    status: 'active',
    is_deleted: false,
    created_by: 1,
    updated_by: 1,
  },
];

let mockDepartments: Department[] = [
  {
    id: 1,
    name: 'Engineering',
    description: 'Software Development and Technology',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  },
  {
    id: 2,
    name: 'Human Resources',
    description: 'HR Operations and People Management',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  },
  {
    id: 3,
    name: 'Finance',
    description: 'Financial Operations and Accounting',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  },
  {
    id: 4,
    name: 'Marketing',
    description: 'Marketing and Sales Operations',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  },
  {
    id: 5,
    name: 'Operations',
    description: 'Operations and Process Management',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  },
];

// Utility functions
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const generateId = () => Math.max(...mockEmployees.map(emp => emp.id), 0) + 1;

const filterEmployees = (employees: Employee[], filters: EmployeeFilters): Employee[] => {
  return employees.filter(employee => {
    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const matchesSearch =
        employee.first_name.toLowerCase().includes(searchLower) ||
        employee.last_name.toLowerCase().includes(searchLower) ||
        employee.email.toLowerCase().includes(searchLower) ||
        employee.employee_id.toLowerCase().includes(searchLower) ||
        employee.job_title.toLowerCase().includes(searchLower);

      if (!matchesSearch) return false;
    }

    // Department filter
    if (filters.department && employee.department?.id !== filters.department) {
      return false;
    }

    // Status filter
    if (filters.status && employee.status !== filters.status) {
      return false;
    }

    // Employment type filter
    if (filters.employment_type && employee.employment_type !== filters.employment_type) {
      return false;
    }

    // Role filter
    if (filters.role && employee.role !== filters.role) {
      return false;
    }

    // Supervisor filter
    if (filters.supervisor && employee.supervisor_id !== filters.supervisor) {
      return false;
    }

    // Hire date range filter
    if (filters.hire_date_from || filters.hire_date_to) {
      const hireDate = new Date(employee.hire_date);
      if (filters.hire_date_from && hireDate < new Date(filters.hire_date_from)) {
        return false;
      }
      if (filters.hire_date_to && hireDate > new Date(filters.hire_date_to)) {
        return false;
      }
    }

    // Salary range filter
    if (filters.salary_min && employee.salary < filters.salary_min) {
      return false;
    }
    if (filters.salary_max && employee.salary > filters.salary_max) {
      return false;
    }

    return true;
  });
};

// Mock user credentials for login
const mockCredentials = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    userId: 1
  },
  {
    email: '<EMAIL>',
    password: 'supervisor123',
    userId: 2
  },
  {
    email: '<EMAIL>',
    password: 'employee123',
    userId: 1 // Maria Cotton (first employee)
  },
  {
    email: '<EMAIL>',
    password: 'hr123',
    userId: 3 // Sarah Johnson (HR)
  },
  {
    email: '<EMAIL>',
    password: 'accountant123',
    userId: 4 // David Wilson (Accountant)
  }
];

// Mock users for authentication
const mockUsers: User[] = [
  {
    id: 1,
    email: '<EMAIL>',
    first_name: 'Admin',
    last_name: 'User',
    employee_id: 'ADMIN001',
    phone_number: '+254-700-000-001',
    profile_picture: '/api/placeholder/100/100',
    role: 'hr',
    department: mockDepartments[1], // HR Department
    is_active: true,
    date_joined: '2022-01-01',
  },
  {
    id: 2,
    email: '<EMAIL>',
    first_name: 'John',
    last_name: 'Smith',
    employee_id: 'SUP001',
    phone_number: '+254-700-000-002',
    profile_picture: '/api/placeholder/100/100',
    role: 'supervisor',
    department: { id: 5, name: 'Operations', description: 'Operations Department', created_at: '2023-01-01', updated_at: '2023-01-01' },
    is_active: true,
    date_joined: '2022-06-01',
  },
  {
    id: 3,
    email: '<EMAIL>',
    first_name: 'Sarah',
    last_name: 'Johnson',
    employee_id: 'HR001',
    phone_number: '+254-700-000-003',
    profile_picture: '/api/placeholder/100/100',
    role: 'hr',
    department: mockDepartments[1], // HR Department
    is_active: true,
    date_joined: '2022-03-15',
  },
  {
    id: 4,
    email: '<EMAIL>',
    first_name: 'David',
    last_name: 'Wilson',
    employee_id: 'ACC001',
    phone_number: '+254-700-000-004',
    profile_picture: '/api/placeholder/100/100',
    role: 'accountant',
    department: mockDepartments[2], // Finance Department
    is_active: true,
    date_joined: '2023-02-01',
  },
  {
    id: 5,
    email: '<EMAIL>',
    first_name: 'Maria',
    last_name: 'Cotton',
    employee_id: 'EMP001',
    phone_number: '+254-700-000-005',
    profile_picture: '/api/placeholder/100/100',
    role: 'employee',
    department: { id: 5, name: 'Operations', description: 'Operations Department', created_at: '2023-01-01', updated_at: '2023-01-01' },
    is_active: true,
    date_joined: '2023-01-15',
  }
];

// Store current user email for mock authentication
let currentUserEmail: string | null = null;

export const mockApi = {
  // Authentication
  async login(credentials: LoginCredentials): Promise<AuthTokens> {
    await delay(1000);

    // Find matching credentials
    const validCredential = mockCredentials.find(
      cred => cred.email === credentials.email && cred.password === credentials.password
    );

    if (!validCredential) {
      throw new Error('Invalid email or password');
    }

    // Store current user email for getCurrentUser
    currentUserEmail = credentials.email;

    return {
      access: 'mock-access-token-' + Date.now(),
      refresh: 'mock-refresh-token-' + Date.now(),
    };
  },

  async getCurrentUser(): Promise<User> {
    await delay(500);

    if (!currentUserEmail) {
      throw new Error('No user logged in');
    }

    const user = mockUsers.find(u => u.email === currentUserEmail);
    if (!user) {
      throw new Error('User not found');
    }

    return user;
  },

  // Logout helper
  logout(): void {
    currentUserEmail = null;
  },

  // Employee CRUD Operations
  async getEmployees(
    filters: EmployeeFilters = {},
    page: number = 1,
    pageSize: number = 20
  ): Promise<PaginatedResponse<Employee>> {
    await delay(800);

    const activeEmployees = mockEmployees.filter(emp => !emp.is_deleted);
    const filteredEmployees = filterEmployees(activeEmployees, filters);

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedEmployees = filteredEmployees.slice(startIndex, endIndex);

    return {
      count: filteredEmployees.length,
      next: endIndex < filteredEmployees.length ? `page=${page + 1}` : undefined,
      previous: page > 1 ? `page=${page - 1}` : undefined,
      results: paginatedEmployees,
    };
  },

  async getEmployee(id: number): Promise<Employee> {
    await delay(500);

    const employee = mockEmployees.find(emp => emp.id === id && !emp.is_deleted);
    if (!employee) {
      throw new Error('Employee not found');
    }

    return employee;
  },

  async createEmployee(data: EmployeeFormData): Promise<Employee> {
    await delay(1000);

    // Check for unique employee ID
    const existingEmployee = mockEmployees.find(emp =>
      emp.employee_id === data.employee_id && !emp.is_deleted
    );
    if (existingEmployee) {
      throw new Error('Employee ID already exists');
    }

    // Check for unique email
    const existingEmail = mockEmployees.find(emp =>
      emp.email === data.email && !emp.is_deleted
    );
    if (existingEmail) {
      throw new Error('Email already exists');
    }

    const department = mockDepartments.find(dept => dept.id === data.department);

    const newEmployee: Employee = {
      id: generateId(),
      email: data.email,
      first_name: data.first_name,
      last_name: data.last_name,
      employee_id: data.employee_id,
      phone_number: data.phone_number,
      role: data.role as any,
      department,
      is_active: data.status === 'active',
      date_joined: new Date().toISOString(),
      job_title: data.job_title,
      position: data.job_title,
      department_id: data.department,
      department_name: department?.name || '',
      hire_date: data.hire_date,
      employment_type: data.employment_type as any,
      work_location: data.work_location as any,
      supervisor_id: data.supervisor_id,
      salary: data.salary,
      hourly_rate: data.hourly_rate,
      currency: data.currency,
      pay_frequency: data.pay_frequency as any,
      bank_name: data.bank_name,
      bank_account: data.bank_account,
      nssf_number: data.nssf_number,
      nhif_number: data.nhif_number,
      kra_pin: data.kra_pin,
      national_id: data.national_id,
      date_of_birth: data.date_of_birth,
      gender: data.gender as any,
      marital_status: data.marital_status as any,
      nationality: data.nationality,
      address: data.address,
      city: data.city,
      state: data.state,
      postal_code: data.postal_code,
      country: data.country,
      emergency_contact_name: data.emergency_contact_name,
      emergency_contact_phone: data.emergency_contact_phone,
      emergency_contact_relationship: data.emergency_contact_relationship,
      status: data.status as any,
      is_deleted: false,
      created_by: 1,
      updated_by: 1,
    };

    mockEmployees.push(newEmployee);
    return newEmployee;
  },

  async updateEmployee(id: number, data: Partial<EmployeeFormData>): Promise<Employee> {
    await delay(800);

    const employeeIndex = mockEmployees.findIndex(emp => emp.id === id && !emp.is_deleted);
    if (employeeIndex === -1) {
      throw new Error('Employee not found');
    }

    // Check for unique employee ID if being updated
    if (data.employee_id) {
      const existingEmployee = mockEmployees.find(emp =>
        emp.employee_id === data.employee_id && emp.id !== id && !emp.is_deleted
      );
      if (existingEmployee) {
        throw new Error('Employee ID already exists');
      }
    }

    // Check for unique email if being updated
    if (data.email) {
      const existingEmail = mockEmployees.find(emp =>
        emp.email === data.email && emp.id !== id && !emp.is_deleted
      );
      if (existingEmail) {
        throw new Error('Email already exists');
      }
    }

    const department = data.department ? mockDepartments.find(dept => dept.id === data.department) : undefined;

    const updatedEmployee: Employee = {
      ...mockEmployees[employeeIndex],
      ...(data as any),
      department: department || mockEmployees[employeeIndex].department,
      position: data.job_title || mockEmployees[employeeIndex].position,
      department_id: data.department || mockEmployees[employeeIndex].department_id,
      department_name: department?.name || mockEmployees[employeeIndex].department_name,
      updated_by: 1,
    };

    mockEmployees[employeeIndex] = updatedEmployee;
    return updatedEmployee;
  },

  async deleteEmployee(id: number): Promise<void> {
    await delay(500);

    const employeeIndex = mockEmployees.findIndex(emp => emp.id === id && !emp.is_deleted);
    if (employeeIndex === -1) {
      throw new Error('Employee not found');
    }

    // Soft delete
    mockEmployees[employeeIndex] = {
      ...mockEmployees[employeeIndex],
      is_deleted: true,
      deleted_at: new Date().toISOString(),
      deleted_by: 1,
    };
  },

  async restoreEmployee(id: number): Promise<Employee> {
    await delay(500);

    const employeeIndex = mockEmployees.findIndex(emp => emp.id === id && emp.is_deleted);
    if (employeeIndex === -1) {
      throw new Error('Deleted employee not found');
    }

    mockEmployees[employeeIndex] = {
      ...mockEmployees[employeeIndex],
      is_deleted: false,
      deleted_at: undefined,
      deleted_by: undefined,
    };

    return mockEmployees[employeeIndex];
  },

  // Bulk operations
  async bulkEmployeeAction(action: EmployeeBulkAction): Promise<{ success: number; failed: number; errors?: string[] }> {
    await delay(1000);

    let success = 0;
    let failed = 0;
    const errors: string[] = [];

    for (const employeeId of action.employee_ids) {
      try {
        const employeeIndex = mockEmployees.findIndex(emp => emp.id === employeeId && !emp.is_deleted);
        if (employeeIndex === -1) {
          failed++;
          errors.push(`Employee with ID ${employeeId} not found`);
          continue;
        }

        switch (action.action) {
          case 'activate':
            mockEmployees[employeeIndex].status = 'active';
            mockEmployees[employeeIndex].is_active = true;
            break;
          case 'deactivate':
            mockEmployees[employeeIndex].status = 'inactive';
            mockEmployees[employeeIndex].is_active = false;
            break;
          case 'delete':
            mockEmployees[employeeIndex].is_deleted = true;
            mockEmployees[employeeIndex].deleted_at = new Date().toISOString();
            mockEmployees[employeeIndex].deleted_by = 1;
            break;
          case 'transfer':
            if (action.data?.department) {
              const department = mockDepartments.find(dept => dept.id === action.data!.department);
              if (department) {
                mockEmployees[employeeIndex].department = department;
              }
            }
            break;
          case 'update_role':
            if (action.data?.role) {
              mockEmployees[employeeIndex].role = action.data.role as any;
            }
            break;
        }

        mockEmployees[employeeIndex].updated_by = 1;
        success++;
      } catch (error) {
        failed++;
        errors.push(`Failed to process employee ${employeeId}: ${error}`);
      }
    }

    return { success, failed, errors: errors.length > 0 ? errors : undefined };
  },

  // File operations
  async uploadEmployeeProfilePicture(id: number, file: File): Promise<{ profile_picture: string }> {
    await delay(1500);

    const employeeIndex = mockEmployees.findIndex(emp => emp.id === id && !emp.is_deleted);
    if (employeeIndex === -1) {
      throw new Error('Employee not found');
    }

    // Simulate file upload
    const profilePictureUrl = `/api/uploads/profiles/${id}_${Date.now()}.jpg`;
    mockEmployees[employeeIndex].profile_picture = profilePictureUrl;

    return { profile_picture: profilePictureUrl };
  },

  async uploadEmployeeDocument(
    id: number,
    file: File,
    documentType: string
  ): Promise<{ document_url: string; document_id: number }> {
    await delay(1200);

    const employee = mockEmployees.find(emp => emp.id === id && !emp.is_deleted);
    if (!employee) {
      throw new Error('Employee not found');
    }

    // Simulate document upload
    const documentUrl = `/api/uploads/documents/${id}_${documentType}_${Date.now()}.pdf`;
    const documentId = Math.floor(Math.random() * 10000);

    return { document_url: documentUrl, document_id: documentId };
  },

  // Statistics
  async getEmployeeStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    on_leave: number;
    terminated: number;
    by_department: { department: string; count: number }[];
    by_role: { role: string; count: number }[];
  }> {
    await delay(600);

    const activeEmployees = mockEmployees.filter(emp => !emp.is_deleted);

    const stats = {
      total: activeEmployees.length,
      active: activeEmployees.filter(emp => emp.status === 'active').length,
      inactive: activeEmployees.filter(emp => emp.status === 'inactive').length,
      on_leave: activeEmployees.filter(emp => emp.status === 'on_leave').length,
      terminated: activeEmployees.filter(emp => emp.status === 'terminated').length,
      by_department: [] as { department: string; count: number }[],
      by_role: [] as { role: string; count: number }[],
    };

    // Group by department
    const departmentCounts: { [key: string]: number } = {};
    activeEmployees.forEach(emp => {
      const deptName = emp.department?.name || 'No Department';
      departmentCounts[deptName] = (departmentCounts[deptName] || 0) + 1;
    });
    stats.by_department = Object.entries(departmentCounts).map(([department, count]) => ({
      department,
      count,
    }));

    // Group by role
    const roleCounts: { [key: string]: number } = {};
    activeEmployees.forEach(emp => {
      roleCounts[emp.role] = (roleCounts[emp.role] || 0) + 1;
    });
    stats.by_role = Object.entries(roleCounts).map(([role, count]) => ({
      role,
      count,
    }));

    return stats;
  },

  // Department operations
  async getDepartments(): Promise<Department[]> {
    await delay(400);
    return [...mockDepartments];
  },

  // Supervisor operations
  async getSupervisors(): Promise<Employee[]> {
    await delay(500);
    return mockEmployees.filter(emp =>
      !emp.is_deleted &&
      (emp.role === 'supervisor' || emp.role === 'hr' || emp.role === 'admin')
    );
  },

  // Export operations
  async exportEmployees(
    format: 'csv' | 'excel' | 'pdf',
    filters?: EmployeeFilters
  ): Promise<{ download_url: string }> {
    await delay(2000);

    const filteredEmployees = filters ? filterEmployees(mockEmployees.filter(emp => !emp.is_deleted), filters) : mockEmployees.filter(emp => !emp.is_deleted);

    // Simulate file generation
    const timestamp = Date.now();
    const downloadUrl = `/api/exports/employees_${format}_${timestamp}.${format === 'pdf' ? 'pdf' : format === 'excel' ? 'xlsx' : 'csv'}`;

    return { download_url: downloadUrl };
  },

  // Import operations
  async importEmployees(file: File): Promise<{
    success: number;
    failed: number;
    errors?: string[];
    preview?: Employee[];
  }> {
    await delay(3000);

    // Simulate file processing
    const mockImportResults = {
      success: Math.floor(Math.random() * 10) + 5,
      failed: Math.floor(Math.random() * 3),
      errors: [
        'Row 3: Invalid email format',
        'Row 7: Employee ID already exists',
      ],
      preview: mockEmployees.slice(0, 3), // Show preview of first 3 employees
    };

    return mockImportResults;
  },

  // Validation operations
  async validateEmployeeData(data: EmployeeFormData): Promise<{
    valid: boolean;
    errors: { [key: string]: string[] };
  }> {
    await delay(500);

    const errors: { [key: string]: string[] } = {};

    // Validate required fields
    if (!data.first_name?.trim()) {
      errors.first_name = ['First name is required'];
    }
    if (!data.last_name?.trim()) {
      errors.last_name = ['Last name is required'];
    }
    if (!data.email?.trim()) {
      errors.email = ['Email is required'];
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.email = ['Invalid email format'];
    }
    if (!data.employee_id?.trim()) {
      errors.employee_id = ['Employee ID is required'];
    }

    // Validate salary
    if (data.salary && data.salary < 0) {
      errors.salary = ['Salary must be positive'];
    }

    // Validate phone number
    if (data.phone_number && !/^\+?[\d\s\-\(\)]+$/.test(data.phone_number)) {
      errors.phone_number = ['Invalid phone number format'];
    }

    return {
      valid: Object.keys(errors).length === 0,
      errors,
    };
  },

  // Uniqueness checks
  async checkEmployeeIdUnique(employeeId: string, excludeId?: number): Promise<boolean> {
    await delay(300);

    const existing = mockEmployees.find(emp =>
      emp.employee_id === employeeId &&
      !emp.is_deleted &&
      emp.id !== excludeId
    );

    return !existing;
  },

  async checkEmailUnique(email: string, excludeId?: number): Promise<boolean> {
    await delay(300);

    const existing = mockEmployees.find(emp =>
      emp.email === email &&
      !emp.is_deleted &&
      emp.id !== excludeId
    );

    return !existing;
  },
};
