'use client';

import React, { useState, useEffect } from 'react';
import { 
  Crown, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Users, 
  Building, 
  Mail,
  Phone,
  Calendar,
  MoreHorizontal,
  Eye,
  UserCheck,
  UserX,
  Award,
  Target,
  TrendingUp
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useAuth } from '@/providers/AuthProvider';
import { cn, getInitials, formatDate } from '@/lib/utils';

interface TeamLead {
  id: number;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  profile_picture?: string;
  department_id: number;
  department_name: string;
  team_size: number;
  hire_date: string;
  lead_since: string;
  performance_rating: number;
  projects_managed: number;
  status: 'active' | 'inactive';
  leadership_level: 'junior' | 'senior' | 'principal';
}

const TeamLeadsPage: React.FC = () => {
  const { user } = useAuth();
  const [teamLeads, setTeamLeads] = useState<TeamLead[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterDepartment, setFilterDepartment] = useState('');
  const [filterLevel, setFilterLevel] = useState('');

  // Mock data
  const mockTeamLeads: TeamLead[] = [
    {
      id: 1,
      employee_id: 'EMP002',
      first_name: 'John',
      last_name: 'Smith',
      email: '<EMAIL>',
      phone_number: '******-0201',
      profile_picture: '/api/placeholder/100/100',
      department_id: 1,
      department_name: 'Engineering',
      team_size: 12,
      hire_date: '2022-06-01',
      lead_since: '2023-01-15',
      performance_rating: 4.8,
      projects_managed: 8,
      status: 'active',
      leadership_level: 'senior'
    },
    {
      id: 2,
      employee_id: 'EMP003',
      first_name: 'Sarah',
      last_name: 'Johnson',
      email: '<EMAIL>',
      phone_number: '******-0301',
      department_id: 2,
      department_name: 'Human Resources',
      team_size: 6,
      hire_date: '2022-03-15',
      lead_since: '2022-09-01',
      performance_rating: 4.9,
      projects_managed: 12,
      status: 'active',
      leadership_level: 'principal'
    },
    {
      id: 3,
      employee_id: 'EMP004',
      first_name: 'David',
      last_name: 'Wilson',
      email: '<EMAIL>',
      phone_number: '******-0401',
      department_id: 3,
      department_name: 'Finance',
      team_size: 8,
      hire_date: '2023-02-01',
      lead_since: '2023-08-15',
      performance_rating: 4.6,
      projects_managed: 5,
      status: 'active',
      leadership_level: 'junior'
    },
    {
      id: 4,
      employee_id: 'EMP005',
      first_name: 'Emily',
      last_name: 'Davis',
      email: '<EMAIL>',
      phone_number: '******-0501',
      department_id: 4,
      department_name: 'Marketing',
      team_size: 10,
      hire_date: '2023-04-01',
      lead_since: '2023-10-01',
      performance_rating: 4.7,
      projects_managed: 6,
      status: 'active',
      leadership_level: 'senior'
    }
  ];

  useEffect(() => {
    const loadTeamLeads = async () => {
      setLoading(true);
      setTimeout(() => {
        setTeamLeads(mockTeamLeads);
        setLoading(false);
      }, 1000);
    };

    loadTeamLeads();
  }, []);

  const filteredTeamLeads = teamLeads.filter(lead => {
    const matchesSearch = 
      lead.first_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      lead.last_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      lead.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      lead.department_name.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesDepartment = !filterDepartment || lead.department_name === filterDepartment;
    const matchesLevel = !filterLevel || lead.leadership_level === filterLevel;
    
    return matchesSearch && matchesDepartment && matchesLevel;
  });

  const departments = Array.from(new Set(teamLeads.map(lead => lead.department_name)));
  const totalTeamSize = teamLeads.reduce((sum, lead) => sum + lead.team_size, 0);
  const avgPerformance = teamLeads.reduce((sum, lead) => sum + lead.performance_rating, 0) / teamLeads.length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Team Leads Management</h1>
            <p className="text-gray-600 mt-1">
              Manage team leaders and track their performance across departments
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <Button variant="primary">
              <Plus className="h-4 w-4 mr-2" />
              Assign Team Lead
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Crown className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Team Leads</p>
                <p className="text-2xl font-semibold text-gray-900">{teamLeads.length}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Team Members</p>
                <p className="text-2xl font-semibold text-gray-900">{totalTeamSize}</p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Award className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Avg Performance</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {avgPerformance ? avgPerformance.toFixed(1) : '0.0'}
                </p>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Building className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Departments</p>
                <p className="text-2xl font-semibold text-gray-900">{departments.length}</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <div className="w-full sm:w-80">
              <Input
                placeholder="Search team leads..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={<Search className="h-4 w-4" />}
              />
            </div>
            
            <select
              value={filterDepartment}
              onChange={(e) => setFilterDepartment(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="">All Departments</option>
              {departments.map(dept => (
                <option key={dept} value={dept}>{dept}</option>
              ))}
            </select>
            
            <select
              value={filterLevel}
              onChange={(e) => setFilterLevel(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="">All Levels</option>
              <option value="junior">Junior</option>
              <option value="senior">Senior</option>
              <option value="principal">Principal</option>
            </select>
          </div>
          
          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-600">
              {filteredTeamLeads.length} team leads
            </span>
          </div>
        </div>
      </div>

      {/* Team Leads Grid */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="p-6">
            <div className="animate-pulse space-y-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTeamLeads.map((teamLead) => (
                <TeamLeadCard key={teamLead.id} teamLead={teamLead} />
              ))}
            </div>
          </div>
        )}

        {!loading && filteredTeamLeads.length === 0 && (
          <div className="text-center py-12">
            <Crown className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No team leads found
            </h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || filterDepartment || filterLevel 
                ? 'Try adjusting your search or filter criteria.' 
                : 'Get started by assigning team leads to departments.'
              }
            </p>
            <Button variant="primary">
              <Plus className="h-4 w-4 mr-2" />
              Assign Team Lead
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

// Team Lead Card Component
interface TeamLeadCardProps {
  teamLead: TeamLead;
}

const TeamLeadCard: React.FC<TeamLeadCardProps> = ({ teamLead }) => {
  const [showActions, setShowActions] = useState(false);
  const userInitials = getInitials(teamLead.first_name, teamLead.last_name);

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'junior': return 'bg-blue-100 text-blue-800';
      case 'senior': return 'bg-purple-100 text-purple-800';
      case 'principal': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPerformanceColor = (rating: number) => {
    if (rating >= 4.5) return 'text-green-600';
    if (rating >= 4.0) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <Card className="relative hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        {/* Actions Menu */}
        <div className="absolute top-4 right-4">
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-1 rounded-full hover:bg-gray-100"
            >
              <MoreHorizontal className="h-4 w-4 text-gray-500" />
            </button>

            {showActions && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <Eye className="h-4 w-4 mr-2" />
                    View Profile
                  </button>
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Details
                  </button>
                  <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <Users className="h-4 w-4 mr-2" />
                    Manage Team
                  </button>
                  <button className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                    <UserX className="h-4 w-4 mr-2" />
                    Remove Lead
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Team Lead Info */}
        <div className="flex items-center mb-4">
          {teamLead.profile_picture ? (
            <img
              src={teamLead.profile_picture}
              alt={`${teamLead.first_name} ${teamLead.last_name}`}
              className="w-12 h-12 rounded-full object-cover mr-3"
            />
          ) : (
            <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mr-3">
              <span className="text-white font-medium">{userInitials}</span>
            </div>
          )}
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900">
              {teamLead.first_name} {teamLead.last_name}
            </h3>
            <p className="text-sm text-gray-600">{teamLead.department_name}</p>
            <span className={cn(
              'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1',
              getLevelColor(teamLead.leadership_level)
            )}>
              {teamLead.leadership_level.charAt(0).toUpperCase() + teamLead.leadership_level.slice(1)} Lead
            </span>
          </div>
        </div>

        {/* Performance & Stats */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm text-gray-600">
              <Users className="h-4 w-4 mr-2" />
              <span>Team Size</span>
            </div>
            <span className="text-sm font-medium text-gray-900">{teamLead.team_size}</span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm text-gray-600">
              <Award className="h-4 w-4 mr-2" />
              <span>Performance</span>
            </div>
            <span className={cn('text-sm font-medium', getPerformanceColor(teamLead.performance_rating))}>
              {teamLead.performance_rating}/5.0
            </span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm text-gray-600">
              <Target className="h-4 w-4 mr-2" />
              <span>Projects</span>
            </div>
            <span className="text-sm font-medium text-gray-900">{teamLead.projects_managed}</span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm text-gray-600">
              <Calendar className="h-4 w-4 mr-2" />
              <span>Lead Since</span>
            </div>
            <span className="text-sm font-medium text-gray-900">
              {formatDate(teamLead.lead_since)}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm text-gray-600">
              <Mail className="h-4 w-4 mr-2" />
              <span>Contact</span>
            </div>
            <span className="text-sm font-medium text-gray-900 truncate">
              {teamLead.email}
            </span>
          </div>
        </div>

        {/* Status Badge */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <span className={cn(
            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
            teamLead.status === 'active' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-gray-100 text-gray-800'
          )}>
            {teamLead.status === 'active' ? 'Active Lead' : 'Inactive'}
          </span>
        </div>
      </div>
    </Card>
  );
};

export default TeamLeadsPage;
