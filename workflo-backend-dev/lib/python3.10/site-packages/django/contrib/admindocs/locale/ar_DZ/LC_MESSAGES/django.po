# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON>, 2022
# Rite<PERSON> <<EMAIL>>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2022-07-24 20:19+0000\n"
"Last-Translator: <PERSON><PERSON>n"
"Language-Team: Arabic (Algeria) (http://www.transifex.com/django/django/"
"language/ar_DZ/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar_DZ\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

msgid "Administrative Documentation"
msgstr "الوثائق الإدارية"

msgid "Home"
msgstr "الرئيسية"

msgid "Documentation"
msgstr "الوثائق"

msgid "Bookmarklets"
msgstr "أوامر المفضلة"

msgid "Documentation bookmarklets"
msgstr "أوامر مفضلة للوثائق"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"لتثبيت أوامر المفضلة، اسحب الرابط إلى المواقع المفضلة في شريط الأدوات الخاص "
"بك، أو إضغط بزر الماوس الأيمن على الرابط وأضفه إلى قائمة المواقع المفضلة "
"الخاصة بك. الآن يمكنك اختيار أوامر المفضلة من أي صفحة في الموقع."

msgid "Documentation for this page"
msgstr "الوثائق لهذه الصفحة"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "ينتقل بك من أي صفحة إلى وثائق العرض الذي أنشأ هذه الصفحة."

msgid "Tags"
msgstr "الوسوم"

msgid "List of all the template tags and their functions."
msgstr "قائمة بجميع وسوم القوالب ووظائفهم."

msgid "Filters"
msgstr "الفلاتر"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"المرشحات هي الإجراءات التي يمكن تطبيقها على المتغيرات في قالب لتغيير الناتج."

msgid "Models"
msgstr "النماذج"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"الموديلات هي وصف لجميع الكائنات في النظام والحقول المرتبطة بها. كل موديول "
"يحتوي على قائمة الحقول التي يمكن الوصول إليها كمتغيرات قالب"

msgid "Views"
msgstr "إستعراض"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"يتم إنشاء كل صفحة على الموقع علني من خلال عرض. يعرّف العرض الذي يستخدم كقالب "
"لتوليد الصفحة والتي هي الكائنات المتاحة لذلك القالب."

msgid "Tools for your browser to quickly access admin functionality."
msgstr "أدوات المتصفح لكي تتمكن من الوصول بسرعة وظائف المشرف."

msgid "Please install docutils"
msgstr "الرجاء تثبيت docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"نظام إدارة التوثيق يحتاج إلى<a href=\"%(link)s\"> وثيقة</a>من مكتبة بايثون "

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"الرجاء الطلب من مشرف النظام لتنصيب <a href=\\\"%(link)s\\\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "المودل: %(name)s"

msgid "Fields"
msgstr "الحقول"

msgid "Field"
msgstr "حقل"

msgid "Type"
msgstr "نوع"

msgid "Description"
msgstr "وصف"

msgid "Methods with arguments"
msgstr "طرق بمعطيات"

msgid "Method"
msgstr "الطريقة"

msgid "Arguments"
msgstr "معطيات"

msgid "Back to Model documentation"
msgstr "العودة إلى وثائق الموديول"

msgid "Model documentation"
msgstr "وثائق الموديول"

msgid "Model groups"
msgstr "مجموعات الموديول"

msgid "Templates"
msgstr "القوالب"

#, python-format
msgid "Template: %(name)s"
msgstr "قالب: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "قالب: \\\"%(name)s\\\""

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "البحث عن مسار القالب \\\"%(name)s\\\":"

msgid "(does not exist)"
msgstr "(غير موجود)"

msgid "Back to Documentation"
msgstr "العودة إلى الوثائق"

msgid "Template filters"
msgstr "مرشحات القالب"

msgid "Template filter documentation"
msgstr "وثائق مرشح القالب"

msgid "Built-in filters"
msgstr "المرشحات المدمجة"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"لإستخدام هذه المرشحات, الرجاء وضع <code>%(code)s</code> في القالب الخاص بك "
"قبل إستخدام المرشّح."

msgid "Template tags"
msgstr "وسوم القالب"

msgid "Template tag documentation"
msgstr "وثائق وسم القالب"

msgid "Built-in tags"
msgstr "المسوم المدمجة"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"لإستخدام هذه الوسوم, الرجاء وضع <code>%(code)s</code> في القالب الخاص بك قبل "
"إستخدام الوسم."

#, python-format
msgid "View: %(name)s"
msgstr "عرض: %(name)s"

msgid "Context:"
msgstr "السياق:"

msgid "Templates:"
msgstr "القوالب:"

msgid "Back to View documentation"
msgstr "العودة لـ : عرض الوثائق"

msgid "View documentation"
msgstr "عرض الوثائق"

msgid "Jump to namespace"
msgstr "القفز إلى مساحة الاسم"

msgid "Empty namespace"
msgstr "مساحة إسم فارغة"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "العروض عبر مساحة الإسم %(name)s"

msgid "Views by empty namespace"
msgstr "العروض عبر مساحة الإسم الفارغة"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
" عرض الخاصية: <code>%(full_name)s</code>. الإسم: <code>%(url_name)s</code>."
"\\n\n"

msgid "tag:"
msgstr "وسم:"

msgid "filter:"
msgstr "مرشّح"

msgid "view:"
msgstr "عرض:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "البرنامج %(app_label)r غير موجود"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "النموذج %(model_name)r غير موجود في التطبيق %(app_label)r"

msgid "model:"
msgstr "نموذج:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "العنصر `%(app_label)s.%(data_type)s` المرتبط"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "عناصر `%(app_label)s.%(object_name)s` مرتبطة"

#, python-format
msgid "all %s"
msgstr "كل %s"

#, python-format
msgid "number of %s"
msgstr "عدد %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "يبدو أن %s ليس عنصر urlpattern"
