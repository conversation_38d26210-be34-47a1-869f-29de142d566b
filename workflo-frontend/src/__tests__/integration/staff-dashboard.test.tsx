import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '@/__tests__/utils/test-utils';
import StaffDashboard from '@/app/(auth)/(staff)/staff/page';

// Mock the current time for consistent testing
const mockDate = new Date('2024-12-20T10:30:00Z');
jest.useFakeTimers();
jest.setSystemTime(mockDate);

describe('Staff Dashboard Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('dashboard rendering', () => {
    it('renders welcome message with user name', () => {
      render(<StaffDashboard />);

      expect(screen.getByText(/Good morning, John!/)).toBeInTheDocument();
      expect(screen.getByText(/Friday, December 20, 2024/)).toBeInTheDocument();
    });

    it('displays current time', () => {
      render(<StaffDashboard />);

      // The time should be displayed (mocked to 10:30:00)
      expect(screen.getByText(/10:30/)).toBeInTheDocument();
    });

    it('shows today\'s stats cards', () => {
      render(<StaffDashboard />);

      expect(screen.getByText('Check-in Time')).toBeInTheDocument();
      expect(screen.getByText('Hours Worked')).toBeInTheDocument();
      expect(screen.getByText('Tasks Completed')).toBeInTheDocument();
      expect(screen.getByText('Leave Balance')).toBeInTheDocument();
    });
  });

  describe('quick actions', () => {
    it('renders all quick action buttons', () => {
      render(<StaffDashboard />);

      expect(screen.getByText('Check In')).toBeInTheDocument();
      expect(screen.getByText('Apply Leave')).toBeInTheDocument();
      expect(screen.getByText('View Payslip')).toBeInTheDocument();
      expect(screen.getByText('Submit Ticket')).toBeInTheDocument();
    });

    it('handles check-in action', async () => {
      const user = userEvent.setup();
      
      render(<StaffDashboard />);

      const checkInButton = screen.getByText('Check In');
      await user.click(checkInButton);

      // Should show some feedback or change state
      // This would depend on the actual implementation
    });
  });

  describe('recent activities', () => {
    it('displays recent activities section', () => {
      render(<StaffDashboard />);

      expect(screen.getByText('Recent Activities')).toBeInTheDocument();
      
      // Check for some sample activities
      expect(screen.getByText('Checked in at 08:30 AM')).toBeInTheDocument();
      expect(screen.getByText('Submitted leave application')).toBeInTheDocument();
      expect(screen.getByText('Completed project milestone')).toBeInTheDocument();
    });

    it('shows view all activities link', () => {
      render(<StaffDashboard />);

      expect(screen.getByText('View All Activities')).toBeInTheDocument();
    });
  });

  describe('upcoming events', () => {
    it('displays upcoming events section', () => {
      render(<StaffDashboard />);

      expect(screen.getByText('Upcoming Events')).toBeInTheDocument();
      
      // Check for sample events
      expect(screen.getByText('Team Meeting')).toBeInTheDocument();
      expect(screen.getByText('Performance Review')).toBeInTheDocument();
      expect(screen.getByText('Company Holiday')).toBeInTheDocument();
    });

    it('shows event details', () => {
      render(<StaffDashboard />);

      expect(screen.getByText('Weekly team sync meeting')).toBeInTheDocument();
      expect(screen.getByText('Q4 performance review session')).toBeInTheDocument();
      expect(screen.getByText('Christmas Day - Office Closed')).toBeInTheDocument();
    });
  });

  describe('announcements', () => {
    it('displays announcements section', () => {
      render(<StaffDashboard />);

      expect(screen.getByText('Announcements')).toBeInTheDocument();
      
      // Check for sample announcements
      expect(screen.getByText('New Health Insurance Policy')).toBeInTheDocument();
      expect(screen.getByText('Office Renovation Update')).toBeInTheDocument();
    });

    it('shows announcement details', () => {
      render(<StaffDashboard />);

      expect(screen.getByText(/Enhanced health insurance coverage/)).toBeInTheDocument();
      expect(screen.getByText(/Office renovation will begin/)).toBeInTheDocument();
    });
  });

  describe('responsive behavior', () => {
    it('adapts layout for mobile screens', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<StaffDashboard />);

      // Check that grid layouts adapt to mobile
      const statsGrid = screen.getByText('Check-in Time').closest('.grid');
      expect(statsGrid).toHaveClass('grid-cols-1');
    });
  });

  describe('real-time updates', () => {
    it('updates time every second', async () => {
      render(<StaffDashboard />);

      // Initial time
      expect(screen.getByText(/10:30/)).toBeInTheDocument();

      // Advance time by 1 minute
      jest.advanceTimersByTime(60000);

      await waitFor(() => {
        expect(screen.getByText(/10:31/)).toBeInTheDocument();
      });
    });

    it('updates working hours when checked in', async () => {
      const user = userEvent.setup();
      
      render(<StaffDashboard />);

      // Simulate check-in
      const checkInButton = screen.getByText('Check In');
      await user.click(checkInButton);

      // Advance time to simulate working
      jest.advanceTimersByTime(3600000); // 1 hour

      // Should show updated working hours
      await waitFor(() => {
        expect(screen.getByText(/1:00/)).toBeInTheDocument(); // 1 hour worked
      });
    });
  });

  describe('error handling', () => {
    it('handles missing user data gracefully', () => {
      render(<StaffDashboard />, {
        authContextValue: {
          user: null,
          isAuthenticated: true,
          isLoading: false,
          login: jest.fn(),
          logout: jest.fn(),
          getCurrentUser: jest.fn(),
          updateProfile: jest.fn(),
          changePassword: jest.fn(),
        }
      });

      // Should still render without crashing
      expect(screen.getByText(/Good morning/)).toBeInTheDocument();
    });

    it('shows loading state when user data is loading', () => {
      render(<StaffDashboard />, {
        authContextValue: {
          user: null,
          isAuthenticated: false,
          isLoading: true,
          login: jest.fn(),
          logout: jest.fn(),
          getCurrentUser: jest.fn(),
          updateProfile: jest.fn(),
          changePassword: jest.fn(),
        }
      });

      // Should show loading state
      expect(screen.getByText('Checking permissions...')).toBeInTheDocument();
    });
  });

  describe('accessibility', () => {
    it('has proper heading structure', () => {
      render(<StaffDashboard />);

      // Check for proper heading hierarchy
      const mainHeading = screen.getByRole('heading', { level: 1 });
      expect(mainHeading).toBeInTheDocument();

      const sectionHeadings = screen.getAllByRole('heading', { level: 2 });
      expect(sectionHeadings.length).toBeGreaterThan(0);
    });

    it('has accessible buttons', () => {
      render(<StaffDashboard />);

      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toBeVisible();
        // Each button should have accessible text
        expect(button).toHaveTextContent(/.+/);
      });
    });

    it('has proper ARIA labels where needed', () => {
      render(<StaffDashboard />);

      // Check for ARIA labels on interactive elements
      const timeDisplay = screen.getByText(/10:30/);
      expect(timeDisplay.closest('[role="timer"]')).toBeInTheDocument();
    });
  });

  describe('performance', () => {
    it('renders efficiently without unnecessary re-renders', () => {
      const renderSpy = jest.fn();
      
      const TestWrapper = () => {
        renderSpy();
        return <StaffDashboard />;
      };

      render(<TestWrapper />);

      // Should only render once initially
      expect(renderSpy).toHaveBeenCalledTimes(1);

      // Advance time slightly
      jest.advanceTimersByTime(1000);

      // Should not cause unnecessary re-renders
      expect(renderSpy).toHaveBeenCalledTimes(1);
    });
  });
});
