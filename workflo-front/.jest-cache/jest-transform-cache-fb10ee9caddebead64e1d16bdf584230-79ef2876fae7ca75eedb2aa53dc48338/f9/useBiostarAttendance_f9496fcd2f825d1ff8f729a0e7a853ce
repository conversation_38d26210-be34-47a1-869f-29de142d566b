b55993b5435a96605ac8491b4be45187
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    useBiostarAttendance: function() {
        return useBiostarAttendance;
    },
    useBiostarDevices: function() {
        return useBiostarDevices;
    },
    useBiostarRealTime: function() {
        return useBiostarRealTime;
    }
});
const _react = require("react");
const _attendanceService = require("../lib/attendanceService");
const useBiostarAttendance = (options = {})=>{
    const { employeeId, autoRefresh = true, refreshInterval = 300000, enableRealTime = true } = options;
    // State
    const [todayAttendance, setTodayAttendance] = (0, _react.useState)(null);
    const [attendanceRecords, setAttendanceRecords] = (0, _react.useState)([]);
    const [devices, setDevices] = (0, _react.useState)([]);
    const [realtimeUpdates, setRealtimeUpdates] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(true);
    const [connected, setConnected] = (0, _react.useState)(true); // Always connected for mock data
    const [error, setError] = (0, _react.useState)(null);
    const [summary, setSummary] = (0, _react.useState)(null);
    // Load attendance data (using mock data instead of BioStar API)
    const loadAttendanceData = (0, _react.useCallback)(async ()=>{
        if (!employeeId) return;
        try {
            setLoading(true);
            setError(null);
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, 500));
            // Mock today's attendance
            const today = {
                id: 'att-today',
                employee_id: employeeId,
                employee_name: 'Current User',
                date: new Date().toISOString().split('T')[0],
                first_in: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                last_out: null,
                total_hours: 2,
                break_time: 0,
                overtime: 0,
                status: 'PRESENT',
                events: [
                    {
                        id: 'event-1',
                        user_id: employeeId,
                        device_id: 'dev-001',
                        event_type: 'ENTRY',
                        datetime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                        user_name: 'Current User',
                        device_name: 'Main Entrance'
                    }
                ],
                biostar_synced: true
            };
            setTodayAttendance(today);
            // Mock attendance summary
            const summaryData = {
                todayStatus: 'PRESENT',
                checkInTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                hoursWorked: 2,
                breakTime: 0,
                overtime: 0,
                weeklyHours: 32,
                monthlyAttendance: 20
            };
            setSummary(summaryData);
            // Mock recent attendance records
            const records = Array.from({
                length: 10
            }, (_, i)=>({
                    id: `att-${i}`,
                    employee_id: employeeId,
                    employee_name: 'Current User',
                    date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    first_in: new Date(Date.now() - i * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000).toISOString(),
                    last_out: new Date(Date.now() - i * 24 * 60 * 60 * 1000 + 17 * 60 * 60 * 1000).toISOString(),
                    total_hours: 8,
                    break_time: 1,
                    overtime: 0,
                    status: i === 0 ? 'PRESENT' : 'PRESENT',
                    events: [],
                    biostar_synced: true
                }));
            setAttendanceRecords(records);
            // Mock devices
            const deviceList = [
                {
                    id: 'dev-001',
                    name: 'Main Entrance',
                    ip: '*************',
                    port: 8080,
                    status: 'ONLINE',
                    type: 'Fingerprint Scanner',
                    location: 'Main Building'
                },
                {
                    id: 'dev-002',
                    name: 'Office Floor',
                    ip: '*************',
                    port: 8080,
                    status: 'ONLINE',
                    type: 'Face Recognition',
                    location: 'Second Floor'
                },
                {
                    id: 'dev-003',
                    name: 'Back Entrance',
                    ip: '*************',
                    port: 8080,
                    status: 'OFFLINE',
                    type: 'Fingerprint Scanner',
                    location: 'Back Building'
                }
            ];
            setDevices(deviceList);
            setConnected(true);
        } catch (err) {
            console.error('Failed to load attendance data:', err);
            setError(err instanceof Error ? err.message : 'Failed to load attendance data');
            setConnected(false);
        } finally{
            setLoading(false);
        }
    }, [
        employeeId
    ]);
    // Refresh function
    const refresh = (0, _react.useCallback)(async ()=>{
        await loadAttendanceData();
    }, [
        loadAttendanceData
    ]);
    // Get attendance range (using mock data)
    const getAttendanceRange = (0, _react.useCallback)(async (startDate, endDate)=>{
        if (!employeeId) return [];
        try {
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, 300));
            // Generate mock records for the date range
            const start = new Date(startDate);
            const end = new Date(endDate);
            const records = [];
            for(let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)){
                // Skip weekends
                if (d.getDay() === 0 || d.getDay() === 6) continue;
                records.push({
                    id: `att-${d.toISOString().split('T')[0]}`,
                    employee_id: employeeId,
                    employee_name: 'Current User',
                    date: d.toISOString().split('T')[0],
                    first_in: new Date(d.getTime() + 8 * 60 * 60 * 1000).toISOString(),
                    last_out: new Date(d.getTime() + 17 * 60 * 60 * 1000).toISOString(),
                    total_hours: 8,
                    break_time: 1,
                    overtime: 0,
                    status: 'PRESENT',
                    events: [],
                    biostar_synced: true
                });
            }
            return records;
        } catch (err) {
            console.error('Failed to get attendance range:', err);
            setError(err instanceof Error ? err.message : 'Failed to get attendance range');
            return [];
        }
    }, [
        employeeId
    ]);
    // Real-time updates handler
    const handleRealTimeUpdate = (0, _react.useCallback)((update)=>{
        setRealtimeUpdates((prev)=>{
            // Add new update and keep only last 10
            const newUpdates = [
                update,
                ...prev
            ];
            return newUpdates.slice(0, 10);
        });
        // If it's for current employee, refresh today's data
        if (update.employee_id === employeeId) {
            // Debounce the refresh to avoid too many calls
            setTimeout(()=>{
                loadAttendanceData();
            }, 2000);
        }
    }, [
        employeeId,
        loadAttendanceData
    ]);
    // Initial load
    (0, _react.useEffect)(()=>{
        if (employeeId) {
            loadAttendanceData();
        }
    }, [
        employeeId,
        loadAttendanceData
    ]);
    // Auto refresh
    (0, _react.useEffect)(()=>{
        if (!autoRefresh || !employeeId) return;
        const interval = setInterval(()=>{
            loadAttendanceData();
        }, refreshInterval);
        return ()=>clearInterval(interval);
    }, [
        autoRefresh,
        refreshInterval,
        employeeId,
        loadAttendanceData
    ]);
    // Real-time monitoring (disabled - using mock data)
    (0, _react.useEffect)(()=>{
        if (!enableRealTime) return;
        // Generate mock real-time updates periodically
        const interval = setInterval(()=>{
            const mockUpdate = {
                id: `update-${Date.now()}`,
                employee_id: `EMP${Math.floor(Math.random() * 100).toString().padStart(3, '0')}`,
                employee_name: `Employee ${Math.floor(Math.random() * 100)}`,
                event_type: Math.random() > 0.5 ? 'ENTRY' : 'EXIT',
                device_name: [
                    'Main Entrance',
                    'Office Floor',
                    'Back Entrance'
                ][Math.floor(Math.random() * 3)],
                timestamp: new Date().toISOString(),
                status: 'PRESENT'
            };
            handleRealTimeUpdate(mockUpdate);
        }, 30000); // Every 30 seconds
        return ()=>clearInterval(interval);
    }, [
        enableRealTime,
        handleRealTimeUpdate
    ]);
    return {
        // Data
        todayAttendance,
        attendanceRecords,
        devices,
        realtimeUpdates,
        // Status
        loading,
        connected,
        error,
        // Actions
        refresh,
        getAttendanceRange,
        // Summary
        summary
    };
};
const useBiostarDevices = ()=>{
    const [devices, setDevices] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(true);
    const [error, setError] = (0, _react.useState)(null);
    const loadDevices = (0, _react.useCallback)(async ()=>{
        try {
            setLoading(true);
            setError(null);
            // Simulate API delay
            await new Promise((resolve)=>setTimeout(resolve, 300));
            // Mock device list
            const deviceList = [
                {
                    id: 'dev-001',
                    name: 'Main Entrance',
                    ip: '*************',
                    port: 8080,
                    status: 'ONLINE',
                    type: 'Fingerprint Scanner',
                    location: 'Main Building'
                },
                {
                    id: 'dev-002',
                    name: 'Office Floor',
                    ip: '*************',
                    port: 8080,
                    status: 'ONLINE',
                    type: 'Face Recognition',
                    location: 'Second Floor'
                },
                {
                    id: 'dev-003',
                    name: 'Back Entrance',
                    ip: '*************',
                    port: 8080,
                    status: Math.random() > 0.5 ? 'ONLINE' : 'OFFLINE',
                    type: 'Fingerprint Scanner',
                    location: 'Back Building'
                },
                {
                    id: 'dev-004',
                    name: 'Conference Room',
                    ip: '*************',
                    port: 8080,
                    status: 'ONLINE',
                    type: 'Card Reader',
                    location: 'Third Floor'
                }
            ];
            setDevices(deviceList);
        } catch (err) {
            console.error('Failed to load devices:', err);
            setError(err instanceof Error ? err.message : 'Failed to load devices');
        } finally{
            setLoading(false);
        }
    }, []);
    (0, _react.useEffect)(()=>{
        loadDevices();
        // Refresh devices every 2 minutes
        const interval = setInterval(loadDevices, 120000);
        return ()=>clearInterval(interval);
    }, [
        loadDevices
    ]);
    return {
        devices,
        loading,
        error,
        refresh: loadDevices,
        onlineDevices: devices.filter((d)=>d.status === 'ONLINE'),
        offlineDevices: devices.filter((d)=>d.status === 'OFFLINE')
    };
};
const useBiostarRealTime = ()=>{
    const [updates, setUpdates] = (0, _react.useState)([]);
    const [isMonitoring, setIsMonitoring] = (0, _react.useState)(false);
    const startMonitoring = (0, _react.useCallback)(()=>{
        if (isMonitoring) return;
        const handleUpdate = (update)=>{
            setUpdates((prev)=>[
                    update,
                    ...prev.slice(0, 19)
                ]); // Keep last 20 updates
        };
        _attendanceService.attendanceService.startRealTimeMonitoring(handleUpdate);
        setIsMonitoring(true);
        return ()=>{
            _attendanceService.attendanceService.stopRealTimeMonitoring(handleUpdate);
            setIsMonitoring(false);
        };
    }, [
        isMonitoring
    ]);
    const stopMonitoring = (0, _react.useCallback)(()=>{
        _attendanceService.attendanceService.stopRealTimeMonitoring();
        setIsMonitoring(false);
    }, []);
    const clearUpdates = (0, _react.useCallback)(()=>{
        setUpdates([]);
    }, []);
    return {
        updates,
        isMonitoring,
        startMonitoring,
        stopMonitoring,
        clearUpdates
    };
};

//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi9ob21lL2hwL0RvY3VtZW50cy9hdWdtZW50LXByb2plY3RzL25heWEvd29ya2Zsby1mcm9udC9zcmMvaG9va3MvdXNlQmlvc3RhckF0dGVuZGFuY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBhdHRlbmRhbmNlU2VydmljZSB9IGZyb20gJ0AvbGliL2F0dGVuZGFuY2VTZXJ2aWNlJztcbmltcG9ydCB7IEF0dGVuZGFuY2VSZWNvcmQsIFJlYWxUaW1lQXR0ZW5kYW5jZSwgQmlvbWV0cmljRGV2aWNlIH0gZnJvbSAnQC90eXBlcyc7XG5cbmludGVyZmFjZSBVc2VCaW9zdGFyQXR0ZW5kYW5jZU9wdGlvbnMge1xuICBlbXBsb3llZUlkPzogc3RyaW5nO1xuICBhdXRvUmVmcmVzaD86IGJvb2xlYW47XG4gIHJlZnJlc2hJbnRlcnZhbD86IG51bWJlcjtcbiAgZW5hYmxlUmVhbFRpbWU/OiBib29sZWFuO1xufVxuXG5pbnRlcmZhY2UgVXNlQmlvc3RhckF0dGVuZGFuY2VSZXR1cm4ge1xuICAvLyBEYXRhXG4gIHRvZGF5QXR0ZW5kYW5jZTogQXR0ZW5kYW5jZVJlY29yZCB8IG51bGw7XG4gIGF0dGVuZGFuY2VSZWNvcmRzOiBBdHRlbmRhbmNlUmVjb3JkW107XG4gIGRldmljZXM6IEJpb21ldHJpY0RldmljZVtdO1xuICByZWFsdGltZVVwZGF0ZXM6IFJlYWxUaW1lQXR0ZW5kYW5jZVtdO1xuXG4gIC8vIFN0YXR1c1xuICBsb2FkaW5nOiBib29sZWFuO1xuICBjb25uZWN0ZWQ6IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xuXG4gIC8vIEFjdGlvbnNcbiAgcmVmcmVzaDogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgZ2V0QXR0ZW5kYW5jZVJhbmdlOiAoc3RhcnREYXRlOiBzdHJpbmcsIGVuZERhdGU6IHN0cmluZykgPT4gUHJvbWlzZTxBdHRlbmRhbmNlUmVjb3JkW10+O1xuXG4gIC8vIFN1bW1hcnkgZGF0YVxuICBzdW1tYXJ5OiB7XG4gICAgdG9kYXlTdGF0dXM6ICdQUkVTRU5UJyB8ICdBQlNFTlQnIHwgJ0xBVEUnIHwgJ0VBUkxZX09VVCc7XG4gICAgY2hlY2tJblRpbWU/OiBzdHJpbmc7XG4gICAgY2hlY2tPdXRUaW1lPzogc3RyaW5nO1xuICAgIGhvdXJzV29ya2VkOiBudW1iZXI7XG4gICAgYnJlYWtUaW1lOiBudW1iZXI7XG4gICAgb3ZlcnRpbWU6IG51bWJlcjtcbiAgICB3ZWVrbHlIb3VyczogbnVtYmVyO1xuICAgIG1vbnRobHlBdHRlbmRhbmNlOiBudW1iZXI7XG4gIH0gfCBudWxsO1xufVxuXG5leHBvcnQgY29uc3QgdXNlQmlvc3RhckF0dGVuZGFuY2UgPSAoXG4gIG9wdGlvbnM6IFVzZUJpb3N0YXJBdHRlbmRhbmNlT3B0aW9ucyA9IHt9XG4pOiBVc2VCaW9zdGFyQXR0ZW5kYW5jZVJldHVybiA9PiB7XG4gIGNvbnN0IHtcbiAgICBlbXBsb3llZUlkLFxuICAgIGF1dG9SZWZyZXNoID0gdHJ1ZSxcbiAgICByZWZyZXNoSW50ZXJ2YWwgPSAzMDAwMDAsIC8vIDUgbWludXRlc1xuICAgIGVuYWJsZVJlYWxUaW1lID0gdHJ1ZVxuICB9ID0gb3B0aW9ucztcblxuICAvLyBTdGF0ZVxuICBjb25zdCBbdG9kYXlBdHRlbmRhbmNlLCBzZXRUb2RheUF0dGVuZGFuY2VdID0gdXNlU3RhdGU8QXR0ZW5kYW5jZVJlY29yZCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbYXR0ZW5kYW5jZVJlY29yZHMsIHNldEF0dGVuZGFuY2VSZWNvcmRzXSA9IHVzZVN0YXRlPEF0dGVuZGFuY2VSZWNvcmRbXT4oW10pO1xuICBjb25zdCBbZGV2aWNlcywgc2V0RGV2aWNlc10gPSB1c2VTdGF0ZTxCaW9tZXRyaWNEZXZpY2VbXT4oW10pO1xuICBjb25zdCBbcmVhbHRpbWVVcGRhdGVzLCBzZXRSZWFsdGltZVVwZGF0ZXNdID0gdXNlU3RhdGU8UmVhbFRpbWVBdHRlbmRhbmNlW10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtjb25uZWN0ZWQsIHNldENvbm5lY3RlZF0gPSB1c2VTdGF0ZSh0cnVlKTsgLy8gQWx3YXlzIGNvbm5lY3RlZCBmb3IgbW9jayBkYXRhXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzdW1tYXJ5LCBzZXRTdW1tYXJ5XSA9IHVzZVN0YXRlPGFueT4obnVsbCk7XG5cbiAgLy8gTG9hZCBhdHRlbmRhbmNlIGRhdGEgKHVzaW5nIG1vY2sgZGF0YSBpbnN0ZWFkIG9mIEJpb1N0YXIgQVBJKVxuICBjb25zdCBsb2FkQXR0ZW5kYW5jZURhdGEgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFlbXBsb3llZUlkKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgICAvLyBTaW11bGF0ZSBBUEkgZGVsYXlcbiAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCA1MDApKTtcblxuICAgICAgLy8gTW9jayB0b2RheSdzIGF0dGVuZGFuY2VcbiAgICAgIGNvbnN0IHRvZGF5OiBBdHRlbmRhbmNlUmVjb3JkID0ge1xuICAgICAgICBpZDogJ2F0dC10b2RheScsXG4gICAgICAgIGVtcGxveWVlX2lkOiBlbXBsb3llZUlkLFxuICAgICAgICBlbXBsb3llZV9uYW1lOiAnQ3VycmVudCBVc2VyJyxcbiAgICAgICAgZGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sXG4gICAgICAgIGZpcnN0X2luOiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMiAqIDYwICogNjAgKiAxMDAwKS50b0lTT1N0cmluZygpLCAvLyAyIGhvdXJzIGFnb1xuICAgICAgICBsYXN0X291dDogbnVsbCxcbiAgICAgICAgdG90YWxfaG91cnM6IDIsXG4gICAgICAgIGJyZWFrX3RpbWU6IDAsXG4gICAgICAgIG92ZXJ0aW1lOiAwLFxuICAgICAgICBzdGF0dXM6ICdQUkVTRU5UJyxcbiAgICAgICAgZXZlbnRzOiBbXG4gICAgICAgICAge1xuICAgICAgICAgICAgaWQ6ICdldmVudC0xJyxcbiAgICAgICAgICAgIHVzZXJfaWQ6IGVtcGxveWVlSWQsXG4gICAgICAgICAgICBkZXZpY2VfaWQ6ICdkZXYtMDAxJyxcbiAgICAgICAgICAgIGV2ZW50X3R5cGU6ICdFTlRSWScsXG4gICAgICAgICAgICBkYXRldGltZTogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDIgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgIHVzZXJfbmFtZTogJ0N1cnJlbnQgVXNlcicsXG4gICAgICAgICAgICBkZXZpY2VfbmFtZTogJ01haW4gRW50cmFuY2UnXG4gICAgICAgICAgfVxuICAgICAgICBdLFxuICAgICAgICBiaW9zdGFyX3N5bmNlZDogdHJ1ZVxuICAgICAgfTtcbiAgICAgIHNldFRvZGF5QXR0ZW5kYW5jZSh0b2RheSk7XG5cbiAgICAgIC8vIE1vY2sgYXR0ZW5kYW5jZSBzdW1tYXJ5XG4gICAgICBjb25zdCBzdW1tYXJ5RGF0YSA9IHtcbiAgICAgICAgdG9kYXlTdGF0dXM6ICdQUkVTRU5UJyBhcyBjb25zdCxcbiAgICAgICAgY2hlY2tJblRpbWU6IG5ldyBEYXRlKERhdGUubm93KCkgLSAyICogNjAgKiA2MCAqIDEwMDApLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIGhvdXJzV29ya2VkOiAyLFxuICAgICAgICBicmVha1RpbWU6IDAsXG4gICAgICAgIG92ZXJ0aW1lOiAwLFxuICAgICAgICB3ZWVrbHlIb3VyczogMzIsXG4gICAgICAgIG1vbnRobHlBdHRlbmRhbmNlOiAyMFxuICAgICAgfTtcbiAgICAgIHNldFN1bW1hcnkoc3VtbWFyeURhdGEpO1xuXG4gICAgICAvLyBNb2NrIHJlY2VudCBhdHRlbmRhbmNlIHJlY29yZHNcbiAgICAgIGNvbnN0IHJlY29yZHM6IEF0dGVuZGFuY2VSZWNvcmRbXSA9IEFycmF5LmZyb20oeyBsZW5ndGg6IDEwIH0sIChfLCBpKSA9PiAoe1xuICAgICAgICBpZDogYGF0dC0ke2l9YCxcbiAgICAgICAgZW1wbG95ZWVfaWQ6IGVtcGxveWVlSWQsXG4gICAgICAgIGVtcGxveWVlX25hbWU6ICdDdXJyZW50IFVzZXInLFxuICAgICAgICBkYXRlOiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gaSAqIDI0ICogNjAgKiA2MCAqIDEwMDApLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSxcbiAgICAgICAgZmlyc3RfaW46IG5ldyBEYXRlKERhdGUubm93KCkgLSBpICogMjQgKiA2MCAqIDYwICogMTAwMCArIDggKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgbGFzdF9vdXQ6IG5ldyBEYXRlKERhdGUubm93KCkgLSBpICogMjQgKiA2MCAqIDYwICogMTAwMCArIDE3ICogNjAgKiA2MCAqIDEwMDApLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHRvdGFsX2hvdXJzOiA4LFxuICAgICAgICBicmVha190aW1lOiAxLFxuICAgICAgICBvdmVydGltZTogMCxcbiAgICAgICAgc3RhdHVzOiBpID09PSAwID8gJ1BSRVNFTlQnIDogJ1BSRVNFTlQnLFxuICAgICAgICBldmVudHM6IFtdLFxuICAgICAgICBiaW9zdGFyX3N5bmNlZDogdHJ1ZVxuICAgICAgfSkpO1xuICAgICAgc2V0QXR0ZW5kYW5jZVJlY29yZHMocmVjb3Jkcyk7XG5cbiAgICAgIC8vIE1vY2sgZGV2aWNlc1xuICAgICAgY29uc3QgZGV2aWNlTGlzdDogQmlvbWV0cmljRGV2aWNlW10gPSBbXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJ2Rldi0wMDEnLFxuICAgICAgICAgIG5hbWU6ICdNYWluIEVudHJhbmNlJyxcbiAgICAgICAgICBpcDogJzE5Mi4xNjguMS4xMDAnLFxuICAgICAgICAgIHBvcnQ6IDgwODAsXG4gICAgICAgICAgc3RhdHVzOiAnT05MSU5FJyxcbiAgICAgICAgICB0eXBlOiAnRmluZ2VycHJpbnQgU2Nhbm5lcicsXG4gICAgICAgICAgbG9jYXRpb246ICdNYWluIEJ1aWxkaW5nJ1xuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6ICdkZXYtMDAyJyxcbiAgICAgICAgICBuYW1lOiAnT2ZmaWNlIEZsb29yJyxcbiAgICAgICAgICBpcDogJzE5Mi4xNjguMS4xMDEnLFxuICAgICAgICAgIHBvcnQ6IDgwODAsXG4gICAgICAgICAgc3RhdHVzOiAnT05MSU5FJyxcbiAgICAgICAgICB0eXBlOiAnRmFjZSBSZWNvZ25pdGlvbicsXG4gICAgICAgICAgbG9jYXRpb246ICdTZWNvbmQgRmxvb3InXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJ2Rldi0wMDMnLFxuICAgICAgICAgIG5hbWU6ICdCYWNrIEVudHJhbmNlJyxcbiAgICAgICAgICBpcDogJzE5Mi4xNjguMS4xMDInLFxuICAgICAgICAgIHBvcnQ6IDgwODAsXG4gICAgICAgICAgc3RhdHVzOiAnT0ZGTElORScsXG4gICAgICAgICAgdHlwZTogJ0ZpbmdlcnByaW50IFNjYW5uZXInLFxuICAgICAgICAgIGxvY2F0aW9uOiAnQmFjayBCdWlsZGluZydcbiAgICAgICAgfVxuICAgICAgXTtcbiAgICAgIHNldERldmljZXMoZGV2aWNlTGlzdCk7XG5cbiAgICAgIHNldENvbm5lY3RlZCh0cnVlKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGF0dGVuZGFuY2UgZGF0YTonLCBlcnIpO1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gbG9hZCBhdHRlbmRhbmNlIGRhdGEnKTtcbiAgICAgIHNldENvbm5lY3RlZChmYWxzZSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfSwgW2VtcGxveWVlSWRdKTtcblxuICAvLyBSZWZyZXNoIGZ1bmN0aW9uXG4gIGNvbnN0IHJlZnJlc2ggPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgYXdhaXQgbG9hZEF0dGVuZGFuY2VEYXRhKCk7XG4gIH0sIFtsb2FkQXR0ZW5kYW5jZURhdGFdKTtcblxuICAvLyBHZXQgYXR0ZW5kYW5jZSByYW5nZSAodXNpbmcgbW9jayBkYXRhKVxuICBjb25zdCBnZXRBdHRlbmRhbmNlUmFuZ2UgPSB1c2VDYWxsYmFjayhhc3luYyAoc3RhcnREYXRlOiBzdHJpbmcsIGVuZERhdGU6IHN0cmluZykgPT4ge1xuICAgIGlmICghZW1wbG95ZWVJZCkgcmV0dXJuIFtdO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIFNpbXVsYXRlIEFQSSBkZWxheVxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDMwMCkpO1xuXG4gICAgICAvLyBHZW5lcmF0ZSBtb2NrIHJlY29yZHMgZm9yIHRoZSBkYXRlIHJhbmdlXG4gICAgICBjb25zdCBzdGFydCA9IG5ldyBEYXRlKHN0YXJ0RGF0ZSk7XG4gICAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZShlbmREYXRlKTtcbiAgICAgIGNvbnN0IHJlY29yZHM6IEF0dGVuZGFuY2VSZWNvcmRbXSA9IFtdO1xuXG4gICAgICBmb3IgKGxldCBkID0gbmV3IERhdGUoc3RhcnQpOyBkIDw9IGVuZDsgZC5zZXREYXRlKGQuZ2V0RGF0ZSgpICsgMSkpIHtcbiAgICAgICAgLy8gU2tpcCB3ZWVrZW5kc1xuICAgICAgICBpZiAoZC5nZXREYXkoKSA9PT0gMCB8fCBkLmdldERheSgpID09PSA2KSBjb250aW51ZTtcblxuICAgICAgICByZWNvcmRzLnB1c2goe1xuICAgICAgICAgIGlkOiBgYXR0LSR7ZC50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF19YCxcbiAgICAgICAgICBlbXBsb3llZV9pZDogZW1wbG95ZWVJZCxcbiAgICAgICAgICBlbXBsb3llZV9uYW1lOiAnQ3VycmVudCBVc2VyJyxcbiAgICAgICAgICBkYXRlOiBkLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSxcbiAgICAgICAgICBmaXJzdF9pbjogbmV3IERhdGUoZC5nZXRUaW1lKCkgKyA4ICogNjAgKiA2MCAqIDEwMDApLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgbGFzdF9vdXQ6IG5ldyBEYXRlKGQuZ2V0VGltZSgpICsgMTcgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICB0b3RhbF9ob3VyczogOCxcbiAgICAgICAgICBicmVha190aW1lOiAxLFxuICAgICAgICAgIG92ZXJ0aW1lOiAwLFxuICAgICAgICAgIHN0YXR1czogJ1BSRVNFTlQnLFxuICAgICAgICAgIGV2ZW50czogW10sXG4gICAgICAgICAgYmlvc3Rhcl9zeW5jZWQ6IHRydWVcbiAgICAgICAgfSk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiByZWNvcmRzO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGdldCBhdHRlbmRhbmNlIHJhbmdlOicsIGVycik7XG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byBnZXQgYXR0ZW5kYW5jZSByYW5nZScpO1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH1cbiAgfSwgW2VtcGxveWVlSWRdKTtcblxuICAvLyBSZWFsLXRpbWUgdXBkYXRlcyBoYW5kbGVyXG4gIGNvbnN0IGhhbmRsZVJlYWxUaW1lVXBkYXRlID0gdXNlQ2FsbGJhY2soKHVwZGF0ZTogUmVhbFRpbWVBdHRlbmRhbmNlKSA9PiB7XG4gICAgc2V0UmVhbHRpbWVVcGRhdGVzKHByZXYgPT4ge1xuICAgICAgLy8gQWRkIG5ldyB1cGRhdGUgYW5kIGtlZXAgb25seSBsYXN0IDEwXG4gICAgICBjb25zdCBuZXdVcGRhdGVzID0gW3VwZGF0ZSwgLi4ucHJldl07XG4gICAgICByZXR1cm4gbmV3VXBkYXRlcy5zbGljZSgwLCAxMCk7XG4gICAgfSk7XG5cbiAgICAvLyBJZiBpdCdzIGZvciBjdXJyZW50IGVtcGxveWVlLCByZWZyZXNoIHRvZGF5J3MgZGF0YVxuICAgIGlmICh1cGRhdGUuZW1wbG95ZWVfaWQgPT09IGVtcGxveWVlSWQpIHtcbiAgICAgIC8vIERlYm91bmNlIHRoZSByZWZyZXNoIHRvIGF2b2lkIHRvbyBtYW55IGNhbGxzXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgbG9hZEF0dGVuZGFuY2VEYXRhKCk7XG4gICAgICB9LCAyMDAwKTtcbiAgICB9XG4gIH0sIFtlbXBsb3llZUlkLCBsb2FkQXR0ZW5kYW5jZURhdGFdKTtcblxuICAvLyBJbml0aWFsIGxvYWRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoZW1wbG95ZWVJZCkge1xuICAgICAgbG9hZEF0dGVuZGFuY2VEYXRhKCk7XG4gICAgfVxuICB9LCBbZW1wbG95ZWVJZCwgbG9hZEF0dGVuZGFuY2VEYXRhXSk7XG5cbiAgLy8gQXV0byByZWZyZXNoXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFhdXRvUmVmcmVzaCB8fCAhZW1wbG95ZWVJZCkgcmV0dXJuO1xuXG4gICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICBsb2FkQXR0ZW5kYW5jZURhdGEoKTtcbiAgICB9LCByZWZyZXNoSW50ZXJ2YWwpO1xuXG4gICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xuICB9LCBbYXV0b1JlZnJlc2gsIHJlZnJlc2hJbnRlcnZhbCwgZW1wbG95ZWVJZCwgbG9hZEF0dGVuZGFuY2VEYXRhXSk7XG5cbiAgLy8gUmVhbC10aW1lIG1vbml0b3JpbmcgKGRpc2FibGVkIC0gdXNpbmcgbW9jayBkYXRhKVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghZW5hYmxlUmVhbFRpbWUpIHJldHVybjtcblxuICAgIC8vIEdlbmVyYXRlIG1vY2sgcmVhbC10aW1lIHVwZGF0ZXMgcGVyaW9kaWNhbGx5XG4gICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICBjb25zdCBtb2NrVXBkYXRlOiBSZWFsVGltZUF0dGVuZGFuY2UgPSB7XG4gICAgICAgIGlkOiBgdXBkYXRlLSR7RGF0ZS5ub3coKX1gLFxuICAgICAgICBlbXBsb3llZV9pZDogYEVNUCR7TWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMTAwKS50b1N0cmluZygpLnBhZFN0YXJ0KDMsICcwJyl9YCxcbiAgICAgICAgZW1wbG95ZWVfbmFtZTogYEVtcGxveWVlICR7TWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMTAwKX1gLFxuICAgICAgICBldmVudF90eXBlOiBNYXRoLnJhbmRvbSgpID4gMC41ID8gJ0VOVFJZJyA6ICdFWElUJyxcbiAgICAgICAgZGV2aWNlX25hbWU6IFsnTWFpbiBFbnRyYW5jZScsICdPZmZpY2UgRmxvb3InLCAnQmFjayBFbnRyYW5jZSddW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDMpXSxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHN0YXR1czogJ1BSRVNFTlQnXG4gICAgICB9O1xuXG4gICAgICBoYW5kbGVSZWFsVGltZVVwZGF0ZShtb2NrVXBkYXRlKTtcbiAgICB9LCAzMDAwMCk7IC8vIEV2ZXJ5IDMwIHNlY29uZHNcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgfSwgW2VuYWJsZVJlYWxUaW1lLCBoYW5kbGVSZWFsVGltZVVwZGF0ZV0pO1xuXG4gIHJldHVybiB7XG4gICAgLy8gRGF0YVxuICAgIHRvZGF5QXR0ZW5kYW5jZSxcbiAgICBhdHRlbmRhbmNlUmVjb3JkcyxcbiAgICBkZXZpY2VzLFxuICAgIHJlYWx0aW1lVXBkYXRlcyxcblxuICAgIC8vIFN0YXR1c1xuICAgIGxvYWRpbmcsXG4gICAgY29ubmVjdGVkLFxuICAgIGVycm9yLFxuXG4gICAgLy8gQWN0aW9uc1xuICAgIHJlZnJlc2gsXG4gICAgZ2V0QXR0ZW5kYW5jZVJhbmdlLFxuXG4gICAgLy8gU3VtbWFyeVxuICAgIHN1bW1hcnlcbiAgfTtcbn07XG5cbi8vIEhvb2sgZm9yIGRldmljZSBtb25pdG9yaW5nICh1c2luZyBtb2NrIGRhdGEpXG5leHBvcnQgY29uc3QgdXNlQmlvc3RhckRldmljZXMgPSAoKSA9PiB7XG4gIGNvbnN0IFtkZXZpY2VzLCBzZXREZXZpY2VzXSA9IHVzZVN0YXRlPEJpb21ldHJpY0RldmljZVtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuXG4gIGNvbnN0IGxvYWREZXZpY2VzID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICAgIC8vIFNpbXVsYXRlIEFQSSBkZWxheVxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDMwMCkpO1xuXG4gICAgICAvLyBNb2NrIGRldmljZSBsaXN0XG4gICAgICBjb25zdCBkZXZpY2VMaXN0OiBCaW9tZXRyaWNEZXZpY2VbXSA9IFtcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnZGV2LTAwMScsXG4gICAgICAgICAgbmFtZTogJ01haW4gRW50cmFuY2UnLFxuICAgICAgICAgIGlwOiAnMTkyLjE2OC4xLjEwMCcsXG4gICAgICAgICAgcG9ydDogODA4MCxcbiAgICAgICAgICBzdGF0dXM6ICdPTkxJTkUnLFxuICAgICAgICAgIHR5cGU6ICdGaW5nZXJwcmludCBTY2FubmVyJyxcbiAgICAgICAgICBsb2NhdGlvbjogJ01haW4gQnVpbGRpbmcnXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJ2Rldi0wMDInLFxuICAgICAgICAgIG5hbWU6ICdPZmZpY2UgRmxvb3InLFxuICAgICAgICAgIGlwOiAnMTkyLjE2OC4xLjEwMScsXG4gICAgICAgICAgcG9ydDogODA4MCxcbiAgICAgICAgICBzdGF0dXM6ICdPTkxJTkUnLFxuICAgICAgICAgIHR5cGU6ICdGYWNlIFJlY29nbml0aW9uJyxcbiAgICAgICAgICBsb2NhdGlvbjogJ1NlY29uZCBGbG9vcidcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnZGV2LTAwMycsXG4gICAgICAgICAgbmFtZTogJ0JhY2sgRW50cmFuY2UnLFxuICAgICAgICAgIGlwOiAnMTkyLjE2OC4xLjEwMicsXG4gICAgICAgICAgcG9ydDogODA4MCxcbiAgICAgICAgICBzdGF0dXM6IE1hdGgucmFuZG9tKCkgPiAwLjUgPyAnT05MSU5FJyA6ICdPRkZMSU5FJywgLy8gUmFuZG9tIHN0YXR1c1xuICAgICAgICAgIHR5cGU6ICdGaW5nZXJwcmludCBTY2FubmVyJyxcbiAgICAgICAgICBsb2NhdGlvbjogJ0JhY2sgQnVpbGRpbmcnXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJ2Rldi0wMDQnLFxuICAgICAgICAgIG5hbWU6ICdDb25mZXJlbmNlIFJvb20nLFxuICAgICAgICAgIGlwOiAnMTkyLjE2OC4xLjEwMycsXG4gICAgICAgICAgcG9ydDogODA4MCxcbiAgICAgICAgICBzdGF0dXM6ICdPTkxJTkUnLFxuICAgICAgICAgIHR5cGU6ICdDYXJkIFJlYWRlcicsXG4gICAgICAgICAgbG9jYXRpb246ICdUaGlyZCBGbG9vcidcbiAgICAgICAgfVxuICAgICAgXTtcblxuICAgICAgc2V0RGV2aWNlcyhkZXZpY2VMaXN0KTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGRldmljZXM6JywgZXJyKTtcbiAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGxvYWQgZGV2aWNlcycpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxvYWREZXZpY2VzKCk7XG5cbiAgICAvLyBSZWZyZXNoIGRldmljZXMgZXZlcnkgMiBtaW51dGVzXG4gICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbChsb2FkRGV2aWNlcywgMTIwMDAwKTtcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XG4gIH0sIFtsb2FkRGV2aWNlc10pO1xuXG4gIHJldHVybiB7XG4gICAgZGV2aWNlcyxcbiAgICBsb2FkaW5nLFxuICAgIGVycm9yLFxuICAgIHJlZnJlc2g6IGxvYWREZXZpY2VzLFxuICAgIG9ubGluZURldmljZXM6IGRldmljZXMuZmlsdGVyKGQgPT4gZC5zdGF0dXMgPT09ICdPTkxJTkUnKSxcbiAgICBvZmZsaW5lRGV2aWNlczogZGV2aWNlcy5maWx0ZXIoZCA9PiBkLnN0YXR1cyA9PT0gJ09GRkxJTkUnKVxuICB9O1xufTtcblxuLy8gSG9vayBmb3IgcmVhbC10aW1lIG1vbml0b3Jpbmcgb25seVxuZXhwb3J0IGNvbnN0IHVzZUJpb3N0YXJSZWFsVGltZSA9ICgpID0+IHtcbiAgY29uc3QgW3VwZGF0ZXMsIHNldFVwZGF0ZXNdID0gdXNlU3RhdGU8UmVhbFRpbWVBdHRlbmRhbmNlW10+KFtdKTtcbiAgY29uc3QgW2lzTW9uaXRvcmluZywgc2V0SXNNb25pdG9yaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICBjb25zdCBzdGFydE1vbml0b3JpbmcgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKGlzTW9uaXRvcmluZykgcmV0dXJuO1xuXG4gICAgY29uc3QgaGFuZGxlVXBkYXRlID0gKHVwZGF0ZTogUmVhbFRpbWVBdHRlbmRhbmNlKSA9PiB7XG4gICAgICBzZXRVcGRhdGVzKHByZXYgPT4gW3VwZGF0ZSwgLi4ucHJldi5zbGljZSgwLCAxOSldKTsgLy8gS2VlcCBsYXN0IDIwIHVwZGF0ZXNcbiAgICB9O1xuXG4gICAgYXR0ZW5kYW5jZVNlcnZpY2Uuc3RhcnRSZWFsVGltZU1vbml0b3JpbmcoaGFuZGxlVXBkYXRlKTtcbiAgICBzZXRJc01vbml0b3JpbmcodHJ1ZSk7XG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgYXR0ZW5kYW5jZVNlcnZpY2Uuc3RvcFJlYWxUaW1lTW9uaXRvcmluZyhoYW5kbGVVcGRhdGUpO1xuICAgICAgc2V0SXNNb25pdG9yaW5nKGZhbHNlKTtcbiAgICB9O1xuICB9LCBbaXNNb25pdG9yaW5nXSk7XG5cbiAgY29uc3Qgc3RvcE1vbml0b3JpbmcgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgYXR0ZW5kYW5jZVNlcnZpY2Uuc3RvcFJlYWxUaW1lTW9uaXRvcmluZygpO1xuICAgIHNldElzTW9uaXRvcmluZyhmYWxzZSk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBjbGVhclVwZGF0ZXMgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgc2V0VXBkYXRlcyhbXSk7XG4gIH0sIFtdKTtcblxuICByZXR1cm4ge1xuICAgIHVwZGF0ZXMsXG4gICAgaXNNb25pdG9yaW5nLFxuICAgIHN0YXJ0TW9uaXRvcmluZyxcbiAgICBzdG9wTW9uaXRvcmluZyxcbiAgICBjbGVhclVwZGF0ZXNcbiAgfTtcbn07XG4iXSwibmFtZXMiOlsidXNlQmlvc3RhckF0dGVuZGFuY2UiLCJ1c2VCaW9zdGFyRGV2aWNlcyIsInVzZUJpb3N0YXJSZWFsVGltZSIsIm9wdGlvbnMiLCJlbXBsb3llZUlkIiwiYXV0b1JlZnJlc2giLCJyZWZyZXNoSW50ZXJ2YWwiLCJlbmFibGVSZWFsVGltZSIsInRvZGF5QXR0ZW5kYW5jZSIsInNldFRvZGF5QXR0ZW5kYW5jZSIsInVzZVN0YXRlIiwiYXR0ZW5kYW5jZVJlY29yZHMiLCJzZXRBdHRlbmRhbmNlUmVjb3JkcyIsImRldmljZXMiLCJzZXREZXZpY2VzIiwicmVhbHRpbWVVcGRhdGVzIiwic2V0UmVhbHRpbWVVcGRhdGVzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJjb25uZWN0ZWQiLCJzZXRDb25uZWN0ZWQiLCJlcnJvciIsInNldEVycm9yIiwic3VtbWFyeSIsInNldFN1bW1hcnkiLCJsb2FkQXR0ZW5kYW5jZURhdGEiLCJ1c2VDYWxsYmFjayIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsInRvZGF5IiwiaWQiLCJlbXBsb3llZV9pZCIsImVtcGxveWVlX25hbWUiLCJkYXRlIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJmaXJzdF9pbiIsIm5vdyIsImxhc3Rfb3V0IiwidG90YWxfaG91cnMiLCJicmVha190aW1lIiwib3ZlcnRpbWUiLCJzdGF0dXMiLCJldmVudHMiLCJ1c2VyX2lkIiwiZGV2aWNlX2lkIiwiZXZlbnRfdHlwZSIsImRhdGV0aW1lIiwidXNlcl9uYW1lIiwiZGV2aWNlX25hbWUiLCJiaW9zdGFyX3N5bmNlZCIsInN1bW1hcnlEYXRhIiwidG9kYXlTdGF0dXMiLCJjaGVja0luVGltZSIsImhvdXJzV29ya2VkIiwiYnJlYWtUaW1lIiwid2Vla2x5SG91cnMiLCJtb250aGx5QXR0ZW5kYW5jZSIsInJlY29yZHMiLCJBcnJheSIsImZyb20iLCJsZW5ndGgiLCJfIiwiaSIsImRldmljZUxpc3QiLCJuYW1lIiwiaXAiLCJwb3J0IiwidHlwZSIsImxvY2F0aW9uIiwiZXJyIiwiY29uc29sZSIsIkVycm9yIiwibWVzc2FnZSIsInJlZnJlc2giLCJnZXRBdHRlbmRhbmNlUmFuZ2UiLCJzdGFydERhdGUiLCJlbmREYXRlIiwic3RhcnQiLCJlbmQiLCJkIiwic2V0RGF0ZSIsImdldERhdGUiLCJnZXREYXkiLCJwdXNoIiwiZ2V0VGltZSIsImhhbmRsZVJlYWxUaW1lVXBkYXRlIiwidXBkYXRlIiwicHJldiIsIm5ld1VwZGF0ZXMiLCJzbGljZSIsInVzZUVmZmVjdCIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwibW9ja1VwZGF0ZSIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJ0aW1lc3RhbXAiLCJsb2FkRGV2aWNlcyIsIm9ubGluZURldmljZXMiLCJmaWx0ZXIiLCJvZmZsaW5lRGV2aWNlcyIsInVwZGF0ZXMiLCJzZXRVcGRhdGVzIiwiaXNNb25pdG9yaW5nIiwic2V0SXNNb25pdG9yaW5nIiwic3RhcnRNb25pdG9yaW5nIiwiaGFuZGxlVXBkYXRlIiwiYXR0ZW5kYW5jZVNlcnZpY2UiLCJzdGFydFJlYWxUaW1lTW9uaXRvcmluZyIsInN0b3BSZWFsVGltZU1vbml0b3JpbmciLCJzdG9wTW9uaXRvcmluZyIsImNsZWFyVXBkYXRlcyJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7SUF3Q2FBLG9CQUFvQjtlQUFwQkE7O0lBOFBBQyxpQkFBaUI7ZUFBakJBOztJQWlGQUMsa0JBQWtCO2VBQWxCQTs7O3VCQXZYb0M7bUNBQ2Y7QUF1QzNCLE1BQU1GLHVCQUF1QixDQUNsQ0csVUFBdUMsQ0FBQyxDQUFDO0lBRXpDLE1BQU0sRUFDSkMsVUFBVSxFQUNWQyxjQUFjLElBQUksRUFDbEJDLGtCQUFrQixNQUFNLEVBQ3hCQyxpQkFBaUIsSUFBSSxFQUN0QixHQUFHSjtJQUVKLFFBQVE7SUFDUixNQUFNLENBQUNLLGlCQUFpQkMsbUJBQW1CLEdBQUdDLElBQUFBLGVBQVEsRUFBMEI7SUFDaEYsTUFBTSxDQUFDQyxtQkFBbUJDLHFCQUFxQixHQUFHRixJQUFBQSxlQUFRLEVBQXFCLEVBQUU7SUFDakYsTUFBTSxDQUFDRyxTQUFTQyxXQUFXLEdBQUdKLElBQUFBLGVBQVEsRUFBb0IsRUFBRTtJQUM1RCxNQUFNLENBQUNLLGlCQUFpQkMsbUJBQW1CLEdBQUdOLElBQUFBLGVBQVEsRUFBdUIsRUFBRTtJQUMvRSxNQUFNLENBQUNPLFNBQVNDLFdBQVcsR0FBR1IsSUFBQUEsZUFBUSxFQUFDO0lBQ3ZDLE1BQU0sQ0FBQ1MsV0FBV0MsYUFBYSxHQUFHVixJQUFBQSxlQUFRLEVBQUMsT0FBTyxpQ0FBaUM7SUFDbkYsTUFBTSxDQUFDVyxPQUFPQyxTQUFTLEdBQUdaLElBQUFBLGVBQVEsRUFBZ0I7SUFDbEQsTUFBTSxDQUFDYSxTQUFTQyxXQUFXLEdBQUdkLElBQUFBLGVBQVEsRUFBTTtJQUU1QyxnRUFBZ0U7SUFDaEUsTUFBTWUscUJBQXFCQyxJQUFBQSxrQkFBVyxFQUFDO1FBQ3JDLElBQUksQ0FBQ3RCLFlBQVk7UUFFakIsSUFBSTtZQUNGYyxXQUFXO1lBQ1hJLFNBQVM7WUFFVCxxQkFBcUI7WUFDckIsTUFBTSxJQUFJSyxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO1lBRWpELDBCQUEwQjtZQUMxQixNQUFNRSxRQUEwQjtnQkFDOUJDLElBQUk7Z0JBQ0pDLGFBQWE1QjtnQkFDYjZCLGVBQWU7Z0JBQ2ZDLE1BQU0sSUFBSUMsT0FBT0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7Z0JBQzVDQyxVQUFVLElBQUlILEtBQUtBLEtBQUtJLEdBQUcsS0FBSyxJQUFJLEtBQUssS0FBSyxNQUFNSCxXQUFXO2dCQUMvREksVUFBVTtnQkFDVkMsYUFBYTtnQkFDYkMsWUFBWTtnQkFDWkMsVUFBVTtnQkFDVkMsUUFBUTtnQkFDUkMsUUFBUTtvQkFDTjt3QkFDRWQsSUFBSTt3QkFDSmUsU0FBUzFDO3dCQUNUMkMsV0FBVzt3QkFDWEMsWUFBWTt3QkFDWkMsVUFBVSxJQUFJZCxLQUFLQSxLQUFLSSxHQUFHLEtBQUssSUFBSSxLQUFLLEtBQUssTUFBTUgsV0FBVzt3QkFDL0RjLFdBQVc7d0JBQ1hDLGFBQWE7b0JBQ2Y7aUJBQ0Q7Z0JBQ0RDLGdCQUFnQjtZQUNsQjtZQUNBM0MsbUJBQW1CcUI7WUFFbkIsMEJBQTBCO1lBQzFCLE1BQU11QixjQUFjO2dCQUNsQkMsYUFBYTtnQkFDYkMsYUFBYSxJQUFJcEIsS0FBS0EsS0FBS0ksR0FBRyxLQUFLLElBQUksS0FBSyxLQUFLLE1BQU1ILFdBQVc7Z0JBQ2xFb0IsYUFBYTtnQkFDYkMsV0FBVztnQkFDWGQsVUFBVTtnQkFDVmUsYUFBYTtnQkFDYkMsbUJBQW1CO1lBQ3JCO1lBQ0FuQyxXQUFXNkI7WUFFWCxpQ0FBaUM7WUFDakMsTUFBTU8sVUFBOEJDLE1BQU1DLElBQUksQ0FBQztnQkFBRUMsUUFBUTtZQUFHLEdBQUcsQ0FBQ0MsR0FBR0MsSUFBTyxDQUFBO29CQUN4RWxDLElBQUksQ0FBQyxJQUFJLEVBQUVrQyxHQUFHO29CQUNkakMsYUFBYTVCO29CQUNiNkIsZUFBZTtvQkFDZkMsTUFBTSxJQUFJQyxLQUFLQSxLQUFLSSxHQUFHLEtBQUswQixJQUFJLEtBQUssS0FBSyxLQUFLLE1BQU03QixXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtvQkFDaEZDLFVBQVUsSUFBSUgsS0FBS0EsS0FBS0ksR0FBRyxLQUFLMEIsSUFBSSxLQUFLLEtBQUssS0FBSyxPQUFPLElBQUksS0FBSyxLQUFLLE1BQU03QixXQUFXO29CQUN6RkksVUFBVSxJQUFJTCxLQUFLQSxLQUFLSSxHQUFHLEtBQUswQixJQUFJLEtBQUssS0FBSyxLQUFLLE9BQU8sS0FBSyxLQUFLLEtBQUssTUFBTTdCLFdBQVc7b0JBQzFGSyxhQUFhO29CQUNiQyxZQUFZO29CQUNaQyxVQUFVO29CQUNWQyxRQUFRcUIsTUFBTSxJQUFJLFlBQVk7b0JBQzlCcEIsUUFBUSxFQUFFO29CQUNWTyxnQkFBZ0I7Z0JBQ2xCLENBQUE7WUFDQXhDLHFCQUFxQmdEO1lBRXJCLGVBQWU7WUFDZixNQUFNTSxhQUFnQztnQkFDcEM7b0JBQ0VuQyxJQUFJO29CQUNKb0MsTUFBTTtvQkFDTkMsSUFBSTtvQkFDSkMsTUFBTTtvQkFDTnpCLFFBQVE7b0JBQ1IwQixNQUFNO29CQUNOQyxVQUFVO2dCQUNaO2dCQUNBO29CQUNFeEMsSUFBSTtvQkFDSm9DLE1BQU07b0JBQ05DLElBQUk7b0JBQ0pDLE1BQU07b0JBQ056QixRQUFRO29CQUNSMEIsTUFBTTtvQkFDTkMsVUFBVTtnQkFDWjtnQkFDQTtvQkFDRXhDLElBQUk7b0JBQ0pvQyxNQUFNO29CQUNOQyxJQUFJO29CQUNKQyxNQUFNO29CQUNOekIsUUFBUTtvQkFDUjBCLE1BQU07b0JBQ05DLFVBQVU7Z0JBQ1o7YUFDRDtZQUNEekQsV0FBV29EO1lBRVg5QyxhQUFhO1FBQ2YsRUFBRSxPQUFPb0QsS0FBSztZQUNaQyxRQUFRcEQsS0FBSyxDQUFDLG1DQUFtQ21EO1lBQ2pEbEQsU0FBU2tELGVBQWVFLFFBQVFGLElBQUlHLE9BQU8sR0FBRztZQUM5Q3ZELGFBQWE7UUFDZixTQUFVO1lBQ1JGLFdBQVc7UUFDYjtJQUNGLEdBQUc7UUFBQ2Q7S0FBVztJQUVmLG1CQUFtQjtJQUNuQixNQUFNd0UsVUFBVWxELElBQUFBLGtCQUFXLEVBQUM7UUFDMUIsTUFBTUQ7SUFDUixHQUFHO1FBQUNBO0tBQW1CO0lBRXZCLHlDQUF5QztJQUN6QyxNQUFNb0QscUJBQXFCbkQsSUFBQUEsa0JBQVcsRUFBQyxPQUFPb0QsV0FBbUJDO1FBQy9ELElBQUksQ0FBQzNFLFlBQVksT0FBTyxFQUFFO1FBRTFCLElBQUk7WUFDRixxQkFBcUI7WUFDckIsTUFBTSxJQUFJdUIsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztZQUVqRCwyQ0FBMkM7WUFDM0MsTUFBTW9ELFFBQVEsSUFBSTdDLEtBQUsyQztZQUN2QixNQUFNRyxNQUFNLElBQUk5QyxLQUFLNEM7WUFDckIsTUFBTW5CLFVBQThCLEVBQUU7WUFFdEMsSUFBSyxJQUFJc0IsSUFBSSxJQUFJL0MsS0FBSzZDLFFBQVFFLEtBQUtELEtBQUtDLEVBQUVDLE9BQU8sQ0FBQ0QsRUFBRUUsT0FBTyxLQUFLLEdBQUk7Z0JBQ2xFLGdCQUFnQjtnQkFDaEIsSUFBSUYsRUFBRUcsTUFBTSxPQUFPLEtBQUtILEVBQUVHLE1BQU0sT0FBTyxHQUFHO2dCQUUxQ3pCLFFBQVEwQixJQUFJLENBQUM7b0JBQ1h2RCxJQUFJLENBQUMsSUFBSSxFQUFFbUQsRUFBRTlDLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUU7b0JBQzFDTCxhQUFhNUI7b0JBQ2I2QixlQUFlO29CQUNmQyxNQUFNZ0QsRUFBRTlDLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO29CQUNuQ0MsVUFBVSxJQUFJSCxLQUFLK0MsRUFBRUssT0FBTyxLQUFLLElBQUksS0FBSyxLQUFLLE1BQU1uRCxXQUFXO29CQUNoRUksVUFBVSxJQUFJTCxLQUFLK0MsRUFBRUssT0FBTyxLQUFLLEtBQUssS0FBSyxLQUFLLE1BQU1uRCxXQUFXO29CQUNqRUssYUFBYTtvQkFDYkMsWUFBWTtvQkFDWkMsVUFBVTtvQkFDVkMsUUFBUTtvQkFDUkMsUUFBUSxFQUFFO29CQUNWTyxnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFFQSxPQUFPUTtRQUNULEVBQUUsT0FBT1ksS0FBSztZQUNaQyxRQUFRcEQsS0FBSyxDQUFDLG1DQUFtQ21EO1lBQ2pEbEQsU0FBU2tELGVBQWVFLFFBQVFGLElBQUlHLE9BQU8sR0FBRztZQUM5QyxPQUFPLEVBQUU7UUFDWDtJQUNGLEdBQUc7UUFBQ3ZFO0tBQVc7SUFFZiw0QkFBNEI7SUFDNUIsTUFBTW9GLHVCQUF1QjlELElBQUFBLGtCQUFXLEVBQUMsQ0FBQytEO1FBQ3hDekUsbUJBQW1CMEUsQ0FBQUE7WUFDakIsdUNBQXVDO1lBQ3ZDLE1BQU1DLGFBQWE7Z0JBQUNGO21CQUFXQzthQUFLO1lBQ3BDLE9BQU9DLFdBQVdDLEtBQUssQ0FBQyxHQUFHO1FBQzdCO1FBRUEscURBQXFEO1FBQ3JELElBQUlILE9BQU96RCxXQUFXLEtBQUs1QixZQUFZO1lBQ3JDLCtDQUErQztZQUMvQ3lCLFdBQVc7Z0JBQ1RKO1lBQ0YsR0FBRztRQUNMO0lBQ0YsR0FBRztRQUFDckI7UUFBWXFCO0tBQW1CO0lBRW5DLGVBQWU7SUFDZm9FLElBQUFBLGdCQUFTLEVBQUM7UUFDUixJQUFJekYsWUFBWTtZQUNkcUI7UUFDRjtJQUNGLEdBQUc7UUFBQ3JCO1FBQVlxQjtLQUFtQjtJQUVuQyxlQUFlO0lBQ2ZvRSxJQUFBQSxnQkFBUyxFQUFDO1FBQ1IsSUFBSSxDQUFDeEYsZUFBZSxDQUFDRCxZQUFZO1FBRWpDLE1BQU0wRixXQUFXQyxZQUFZO1lBQzNCdEU7UUFDRixHQUFHbkI7UUFFSCxPQUFPLElBQU0wRixjQUFjRjtJQUM3QixHQUFHO1FBQUN6RjtRQUFhQztRQUFpQkY7UUFBWXFCO0tBQW1CO0lBRWpFLG9EQUFvRDtJQUNwRG9FLElBQUFBLGdCQUFTLEVBQUM7UUFDUixJQUFJLENBQUN0RixnQkFBZ0I7UUFFckIsK0NBQStDO1FBQy9DLE1BQU11RixXQUFXQyxZQUFZO1lBQzNCLE1BQU1FLGFBQWlDO2dCQUNyQ2xFLElBQUksQ0FBQyxPQUFPLEVBQUVJLEtBQUtJLEdBQUcsSUFBSTtnQkFDMUJQLGFBQWEsQ0FBQyxHQUFHLEVBQUVrRSxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBSyxLQUFLQyxRQUFRLEdBQUdDLFFBQVEsQ0FBQyxHQUFHLE1BQU07Z0JBQ2hGckUsZUFBZSxDQUFDLFNBQVMsRUFBRWlFLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLLE1BQU07Z0JBQzVEcEQsWUFBWWtELEtBQUtFLE1BQU0sS0FBSyxNQUFNLFVBQVU7Z0JBQzVDakQsYUFBYTtvQkFBQztvQkFBaUI7b0JBQWdCO2lCQUFnQixDQUFDK0MsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUssR0FBRztnQkFDOUZHLFdBQVcsSUFBSXBFLE9BQU9DLFdBQVc7Z0JBQ2pDUSxRQUFRO1lBQ1Y7WUFFQTRDLHFCQUFxQlM7UUFDdkIsR0FBRyxRQUFRLG1CQUFtQjtRQUU5QixPQUFPLElBQU1ELGNBQWNGO0lBQzdCLEdBQUc7UUFBQ3ZGO1FBQWdCaUY7S0FBcUI7SUFFekMsT0FBTztRQUNMLE9BQU87UUFDUGhGO1FBQ0FHO1FBQ0FFO1FBQ0FFO1FBRUEsU0FBUztRQUNURTtRQUNBRTtRQUNBRTtRQUVBLFVBQVU7UUFDVnVEO1FBQ0FDO1FBRUEsVUFBVTtRQUNWdEQ7SUFDRjtBQUNGO0FBR08sTUFBTXRCLG9CQUFvQjtJQUMvQixNQUFNLENBQUNZLFNBQVNDLFdBQVcsR0FBR0osSUFBQUEsZUFBUSxFQUFvQixFQUFFO0lBQzVELE1BQU0sQ0FBQ08sU0FBU0MsV0FBVyxHQUFHUixJQUFBQSxlQUFRLEVBQUM7SUFDdkMsTUFBTSxDQUFDVyxPQUFPQyxTQUFTLEdBQUdaLElBQUFBLGVBQVEsRUFBZ0I7SUFFbEQsTUFBTThGLGNBQWM5RSxJQUFBQSxrQkFBVyxFQUFDO1FBQzlCLElBQUk7WUFDRlIsV0FBVztZQUNYSSxTQUFTO1lBRVQscUJBQXFCO1lBQ3JCLE1BQU0sSUFBSUssUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztZQUVqRCxtQkFBbUI7WUFDbkIsTUFBTXNDLGFBQWdDO2dCQUNwQztvQkFDRW5DLElBQUk7b0JBQ0pvQyxNQUFNO29CQUNOQyxJQUFJO29CQUNKQyxNQUFNO29CQUNOekIsUUFBUTtvQkFDUjBCLE1BQU07b0JBQ05DLFVBQVU7Z0JBQ1o7Z0JBQ0E7b0JBQ0V4QyxJQUFJO29CQUNKb0MsTUFBTTtvQkFDTkMsSUFBSTtvQkFDSkMsTUFBTTtvQkFDTnpCLFFBQVE7b0JBQ1IwQixNQUFNO29CQUNOQyxVQUFVO2dCQUNaO2dCQUNBO29CQUNFeEMsSUFBSTtvQkFDSm9DLE1BQU07b0JBQ05DLElBQUk7b0JBQ0pDLE1BQU07b0JBQ056QixRQUFRc0QsS0FBS0UsTUFBTSxLQUFLLE1BQU0sV0FBVztvQkFDekM5QixNQUFNO29CQUNOQyxVQUFVO2dCQUNaO2dCQUNBO29CQUNFeEMsSUFBSTtvQkFDSm9DLE1BQU07b0JBQ05DLElBQUk7b0JBQ0pDLE1BQU07b0JBQ056QixRQUFRO29CQUNSMEIsTUFBTTtvQkFDTkMsVUFBVTtnQkFDWjthQUNEO1lBRUR6RCxXQUFXb0Q7UUFDYixFQUFFLE9BQU9NLEtBQUs7WUFDWkMsUUFBUXBELEtBQUssQ0FBQywyQkFBMkJtRDtZQUN6Q2xELFNBQVNrRCxlQUFlRSxRQUFRRixJQUFJRyxPQUFPLEdBQUc7UUFDaEQsU0FBVTtZQUNSekQsV0FBVztRQUNiO0lBQ0YsR0FBRyxFQUFFO0lBRUwyRSxJQUFBQSxnQkFBUyxFQUFDO1FBQ1JXO1FBRUEsa0NBQWtDO1FBQ2xDLE1BQU1WLFdBQVdDLFlBQVlTLGFBQWE7UUFDMUMsT0FBTyxJQUFNUixjQUFjRjtJQUM3QixHQUFHO1FBQUNVO0tBQVk7SUFFaEIsT0FBTztRQUNMM0Y7UUFDQUk7UUFDQUk7UUFDQXVELFNBQVM0QjtRQUNUQyxlQUFlNUYsUUFBUTZGLE1BQU0sQ0FBQ3hCLENBQUFBLElBQUtBLEVBQUV0QyxNQUFNLEtBQUs7UUFDaEQrRCxnQkFBZ0I5RixRQUFRNkYsTUFBTSxDQUFDeEIsQ0FBQUEsSUFBS0EsRUFBRXRDLE1BQU0sS0FBSztJQUNuRDtBQUNGO0FBR08sTUFBTTFDLHFCQUFxQjtJQUNoQyxNQUFNLENBQUMwRyxTQUFTQyxXQUFXLEdBQUduRyxJQUFBQSxlQUFRLEVBQXVCLEVBQUU7SUFDL0QsTUFBTSxDQUFDb0csY0FBY0MsZ0JBQWdCLEdBQUdyRyxJQUFBQSxlQUFRLEVBQUM7SUFFakQsTUFBTXNHLGtCQUFrQnRGLElBQUFBLGtCQUFXLEVBQUM7UUFDbEMsSUFBSW9GLGNBQWM7UUFFbEIsTUFBTUcsZUFBZSxDQUFDeEI7WUFDcEJvQixXQUFXbkIsQ0FBQUEsT0FBUTtvQkFBQ0Q7dUJBQVdDLEtBQUtFLEtBQUssQ0FBQyxHQUFHO2lCQUFJLEdBQUcsdUJBQXVCO1FBQzdFO1FBRUFzQixvQ0FBaUIsQ0FBQ0MsdUJBQXVCLENBQUNGO1FBQzFDRixnQkFBZ0I7UUFFaEIsT0FBTztZQUNMRyxvQ0FBaUIsQ0FBQ0Usc0JBQXNCLENBQUNIO1lBQ3pDRixnQkFBZ0I7UUFDbEI7SUFDRixHQUFHO1FBQUNEO0tBQWE7SUFFakIsTUFBTU8saUJBQWlCM0YsSUFBQUEsa0JBQVcsRUFBQztRQUNqQ3dGLG9DQUFpQixDQUFDRSxzQkFBc0I7UUFDeENMLGdCQUFnQjtJQUNsQixHQUFHLEVBQUU7SUFFTCxNQUFNTyxlQUFlNUYsSUFBQUEsa0JBQVcsRUFBQztRQUMvQm1GLFdBQVcsRUFBRTtJQUNmLEdBQUcsRUFBRTtJQUVMLE9BQU87UUFDTEQ7UUFDQUU7UUFDQUU7UUFDQUs7UUFDQUM7SUFDRjtBQUNGIn0=