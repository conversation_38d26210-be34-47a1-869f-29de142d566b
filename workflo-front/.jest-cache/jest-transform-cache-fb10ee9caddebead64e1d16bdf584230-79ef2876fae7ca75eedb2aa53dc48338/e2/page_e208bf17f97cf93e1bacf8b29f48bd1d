892f73206909150df0a03d569a9a4ab1
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_wildcard(require("react"));
const _lucidereact = require("lucide-react");
const _Card = /*#__PURE__*/ _interop_require_default(require("../../../../components/ui/Card"));
const _AuthProvider = require("../../../../providers/AuthProvider");
const _utils = require("../../../../lib/utils");
const _useBiostarAttendance = require("../../../../hooks/useBiostarAttendance");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
const StaffDashboard = ()=>{
    const { user } = (0, _AuthProvider.useAuth)();
    const [currentTime, setCurrentTime] = (0, _react.useState)(new Date());
    // Use BioStar attendance hook
    const { summary: attendanceSummary, realtimeUpdates, connected: biostarConnected, loading } = (0, _useBiostarAttendance.useBiostarAttendance)({
        employeeId: user?.employee_id,
        autoRefresh: true,
        enableRealTime: true
    });
    (0, _react.useEffect)(()=>{
        const timer = setInterval(()=>{
            setCurrentTime(new Date());
        }, 1000);
        return ()=>clearInterval(timer);
    }, []);
    const todayStats = {
        checkInTime: attendanceSummary?.checkInTime ? new Date(attendanceSummary.checkInTime).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
        }) : 'Not checked in',
        hoursWorked: attendanceSummary?.hoursWorked?.toFixed(1) || '0.0',
        tasksCompleted: 4,
        meetingsToday: 2,
        leaveBalance: 15,
        pendingApprovals: 1,
        attendanceStatus: attendanceSummary?.todayStatus || 'ABSENT',
        weeklyHours: attendanceSummary?.weeklyHours?.toFixed(1) || '0.0',
        monthlyAttendance: attendanceSummary?.monthlyAttendance || 0
    };
    const recentActivities = [
        {
            id: 1,
            type: 'check_in',
            description: 'Checked in for the day',
            time: '08:30 AM',
            icon: _lucidereact.Clock,
            color: 'text-green-500'
        },
        {
            id: 2,
            type: 'task',
            description: 'Completed project review',
            time: '10:15 AM',
            icon: _lucidereact.CheckCircle,
            color: 'text-blue-500'
        },
        {
            id: 3,
            type: 'meeting',
            description: 'Team standup meeting',
            time: '11:00 AM',
            icon: _lucidereact.Users,
            color: 'text-purple-500'
        },
        {
            id: 4,
            type: 'document',
            description: 'Updated timesheet',
            time: '02:30 PM',
            icon: _lucidereact.FileText,
            color: 'text-orange-500'
        }
    ];
    const upcomingEvents = [
        {
            id: 1,
            title: 'Project Review Meeting',
            time: '3:00 PM',
            type: 'meeting',
            participants: 5
        },
        {
            id: 2,
            title: 'Training Session: React Best Practices',
            time: 'Tomorrow 10:00 AM',
            type: 'training',
            duration: '2 hours'
        },
        {
            id: 3,
            title: 'Performance Review',
            time: 'Friday 2:00 PM',
            type: 'review',
            with: 'Manager'
        }
    ];
    return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
        className: "space-y-4 sm:space-y-6",
        children: [
            /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                className: "bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-4 sm:p-6 text-white",
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "flex-1",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("h1", {
                                    className: "text-xl sm:text-2xl font-bold leading-tight",
                                    children: [
                                        "Good ",
                                        currentTime.getHours() < 12 ? 'Morning' : currentTime.getHours() < 18 ? 'Afternoon' : 'Evening',
                                        ", ",
                                        user?.first_name,
                                        "!"
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                    className: "text-orange-100 mt-1 text-sm sm:text-base",
                                    children: (0, _utils.formatDate)(new Date().toISOString(), 'EEEE, MMMM dd, yyyy')
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex flex-col sm:flex-row sm:items-center mt-3 sm:mt-2 space-y-2 sm:space-y-0 sm:space-x-4",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-1",
                                            children: [
                                                biostarConnected ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Wifi, {
                                                    className: "h-4 w-4 text-green-300"
                                                }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.WifiOff, {
                                                    className: "h-4 w-4 text-red-300"
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("span", {
                                                    className: "text-sm text-orange-100",
                                                    children: [
                                                        "BioStar ",
                                                        biostarConnected ? 'Connected' : 'Disconnected'
                                                    ]
                                                })
                                            ]
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-1",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: (0, _utils.cn)("w-2 h-2 rounded-full", todayStats.attendanceStatus === 'PRESENT' ? 'bg-green-300' : todayStats.attendanceStatus === 'LATE' ? 'bg-yellow-300' : todayStats.attendanceStatus === 'EARLY_OUT' ? 'bg-orange-300' : 'bg-red-300')
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                    className: "text-sm text-orange-100",
                                                    children: todayStats.attendanceStatus
                                                })
                                            ]
                                        })
                                    ]
                                })
                            ]
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "text-center sm:text-right",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "text-2xl sm:text-3xl font-bold",
                                    children: currentTime.toLocaleTimeString([], {
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    })
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "text-orange-100 text-sm",
                                    children: "Current Time"
                                }),
                                loading && /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "text-orange-100 text-xs mt-1",
                                    children: "Loading attendance..."
                                })
                            ]
                        })
                    ]
                })
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-4 sm:p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-green-100 rounded-lg flex-shrink-0",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                                            className: "h-5 w-5 sm:h-6 sm:w-6 text-green-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-3 sm:ml-4 min-w-0 flex-1",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-xs sm:text-sm font-medium text-gray-600 truncate",
                                                children: "Check-in Time"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-lg sm:text-2xl font-bold text-gray-900 truncate",
                                                children: todayStats.checkInTime
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-4 sm:p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-blue-100 rounded-lg flex-shrink-0",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Activity, {
                                            className: "h-5 w-5 sm:h-6 sm:w-6 text-blue-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-3 sm:ml-4 min-w-0 flex-1",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-xs sm:text-sm font-medium text-gray-600 truncate",
                                                children: "Hours Worked"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                className: "text-lg sm:text-2xl font-bold text-gray-900 truncate",
                                                children: [
                                                    todayStats.hoursWorked,
                                                    "h"
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-4 sm:p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-purple-100 rounded-lg flex-shrink-0",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Target, {
                                            className: "h-5 w-5 sm:h-6 sm:w-6 text-purple-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-3 sm:ml-4 min-w-0 flex-1",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-xs sm:text-sm font-medium text-gray-600 truncate",
                                                children: "Tasks Completed"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-lg sm:text-2xl font-bold text-gray-900 truncate",
                                                children: todayStats.tasksCompleted
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                            className: "p-4 sm:p-6",
                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                className: "flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                        className: "p-2 bg-orange-100 rounded-lg flex-shrink-0",
                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Calendar, {
                                            className: "h-5 w-5 sm:h-6 sm:w-6 text-orange-600"
                                        })
                                    }),
                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                        className: "ml-3 sm:ml-4 min-w-0 flex-1",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                className: "text-xs sm:text-sm font-medium text-gray-600 truncate",
                                                children: "Weekly Hours"
                                            }),
                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                className: "text-lg sm:text-2xl font-bold text-gray-900 truncate",
                                                children: [
                                                    todayStats.weeklyHours,
                                                    "h"
                                                ]
                                            })
                                        ]
                                    })
                                ]
                            })
                        })
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                className: "grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6",
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "p-4 sm:p-6",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "flex items-center justify-between mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                            className: "text-base sm:text-lg font-medium text-gray-900",
                                            children: "Today's Activities"
                                        }),
                                        biostarConnected && /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "flex items-center space-x-1",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                    className: "w-2 h-2 bg-green-500 rounded-full animate-pulse"
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                                    className: "text-xs text-gray-500",
                                                    children: "Live"
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                    className: "space-y-3 sm:space-y-4",
                                    children: [
                                        realtimeUpdates.slice(0, 2).map((update, index)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex items-start space-x-3",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                        className: "p-2 rounded-full bg-blue-100 flex-shrink-0",
                                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                                                            className: "h-4 w-4 text-blue-600"
                                                        })
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                        className: "flex-1 min-w-0",
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                                className: "text-sm font-medium text-gray-900 truncate",
                                                                children: [
                                                                    update.employee_name,
                                                                    " - ",
                                                                    update.event_type.replace('_', ' ')
                                                                ]
                                                            }),
                                                            /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                                className: "text-xs text-gray-500 truncate",
                                                                children: [
                                                                    new Date(update.timestamp).toLocaleTimeString(),
                                                                    " at ",
                                                                    update.device_name
                                                                ]
                                                            })
                                                        ]
                                                    })
                                                ]
                                            }, `realtime-${index}`)),
                                        recentActivities.map((activity)=>{
                                            const Icon = activity.icon;
                                            return /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                className: "flex items-start space-x-3",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                                        className: (0, _utils.cn)('p-2 rounded-full bg-gray-100 flex-shrink-0', activity.color),
                                                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Icon, {
                                                            className: "h-4 w-4"
                                                        })
                                                    }),
                                                    /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                                        className: "flex-1 min-w-0",
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                                className: "text-sm font-medium text-gray-900 truncate",
                                                                children: activity.description
                                                            }),
                                                            /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                                className: "text-xs text-gray-500",
                                                                children: activity.time
                                                            })
                                                        ]
                                                    })
                                                ]
                                            }, activity.id);
                                        })
                                    ]
                                })
                            ]
                        })
                    }),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                        children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "p-4 sm:p-6",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                                    className: "text-base sm:text-lg font-medium text-gray-900 mb-4",
                                    children: "Upcoming Events"
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                                    className: "space-y-3 sm:space-y-4",
                                    children: upcomingEvents.map((event)=>/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                                            className: "border-l-4 border-orange-500 pl-3 sm:pl-4",
                                            children: [
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("h4", {
                                                    className: "text-sm font-medium text-gray-900 leading-tight",
                                                    children: event.title
                                                }),
                                                /*#__PURE__*/ (0, _jsxruntime.jsx)("p", {
                                                    className: "text-xs text-gray-500 mt-1",
                                                    children: event.time
                                                }),
                                                event.participants && /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                    className: "text-xs text-gray-500",
                                                    children: [
                                                        event.participants,
                                                        " participants"
                                                    ]
                                                }),
                                                event.duration && /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                    className: "text-xs text-gray-500",
                                                    children: [
                                                        "Duration: ",
                                                        event.duration
                                                    ]
                                                }),
                                                event.with && /*#__PURE__*/ (0, _jsxruntime.jsxs)("p", {
                                                    className: "text-xs text-gray-500",
                                                    children: [
                                                        "With: ",
                                                        event.with
                                                    ]
                                                })
                                            ]
                                        }, event.id))
                                })
                            ]
                        })
                    })
                ]
            }),
            /*#__PURE__*/ (0, _jsxruntime.jsx)(_Card.default, {
                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                    className: "p-4 sm:p-6",
                    children: [
                        /*#__PURE__*/ (0, _jsxruntime.jsx)("h3", {
                            className: "text-base sm:text-lg font-medium text-gray-900 mb-4",
                            children: "Quick Actions"
                        }),
                        /*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                            className: "grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4",
                            children: [
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                                    className: "p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center touch-manipulation",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.FileText, {
                                            className: "h-5 w-5 sm:h-6 sm:w-6 text-orange-500 mx-auto mb-2"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-xs sm:text-sm font-medium text-gray-900 leading-tight",
                                            children: "Apply Leave"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                                    className: "p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center touch-manipulation",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.Clock, {
                                            className: "h-5 w-5 sm:h-6 sm:w-6 text-blue-500 mx-auto mb-2"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-xs sm:text-sm font-medium text-gray-900 leading-tight",
                                            children: "Check Out"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                                    className: "p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center touch-manipulation",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.User, {
                                            className: "h-5 w-5 sm:h-6 sm:w-6 text-green-500 mx-auto mb-2"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-xs sm:text-sm font-medium text-gray-900 leading-tight",
                                            children: "Update Profile"
                                        })
                                    ]
                                }),
                                /*#__PURE__*/ (0, _jsxruntime.jsxs)("button", {
                                    className: "p-3 sm:p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center touch-manipulation",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)(_lucidereact.TrendingUp, {
                                            className: "h-5 w-5 sm:h-6 sm:w-6 text-purple-500 mx-auto mb-2"
                                        }),
                                        /*#__PURE__*/ (0, _jsxruntime.jsx)("span", {
                                            className: "text-xs sm:text-sm font-medium text-gray-900 leading-tight",
                                            children: "View Performance"
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            })
        ]
    });
};
const _default = StaffDashboard;

//# sourceMappingURL=data:application/json;base64,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