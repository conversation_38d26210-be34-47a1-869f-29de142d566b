import { Employee, EmployeeFormData, EmployeeFilters, EmployeeBulkAction, Department, PaginatedResponse } from '@/types';
import { mockApi } from './mockApi';

export class EmployeeApi {
  private static baseUrl = '/api/employees';

  // Get all employees with filters and pagination
  static async getEmployees(
    filters?: EmployeeFilters,
    page: number = 1,
    pageSize: number = 10
  ): Promise<PaginatedResponse<Employee>> {
    try {
      // Mock implementation - replace with actual API call
      return mockApi.getEmployees(filters, page, pageSize);
    } catch (error) {
      console.error('Error fetching employees:', error);
      throw error;
    }
  }

  // Get single employee by ID
  static async getEmployee(id: number): Promise<Employee> {
    try {
      return mockApi.getEmployee(id);
    } catch (error) {
      console.error('Error fetching employee:', error);
      throw error;
    }
  }

  // Create new employee
  static async createEmployee(data: EmployeeFormData): Promise<Employee> {
    try {
      return mockApi.createEmployee(data);
    } catch (error) {
      console.error('Error creating employee:', error);
      throw error;
    }
  }

  // Update existing employee
  static async updateEmployee(id: number, data: Partial<EmployeeFormData>): Promise<Employee> {
    try {
      return mockApi.updateEmployee(id, data);
    } catch (error) {
      console.error('Error updating employee:', error);
      throw error;
    }
  }

  // Soft delete employee
  static async deleteEmployee(id: number): Promise<void> {
    try {
      return mockApi.deleteEmployee(id);
    } catch (error) {
      console.error('Error deleting employee:', error);
      throw error;
    }
  }

  // Restore soft-deleted employee
  static async restoreEmployee(id: number): Promise<Employee> {
    try {
      return mockApi.restoreEmployee(id);
    } catch (error) {
      console.error('Error restoring employee:', error);
      throw error;
    }
  }

  // Bulk operations
  static async bulkAction(action: EmployeeBulkAction): Promise<{ success: number; failed: number; errors?: string[] }> {
    try {
      return mockApi.bulkEmployeeAction(action);
    } catch (error) {
      console.error('Error performing bulk action:', error);
      throw error;
    }
  }

  // Upload employee profile picture
  static async uploadProfilePicture(id: number, file: File): Promise<{ profile_picture: string }> {
    try {
      return mockApi.uploadEmployeeProfilePicture(id, file);
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      throw error;
    }
  }

  // Upload employee document
  static async uploadDocument(
    id: number, 
    file: File, 
    documentType: string
  ): Promise<{ document_url: string; document_id: number }> {
    try {
      return mockApi.uploadEmployeeDocument(id, file, documentType);
    } catch (error) {
      console.error('Error uploading document:', error);
      throw error;
    }
  }

  // Get employee statistics
  static async getEmployeeStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    on_leave: number;
    terminated: number;
    by_department: { department: string; count: number }[];
    by_role: { role: string; count: number }[];
  }> {
    try {
      return mockApi.getEmployeeStats();
    } catch (error) {
      console.error('Error fetching employee stats:', error);
      throw error;
    }
  }

  // Get departments for dropdowns
  static async getDepartments(): Promise<Department[]> {
    try {
      return mockApi.getDepartments();
    } catch (error) {
      console.error('Error fetching departments:', error);
      throw error;
    }
  }

  // Get supervisors for dropdowns
  static async getSupervisors(): Promise<Employee[]> {
    try {
      return mockApi.getSupervisors();
    } catch (error) {
      console.error('Error fetching supervisors:', error);
      throw error;
    }
  }

  // Export employees data
  static async exportEmployees(
    format: 'csv' | 'excel' | 'pdf',
    filters?: EmployeeFilters
  ): Promise<{ download_url: string }> {
    try {
      return mockApi.exportEmployees(format, filters);
    } catch (error) {
      console.error('Error exporting employees:', error);
      throw error;
    }
  }

  // Import employees from file
  static async importEmployees(file: File): Promise<{
    success: number;
    failed: number;
    errors?: string[];
    preview?: Employee[];
  }> {
    try {
      return mockApi.importEmployees(file);
    } catch (error) {
      console.error('Error importing employees:', error);
      throw error;
    }
  }

  // Validate employee data
  static async validateEmployeeData(data: EmployeeFormData): Promise<{
    valid: boolean;
    errors: { [key: string]: string[] };
  }> {
    try {
      return mockApi.validateEmployeeData(data);
    } catch (error) {
      console.error('Error validating employee data:', error);
      throw error;
    }
  }

  // Check if employee ID is unique
  static async checkEmployeeIdUnique(employeeId: string, excludeId?: number): Promise<boolean> {
    try {
      return mockApi.checkEmployeeIdUnique(employeeId, excludeId);
    } catch (error) {
      console.error('Error checking employee ID uniqueness:', error);
      throw error;
    }
  }

  // Check if email is unique
  static async checkEmailUnique(email: string, excludeId?: number): Promise<boolean> {
    try {
      return mockApi.checkEmailUnique(email, excludeId);
    } catch (error) {
      console.error('Error checking email uniqueness:', error);
      throw error;
    }
  }
}
