'use client';

import React, { useState, useEffect } from 'react';
import {
  X,
  Save,
  Plus,
  Trash2,
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  GraduationCap,
  Award,
  Users,
  Calendar,
  DollarSign,
  Upload,
  Camera
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { cn } from '@/lib/utils';
import { Employee, CreateEmployeeRequest, UpdateEmployeeRequest } from '@/lib/employeeService';

interface EmployeeFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: CreateEmployeeRequest | UpdateEmployeeRequest) => Promise<void>;
  employee?: Employee | null;
  mode: 'create' | 'edit';
}

const EmployeeForm: React.FC<EmployeeFormProps> = ({
  isOpen,
  onClose,
  onSave,
  employee,
  mode
}) => {
  const [activeTab, setActiveTab] = useState<'personal' | 'contact' | 'position' | 'education' | 'experience'>('personal');
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CreateEmployeeRequest>({
    employee_id: '',
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    date_of_birth: '',
    gender: 'prefer_not_to_say',
    nationality: '',
    marital_status: 'single',
    emergency_contact: {
      name: '',
      relationship: '',
      phone: '',
      email: ''
    },
    address: {
      street: '',
      city: '',
      state: '',
      postal_code: '',
      country: ''
    },
    position: {
      title: '',
      department: '',
      manager: '',
      employment_type: 'full_time',
      start_date: '',
      salary: 0,
      currency: 'KSH'
    },
    skills: [],
    education: [],
    certifications: [],
    work_experience: [],
    profile_picture: ''
  });

  const [newSkill, setNewSkill] = useState('');

  useEffect(() => {
    if (employee && mode === 'edit') {
      setFormData({
        employee_id: employee.employee_id,
        first_name: employee.first_name,
        last_name: employee.last_name,
        email: employee.email,
        phone: employee.phone,
        date_of_birth: employee.date_of_birth,
        gender: employee.gender,
        nationality: employee.nationality,
        marital_status: employee.marital_status,
        emergency_contact: employee.emergency_contact,
        address: employee.address,
        position: employee.position,
        skills: employee.skills,
        education: employee.education,
        certifications: employee.certifications,
        work_experience: employee.work_experience,
        profile_picture: employee.profile_picture
      });
    } else if (mode === 'create') {
      // Reset form for create mode
      setFormData({
        employee_id: '',
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        date_of_birth: '',
        gender: 'prefer_not_to_say',
        nationality: '',
        marital_status: 'single',
        emergency_contact: {
          name: '',
          relationship: '',
          phone: '',
          email: ''
        },
        address: {
          street: '',
          city: '',
          state: '',
          postal_code: '',
          country: ''
        },
        position: {
          title: '',
          department: '',
          manager: '',
          employment_type: 'full_time',
          start_date: '',
          salary: 0,
          currency: 'KSH'
        },
        skills: [],
        education: [],
        certifications: [],
        work_experience: [],
        profile_picture: ''
      });
    }
  }, [employee, mode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (mode === 'edit' && employee) {
        await onSave({ id: employee.id, ...formData } as UpdateEmployeeRequest);
      } else {
        await onSave(formData);
      }
      onClose();
    } catch (error) {
      console.error('Error saving employee:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (path: string, value: any) => {
    setFormData(prev => {
      const keys = path.split('.');
      const newData = { ...prev };
      let current: any = newData;

      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return newData;
    });
  };

  const addSkill = () => {
    if (newSkill.trim() && !formData.skills.includes(newSkill.trim())) {
      setFormData(prev => ({
        ...prev,
        skills: [...prev.skills, newSkill.trim()]
      }));
      setNewSkill('');
    }
  };

  const removeSkill = (skillToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.filter(skill => skill !== skillToRemove)
    }));
  };

  const addEducation = () => {
    setFormData(prev => ({
      ...prev,
      education: [
        ...prev.education,
        {
          degree: '',
          institution: '',
          graduation_year: new Date().getFullYear(),
          field_of_study: ''
        }
      ]
    }));
  };

  const updateEducation = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      education: prev.education.map((edu, i) =>
        i === index ? { ...edu, [field]: value } : edu
      )
    }));
  };

  const removeEducation = (index: number) => {
    setFormData(prev => ({
      ...prev,
      education: prev.education.filter((_, i) => i !== index)
    }));
  };

  const addCertification = () => {
    setFormData(prev => ({
      ...prev,
      certifications: [
        ...prev.certifications,
        {
          name: '',
          issuer: '',
          issue_date: '',
          expiry_date: '',
          credential_id: ''
        }
      ]
    }));
  };

  const updateCertification = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      certifications: prev.certifications.map((cert, i) =>
        i === index ? { ...cert, [field]: value } : cert
      )
    }));
  };

  const removeCertification = (index: number) => {
    setFormData(prev => ({
      ...prev,
      certifications: prev.certifications.filter((_, i) => i !== index)
    }));
  };

  const addWorkExperience = () => {
    setFormData(prev => ({
      ...prev,
      work_experience: [
        ...prev.work_experience,
        {
          company: '',
          position: '',
          start_date: '',
          end_date: '',
          description: ''
        }
      ]
    }));
  };

  const updateWorkExperience = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      work_experience: prev.work_experience.map((exp, i) =>
        i === index ? { ...exp, [field]: value } : exp
      )
    }));
  };

  const removeWorkExperience = (index: number) => {
    setFormData(prev => ({
      ...prev,
      work_experience: prev.work_experience.filter((_, i) => i !== index)
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-y-auto">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[95vh] my-8 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <User className="h-6 w-6 text-orange-600 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {mode === 'create' ? 'Add New Employee' : 'Edit Employee'}
              </h3>
              <p className="text-sm text-gray-600">
                {mode === 'create'
                  ? 'Fill in the employee information below'
                  : `Update information for ${employee?.first_name} ${employee?.last_name}`
                }
              </p>
            </div>
          </div>
          <button onClick={onClose} className="p-2 hover:bg-gray-100 rounded-full">
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { key: 'personal', label: 'Personal Info', icon: User },
              { key: 'contact', label: 'Contact & Address', icon: MapPin },
              { key: 'position', label: 'Position & Salary', icon: Briefcase },
              { key: 'education', label: 'Education & Skills', icon: GraduationCap },
              { key: 'experience', label: 'Experience', icon: Award },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={cn(
                    'py-4 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2',
                    activeTab === tab.key
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Form Content */}
        <form onSubmit={handleSubmit} className="flex-1 overflow-hidden">
          <div className="p-6 overflow-y-auto max-h-96">
            {activeTab === 'personal' && (
              <PersonalInfoTab
                formData={formData}
                updateFormData={updateFormData}
                newSkill={newSkill}
                setNewSkill={setNewSkill}
                addSkill={addSkill}
                removeSkill={removeSkill}
              />
            )}

            {activeTab === 'contact' && (
              <ContactTab formData={formData} updateFormData={updateFormData} />
            )}

            {activeTab === 'position' && (
              <PositionTab formData={formData} updateFormData={updateFormData} />
            )}

            {activeTab === 'education' && (
              <EducationTab
                formData={formData}
                addEducation={addEducation}
                updateEducation={updateEducation}
                removeEducation={removeEducation}
                addCertification={addCertification}
                updateCertification={updateCertification}
                removeCertification={removeCertification}
              />
            )}

            {activeTab === 'experience' && (
              <ExperienceTab
                formData={formData}
                addWorkExperience={addWorkExperience}
                updateWorkExperience={updateWorkExperience}
                removeWorkExperience={removeWorkExperience}
              />
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
            <Button type="button" variant="secondary" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" variant="primary" disabled={loading}>
              <Save className="h-4 w-4 mr-2" />
              {loading ? 'Saving...' : (mode === 'create' ? 'Create Employee' : 'Update Employee')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Personal Info Tab Component
interface PersonalInfoTabProps {
  formData: CreateEmployeeRequest;
  updateFormData: (path: string, value: any) => void;
  newSkill: string;
  setNewSkill: (value: string) => void;
  addSkill: () => void;
  removeSkill: (skill: string) => void;
}

const PersonalInfoTab: React.FC<PersonalInfoTabProps> = ({
  formData,
  updateFormData,
  newSkill,
  setNewSkill,
  addSkill,
  removeSkill
}) => {
  return (
    <div className="space-y-6">
      {/* Profile Picture */}
      <div className="flex items-center space-x-6">
        <div className="relative">
          <div className="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
            {formData.profile_picture ? (
              <img
                src={formData.profile_picture}
                alt="Profile"
                className="w-full h-full object-cover"
              />
            ) : (
              <User className="h-12 w-12 text-gray-400" />
            )}
          </div>
          <button
            type="button"
            className="absolute bottom-0 right-0 bg-orange-600 text-white p-1 rounded-full hover:bg-orange-700"
          >
            <Camera className="h-3 w-3" />
          </button>
        </div>
        <div>
          <h4 className="text-sm font-medium text-gray-900">Profile Picture</h4>
          <p className="text-sm text-gray-600">Upload a profile picture for the employee</p>
          <Button type="button" variant="secondary" size="sm" className="mt-2">
            <Upload className="h-4 w-4 mr-2" />
            Upload Photo
          </Button>
        </div>
      </div>

      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Employee ID *
          </label>
          <Input
            value={formData.employee_id}
            onChange={(e) => updateFormData('employee_id', e.target.value)}
            placeholder="EMP001"
            required
          />
        </div>
        <div></div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            First Name *
          </label>
          <Input
            value={formData.first_name}
            onChange={(e) => updateFormData('first_name', e.target.value)}
            placeholder="John"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Last Name *
          </label>
          <Input
            value={formData.last_name}
            onChange={(e) => updateFormData('last_name', e.target.value)}
            placeholder="Doe"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Date of Birth
          </label>
          <Input
            type="date"
            value={formData.date_of_birth}
            onChange={(e) => updateFormData('date_of_birth', e.target.value)}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Gender
          </label>
          <select
            value={formData.gender}
            onChange={(e) => updateFormData('gender', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value="male">Male</option>
            <option value="female">Female</option>
            <option value="other">Other</option>
            <option value="prefer_not_to_say">Prefer not to say</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nationality
          </label>
          <Input
            value={formData.nationality}
            onChange={(e) => updateFormData('nationality', e.target.value)}
            placeholder="American"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Marital Status
          </label>
          <select
            value={formData.marital_status}
            onChange={(e) => updateFormData('marital_status', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value="single">Single</option>
            <option value="married">Married</option>
            <option value="divorced">Divorced</option>
            <option value="widowed">Widowed</option>
            <option value="separated">Separated</option>
          </select>
        </div>
      </div>

      {/* Skills */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Skills
        </label>
        <div className="flex space-x-2 mb-3">
          <Input
            value={newSkill}
            onChange={(e) => setNewSkill(e.target.value)}
            placeholder="Add a skill"
            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}
          />
          <Button type="button" onClick={addSkill} variant="secondary">
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex flex-wrap gap-2">
          {formData.skills.map((skill, index) => (
            <span
              key={index}
              className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-orange-100 text-orange-800"
            >
              {skill}
              <button
                type="button"
                onClick={() => removeSkill(skill)}
                className="ml-2 text-orange-600 hover:text-orange-800"
              >
                <X className="h-3 w-3" />
              </button>
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};

// Contact Tab Component
interface ContactTabProps {
  formData: CreateEmployeeRequest;
  updateFormData: (path: string, value: any) => void;
}

const ContactTab: React.FC<ContactTabProps> = ({ formData, updateFormData }) => {
  return (
    <div className="space-y-6">
      {/* Contact Information */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Address *
            </label>
            <Input
              type="email"
              value={formData.email}
              onChange={(e) => updateFormData('email', e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number *
            </label>
            <Input
              type="tel"
              value={formData.phone}
              onChange={(e) => updateFormData('phone', e.target.value)}
              placeholder="******-0123"
              required
            />
          </div>
        </div>
      </div>

      {/* Address */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Address</h4>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Street Address
            </label>
            <Input
              value={formData.address.street}
              onChange={(e) => updateFormData('address.street', e.target.value)}
              placeholder="123 Main St"
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                City
              </label>
              <Input
                value={formData.address.city}
                onChange={(e) => updateFormData('address.city', e.target.value)}
                placeholder="New York"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                State/Province
              </label>
              <Input
                value={formData.address.state}
                onChange={(e) => updateFormData('address.state', e.target.value)}
                placeholder="NY"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Postal Code
              </label>
              <Input
                value={formData.address.postal_code}
                onChange={(e) => updateFormData('address.postal_code', e.target.value)}
                placeholder="10001"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Country
              </label>
              <Input
                value={formData.address.country}
                onChange={(e) => updateFormData('address.country', e.target.value)}
                placeholder="United States"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Emergency Contact */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Emergency Contact</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Contact Name
            </label>
            <Input
              value={formData.emergency_contact.name}
              onChange={(e) => updateFormData('emergency_contact.name', e.target.value)}
              placeholder="Jane Doe"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Relationship
            </label>
            <Input
              value={formData.emergency_contact.relationship}
              onChange={(e) => updateFormData('emergency_contact.relationship', e.target.value)}
              placeholder="Spouse"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <Input
              type="tel"
              value={formData.emergency_contact.phone}
              onChange={(e) => updateFormData('emergency_contact.phone', e.target.value)}
              placeholder="******-0124"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email (Optional)
            </label>
            <Input
              type="email"
              value={formData.emergency_contact.email || ''}
              onChange={(e) => updateFormData('emergency_contact.email', e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

// Position Tab Component
interface PositionTabProps {
  formData: CreateEmployeeRequest;
  updateFormData: (path: string, value: any) => void;
}

const PositionTab: React.FC<PositionTabProps> = ({ formData, updateFormData }) => {
  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Position Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Job Title *
            </label>
            <Input
              value={formData.position.title}
              onChange={(e) => updateFormData('position.title', e.target.value)}
              placeholder="Senior Software Engineer"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Department *
            </label>
            <Input
              value={formData.position.department}
              onChange={(e) => updateFormData('position.department', e.target.value)}
              placeholder="Engineering"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Manager
            </label>
            <Input
              value={formData.position.manager}
              onChange={(e) => updateFormData('position.manager', e.target.value)}
              placeholder="Sarah Johnson"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Employment Type
            </label>
            <select
              value={formData.position.employment_type}
              onChange={(e) => updateFormData('position.employment_type', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="full_time">Full Time</option>
              <option value="part_time">Part Time</option>
              <option value="contract">Contract</option>
              <option value="intern">Intern</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Start Date *
            </label>
            <Input
              type="date"
              value={formData.position.start_date}
              onChange={(e) => updateFormData('position.start_date', e.target.value)}
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Salary
            </label>
            <div className="flex space-x-2">
              <Input
                type="number"
                value={formData.position.salary}
                onChange={(e) => updateFormData('position.salary', parseFloat(e.target.value) || 0)}
                placeholder="1200000"
              />
              <select
                value={formData.position.currency}
                onChange={(e) => updateFormData('position.currency', e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                <option value="KSH">KSH</option>
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Education Tab Component
interface EducationTabProps {
  formData: CreateEmployeeRequest;
  addEducation: () => void;
  updateEducation: (index: number, field: string, value: any) => void;
  removeEducation: (index: number) => void;
  addCertification: () => void;
  updateCertification: (index: number, field: string, value: any) => void;
  removeCertification: (index: number) => void;
}

const EducationTab: React.FC<EducationTabProps> = ({
  formData,
  addEducation,
  updateEducation,
  removeEducation,
  addCertification,
  updateCertification,
  removeCertification
}) => {
  return (
    <div className="space-y-6">
      {/* Education */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-medium text-gray-900">Education</h4>
          <Button type="button" onClick={addEducation} variant="secondary" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Education
          </Button>
        </div>
        <div className="space-y-4">
          {formData.education.map((edu, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h5 className="text-sm font-medium text-gray-900">Education #{index + 1}</h5>
                <Button
                  type="button"
                  onClick={() => removeEducation(index)}
                  variant="secondary"
                  size="sm"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Degree
                  </label>
                  <Input
                    value={edu.degree}
                    onChange={(e) => updateEducation(index, 'degree', e.target.value)}
                    placeholder="Bachelor of Science"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Institution
                  </label>
                  <Input
                    value={edu.institution}
                    onChange={(e) => updateEducation(index, 'institution', e.target.value)}
                    placeholder="MIT"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Field of Study
                  </label>
                  <Input
                    value={edu.field_of_study}
                    onChange={(e) => updateEducation(index, 'field_of_study', e.target.value)}
                    placeholder="Computer Science"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Graduation Year
                  </label>
                  <Input
                    type="number"
                    value={edu.graduation_year}
                    onChange={(e) => updateEducation(index, 'graduation_year', parseInt(e.target.value) || new Date().getFullYear())}
                    placeholder="2012"
                  />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Certifications */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-medium text-gray-900">Certifications</h4>
          <Button type="button" onClick={addCertification} variant="secondary" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Certification
          </Button>
        </div>
        <div className="space-y-4">
          {formData.certifications.map((cert, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h5 className="text-sm font-medium text-gray-900">Certification #{index + 1}</h5>
                <Button
                  type="button"
                  onClick={() => removeCertification(index)}
                  variant="secondary"
                  size="sm"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Certification Name
                  </label>
                  <Input
                    value={cert.name}
                    onChange={(e) => updateCertification(index, 'name', e.target.value)}
                    placeholder="AWS Certified Solutions Architect"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Issuer
                  </label>
                  <Input
                    value={cert.issuer}
                    onChange={(e) => updateCertification(index, 'issuer', e.target.value)}
                    placeholder="Amazon Web Services"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Issue Date
                  </label>
                  <Input
                    type="date"
                    value={cert.issue_date}
                    onChange={(e) => updateCertification(index, 'issue_date', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Expiry Date (Optional)
                  </label>
                  <Input
                    type="date"
                    value={cert.expiry_date || ''}
                    onChange={(e) => updateCertification(index, 'expiry_date', e.target.value)}
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Credential ID (Optional)
                  </label>
                  <Input
                    value={cert.credential_id || ''}
                    onChange={(e) => updateCertification(index, 'credential_id', e.target.value)}
                    placeholder="AWS-SA-12345"
                  />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

// Experience Tab Component
interface ExperienceTabProps {
  formData: CreateEmployeeRequest;
  addWorkExperience: () => void;
  updateWorkExperience: (index: number, field: string, value: any) => void;
  removeWorkExperience: (index: number) => void;
}

const ExperienceTab: React.FC<ExperienceTabProps> = ({
  formData,
  addWorkExperience,
  updateWorkExperience,
  removeWorkExperience
}) => {
  return (
    <div className="space-y-6">
      <div>
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-medium text-gray-900">Work Experience</h4>
          <Button type="button" onClick={addWorkExperience} variant="secondary" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Experience
          </Button>
        </div>
        <div className="space-y-4">
          {formData.work_experience.map((exp, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h5 className="text-sm font-medium text-gray-900">Experience #{index + 1}</h5>
                <Button
                  type="button"
                  onClick={() => removeWorkExperience(index)}
                  variant="secondary"
                  size="sm"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Company
                  </label>
                  <Input
                    value={exp.company}
                    onChange={(e) => updateWorkExperience(index, 'company', e.target.value)}
                    placeholder="Tech Corp"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Position
                  </label>
                  <Input
                    value={exp.position}
                    onChange={(e) => updateWorkExperience(index, 'position', e.target.value)}
                    placeholder="Software Engineer"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Start Date
                  </label>
                  <Input
                    type="date"
                    value={exp.start_date}
                    onChange={(e) => updateWorkExperience(index, 'start_date', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    End Date (Optional)
                  </label>
                  <Input
                    type="date"
                    value={exp.end_date || ''}
                    onChange={(e) => updateWorkExperience(index, 'end_date', e.target.value)}
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={exp.description}
                    onChange={(e) => updateWorkExperience(index, 'description', e.target.value)}
                    placeholder="Developed web applications using React and Node.js"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EmployeeForm;
