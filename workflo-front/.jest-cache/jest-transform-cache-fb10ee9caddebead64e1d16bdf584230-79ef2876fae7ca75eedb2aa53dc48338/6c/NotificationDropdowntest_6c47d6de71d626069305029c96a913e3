1c41b220f4913f02a77c5924cd3d776c
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _jsxruntime = require("react/jsx-runtime");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
const _react1 = require("@testing-library/react");
const _userevent = /*#__PURE__*/ _interop_require_default(require("@testing-library/user-event"));
const _testutils = require("../../utils/test-utils");
const _NotificationDropdown = /*#__PURE__*/ _interop_require_default(require("../../../components/ui/NotificationDropdown"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const mockNotifications = [
    {
        id: '1',
        type: 'leave_update',
        title: 'Leave Application Approved',
        message: 'Your leave application for Dec 25-29 has been approved.',
        timestamp: '2024-12-20T10:00:00Z',
        read: false,
        priority: 'medium',
        user_id: 1
    },
    {
        id: '2',
        type: 'payroll_update',
        title: 'Payslip Available',
        message: 'Your December payslip is now available for download.',
        timestamp: '2024-12-19T15:30:00Z',
        read: true,
        priority: 'low',
        user_id: 1
    },
    {
        id: '3',
        type: 'system_maintenance',
        title: 'System Maintenance',
        message: 'Scheduled maintenance on Dec 22, 2024 from 2:00 AM to 4:00 AM.',
        timestamp: '2024-12-18T09:00:00Z',
        read: false,
        priority: 'high',
        user_id: 1
    }
];
describe('NotificationDropdown', ()=>{
    const mockNotificationContext = {
        notifications: mockNotifications,
        unreadCount: 2,
        isConnected: true,
        connectionState: 'connected',
        markAsRead: jest.fn(),
        markAllAsRead: jest.fn(),
        clearNotifications: jest.fn(),
        requestPermission: jest.fn().mockResolvedValue('granted')
    };
    beforeEach(()=>{
        jest.clearAllMocks();
    });
    describe('notification bell', ()=>{
        it('renders notification bell with unread count', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            expect(_react1.screen.getByText('2')).toBeInTheDocument(); // Unread count badge
        });
        it('shows connected status indicator', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            const bell = _react1.screen.getByRole('button');
            const statusIndicator = bell.querySelector('.bg-green-500');
            expect(statusIndicator).toBeInTheDocument();
        });
        it('shows disconnected status when not connected', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: {
                    ...mockNotificationContext,
                    isConnected: false,
                    connectionState: 'disconnected'
                }
            });
            const bell = _react1.screen.getByRole('button');
            const statusIndicator = bell.querySelector('.bg-red-500');
            expect(statusIndicator).toBeInTheDocument();
        });
        it('does not show unread count when there are no unread notifications', ()=>{
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: {
                    ...mockNotificationContext,
                    unreadCount: 0
                }
            });
            expect(_react1.screen.queryByText('2')).not.toBeInTheDocument();
        });
    });
    describe('dropdown functionality', ()=>{
        it('opens dropdown when bell is clicked', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            expect(_react1.screen.getByText('Notifications')).toBeInTheDocument();
            expect(_react1.screen.getByText('2 unread notifications')).toBeInTheDocument();
        });
        it('closes dropdown when close button is clicked', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            // Open dropdown
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            // Close dropdown
            const closeButton = _react1.screen.getByRole('button', {
                name: ''
            }); // X button
            await user.click(closeButton);
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.queryByText('Notifications')).not.toBeInTheDocument();
            });
        });
        it('closes dropdown when clicking outside', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsxs)("div", {
                children: [
                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}),
                    /*#__PURE__*/ (0, _jsxruntime.jsx)("div", {
                        "data-testid": "outside",
                        children: "Outside element"
                    })
                ]
            }), {
                notificationContextValue: mockNotificationContext
            });
            // Open dropdown
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            expect(_react1.screen.getByText('Notifications')).toBeInTheDocument();
            // Click outside
            const outsideElement = _react1.screen.getByTestId('outside');
            await user.click(outsideElement);
            await (0, _react1.waitFor)(()=>{
                expect(_react1.screen.queryByText('Notifications')).not.toBeInTheDocument();
            });
        });
    });
    describe('notification list', ()=>{
        it('displays all notifications', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            expect(_react1.screen.getByText('Leave Application Approved')).toBeInTheDocument();
            expect(_react1.screen.getByText('Payslip Available')).toBeInTheDocument();
            expect(_react1.screen.getByText('System Maintenance')).toBeInTheDocument();
        });
        it('shows empty state when no notifications', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: {
                    ...mockNotificationContext,
                    notifications: [],
                    unreadCount: 0
                }
            });
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            expect(_react1.screen.getByText('No notifications yet')).toBeInTheDocument();
        });
        it('marks notification as read when clicked', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            const notification = _react1.screen.getByText('Leave Application Approved');
            await user.click(notification);
            expect(mockNotificationContext.markAsRead).toHaveBeenCalledWith('1');
        });
        it('marks individual notification as read via button', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            // Find the mark as read button for the first notification
            const markAsReadButtons = _react1.screen.getAllByRole('button', {
                name: ''
            });
            const markAsReadButton = markAsReadButtons.find((button)=>button.querySelector('svg')?.getAttribute('data-testid') === 'check');
            if (markAsReadButton) {
                await user.click(markAsReadButton);
                expect(mockNotificationContext.markAsRead).toHaveBeenCalledWith('1');
            }
        });
    });
    describe('notification actions', ()=>{
        it('marks all notifications as read', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            const markAllButton = _react1.screen.getByRole('button', {
                name: ''
            }); // CheckCheck icon
            await user.click(markAllButton);
            expect(mockNotificationContext.markAllAsRead).toHaveBeenCalled();
        });
        it('opens settings panel', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            const settingsButton = _react1.screen.getByRole('button', {
                name: ''
            }); // Settings icon
            await user.click(settingsButton);
            expect(_react1.screen.getByText('Notification Settings')).toBeInTheDocument();
            expect(_react1.screen.getByText('Enable Browser Notifications')).toBeInTheDocument();
            expect(_react1.screen.getByText('Clear All Notifications')).toBeInTheDocument();
        });
        it('requests browser notification permission', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            // Open settings
            const settingsButton = _react1.screen.getByRole('button', {
                name: ''
            }); // Settings icon
            await user.click(settingsButton);
            // Click enable notifications
            const enableButton = _react1.screen.getByText('Enable Browser Notifications');
            await user.click(enableButton);
            expect(mockNotificationContext.requestPermission).toHaveBeenCalled();
        });
        it('clears all notifications', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            // Open settings
            const settingsButton = _react1.screen.getByRole('button', {
                name: ''
            }); // Settings icon
            await user.click(settingsButton);
            // Click clear all
            const clearButton = _react1.screen.getByText('Clear All Notifications');
            await user.click(clearButton);
            expect(mockNotificationContext.clearNotifications).toHaveBeenCalled();
        });
    });
    describe('connection status', ()=>{
        it('shows connection status in header', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            expect(_react1.screen.getByText('connected')).toBeInTheDocument();
        });
        it('shows disconnected status when not connected', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: {
                    ...mockNotificationContext,
                    isConnected: false,
                    connectionState: 'disconnected'
                }
            });
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            expect(_react1.screen.getByText('disconnected')).toBeInTheDocument();
        });
    });
    describe('notification priority styling', ()=>{
        it('applies correct styling for high priority notifications', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            const highPriorityNotification = _react1.screen.getByText('System Maintenance').closest('div');
            expect(highPriorityNotification).toHaveClass('border-l-red-500', 'bg-red-50');
        });
        it('applies correct styling for medium priority notifications', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            const mediumPriorityNotification = _react1.screen.getByText('Leave Application Approved').closest('div');
            expect(mediumPriorityNotification).toHaveClass('border-l-yellow-500', 'bg-yellow-50');
        });
        it('applies correct styling for low priority notifications', async ()=>{
            const user = _userevent.default.setup();
            (0, _testutils.render)(/*#__PURE__*/ (0, _jsxruntime.jsx)(_NotificationDropdown.default, {}), {
                notificationContextValue: mockNotificationContext
            });
            const bell = _react1.screen.getByRole('button');
            await user.click(bell);
            const lowPriorityNotification = _react1.screen.getByText('Payslip Available').closest('div');
            expect(lowPriorityNotification).toHaveClass('border-l-green-500', 'bg-green-50');
        });
    });
});

//# sourceMappingURL=data:application/json;base64,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