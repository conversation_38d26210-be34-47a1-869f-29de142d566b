'use client';

import React from 'react';
import Link from 'next/link';
import {
  User,
  Briefcase,
  DollarSign,
  Calendar,
  FileText,
  Star,
  Clock,
  ChevronRight,
  Edit,
  Eye,
  Download,
  Fingerprint
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useAuth } from '@/providers/AuthProvider';
import { cn, formatCurrency } from '@/lib/utils';

const StaffInfoPage: React.FC = () => {
  const { user } = useAuth();

  const infoSections = [
    {
      title: 'Profile',
      description: 'Personal information and contact details',
      href: '/staff/info/profile',
      icon: User,
      color: 'bg-blue-500',
      stats: {
        label: 'Profile Completion',
        value: '85%'
      }
    },
    {
      title: 'Employment',
      description: 'Job details and employment history',
      href: '/staff/info/employment',
      icon: Briefcase,
      color: 'bg-green-500',
      stats: {
        label: 'Years of Service',
        value: '2.5'
      }
    },
    {
      title: 'Salary',
      description: 'Salary information and payslips',
      href: '/staff/info/salary',
      icon: DollarSign,
      color: 'bg-yellow-500',
      stats: {
        label: 'Current Salary',
        value: 'KSH 95,000'
      }
    },
    {
      title: 'Leave Management',
      description: 'Leave applications and balance',
      href: '/staff/info/leave-management',
      icon: Calendar,
      color: 'bg-purple-500',
      stats: {
        label: 'Leave Balance',
        value: '15 days'
      }
    },
    {
      title: 'Documents',
      description: 'Personal and employment documents',
      href: '/staff/info/documents',
      icon: FileText,
      color: 'bg-orange-500',
      stats: {
        label: 'Documents',
        value: '12 files'
      }
    },
    {
      title: 'Performance',
      description: 'Performance reviews and goals',
      href: '/staff/info/performance',
      icon: Star,
      color: 'bg-pink-500',
      stats: {
        label: 'Last Review',
        value: '4.8/5.0'
      }
    },
    {
      title: 'Time Off',
      description: 'Attendance and time tracking',
      href: '/staff/info/timeoff',
      icon: Clock,
      color: 'bg-indigo-500',
      stats: {
        label: 'This Month',
        value: '160 hrs'
      }
    },
    {
      title: 'BioStar Profile',
      description: 'Biometric data and access history',
      href: '/staff/info/biostar',
      icon: Fingerprint,
      color: 'bg-purple-500',
      stats: {
        label: 'Devices Online',
        value: '3/4'
      }
    }
  ];

  const quickActions = [
    {
      name: 'Update Profile Picture',
      icon: User,
      href: '/staff/info/profile?action=photo'
    },
    {
      name: 'Download Payslip',
      icon: Download,
      href: '/staff/info/salary?action=download'
    },
    {
      name: 'Apply for Leave',
      icon: Calendar,
      href: '/staff/info/leave-management?action=apply'
    },
    {
      name: 'View Performance Goals',
      icon: Star,
      href: '/staff/info/performance?view=goals'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Personal Information Hub</h1>
          <p className="text-gray-600 mt-1">
            Manage your personal information, employment details, and more
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="secondary">
            <Edit className="h-4 w-4 mr-2" />
            Quick Edit
          </Button>
          <Button variant="primary">
            <Eye className="h-4 w-4 mr-2" />
            View Summary
          </Button>
        </div>
      </div>

      {/* Quick Actions */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {quickActions.map((action, index) => {
              const Icon = action.icon;
              return (
                <Link
                  key={index}
                  href={action.href}
                  className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center group"
                >
                  <Icon className="h-6 w-6 text-orange-500 mx-auto mb-2 group-hover:scale-110 transition-transform" />
                  <span className="text-sm font-medium text-gray-900">{action.name}</span>
                </Link>
              );
            })}
          </div>
        </div>
      </Card>

      {/* Information Sections Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {infoSections.map((section, index) => {
          const Icon = section.icon;
          return (
            <Link key={index} href={section.href}>
              <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer group">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className={cn(
                      'p-3 rounded-lg text-white',
                      section.color
                    )}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-orange-500 transition-colors" />
                  </div>

                  <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-orange-600 transition-colors">
                    {section.title}
                  </h3>

                  <p className="text-gray-600 text-sm mb-4">
                    {section.description}
                  </p>

                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">{section.stats.label}</span>
                      <span className="text-sm font-semibold text-gray-900">{section.stats.value}</span>
                    </div>
                  </div>
                </div>
              </Card>
            </Link>
          );
        })}
      </div>

      {/* Recent Activity */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 rounded-full">
                <User className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Profile updated</p>
                <p className="text-xs text-gray-500">Updated contact information - 2 hours ago</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-full">
                <Calendar className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Leave application submitted</p>
                <p className="text-xs text-gray-500">Annual leave for Dec 25-29 - Yesterday</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-100 rounded-full">
                <FileText className="h-4 w-4 text-purple-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Document uploaded</p>
                <p className="text-xs text-gray-500">Updated ID copy - 3 days ago</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="p-2 bg-orange-100 rounded-full">
                <Star className="h-4 w-4 text-orange-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Performance review completed</p>
                <p className="text-xs text-gray-500">Q4 2024 review submitted - 1 week ago</p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default StaffInfoPage;
