'use client';

import React, { useState, useEffect } from 'react';
import {
  FileText,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Download,
  Send,
  MoreHorizontal,
  User,
  Calendar,
  DollarSign,
  CheckCircle,
  Clock,
  AlertTriangle,
  Trash2
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { cn, formatDate, formatCurrency } from '@/lib/utils';
import PayslipModal from '@/components/payroll/PayslipModal';
import DeleteConfirmationModal from '@/components/payroll/DeleteConfirmationModal';

interface Employee {
  id: number;
  name: string;
  employee_id: string;
  department: string;
  base_salary: number;
}

interface Payslip {
  id: number;
  employee_id: number;
  employee_name: string;
  employee_code: string;
  pay_period: string;
  pay_date: string;
  gross_salary: number;
  basic_salary: number;
  allowances: number;
  overtime: number;
  bonuses: number;
  total_deductions: number;
  tax_deduction: number;
  nssf_deduction: number;
  nhif_deduction: number;
  housing_levy: number;
  other_deductions: number;
  net_salary: number;
  status: 'generated' | 'sent' | 'viewed';
  generated_at: string;
}

const PayslipsPage: React.FC = () => {
  const [payslips, setPayslips] = useState<Payslip[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'generated' | 'sent' | 'viewed'>('all');

  // Modal states
  const [isPayslipModalOpen, setIsPayslipModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedPayslip, setSelectedPayslip] = useState<Payslip | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create');

  useEffect(() => {
    fetchPayslips();
    fetchEmployees();
  }, []);

  const fetchEmployees = async () => {
    try {
      // Mock employees data
      const mockEmployees: Employee[] = [
        { id: 1, name: 'John Doe', employee_id: 'EMP001', department: 'Engineering', base_salary: 120000 },
        { id: 2, name: 'Jane Smith', employee_id: 'EMP002', department: 'Marketing', base_salary: 150000 },
        { id: 3, name: 'Michael Johnson', employee_id: 'EMP003', department: 'Sales', base_salary: 110000 },
        { id: 4, name: 'Sarah Wilson', employee_id: 'EMP004', department: 'HR', base_salary: 95000 },
        { id: 5, name: 'David Brown', employee_id: 'EMP005', department: 'Finance', base_salary: 130000 },
        { id: 6, name: 'Emily Davis', employee_id: 'EMP006', department: 'Operations', base_salary: 105000 },
        { id: 7, name: 'Robert Miller', employee_id: 'EMP007', department: 'IT', base_salary: 125000 },
        { id: 8, name: 'Lisa Anderson', employee_id: 'EMP008', department: 'Legal', base_salary: 140000 },
        { id: 9, name: 'James Taylor', employee_id: 'EMP009', department: 'Engineering', base_salary: 115000 },
        { id: 10, name: 'Maria Garcia', employee_id: 'EMP010', department: 'Design', base_salary: 100000 }
      ];
      setEmployees(mockEmployees);
    } catch (error) {
      console.error('Error fetching employees:', error);
    }
  };

  const fetchPayslips = async () => {
    try {
      // Mock comprehensive payslips data
      const mockPayslips: Payslip[] = [
        {
          id: 1,
          employee_id: 1,
          employee_name: 'John Doe',
          employee_code: 'EMP001',
          pay_period: 'January 2024',
          pay_date: '2024-01-31',
          gross_salary: 135000,
          basic_salary: 120000,
          allowances: 10000,
          overtime: 5000,
          bonuses: 0,
          total_deductions: 40000,
          tax_deduction: 25000,
          nssf_deduction: 7200,
          nhif_deduction: 3713,
          housing_levy: 2025,
          other_deductions: 2062,
          net_salary: 95000,
          status: 'sent',
          generated_at: '2024-01-28'
        },
        {
          id: 2,
          employee_id: 2,
          employee_name: 'Jane Smith',
          employee_code: 'EMP002',
          pay_period: 'January 2024',
          pay_date: '2024-01-31',
          gross_salary: 165000,
          basic_salary: 150000,
          allowances: 12000,
          overtime: 3000,
          bonuses: 0,
          total_deductions: 47000,
          tax_deduction: 32000,
          nssf_deduction: 7200,
          nhif_deduction: 4538,
          housing_levy: 2475,
          other_deductions: 787,
          net_salary: 118000,
          status: 'viewed',
          generated_at: '2024-01-28'
        },
        {
          id: 3,
          employee_id: 3,
          employee_name: 'Michael Johnson',
          employee_code: 'EMP003',
          pay_period: 'January 2024',
          pay_date: '2024-01-31',
          gross_salary: 125000,
          basic_salary: 110000,
          allowances: 8000,
          overtime: 7000,
          bonuses: 0,
          total_deductions: 36000,
          tax_deduction: 22000,
          nssf_deduction: 7200,
          nhif_deduction: 3438,
          housing_levy: 1875,
          other_deductions: 1487,
          net_salary: 89000,
          status: 'generated',
          generated_at: '2024-01-28'
        },
        {
          id: 4,
          employee_id: 4,
          employee_name: 'Sarah Wilson',
          employee_code: 'EMP004',
          pay_period: 'January 2024',
          pay_date: '2024-01-31',
          gross_salary: 105000,
          basic_salary: 95000,
          allowances: 6000,
          overtime: 4000,
          bonuses: 0,
          total_deductions: 28000,
          tax_deduction: 16000,
          nssf_deduction: 6300,
          nhif_deduction: 2888,
          housing_levy: 1575,
          other_deductions: 1237,
          net_salary: 77000,
          status: 'sent',
          generated_at: '2024-01-28'
        },
        {
          id: 5,
          employee_id: 5,
          employee_name: 'David Brown',
          employee_code: 'EMP005',
          pay_period: 'January 2024',
          pay_date: '2024-01-31',
          gross_salary: 145000,
          basic_salary: 130000,
          allowances: 9000,
          overtime: 6000,
          bonuses: 0,
          total_deductions: 42000,
          tax_deduction: 28000,
          nssf_deduction: 7200,
          nhif_deduction: 3988,
          housing_levy: 2175,
          other_deductions: 637,
          net_salary: 103000,
          status: 'viewed',
          generated_at: '2024-01-28'
        }
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      setPayslips(mockPayslips);
    } catch (error) {
      console.error('Error fetching payslips:', error);
    } finally {
      setLoading(false);
    }
  };

  // CRUD Operations
  const handleCreatePayslip = () => {
    setModalMode('create');
    setSelectedPayslip(null);
    setIsPayslipModalOpen(true);
  };

  const handleEditPayslip = (payslip: Payslip) => {
    setModalMode('edit');
    setSelectedPayslip(payslip);
    setIsPayslipModalOpen(true);
  };

  const handleViewPayslip = (payslip: Payslip) => {
    setModalMode('view');
    setSelectedPayslip(payslip);
    setIsPayslipModalOpen(true);
  };

  const handleDeletePayslip = (payslip: Payslip) => {
    setSelectedPayslip(payslip);
    setIsDeleteModalOpen(true);
  };

  const handleSavePayslip = async (payslipData: any) => {
    try {
      if (modalMode === 'create') {
        // Create new payslip
        const newPayslip: Payslip = {
          ...payslipData,
          id: Math.max(...payslips.map(p => p.id), 0) + 1,
          generated_at: new Date().toISOString()
        };
        setPayslips(prev => [...prev, newPayslip]);
      } else {
        // Update existing payslip
        setPayslips(prev =>
          prev.map(payslip =>
            payslip.id === selectedPayslip?.id
              ? { ...payslipData, id: payslip.id, generated_at: payslip.generated_at }
              : payslip
          )
        );
      }
    } catch (error) {
      console.error('Error saving payslip:', error);
      throw error;
    }
  };

  const handleConfirmDelete = async () => {
    try {
      if (selectedPayslip) {
        setPayslips(prev => prev.filter(payslip => payslip.id !== selectedPayslip.id));
      }
    } catch (error) {
      console.error('Error deleting payslip:', error);
      throw error;
    }
  };

  const filteredPayslips = payslips.filter(payslip => {
    const matchesSearch =
      payslip.employee_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payslip.employee_code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payslip.pay_period.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesFilter = selectedFilter === 'all' || payslip.status === selectedFilter;

    return matchesSearch && matchesFilter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'generated': return 'bg-blue-100 text-blue-800';
      case 'sent': return 'bg-yellow-100 text-yellow-800';
      case 'viewed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'generated': return FileText;
      case 'sent': return Send;
      case 'viewed': return CheckCircle;
      default: return AlertTriangle;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payslips</h1>
          <p className="text-sm text-gray-600 mt-1">
            Generate and manage individual payslip access for employees
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Bulk Download
          </Button>
          <Button variant="primary" onClick={handleCreatePayslip}>
            <Plus className="h-4 w-4 mr-2" />
            Generate Payslips
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Payslips</p>
                <p className="text-2xl font-bold text-gray-900">{payslips.length}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Send className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Sent</p>
                <p className="text-2xl font-bold text-gray-900">
                  {payslips.filter(p => p.status === 'sent').length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Viewed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {payslips.filter(p => p.status === 'viewed').length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Paid</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(payslips.reduce((sum, p) => sum + p.net_salary, 0), 'KSH')}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="text"
              placeholder="Search payslips..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value as any)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="all">All Payslips</option>
              <option value="generated">Generated</option>
              <option value="sent">Sent</option>
              <option value="viewed">Viewed</option>
            </select>
          </div>
        </div>
      </div>

      {/* Payslips Table */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Payslips Overview</h3>

          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading payslips...</p>
            </div>
          ) : filteredPayslips.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No payslips found
              </h3>
              <p className="text-gray-600 mb-6">
                Generate payslips for employees to provide individual access.
              </p>
              <Button variant="primary">
                <Plus className="h-4 w-4 mr-2" />
                Generate Payslips
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Employee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pay Period
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Gross Salary
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Deductions
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Net Salary
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredPayslips.map((payslip) => {
                    const StatusIcon = getStatusIcon(payslip.status);

                    return (
                      <tr key={payslip.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                <User className="h-5 w-5 text-gray-500" />
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {payslip.employee_name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {payslip.employee_id}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm text-gray-900">
                            <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                            {payslip.pay_period}
                          </div>
                          <div className="text-sm text-gray-500">
                            Pay Date: {formatDate(payslip.pay_date)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {formatCurrency(payslip.gross_salary, 'KSH')}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-red-600">
                            -{formatCurrency(payslip.total_deductions, 'KSH')}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-bold text-green-600">
                            {formatCurrency(payslip.net_salary, 'KSH')}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={cn(
                            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                            getStatusColor(payslip.status)
                          )}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {payslip.status.charAt(0).toUpperCase() + payslip.status.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              title="View Payslip"
                              onClick={() => handleViewPayslip(payslip)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              title="Edit Payslip"
                              onClick={() => handleEditPayslip(payslip)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              title="Download"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              title="Delete Payslip"
                              onClick={() => handleDeletePayslip(payslip)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </Card>

      {/* Modals */}
      <PayslipModal
        isOpen={isPayslipModalOpen}
        onClose={() => setIsPayslipModalOpen(false)}
        onSave={handleSavePayslip}
        payslip={selectedPayslip}
        employees={employees}
        mode={modalMode}
      />

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Payslip"
        message="Are you sure you want to delete this payslip? This action cannot be undone."
        itemName={selectedPayslip ? `${selectedPayslip.employee_name} - ${selectedPayslip.pay_period}` : ''}
        warningMessage="Deleting this payslip will remove all salary calculation records for this employee and period."
      />
    </div>
  );
};

export default PayslipsPage;
