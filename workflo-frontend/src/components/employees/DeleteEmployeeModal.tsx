'use client';

import React, { useState, useEffect } from 'react';
import { X, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle } from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { cn } from '@/lib/utils';

interface Employee {
  id: number;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  department_name: string;
  position: string;
  status: string;
}

interface DeleteEmployeeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (id: number) => Promise<void>;
  employee: Employee | null;
  loading?: boolean;
}

const DeleteEmployeeModal: React.FC<DeleteEmployeeModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  employee,
  loading = false
}) => {
  const [confirmationText, setConfirmationText] = useState('');
  const [step, setStep] = useState<'details' | 'confirm' | 'success'>('details');
  const [error, setError] = useState('');

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setConfirmationText('');
      setStep('details');
      setError('');
    }
  }, [isOpen]);

  const expectedText = employee ? `${employee.first_name} ${employee.last_name}` : '';
  const isConfirmationValid = confirmationText.trim() === expectedText;

  const handleNext = () => {
    if (step === 'details') {
      setStep('confirm');
    }
  };

  const handleBack = () => {
    if (step === 'confirm') {
      setStep('details');
    }
  };

  const handleConfirm = async () => {
    if (!employee || !isConfirmationValid) {
      setError('Please enter the employee name exactly as shown');
      return;
    }

    try {
      await onConfirm(employee.id);
      setStep('success');
      // Auto close after showing success
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error) {
      setError('Failed to delete employee. Please try again.');
      console.error('Delete error:', error);
    }
  };

  if (!isOpen || !employee) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            {step === 'success' ? (
              <CheckCircle className="h-6 w-6 text-green-500 mr-3" />
            ) : (
              <AlertTriangle className="h-6 w-6 text-red-500 mr-3" />
            )}
            <h2 className="text-xl font-semibold text-gray-900">
              {step === 'success' ? 'Employee Deleted' : 'Delete Employee'}
            </h2>
          </div>
          {step !== 'success' && (
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              disabled={loading}
            >
              <X className="h-5 w-5 text-gray-500" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'details' && (
            <div className="space-y-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex">
                  <AlertTriangle className="h-5 w-5 text-red-400 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <h3 className="text-sm font-medium text-red-800">
                      This action cannot be undone
                    </h3>
                    <p className="text-sm text-red-700 mt-1">
                      Deleting this employee will permanently remove all their data from the system.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Employee Details</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Name:</span>
                    <span className="font-medium text-gray-900">
                      {employee.first_name} {employee.last_name}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Employee ID:</span>
                    <span className="font-medium text-gray-900">{employee.employee_id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Email:</span>
                    <span className="font-medium text-gray-900">{employee.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Department:</span>
                    <span className="font-medium text-gray-900">{employee.department_name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Position:</span>
                    <span className="font-medium text-gray-900">{employee.position}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className={cn(
                      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                      employee.status === 'active' ? 'bg-green-100 text-green-800' :
                      employee.status === 'inactive' ? 'bg-gray-100 text-gray-800' :
                      employee.status === 'on_leave' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    )}>
                      {employee.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex">
                  <AlertTriangle className="h-5 w-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <h3 className="text-sm font-medium text-yellow-800">
                      Data that will be permanently deleted:
                    </h3>
                    <ul className="text-sm text-yellow-700 mt-1 list-disc list-inside space-y-1">
                      <li>Personal information and contact details</li>
                      <li>Employment history and records</li>
                      <li>Training records and certifications</li>
                      <li>Performance reviews and evaluations</li>
                      <li>Time tracking and attendance data</li>
                      <li>Associated documents and files</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {step === 'confirm' && (
            <div className="space-y-4">
              <div className="text-center">
                <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Final Confirmation Required
                </h3>
                <p className="text-sm text-gray-600">
                  To confirm deletion, please type the employee's full name exactly as shown below:
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-3 text-center">
                <span className="font-mono text-lg font-medium text-gray-900">
                  {expectedText}
                </span>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Type the employee name to confirm:
                </label>
                <Input
                  value={confirmationText}
                  onChange={(e) => {
                    setConfirmationText(e.target.value);
                    setError('');
                  }}
                  placeholder="Enter employee name"
                  className={cn(
                    confirmationText && !isConfirmationValid && "border-red-300 focus:ring-red-500"
                  )}
                />
                {confirmationText && !isConfirmationValid && (
                  <p className="mt-1 text-sm text-red-600">
                    Name doesn't match. Please type exactly: {expectedText}
                  </p>
                )}
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              )}
            </div>
          )}

          {step === 'success' && (
            <div className="text-center space-y-4">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Employee Successfully Deleted
                </h3>
                <p className="text-sm text-gray-600">
                  {employee.first_name} {employee.last_name} has been permanently removed from the system.
                </p>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <p className="text-sm text-green-700">
                  This window will close automatically in a few seconds.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {step !== 'success' && (
          <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
            <div>
              {step === 'confirm' && (
                <Button
                  type="button"
                  variant="secondary"
                  onClick={handleBack}
                  disabled={loading}
                >
                  Back
                </Button>
              )}
            </div>
            
            <div className="flex items-center space-x-3">
              <Button
                type="button"
                variant="secondary"
                onClick={onClose}
                disabled={loading}
              >
                Cancel
              </Button>
              
              {step === 'details' ? (
                <Button
                  type="button"
                  variant="danger"
                  onClick={handleNext}
                  disabled={loading}
                >
                  Continue
                </Button>
              ) : (
                <Button
                  type="button"
                  variant="danger"
                  onClick={handleConfirm}
                  disabled={loading || !isConfirmationValid}
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Employee
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DeleteEmployeeModal;
