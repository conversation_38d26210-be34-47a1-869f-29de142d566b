{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/components/ui/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useCallback } from 'react';\nimport {\n  Upload,\n  X,\n  File,\n  Image,\n  FileText,\n  CheckCircle,\n  AlertCircle,\n  Loader2\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport fileUploadService, { UploadProgress, UploadResult } from '@/lib/fileUpload';\n\ninterface FileUploadProps {\n  onUpload?: (results: UploadResult[]) => void;\n  onProgress?: (progress: UploadProgress) => void;\n  multiple?: boolean;\n  accept?: string;\n  maxSize?: number;\n  maxFiles?: number;\n  disabled?: boolean;\n  className?: string;\n  children?: React.ReactNode;\n  endpoint: string;\n  folder?: string;\n}\n\ninterface FileWithPreview extends File {\n  preview?: string;\n  uploadResult?: UploadResult;\n  uploading?: boolean;\n  progress?: number;\n}\n\nconst FileUpload: React.FC<FileUploadProps> = ({\n  onUpload,\n  onProgress,\n  multiple = false,\n  accept,\n  maxSize,\n  maxFiles = 10,\n  disabled = false,\n  className,\n  children,\n  endpoint,\n  folder\n}) => {\n  const [files, setFiles] = useState<FileWithPreview[]>([]);\n  const [isDragOver, setIsDragOver] = useState(false);\n  const [isUploading, setIsUploading] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleFileSelect = useCallback((selectedFiles: FileList | null) => {\n    if (!selectedFiles) return;\n\n    const newFiles: FileWithPreview[] = Array.from(selectedFiles).map(file => {\n      const fileWithPreview = file as FileWithPreview;\n      if (fileUploadService.isImage(file)) {\n        fileWithPreview.preview = fileUploadService.createPreviewUrl(file);\n      }\n      return fileWithPreview;\n    });\n\n    if (multiple) {\n      setFiles(prev => [...prev, ...newFiles].slice(0, maxFiles));\n    } else {\n      // Clean up previous preview URLs\n      files.forEach(file => {\n        if (file.preview) {\n          fileUploadService.revokePreviewUrl(file.preview);\n        }\n      });\n      setFiles(newFiles.slice(0, 1));\n    }\n  }, [files, multiple, maxFiles]);\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n    \n    if (disabled) return;\n    \n    handleFileSelect(e.dataTransfer.files);\n  }, [disabled, handleFileSelect]);\n\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    if (!disabled) {\n      setIsDragOver(true);\n    }\n  }, [disabled]);\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  }, []);\n\n  const handleClick = useCallback(() => {\n    if (!disabled && fileInputRef.current) {\n      fileInputRef.current.click();\n    }\n  }, [disabled]);\n\n  const removeFile = useCallback((index: number) => {\n    setFiles(prev => {\n      const newFiles = [...prev];\n      const removedFile = newFiles[index];\n      \n      // Clean up preview URL\n      if (removedFile.preview) {\n        fileUploadService.revokePreviewUrl(removedFile.preview);\n      }\n      \n      newFiles.splice(index, 1);\n      return newFiles;\n    });\n  }, []);\n\n  const uploadFiles = useCallback(async () => {\n    if (files.length === 0 || isUploading) return;\n\n    setIsUploading(true);\n    const results: UploadResult[] = [];\n\n    try {\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        \n        // Update file state to show uploading\n        setFiles(prev => prev.map((f, idx) => \n          idx === i ? { ...f, uploading: true, progress: 0 } : f\n        ));\n\n        const result = await fileUploadService.uploadFile(file, endpoint, {\n          folder,\n          maxSize,\n          onProgress: (progress) => {\n            // Update individual file progress\n            setFiles(prev => prev.map((f, idx) => \n              idx === i ? { ...f, progress: progress.percentage } : f\n            ));\n            \n            // Calculate overall progress\n            if (onProgress) {\n              const overallProgress = {\n                loaded: (i * 100) + progress.percentage,\n                total: files.length * 100,\n                percentage: Math.round(((i * 100) + progress.percentage) / files.length)\n              };\n              onProgress(overallProgress);\n            }\n          }\n        });\n\n        // Update file state with result\n        setFiles(prev => prev.map((f, idx) => \n          idx === i ? { ...f, uploading: false, uploadResult: result } : f\n        ));\n\n        results.push(result);\n      }\n\n      if (onUpload) {\n        onUpload(results);\n      }\n    } catch (error) {\n      console.error('Upload error:', error);\n    } finally {\n      setIsUploading(false);\n    }\n  }, [files, isUploading, endpoint, folder, maxSize, onProgress, onUpload]);\n\n  const getFileIcon = (file: File) => {\n    if (fileUploadService.isImage(file)) {\n      return <Image className=\"h-8 w-8 text-blue-500\" />;\n    } else if (fileUploadService.isPDF(file)) {\n      return <FileText className=\"h-8 w-8 text-red-500\" />;\n    } else {\n      return <File className=\"h-8 w-8 text-gray-500\" />;\n    }\n  };\n\n  const getUploadStatus = (file: FileWithPreview) => {\n    if (file.uploading) {\n      return (\n        <div className=\"flex items-center space-x-2\">\n          <Loader2 className=\"h-4 w-4 animate-spin text-blue-500\" />\n          <span className=\"text-sm text-blue-600\">{file.progress || 0}%</span>\n        </div>\n      );\n    } else if (file.uploadResult) {\n      if (file.uploadResult.success) {\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />;\n      } else {\n        return <AlertCircle className=\"h-4 w-4 text-red-500\" />;\n      }\n    }\n    return null;\n  };\n\n  return (\n    <div className={cn('w-full', className)}>\n      {/* Drop Zone */}\n      <div\n        onDrop={handleDrop}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onClick={handleClick}\n        className={cn(\n          'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',\n          isDragOver\n            ? 'border-orange-500 bg-orange-50'\n            : 'border-gray-300 hover:border-gray-400',\n          disabled && 'opacity-50 cursor-not-allowed'\n        )}\n      >\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          multiple={multiple}\n          accept={accept}\n          onChange={(e) => handleFileSelect(e.target.files)}\n          className=\"hidden\"\n          disabled={disabled}\n        />\n\n        {children || (\n          <div className=\"space-y-2\">\n            <Upload className=\"h-12 w-12 text-gray-400 mx-auto\" />\n            <div>\n              <p className=\"text-sm font-medium text-gray-900\">\n                Click to upload or drag and drop\n              </p>\n              <p className=\"text-xs text-gray-500\">\n                {accept ? `Accepted formats: ${accept}` : 'All file types accepted'}\n              </p>\n              {maxSize && (\n                <p className=\"text-xs text-gray-500\">\n                  Max size: {fileUploadService.formatFileSize(maxSize)}\n                </p>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* File List */}\n      {files.length > 0 && (\n        <div className=\"mt-4 space-y-2\">\n          <div className=\"flex items-center justify-between\">\n            <h4 className=\"text-sm font-medium text-gray-900\">\n              Selected Files ({files.length})\n            </h4>\n            {files.length > 0 && !isUploading && (\n              <button\n                onClick={uploadFiles}\n                className=\"px-3 py-1 bg-orange-500 text-white text-sm rounded-md hover:bg-orange-600 transition-colors\"\n              >\n                Upload All\n              </button>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            {files.map((file, index) => (\n              <div\n                key={`${file.name}-${index}`}\n                className=\"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg\"\n              >\n                {/* File Icon/Preview */}\n                <div className=\"flex-shrink-0\">\n                  {file.preview ? (\n                    <img\n                      src={file.preview}\n                      alt={file.name}\n                      className=\"h-12 w-12 object-cover rounded\"\n                    />\n                  ) : (\n                    getFileIcon(file)\n                  )}\n                </div>\n\n                {/* File Info */}\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900 truncate\">\n                    {file.name}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    {fileUploadService.formatFileSize(file.size)}\n                  </p>\n                  {file.uploadResult && !file.uploadResult.success && (\n                    <p className=\"text-xs text-red-500 mt-1\">\n                      {file.uploadResult.error}\n                    </p>\n                  )}\n                </div>\n\n                {/* Status */}\n                <div className=\"flex items-center space-x-2\">\n                  {getUploadStatus(file)}\n                  {!file.uploading && (\n                    <button\n                      onClick={() => removeFile(index)}\n                      className=\"p-1 text-gray-400 hover:text-red-500 transition-colors\"\n                    >\n                      <X className=\"h-4 w-4\" />\n                    </button>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FileUpload;\n"], "names": ["FileUpload", "onUpload", "onProgress", "multiple", "accept", "maxSize", "maxFiles", "disabled", "className", "children", "endpoint", "folder", "files", "setFiles", "useState", "isDragOver", "setIsDragOver", "isUploading", "setIsUploading", "fileInputRef", "useRef", "handleFileSelect", "useCallback", "selectedFiles", "newFiles", "Array", "from", "map", "file", "fileWithPreview", "fileUploadService", "isImage", "preview", "createPreviewUrl", "prev", "slice", "for<PERSON>ach", "revokePreviewUrl", "handleDrop", "e", "preventDefault", "dataTransfer", "handleDragOver", "handleDragLeave", "handleClick", "current", "click", "removeFile", "index", "removedFile", "splice", "uploadFiles", "length", "results", "i", "f", "idx", "uploading", "progress", "result", "uploadFile", "percentage", "overallProgress", "loaded", "total", "Math", "round", "uploadResult", "push", "error", "console", "getFileIcon", "Image", "isPDF", "FileText", "File", "getUploadStatus", "div", "Loader2", "span", "success", "CheckCircle", "AlertCircle", "cn", "onDrop", "onDragOver", "onDragLeave", "onClick", "input", "ref", "type", "onChange", "target", "Upload", "p", "formatFileSize", "h4", "button", "img", "src", "alt", "name", "size", "X"], "mappings": "AAAA;;;;;+BAiUA;;;eAAA;;;;+DA/TqD;6BAU9C;uBACY;mEAC6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBhE,MAAMA,aAAwC,CAAC,EAC7CC,QAAQ,EACRC,UAAU,EACVC,WAAW,KAAK,EAChBC,MAAM,EACNC,OAAO,EACPC,WAAW,EAAE,EACbC,WAAW,KAAK,EAChBC,SAAS,EACTC,QAAQ,EACRC,QAAQ,EACRC,MAAM,EACP;IACC,MAAM,CAACC,OAAOC,SAAS,GAAGC,IAAAA,eAAQ,EAAoB,EAAE;IACxD,MAAM,CAACC,YAAYC,cAAc,GAAGF,IAAAA,eAAQ,EAAC;IAC7C,MAAM,CAACG,aAAaC,eAAe,GAAGJ,IAAAA,eAAQ,EAAC;IAC/C,MAAMK,eAAeC,IAAAA,aAAM,EAAmB;IAE9C,MAAMC,mBAAmBC,IAAAA,kBAAW,EAAC,CAACC;QACpC,IAAI,CAACA,eAAe;QAEpB,MAAMC,WAA8BC,MAAMC,IAAI,CAACH,eAAeI,GAAG,CAACC,CAAAA;YAChE,MAAMC,kBAAkBD;YACxB,IAAIE,mBAAiB,CAACC,OAAO,CAACH,OAAO;gBACnCC,gBAAgBG,OAAO,GAAGF,mBAAiB,CAACG,gBAAgB,CAACL;YAC/D;YACA,OAAOC;QACT;QAEA,IAAI1B,UAAU;YACZU,SAASqB,CAAAA,OAAQ;uBAAIA;uBAASV;iBAAS,CAACW,KAAK,CAAC,GAAG7B;QACnD,OAAO;YACL,iCAAiC;YACjCM,MAAMwB,OAAO,CAACR,CAAAA;gBACZ,IAAIA,KAAKI,OAAO,EAAE;oBAChBF,mBAAiB,CAACO,gBAAgB,CAACT,KAAKI,OAAO;gBACjD;YACF;YACAnB,SAASW,SAASW,KAAK,CAAC,GAAG;QAC7B;IACF,GAAG;QAACvB;QAAOT;QAAUG;KAAS;IAE9B,MAAMgC,aAAahB,IAAAA,kBAAW,EAAC,CAACiB;QAC9BA,EAAEC,cAAc;QAChBxB,cAAc;QAEd,IAAIT,UAAU;QAEdc,iBAAiBkB,EAAEE,YAAY,CAAC7B,KAAK;IACvC,GAAG;QAACL;QAAUc;KAAiB;IAE/B,MAAMqB,iBAAiBpB,IAAAA,kBAAW,EAAC,CAACiB;QAClCA,EAAEC,cAAc;QAChB,IAAI,CAACjC,UAAU;YACbS,cAAc;QAChB;IACF,GAAG;QAACT;KAAS;IAEb,MAAMoC,kBAAkBrB,IAAAA,kBAAW,EAAC,CAACiB;QACnCA,EAAEC,cAAc;QAChBxB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM4B,cAActB,IAAAA,kBAAW,EAAC;QAC9B,IAAI,CAACf,YAAYY,aAAa0B,OAAO,EAAE;YACrC1B,aAAa0B,OAAO,CAACC,KAAK;QAC5B;IACF,GAAG;QAACvC;KAAS;IAEb,MAAMwC,aAAazB,IAAAA,kBAAW,EAAC,CAAC0B;QAC9BnC,SAASqB,CAAAA;YACP,MAAMV,WAAW;mBAAIU;aAAK;YAC1B,MAAMe,cAAczB,QAAQ,CAACwB,MAAM;YAEnC,uBAAuB;YACvB,IAAIC,YAAYjB,OAAO,EAAE;gBACvBF,mBAAiB,CAACO,gBAAgB,CAACY,YAAYjB,OAAO;YACxD;YAEAR,SAAS0B,MAAM,CAACF,OAAO;YACvB,OAAOxB;QACT;IACF,GAAG,EAAE;IAEL,MAAM2B,cAAc7B,IAAAA,kBAAW,EAAC;QAC9B,IAAIV,MAAMwC,MAAM,KAAK,KAAKnC,aAAa;QAEvCC,eAAe;QACf,MAAMmC,UAA0B,EAAE;QAElC,IAAI;YACF,IAAK,IAAIC,IAAI,GAAGA,IAAI1C,MAAMwC,MAAM,EAAEE,IAAK;gBACrC,MAAM1B,OAAOhB,KAAK,CAAC0C,EAAE;gBAErB,sCAAsC;gBACtCzC,SAASqB,CAAAA,OAAQA,KAAKP,GAAG,CAAC,CAAC4B,GAAGC,MAC5BA,QAAQF,IAAI;4BAAE,GAAGC,CAAC;4BAAEE,WAAW;4BAAMC,UAAU;wBAAE,IAAIH;gBAGvD,MAAMI,SAAS,MAAM7B,mBAAiB,CAAC8B,UAAU,CAAChC,MAAMlB,UAAU;oBAChEC;oBACAN;oBACAH,YAAY,CAACwD;wBACX,kCAAkC;wBAClC7C,SAASqB,CAAAA,OAAQA,KAAKP,GAAG,CAAC,CAAC4B,GAAGC,MAC5BA,QAAQF,IAAI;oCAAE,GAAGC,CAAC;oCAAEG,UAAUA,SAASG,UAAU;gCAAC,IAAIN;wBAGxD,6BAA6B;wBAC7B,IAAIrD,YAAY;4BACd,MAAM4D,kBAAkB;gCACtBC,QAAQ,AAACT,IAAI,MAAOI,SAASG,UAAU;gCACvCG,OAAOpD,MAAMwC,MAAM,GAAG;gCACtBS,YAAYI,KAAKC,KAAK,CAAC,AAAC,CAAA,AAACZ,IAAI,MAAOI,SAASG,UAAU,AAAD,IAAKjD,MAAMwC,MAAM;4BACzE;4BACAlD,WAAW4D;wBACb;oBACF;gBACF;gBAEA,gCAAgC;gBAChCjD,SAASqB,CAAAA,OAAQA,KAAKP,GAAG,CAAC,CAAC4B,GAAGC,MAC5BA,QAAQF,IAAI;4BAAE,GAAGC,CAAC;4BAAEE,WAAW;4BAAOU,cAAcR;wBAAO,IAAIJ;gBAGjEF,QAAQe,IAAI,CAACT;YACf;YAEA,IAAI1D,UAAU;gBACZA,SAASoD;YACX;QACF,EAAE,OAAOgB,OAAO;YACdC,QAAQD,KAAK,CAAC,iBAAiBA;QACjC,SAAU;YACRnD,eAAe;QACjB;IACF,GAAG;QAACN;QAAOK;QAAaP;QAAUC;QAAQN;QAASH;QAAYD;KAAS;IAExE,MAAMsE,cAAc,CAAC3C;QACnB,IAAIE,mBAAiB,CAACC,OAAO,CAACH,OAAO;YACnC,qBAAO,qBAAC4C,kBAAK;gBAAChE,WAAU;;QAC1B,OAAO,IAAIsB,mBAAiB,CAAC2C,KAAK,CAAC7C,OAAO;YACxC,qBAAO,qBAAC8C,qBAAQ;gBAAClE,WAAU;;QAC7B,OAAO;YACL,qBAAO,qBAACmE,iBAAI;gBAACnE,WAAU;;QACzB;IACF;IAEA,MAAMoE,kBAAkB,CAAChD;QACvB,IAAIA,KAAK6B,SAAS,EAAE;YAClB,qBACE,sBAACoB;gBAAIrE,WAAU;;kCACb,qBAACsE,oBAAO;wBAACtE,WAAU;;kCACnB,sBAACuE;wBAAKvE,WAAU;;4BAAyBoB,KAAK8B,QAAQ,IAAI;4BAAE;;;;;QAGlE,OAAO,IAAI9B,KAAKuC,YAAY,EAAE;YAC5B,IAAIvC,KAAKuC,YAAY,CAACa,OAAO,EAAE;gBAC7B,qBAAO,qBAACC,wBAAW;oBAACzE,WAAU;;YAChC,OAAO;gBACL,qBAAO,qBAAC0E,wBAAW;oBAAC1E,WAAU;;YAChC;QACF;QACA,OAAO;IACT;IAEA,qBACE,sBAACqE;QAAIrE,WAAW2E,IAAAA,SAAE,EAAC,UAAU3E;;0BAE3B,sBAACqE;gBACCO,QAAQ9C;gBACR+C,YAAY3C;gBACZ4C,aAAa3C;gBACb4C,SAAS3C;gBACTpC,WAAW2E,IAAAA,SAAE,EACX,sFACApE,aACI,mCACA,yCACJR,YAAY;;kCAGd,qBAACiF;wBACCC,KAAKtE;wBACLuE,MAAK;wBACLvF,UAAUA;wBACVC,QAAQA;wBACRuF,UAAU,CAACpD,IAAMlB,iBAAiBkB,EAAEqD,MAAM,CAAChF,KAAK;wBAChDJ,WAAU;wBACVD,UAAUA;;oBAGXE,0BACC,sBAACoE;wBAAIrE,WAAU;;0CACb,qBAACqF,mBAAM;gCAACrF,WAAU;;0CAClB,sBAACqE;;kDACC,qBAACiB;wCAAEtF,WAAU;kDAAoC;;kDAGjD,qBAACsF;wCAAEtF,WAAU;kDACVJ,SAAS,CAAC,kBAAkB,EAAEA,QAAQ,GAAG;;oCAE3CC,yBACC,sBAACyF;wCAAEtF,WAAU;;4CAAwB;4CACxBsB,mBAAiB,CAACiE,cAAc,CAAC1F;;;;;;;;;YASvDO,MAAMwC,MAAM,GAAG,mBACd,sBAACyB;gBAAIrE,WAAU;;kCACb,sBAACqE;wBAAIrE,WAAU;;0CACb,sBAACwF;gCAAGxF,WAAU;;oCAAoC;oCAC/BI,MAAMwC,MAAM;oCAAC;;;4BAE/BxC,MAAMwC,MAAM,GAAG,KAAK,CAACnC,6BACpB,qBAACgF;gCACCV,SAASpC;gCACT3C,WAAU;0CACX;;;;kCAML,qBAACqE;wBAAIrE,WAAU;kCACZI,MAAMe,GAAG,CAAC,CAACC,MAAMoB,sBAChB,sBAAC6B;gCAECrE,WAAU;;kDAGV,qBAACqE;wCAAIrE,WAAU;kDACZoB,KAAKI,OAAO,iBACX,qBAACkE;4CACCC,KAAKvE,KAAKI,OAAO;4CACjBoE,KAAKxE,KAAKyE,IAAI;4CACd7F,WAAU;6CAGZ+D,YAAY3C;;kDAKhB,sBAACiD;wCAAIrE,WAAU;;0DACb,qBAACsF;gDAAEtF,WAAU;0DACVoB,KAAKyE,IAAI;;0DAEZ,qBAACP;gDAAEtF,WAAU;0DACVsB,mBAAiB,CAACiE,cAAc,CAACnE,KAAK0E,IAAI;;4CAE5C1E,KAAKuC,YAAY,IAAI,CAACvC,KAAKuC,YAAY,CAACa,OAAO,kBAC9C,qBAACc;gDAAEtF,WAAU;0DACVoB,KAAKuC,YAAY,CAACE,KAAK;;;;kDAM9B,sBAACQ;wCAAIrE,WAAU;;4CACZoE,gBAAgBhD;4CAChB,CAACA,KAAK6B,SAAS,kBACd,qBAACwC;gDACCV,SAAS,IAAMxC,WAAWC;gDAC1BxC,WAAU;0DAEV,cAAA,qBAAC+F,cAAC;oDAAC/F,WAAU;;;;;;+BAvCd,GAAGoB,KAAKyE,IAAI,CAAC,CAAC,EAAErD,OAAO;;;;;;AAkD5C;MAEA,WAAehD"}