'use client';

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Home, ArrowLeft, Search, HelpCircle } from 'lucide-react';
import { useAuth } from '@/providers/AuthProvider';
import Button from '@/components/ui/Button';

const NotFoundPage: React.FC = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();

  // Determine the appropriate home route based on user role
  const getHomeRoute = () => {
    if (!isAuthenticated || !user) {
      return '/login';
    }

    switch (user.role) {
      case 'admin':
      case 'hr':
        return '/dashboard';
      case 'supervisor':
        return '/supervisor';
      case 'accountant':
        return '/accountant';
      case 'employee':
        return '/staff';
      default:
        return '/dashboard';
    }
  };

  const handleGoBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push(getHomeRoute());
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-orange-100 flex items-center justify-center px-4">
      <div className="max-w-lg w-full text-center">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="relative">
            <div className="text-9xl font-bold text-orange-200 select-none">
              404
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <Search className="h-16 w-16 text-orange-400 animate-pulse" />
            </div>
          </div>
        </div>

        {/* Error Message */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Page Not Found
          </h1>
          <p className="text-lg text-gray-600 mb-2">
            Oops! The page you're looking for doesn't exist.
          </p>
          <p className="text-gray-500">
            It might have been moved, deleted, or you entered the wrong URL.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              onClick={handleGoBack}
              variant="secondary"
              className="flex items-center justify-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Go Back</span>
            </Button>

            <Link href={getHomeRoute()}>
              <Button
                variant="primary"
                className="w-full sm:w-auto flex items-center justify-center space-x-2"
              >
                <Home className="h-4 w-4" />
                <span>
                  {isAuthenticated ? 'Go to Dashboard' : 'Go to Login'}
                </span>
              </Button>
            </Link>
          </div>

          {/* Additional Help */}
          <div className="pt-6 border-t border-gray-200">
            <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <HelpCircle className="h-4 w-4" />
                <span>Need help?</span>
              </div>
              <Link
                href="/support"
                className="text-orange-600 hover:text-orange-700 font-medium"
              >
                Contact Support
              </Link>
            </div>
          </div>
        </div>

        {/* User Info (if authenticated) */}
        {isAuthenticated && user && (
          <div className="mt-8 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
            <p className="text-sm text-gray-600">
              Logged in as <span className="font-medium text-gray-900">{user.email}</span>
            </p>
            <p className="text-xs text-gray-500 mt-1">
              Role: {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
            </p>
          </div>
        )}

        {/* Quick Navigation (if authenticated) */}
        {isAuthenticated && (
          <div className="mt-6">
            <p className="text-sm text-gray-600 mb-3">Quick Navigation:</p>
            <div className="flex flex-wrap justify-center gap-2">
              {user?.role === 'admin' || user?.role === 'hr' ? (
                <>
                  <Link href="/employees" className="text-xs bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full transition-colors">
                    Employees
                  </Link>
                  <Link href="/payroll" className="text-xs bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full transition-colors">
                    Payroll
                  </Link>
                  <Link href="/reports" className="text-xs bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full transition-colors">
                    Reports
                  </Link>
                </>
              ) : user?.role === 'supervisor' ? (
                <>
                  <Link href="/supervisor/team" className="text-xs bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full transition-colors">
                    Team
                  </Link>
                  <Link href="/supervisor/approvals" className="text-xs bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full transition-colors">
                    Approvals
                  </Link>
                </>
              ) : user?.role === 'accountant' ? (
                <>
                  <Link href="/accountant/payroll" className="text-xs bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full transition-colors">
                    Payroll
                  </Link>
                  <Link href="/accountant/reports" className="text-xs bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full transition-colors">
                    Financial Reports
                  </Link>
                </>
              ) : (
                <>
                  <Link href="/staff/profile" className="text-xs bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full transition-colors">
                    Profile
                  </Link>
                  <Link href="/staff/leave" className="text-xs bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full transition-colors">
                    Leave
                  </Link>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NotFoundPage;
