25075025802c1ead7b3e4526b255e5cd
"use strict";
// Mock Next.js router
jest.mock('next/navigation', ()=>({
        useRouter: ()=>({
                push: jest.fn(),
                replace: jest.fn(),
                back: jest.fn(),
                forward: jest.fn(),
                refresh: jest.fn(),
                prefetch: jest.fn()
            }),
        usePathname: ()=>'/staff',
        useSearchParams: ()=>new URLSearchParams()
    }));
// Mock Next.js image
jest.mock('next/image', ()=>({
        __esModule: true,
        default: (props)=>{
            const { src, alt, ...rest } = props;
            // eslint-disable-next-line @next/next/no-img-element
            return _react.default.createElement('img', {
                src,
                alt,
                ...rest
            });
        }
    }));
Object.defineProperty(exports, "__esModule", {
    value: true
});
require("@testing-library/jest-dom");
const _react = /*#__PURE__*/ _interop_require_default(require("react"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(()=>({
        observe: jest.fn(),
        unobserve: jest.fn(),
        disconnect: jest.fn()
    }));
// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(()=>({
        observe: jest.fn(),
        unobserve: jest.fn(),
        disconnect: jest.fn()
    }));
// Mock WebSocket
global.WebSocket = jest.fn().mockImplementation(()=>({
        close: jest.fn(),
        send: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        readyState: 1,
        CONNECTING: 0,
        OPEN: 1,
        CLOSING: 2,
        CLOSED: 3
    }));
// Mock Notification API
global.Notification = jest.fn().mockImplementation(()=>({
        close: jest.fn(),
        onclick: jest.fn()
    }));
Object.defineProperty(global.Notification, 'permission', {
    value: 'granted',
    writable: true
});
Object.defineProperty(global.Notification, 'requestPermission', {
    value: jest.fn().mockResolvedValue('granted'),
    writable: true
});
// Mock localStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};
global.localStorage = localStorageMock;
// Mock sessionStorage
const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};
global.sessionStorage = sessionStorageMock;
// Mock URL.createObjectURL and URL.revokeObjectURL
global.URL.createObjectURL = jest.fn(()=>'mocked-url');
global.URL.revokeObjectURL = jest.fn();
// Mock fetch
global.fetch = jest.fn();
// Mock console methods to reduce noise in tests
const originalError = console.error;
const originalWarn = console.warn;
beforeAll(()=>{
    console.error = (...args)=>{
        if (typeof args[0] === 'string' && args[0].includes('Warning: ReactDOM.render is no longer supported')) {
            return;
        }
        originalError.call(console, ...args);
    };
    console.warn = (...args)=>{
        if (typeof args[0] === 'string' && (args[0].includes('componentWillReceiveProps') || args[0].includes('componentWillUpdate'))) {
            return;
        }
        originalWarn.call(console, ...args);
    };
});
afterAll(()=>{
    console.error = originalError;
    console.warn = originalWarn;
});
// Reset mocks after each test
afterEach(()=>{
    jest.clearAllMocks();
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
    localStorageMock.clear.mockClear();
    sessionStorageMock.getItem.mockClear();
    sessionStorageMock.setItem.mockClear();
    sessionStorageMock.removeItem.mockClear();
    sessionStorageMock.clear.mockClear();
});

//# sourceMappingURL=data:application/json;base64,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