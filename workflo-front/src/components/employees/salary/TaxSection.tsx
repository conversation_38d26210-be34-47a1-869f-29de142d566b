'use client';

import React, { useState, useEffect } from 'react';
import { 
  Receipt, 
  Plus, 
  Search,
  Edit,
  Trash2,
  Eye,
  MoreHorizontal,
  Calculator,
  FileText,
  Building,
  MapPin,
  Percent,
  DollarSign,
  Calendar,
  TrendingUp,
  TrendingDown,
  X,
  Save
} from 'lucide-react';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { cn, formatDate, formatCurrency } from '@/lib/utils';

interface TaxRecord {
  id: number;
  employee_id: number;
  tax_year: number;
  tax_type: 'federal_income' | 'state_income' | 'social_security' | 'medicare' | 
            'unemployment' | 'disability' | 'local' | 'other';
  tax_name: string;
  tax_rate: number;
  taxable_income: number;
  tax_amount: number;
  tax_withheld: number;
  tax_owed: number;
  filing_status: 'single' | 'married_joint' | 'married_separate' | 'head_of_household';
  exemptions: number;
  deductions: number;
  credits: number;
  jurisdiction: string;
  status: 'calculated' | 'filed' | 'paid' | 'pending' | 'amended';
  due_date?: string;
  filed_date?: string;
  payment_date?: string;
  created_date: string;
  updated_date: string;
}

interface TaxBracket {
  id: number;
  tax_type: string;
  jurisdiction: string;
  min_income: number;
  max_income: number;
  tax_rate: number;
  base_tax: number;
  year: number;
}

interface TaxSectionProps {
  employeeId: number;
  employeeName: string;
  isCurrentUser?: boolean;
}

const TaxSection: React.FC<TaxSectionProps> = ({ 
  employeeId, 
  employeeName, 
  isCurrentUser = false 
}) => {
  const [taxRecords, setTaxRecords] = useState<TaxRecord[]>([]);
  const [taxBrackets, setTaxBrackets] = useState<TaxBracket[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterYear, setFilterYear] = useState(new Date().getFullYear().toString());
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedTaxRecord, setSelectedTaxRecord] = useState<TaxRecord | null>(null);

  // Mock tax records data
  const mockTaxRecords: TaxRecord[] = [
    {
      id: 1,
      employee_id: employeeId,
      tax_year: 2024,
      tax_type: 'federal_income',
      tax_name: 'Federal Income Tax',
      tax_rate: 22,
      taxable_income: 95000,
      tax_amount: 15180,
      tax_withheld: 16000,
      tax_owed: -820,
      filing_status: 'single',
      exemptions: 1,
      deductions: 13850,
      credits: 0,
      jurisdiction: 'United States',
      status: 'calculated',
      due_date: '2025-04-15',
      created_date: '2024-01-01T00:00:00Z',
      updated_date: '2024-01-15T00:00:00Z'
    },
    {
      id: 2,
      employee_id: employeeId,
      tax_year: 2024,
      tax_type: 'state_income',
      tax_name: 'State Income Tax',
      tax_rate: 6.5,
      taxable_income: 95000,
      tax_amount: 6175,
      tax_withheld: 6500,
      tax_owed: -325,
      filing_status: 'single',
      exemptions: 1,
      deductions: 4000,
      credits: 0,
      jurisdiction: 'California',
      status: 'calculated',
      due_date: '2025-04-15',
      created_date: '2024-01-01T00:00:00Z',
      updated_date: '2024-01-15T00:00:00Z'
    },
    {
      id: 3,
      employee_id: employeeId,
      tax_year: 2024,
      tax_type: 'social_security',
      tax_name: 'Social Security Tax',
      tax_rate: 6.2,
      taxable_income: 95000,
      tax_amount: 5890,
      tax_withheld: 5890,
      tax_owed: 0,
      filing_status: 'single',
      exemptions: 0,
      deductions: 0,
      credits: 0,
      jurisdiction: 'United States',
      status: 'paid',
      created_date: '2024-01-01T00:00:00Z',
      updated_date: '2024-12-31T00:00:00Z'
    },
    {
      id: 4,
      employee_id: employeeId,
      tax_year: 2024,
      tax_type: 'medicare',
      tax_name: 'Medicare Tax',
      tax_rate: 1.45,
      taxable_income: 95000,
      tax_amount: 1378,
      tax_withheld: 1378,
      tax_owed: 0,
      filing_status: 'single',
      exemptions: 0,
      deductions: 0,
      credits: 0,
      jurisdiction: 'United States',
      status: 'paid',
      created_date: '2024-01-01T00:00:00Z',
      updated_date: '2024-12-31T00:00:00Z'
    }
  ];

  // Mock tax brackets
  const mockTaxBrackets: TaxBracket[] = [
    { id: 1, tax_type: 'federal_income', jurisdiction: 'United States', min_income: 0, max_income: 11000, tax_rate: 10, base_tax: 0, year: 2024 },
    { id: 2, tax_type: 'federal_income', jurisdiction: 'United States', min_income: 11001, max_income: 44725, tax_rate: 12, base_tax: 1100, year: 2024 },
    { id: 3, tax_type: 'federal_income', jurisdiction: 'United States', min_income: 44726, max_income: 95375, tax_rate: 22, base_tax: 5147, year: 2024 },
    { id: 4, tax_type: 'federal_income', jurisdiction: 'United States', min_income: 95376, max_income: 182050, tax_rate: 24, base_tax: 16290, year: 2024 }
  ];

  useEffect(() => {
    const loadTaxData = async () => {
      setLoading(true);
      setTimeout(() => {
        setTaxRecords(mockTaxRecords);
        setTaxBrackets(mockTaxBrackets);
        setLoading(false);
      }, 1000);
    };

    loadTaxData();
  }, [employeeId]);

  const filteredTaxRecords = taxRecords.filter(record => {
    const matchesSearch = record.tax_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.jurisdiction.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = !filterType || record.tax_type === filterType;
    const matchesYear = record.tax_year.toString() === filterYear;
    return matchesSearch && matchesType && matchesYear;
  });

  const getTaxIcon = (type: string) => {
    switch (type) {
      case 'federal_income': return <Building className="h-5 w-5 text-blue-600" />;
      case 'state_income': return <MapPin className="h-5 w-5 text-green-600" />;
      case 'social_security': return <Receipt className="h-5 w-5 text-purple-600" />;
      case 'medicare': return <Receipt className="h-5 w-5 text-red-600" />;
      case 'unemployment': return <FileText className="h-5 w-5 text-yellow-600" />;
      case 'disability': return <FileText className="h-5 w-5 text-orange-600" />;
      case 'local': return <MapPin className="h-5 w-5 text-indigo-600" />;
      default: return <Calculator className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'calculated': return 'bg-blue-100 text-blue-800';
      case 'filed': return 'bg-green-100 text-green-800';
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'amended': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const calculateTotalTaxLiability = () => {
    return filteredTaxRecords.reduce((total, record) => total + record.tax_amount, 0);
  };

  const calculateTotalWithheld = () => {
    return filteredTaxRecords.reduce((total, record) => total + record.tax_withheld, 0);
  };

  const calculateTotalOwed = () => {
    return filteredTaxRecords.reduce((total, record) => total + record.tax_owed, 0);
  };

  const getEffectiveTaxRate = () => {
    const totalIncome = filteredTaxRecords.reduce((total, record) => total + record.taxable_income, 0);
    const totalTax = calculateTotalTaxLiability();
    return totalIncome > 0 ? (totalTax / totalIncome) * 100 : 0;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Tax Information</h3>
          <p className="text-sm text-gray-600 mt-1">
            Tax records and calculations for {employeeName}
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <div className="text-right">
            <div className="text-sm text-gray-600">Effective Tax Rate</div>
            <div className="text-lg font-bold text-blue-600">
              {getEffectiveTaxRate().toFixed(2)}%
            </div>
          </div>
          {!isCurrentUser && (
            <Button variant="primary" onClick={() => setShowCreateModal(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Tax Record
            </Button>
          )}
        </div>
      </div>

      {/* Tax Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calculator className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Tax Liability</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(calculateTotalTaxLiability())}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingDown className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Withheld</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(calculateTotalWithheld())}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                {calculateTotalOwed() >= 0 ? (
                  <TrendingUp className="h-8 w-8 text-red-600" />
                ) : (
                  <TrendingDown className="h-8 w-8 text-green-600" />
                )}
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">
                  {calculateTotalOwed() >= 0 ? 'Amount Owed' : 'Refund Due'}
                </p>
                <p className={cn(
                  "text-2xl font-semibold",
                  calculateTotalOwed() >= 0 ? "text-red-600" : "text-green-600"
                )}>
                  {formatCurrency(Math.abs(calculateTotalOwed()))}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Percent className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Effective Rate</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {getEffectiveTaxRate().toFixed(2)}%
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
        <div className="flex-1">
          <Input
            placeholder="Search tax records..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="h-4 w-4" />}
          />
        </div>
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="">All Types</option>
          <option value="federal_income">Federal Income</option>
          <option value="state_income">State Income</option>
          <option value="social_security">Social Security</option>
          <option value="medicare">Medicare</option>
          <option value="unemployment">Unemployment</option>
          <option value="local">Local</option>
        </select>
        <select
          value={filterYear}
          onChange={(e) => setFilterYear(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
        >
          <option value="2024">2024</option>
          <option value="2023">2023</option>
          <option value="2022">2022</option>
        </select>
      </div>

      {/* Tax Records List */}
      {loading ? (
        <div className="space-y-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      ) : filteredTaxRecords.length === 0 ? (
        <div className="text-center py-12">
          <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No tax records found
          </h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || filterType
              ? 'Try adjusting your search criteria.'
              : 'No tax records have been created yet.'
            }
          </p>
          {!isCurrentUser && (
            <Button variant="primary" onClick={() => setShowCreateModal(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Tax Record
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredTaxRecords.map((record) => (
            <TaxRecordCard
              key={record.id}
              record={record}
              isCurrentUser={isCurrentUser}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Tax Record Card Component
interface TaxRecordCardProps {
  record: TaxRecord;
  isCurrentUser: boolean;
}

const TaxRecordCard: React.FC<TaxRecordCardProps> = ({ record, isCurrentUser }) => {
  const [showActions, setShowActions] = useState(false);

  const getTaxIcon = (type: string) => {
    switch (type) {
      case 'federal_income': return <Building className="h-5 w-5 text-blue-600" />;
      case 'state_income': return <MapPin className="h-5 w-5 text-green-600" />;
      case 'social_security': return <Receipt className="h-5 w-5 text-purple-600" />;
      case 'medicare': return <Receipt className="h-5 w-5 text-red-600" />;
      case 'unemployment': return <FileText className="h-5 w-5 text-yellow-600" />;
      case 'disability': return <FileText className="h-5 w-5 text-orange-600" />;
      case 'local': return <MapPin className="h-5 w-5 text-indigo-600" />;
      default: return <Calculator className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'calculated': return 'bg-blue-100 text-blue-800';
      case 'filed': return 'bg-green-100 text-green-800';
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'amended': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="relative hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        {/* Actions Menu */}
        {!isCurrentUser && (
          <div className="absolute top-4 right-4">
            <div className="relative">
              <button
                onClick={() => setShowActions(!showActions)}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <MoreHorizontal className="h-4 w-4 text-gray-500" />
              </button>

              {showActions && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                  <div className="py-1">
                    <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </button>
                    <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Record
                    </button>
                    <button className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Record
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Tax Record Info */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div>
            <div className="flex items-center mb-3">
              {getTaxIcon(record.tax_type)}
              <h4 className="text-lg font-medium text-gray-900 ml-3">
                {record.tax_name}
              </h4>
            </div>
            <div className="flex items-center space-x-2 mb-2">
              <span className={cn(
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                getStatusColor(record.status)
              )}>
                {record.status.replace('_', ' ').toUpperCase()}
              </span>
            </div>
            <p className="text-sm text-gray-600">{record.jurisdiction}</p>
          </div>

          <div>
            <h5 className="text-sm font-medium text-gray-900 mb-2">Tax Calculation</h5>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Rate:</span>
                <span className="font-medium">{record.tax_rate}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Taxable Income:</span>
                <span className="font-medium">{formatCurrency(record.taxable_income)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Tax Amount:</span>
                <span className="font-medium text-red-600">{formatCurrency(record.tax_amount)}</span>
              </div>
            </div>
          </div>

          <div>
            <h5 className="text-sm font-medium text-gray-900 mb-2">Withholding & Payment</h5>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Withheld:</span>
                <span className="font-medium text-green-600">{formatCurrency(record.tax_withheld)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Owed/Refund:</span>
                <span className={cn(
                  "font-medium",
                  record.tax_owed >= 0 ? "text-red-600" : "text-green-600"
                )}>
                  {record.tax_owed >= 0 ? '+' : ''}{formatCurrency(record.tax_owed)}
                </span>
              </div>
            </div>
          </div>

          <div>
            <h5 className="text-sm font-medium text-gray-900 mb-2">Filing Information</h5>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Filing Status:</span>
                <span className="font-medium capitalize">{record.filing_status.replace('_', ' ')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Exemptions:</span>
                <span className="font-medium">{record.exemptions}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Deductions:</span>
                <span className="font-medium">{formatCurrency(record.deductions)}</span>
              </div>
              {record.due_date && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Due Date:</span>
                  <span className="font-medium">{formatDate(record.due_date)}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default TaxSection;
