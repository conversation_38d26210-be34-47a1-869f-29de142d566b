{"version": 3, "sources": ["/home/<USER>/Documents/augment-projects/naya/workflo-front/src/__tests__/utils/test-utils.tsx"], "sourcesContent": ["import React, { ReactElement } from 'react';\nimport { render, RenderOptions } from '@testing-library/react';\nimport { AuthProvider } from '@/providers/AuthProvider';\nimport { NotificationProvider } from '@/providers/NotificationProvider';\nimport { User } from '@/types';\n\n// Mock user data for testing\nexport const mockUser: User = {\n  id: 1,\n  employee_id: 'EMP001',\n  first_name: '<PERSON>',\n  last_name: '<PERSON><PERSON>',\n  email: '<EMAIL>',\n  phone_number: '+254700123456',\n  role: 'employee',\n  department: 'Engineering',\n  position: 'Software Engineer',\n  hire_date: '2022-01-15',\n  is_active: true,\n  profile_picture: null,\n  created_at: '2022-01-15T00:00:00Z',\n  updated_at: '2024-01-15T00:00:00Z'\n};\n\nexport const mockAdminUser: User = {\n  ...mockUser,\n  id: 2,\n  employee_id: 'EMP002',\n  first_name: '<PERSON>',\n  last_name: '<PERSON>',\n  email: '<EMAIL>',\n  role: 'hr',\n  position: 'HR Manager'\n};\n\n// Mock auth context values\nexport const mockAuthContextValue = {\n  user: mockUser,\n  isAuthenticated: true,\n  isLoading: false,\n  login: jest.fn(),\n  logout: jest.fn(),\n  getCurrentUser: jest.fn(),\n  updateProfile: jest.fn(),\n  changePassword: jest.fn(),\n};\n\nexport const mockUnauthenticatedAuthContextValue = {\n  user: null,\n  isAuthenticated: false,\n  isLoading: false,\n  login: jest.fn(),\n  logout: jest.fn(),\n  getCurrentUser: jest.fn(),\n  updateProfile: jest.fn(),\n  changePassword: jest.fn(),\n};\n\nexport const mockLoadingAuthContextValue = {\n  user: null,\n  isAuthenticated: false,\n  isLoading: true,\n  login: jest.fn(),\n  logout: jest.fn(),\n  getCurrentUser: jest.fn(),\n  updateProfile: jest.fn(),\n  changePassword: jest.fn(),\n};\n\n// Mock notification context values\nexport const mockNotificationContextValue = {\n  notifications: [],\n  unreadCount: 0,\n  isConnected: true,\n  connectionState: 'connected',\n  markAsRead: jest.fn(),\n  markAllAsRead: jest.fn(),\n  clearNotifications: jest.fn(),\n  requestPermission: jest.fn().mockResolvedValue('granted' as NotificationPermission),\n};\n\n// Custom render function with providers\ninterface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {\n  authContextValue?: typeof mockAuthContextValue;\n  notificationContextValue?: typeof mockNotificationContextValue;\n  withAuth?: boolean;\n  withNotifications?: boolean;\n}\n\nconst AllTheProviders: React.FC<{\n  children: React.ReactNode;\n  authContextValue?: typeof mockAuthContextValue;\n  notificationContextValue?: typeof mockNotificationContextValue;\n  withAuth?: boolean;\n  withNotifications?: boolean;\n}> = ({ \n  children, \n  authContextValue = mockAuthContextValue,\n  notificationContextValue = mockNotificationContextValue,\n  withAuth = true,\n  withNotifications = true\n}) => {\n  let component = <>{children}</>;\n\n  if (withNotifications) {\n    // Mock the notification provider\n    const MockNotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n      const NotificationContext = React.createContext(notificationContextValue);\n      return (\n        <NotificationContext.Provider value={notificationContextValue}>\n          {children}\n        </NotificationContext.Provider>\n      );\n    };\n    component = <MockNotificationProvider>{component}</MockNotificationProvider>;\n  }\n\n  if (withAuth) {\n    // Mock the auth provider\n    const MockAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n      const AuthContext = React.createContext(authContextValue);\n      return (\n        <AuthContext.Provider value={authContextValue}>\n          {children}\n        </AuthContext.Provider>\n      );\n    };\n    component = <MockAuthProvider>{component}</MockAuthProvider>;\n  }\n\n  return component;\n};\n\nconst customRender = (\n  ui: ReactElement,\n  options: CustomRenderOptions = {}\n) => {\n  const {\n    authContextValue,\n    notificationContextValue,\n    withAuth,\n    withNotifications,\n    ...renderOptions\n  } = options;\n\n  return render(ui, {\n    wrapper: ({ children }) => (\n      <AllTheProviders\n        authContextValue={authContextValue}\n        notificationContextValue={notificationContextValue}\n        withAuth={withAuth}\n        withNotifications={withNotifications}\n      >\n        {children}\n      </AllTheProviders>\n    ),\n    ...renderOptions,\n  });\n};\n\n// Helper function to create mock API responses\nexport const createMockApiResponse = <T,>(data: T, status = 200) => ({\n  data,\n  status,\n  statusText: 'OK',\n  headers: {},\n  config: {},\n});\n\n// Helper function to create mock API error\nexport const createMockApiError = (message: string, status = 500) => ({\n  response: {\n    data: { message },\n    status,\n    statusText: 'Internal Server Error',\n  },\n  message,\n});\n\n// Helper function to wait for async operations\nexport const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\n\n// Helper function to create mock file\nexport const createMockFile = (\n  name: string = 'test.pdf',\n  size: number = 1024,\n  type: string = 'application/pdf'\n): File => {\n  const file = new File(['test content'], name, { type });\n  Object.defineProperty(file, 'size', { value: size });\n  return file;\n};\n\n// Helper function to create mock form data\nexport const createMockFormData = (data: Record<string, any>): FormData => {\n  const formData = new FormData();\n  Object.entries(data).forEach(([key, value]) => {\n    formData.append(key, value);\n  });\n  return formData;\n};\n\n// Helper function to mock localStorage\nexport const mockLocalStorage = (data: Record<string, string> = {}) => {\n  const localStorageMock = {\n    getItem: jest.fn((key: string) => data[key] || null),\n    setItem: jest.fn((key: string, value: string) => {\n      data[key] = value;\n    }),\n    removeItem: jest.fn((key: string) => {\n      delete data[key];\n    }),\n    clear: jest.fn(() => {\n      Object.keys(data).forEach(key => delete data[key]);\n    }),\n  };\n\n  Object.defineProperty(window, 'localStorage', {\n    value: localStorageMock,\n    writable: true,\n  });\n\n  return localStorageMock;\n};\n\n// Helper function to mock sessionStorage\nexport const mockSessionStorage = (data: Record<string, string> = {}) => {\n  const sessionStorageMock = {\n    getItem: jest.fn((key: string) => data[key] || null),\n    setItem: jest.fn((key: string, value: string) => {\n      data[key] = value;\n    }),\n    removeItem: jest.fn((key: string) => {\n      delete data[key];\n    }),\n    clear: jest.fn(() => {\n      Object.keys(data).forEach(key => delete data[key]);\n    }),\n  };\n\n  Object.defineProperty(window, 'sessionStorage', {\n    value: sessionStorageMock,\n    writable: true,\n  });\n\n  return sessionStorageMock;\n};\n\n// Re-export everything from React Testing Library\nexport * from '@testing-library/react';\nexport { customRender as render };\n"], "names": ["createMockApiError", "createMockApiResponse", "createMockFile", "createMockFormData", "mockAdminUser", "mockAuthContextValue", "mockLoadingAuthContextValue", "mockLocalStorage", "mockNotificationContextValue", "mockSessionStorage", "mockUnauthenticatedAuthContextValue", "mockUser", "render", "customRender", "waitFor", "id", "employee_id", "first_name", "last_name", "email", "phone_number", "role", "department", "position", "hire_date", "is_active", "profile_picture", "created_at", "updated_at", "user", "isAuthenticated", "isLoading", "login", "jest", "fn", "logout", "getCurrentUser", "updateProfile", "changePassword", "notifications", "unreadCount", "isConnected", "connectionState", "mark<PERSON><PERSON><PERSON>", "markAllAsRead", "clearNotifications", "requestPermission", "mockResolvedValue", "AllTheProviders", "children", "authContextValue", "notificationContextValue", "<PERSON><PERSON><PERSON>", "withNotifications", "component", "MockNotificationProvider", "NotificationContext", "React", "createContext", "Provider", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AuthContext", "ui", "options", "renderOptions", "wrapper", "data", "status", "statusText", "headers", "config", "message", "response", "ms", "Promise", "resolve", "setTimeout", "name", "size", "type", "file", "File", "Object", "defineProperty", "formData", "FormData", "entries", "for<PERSON>ach", "key", "append", "localStorageMock", "getItem", "setItem", "removeItem", "clear", "keys", "window", "writable", "sessionStorageMock"], "mappings": ";;;;;;;;;;;IA0KaA,kBAAkB;eAAlBA;;IATAC,qBAAqB;eAArBA;;IAsBAC,cAAc;eAAdA;;IAWAC,kBAAkB;eAAlBA;;IA1KAC,aAAa;eAAbA;;IAYAC,oBAAoB;eAApBA;;IAsBAC,2BAA2B;eAA3BA;;IAiJAC,gBAAgB;eAAhBA;;IArIAC,4BAA4B;eAA5BA;;IA4JAC,kBAAkB;eAAlBA;;IAnLAC,mCAAmC;eAAnCA;;IAxCAC,QAAQ;eAARA;;IAmPYC,MAAM;eAAtBC;;IAtEIC,OAAO;eAAPA;;;;8DApLuB;qCACE;;;;;;;;;;;;;;;;;;;AAM/B,MAAMH,WAAiB;IAC5BI,IAAI;IACJC,aAAa;IACbC,YAAY;IACZC,WAAW;IACXC,OAAO;IACPC,cAAc;IACdC,MAAM;IACNC,YAAY;IACZC,UAAU;IACVC,WAAW;IACXC,WAAW;IACXC,iBAAiB;IACjBC,YAAY;IACZC,YAAY;AACd;AAEO,MAAMxB,gBAAsB;IACjC,GAAGO,QAAQ;IACXI,IAAI;IACJC,aAAa;IACbC,YAAY;IACZC,WAAW;IACXC,OAAO;IACPE,MAAM;IACNE,UAAU;AACZ;AAGO,MAAMlB,uBAAuB;IAClCwB,MAAMlB;IACNmB,iBAAiB;IACjBC,WAAW;IACXC,OAAOC,KAAKC,EAAE;IACdC,QAAQF,KAAKC,EAAE;IACfE,gBAAgBH,KAAKC,EAAE;IACvBG,eAAeJ,KAAKC,EAAE;IACtBI,gBAAgBL,KAAKC,EAAE;AACzB;AAEO,MAAMxB,sCAAsC;IACjDmB,MAAM;IACNC,iBAAiB;IACjBC,WAAW;IACXC,OAAOC,KAAKC,EAAE;IACdC,QAAQF,KAAKC,EAAE;IACfE,gBAAgBH,KAAKC,EAAE;IACvBG,eAAeJ,KAAKC,EAAE;IACtBI,gBAAgBL,KAAKC,EAAE;AACzB;AAEO,MAAM5B,8BAA8B;IACzCuB,MAAM;IACNC,iBAAiB;IACjBC,WAAW;IACXC,OAAOC,KAAKC,EAAE;IACdC,QAAQF,KAAKC,EAAE;IACfE,gBAAgBH,KAAKC,EAAE;IACvBG,eAAeJ,KAAKC,EAAE;IACtBI,gBAAgBL,KAAKC,EAAE;AACzB;AAGO,MAAM1B,+BAA+B;IAC1C+B,eAAe,EAAE;IACjBC,aAAa;IACbC,aAAa;IACbC,iBAAiB;IACjBC,YAAYV,KAAKC,EAAE;IACnBU,eAAeX,KAAKC,EAAE;IACtBW,oBAAoBZ,KAAKC,EAAE;IAC3BY,mBAAmBb,KAAKC,EAAE,GAAGa,iBAAiB,CAAC;AACjD;AAUA,MAAMC,kBAMD,CAAC,EACJC,QAAQ,EACRC,mBAAmB7C,oBAAoB,EACvC8C,2BAA2B3C,4BAA4B,EACvD4C,WAAW,IAAI,EACfC,oBAAoB,IAAI,EACzB;IACC,IAAIC,0BAAY;kBAAGL;;IAEnB,IAAII,mBAAmB;QACrB,iCAAiC;QACjC,MAAME,2BAAoE,CAAC,EAAEN,QAAQ,EAAE;YACrF,MAAMO,oCAAsBC,cAAK,CAACC,aAAa,CAACP;YAChD,qBACE,qBAACK,oBAAoBG,QAAQ;gBAACC,OAAOT;0BAClCF;;QAGP;QACAK,0BAAY,qBAACC;sBAA0BD;;IACzC;IAEA,IAAIF,UAAU;QACZ,yBAAyB;QACzB,MAAMS,mBAA4D,CAAC,EAAEZ,QAAQ,EAAE;YAC7E,MAAMa,4BAAcL,cAAK,CAACC,aAAa,CAACR;YACxC,qBACE,qBAACY,YAAYH,QAAQ;gBAACC,OAAOV;0BAC1BD;;QAGP;QACAK,0BAAY,qBAACO;sBAAkBP;;IACjC;IAEA,OAAOA;AACT;AAEA,MAAMzC,eAAe,CACnBkD,IACAC,UAA+B,CAAC,CAAC;IAEjC,MAAM,EACJd,gBAAgB,EAChBC,wBAAwB,EACxBC,QAAQ,EACRC,iBAAiB,EACjB,GAAGY,eACJ,GAAGD;IAEJ,OAAOpD,IAAAA,cAAM,EAACmD,IAAI;QAChBG,SAAS,CAAC,EAAEjB,QAAQ,EAAE,iBACpB,qBAACD;gBACCE,kBAAkBA;gBAClBC,0BAA0BA;gBAC1BC,UAAUA;gBACVC,mBAAmBA;0BAElBJ;;QAGL,GAAGgB,aAAa;IAClB;AACF;AAGO,MAAMhE,wBAAwB,CAAKkE,MAASC,SAAS,GAAG,GAAM,CAAA;QACnED;QACAC;QACAC,YAAY;QACZC,SAAS,CAAC;QACVC,QAAQ,CAAC;IACX,CAAA;AAGO,MAAMvE,qBAAqB,CAACwE,SAAiBJ,SAAS,GAAG,GAAM,CAAA;QACpEK,UAAU;YACRN,MAAM;gBAAEK;YAAQ;YAChBJ;YACAC,YAAY;QACd;QACAG;IACF,CAAA;AAGO,MAAM1D,UAAU,CAAC4D,KAAe,IAAIC,QAAQC,CAAAA,UAAWC,WAAWD,SAASF;AAG3E,MAAMxE,iBAAiB,CAC5B4E,OAAe,UAAU,EACzBC,OAAe,IAAI,EACnBC,OAAe,iBAAiB;IAEhC,MAAMC,OAAO,IAAIC,KAAK;QAAC;KAAe,EAAEJ,MAAM;QAAEE;IAAK;IACrDG,OAAOC,cAAc,CAACH,MAAM,QAAQ;QAAErB,OAAOmB;IAAK;IAClD,OAAOE;AACT;AAGO,MAAM9E,qBAAqB,CAACgE;IACjC,MAAMkB,WAAW,IAAIC;IACrBH,OAAOI,OAAO,CAACpB,MAAMqB,OAAO,CAAC,CAAC,CAACC,KAAK7B,MAAM;QACxCyB,SAASK,MAAM,CAACD,KAAK7B;IACvB;IACA,OAAOyB;AACT;AAGO,MAAM9E,mBAAmB,CAAC4D,OAA+B,CAAC,CAAC;IAChE,MAAMwB,mBAAmB;QACvBC,SAAS3D,KAAKC,EAAE,CAAC,CAACuD,MAAgBtB,IAAI,CAACsB,IAAI,IAAI;QAC/CI,SAAS5D,KAAKC,EAAE,CAAC,CAACuD,KAAa7B;YAC7BO,IAAI,CAACsB,IAAI,GAAG7B;QACd;QACAkC,YAAY7D,KAAKC,EAAE,CAAC,CAACuD;YACnB,OAAOtB,IAAI,CAACsB,IAAI;QAClB;QACAM,OAAO9D,KAAKC,EAAE,CAAC;YACbiD,OAAOa,IAAI,CAAC7B,MAAMqB,OAAO,CAACC,CAAAA,MAAO,OAAOtB,IAAI,CAACsB,IAAI;QACnD;IACF;IAEAN,OAAOC,cAAc,CAACa,QAAQ,gBAAgB;QAC5CrC,OAAO+B;QACPO,UAAU;IACZ;IAEA,OAAOP;AACT;AAGO,MAAMlF,qBAAqB,CAAC0D,OAA+B,CAAC,CAAC;IAClE,MAAMgC,qBAAqB;QACzBP,SAAS3D,KAAKC,EAAE,CAAC,CAACuD,MAAgBtB,IAAI,CAACsB,IAAI,IAAI;QAC/CI,SAAS5D,KAAKC,EAAE,CAAC,CAACuD,KAAa7B;YAC7BO,IAAI,CAACsB,IAAI,GAAG7B;QACd;QACAkC,YAAY7D,KAAKC,EAAE,CAAC,CAACuD;YACnB,OAAOtB,IAAI,CAACsB,IAAI;QAClB;QACAM,OAAO9D,KAAKC,EAAE,CAAC;YACbiD,OAAOa,IAAI,CAAC7B,MAAMqB,OAAO,CAACC,CAAAA,MAAO,OAAOtB,IAAI,CAACsB,IAAI;QACnD;IACF;IAEAN,OAAOC,cAAc,CAACa,QAAQ,kBAAkB;QAC9CrC,OAAOuC;QACPD,UAAU;IACZ;IAEA,OAAOC;AACT"}