'use client';

import React, { useEffect, useState, useRef } from 'react';
import { usePathname } from 'next/navigation';

interface RouteTransitionProps {
  children: React.ReactNode;
}

const RouteTransition: React.FC<RouteTransitionProps> = ({ children }) => {
  const pathname = usePathname();
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [displayChildren, setDisplayChildren] = useState(children);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const prevPathnameRef = useRef(pathname);

  useEffect(() => {
    // Only transition if pathname actually changed
    if (prevPathnameRef.current !== pathname) {
      setIsTransitioning(true);

      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        setDisplayChildren(children);
        setIsTransitioning(false);
      }, 50); // Very short transition to prevent flickering

      prevPathnameRef.current = pathname;
    } else {
      // If pathname hasn't changed, just update children immediately
      setDisplayChildren(children);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [pathname, children]);

  if (isTransitioning) {
    return (
      <div className="transition-opacity duration-75 opacity-90">
        {displayChildren}
      </div>
    );
  }

  return <>{children}</>;
};

export default RouteTransition;
