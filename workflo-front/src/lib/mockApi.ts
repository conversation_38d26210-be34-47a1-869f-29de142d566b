import { AuthTokens, User, Employee, Department, LeaveApplication } from '@/types';

// Mock data
const mockUsers: User[] = [
  {
    id: 1,
    email: '<EMAIL>',
    first_name: 'Admin',
    last_name: 'User',
    employee_id: 'ADMIN001',
    phone_number: '+254-700-000-001',
    profile_picture: '/api/placeholder/100/100',
    role: 'hr',
    is_active: true,
    date_joined: '2022-01-01',
    department: {
      id: 1,
      name: 'Human Resources',
      description: 'HR Operations and People Management',
      created_at: '2023-01-01',
      updated_at: '2023-01-01',
    },
  },
  {
    id: 2,
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    employee_id: 'SUP001',
    phone_number: '+254-700-000-002',
    profile_picture: '/api/placeholder/100/100',
    role: 'supervisor',
    is_active: true,
    date_joined: '2022-06-01',
    department: {
      id: 5,
      name: 'Operations',
      description: 'Operations and Process Management',
      created_at: '2023-01-01',
      updated_at: '2023-01-01',
    },
  },
  {
    id: 3,
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: 'Cotton',
    employee_id: 'EMP001',
    phone_number: '+254-700-000-003',
    profile_picture: '/api/placeholder/100/100',
    role: 'employee',
    is_active: true,
    date_joined: '2023-01-15',
    department: {
      id: 5,
      name: 'Operations',
      description: 'Operations and Process Management',
      created_at: '2023-01-01',
      updated_at: '2023-01-01',
    },
  },
  {
    id: 4,
    email: '<EMAIL>',
    first_name: 'David',
    last_name: 'Wilson',
    employee_id: 'ACC001',
    phone_number: '+254-700-000-004',
    profile_picture: '/api/placeholder/100/100',
    role: 'accountant',
    is_active: true,
    date_joined: '2023-02-01',
    department: {
      id: 3,
      name: 'Finance',
      description: 'Financial Operations and Accounting',
      created_at: '2023-01-01',
      updated_at: '2023-01-01',
    },
  },
];

const mockEmployees: Employee[] = [
  {
    ...mockUsers[1],
    job_title: 'Software Engineer',
    position: 'Software Engineer',
    department_id: 2,
    department_name: 'Engineering',
    hire_date: '2023-02-01',
    employment_type: 'full_time',
    work_location: 'office',
    salary: 75000,
    hourly_rate: 36,
    currency: 'USD',
    pay_frequency: 'monthly',
    bank_name: 'Chase Bank',
    bank_account: '****1234',
    nssf_number: 'NSSF001',
    nhif_number: 'NHIF001',
    kra_pin: 'KRA001',
    national_id: '********',
    date_of_birth: '1990-05-15',
    gender: 'male',
    marital_status: 'single',
    nationality: 'American',
    address: '123 Main St',
    city: 'San Francisco',
    state: 'CA',
    postal_code: '94105',
    country: 'USA',
    emergency_contact_name: 'Jane Doe',
    emergency_contact_phone: '******-0199',
    emergency_contact_relationship: 'Sister',
    status: 'active',
    is_deleted: false,
    created_by: 1,
    updated_by: 1,
  },
  {
    id: 3,
    email: '<EMAIL>',
    first_name: 'Maria',
    last_name: 'Cotton',
    employee_id: 'EMP002',
    role: 'employee',
    job_title: 'PHP Team Lead',
    position: 'PHP Team Lead',
    department_id: 2,
    department_name: 'Engineering',
    hire_date: '2023-01-15',
    employment_type: 'full_time',
    work_location: 'office',
    salary: 85000,
    hourly_rate: 41,
    currency: 'USD',
    pay_frequency: 'monthly',
    bank_name: 'Wells Fargo',
    bank_account: '****5678',
    nssf_number: 'NSSF002',
    nhif_number: 'NHIF002',
    kra_pin: 'KRA002',
    national_id: '********',
    date_of_birth: '1988-08-22',
    gender: 'female',
    marital_status: 'married',
    nationality: 'American',
    address: '456 Oak Ave',
    city: 'San Francisco',
    state: 'CA',
    postal_code: '94107',
    country: 'USA',
    emergency_contact_name: 'Carlos Cotton',
    emergency_contact_phone: '******-0298',
    emergency_contact_relationship: 'Spouse',
    status: 'active',
    is_active: true,
    date_joined: '2023-01-15',
    is_deleted: false,
    created_by: 1,
    updated_by: 1,
    department: {
      id: 2,
      name: 'Engineering',
      description: 'Engineering Department',
      created_at: '2023-01-01',
      updated_at: '2023-01-01',
    },
  },
];

// Mock API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock API responses
export const mockApi = {
  // Authentication
  login: async (credentials: { email: string; password: string }): Promise<AuthTokens> => {
    await delay(1000); // Simulate network delay

    const validCredentials = [
      { email: '<EMAIL>', password: 'admin123' },
      { email: '<EMAIL>', password: 'supervisor123' },
      { email: '<EMAIL>', password: 'employee123' },
      { email: '<EMAIL>', password: 'accountant123' },
    ];

    const isValid = validCredentials.some(
      cred => cred.email === credentials.email && cred.password === credentials.password
    );

    if (!isValid) {
      throw new Error('Invalid email or password');
    }

    // Store user email for session management
    if (typeof window !== 'undefined') {
      localStorage.setItem('mock_user_email', credentials.email);
    }

    return {
      access: 'mock-access-token-' + Date.now(),
      refresh: 'mock-refresh-token-' + Date.now(),
    };
  },

  getCurrentUser: async (): Promise<User> => {
    await delay(500);

    // Check if we're in browser environment
    if (typeof window === 'undefined') {
      throw new Error('Not in browser environment');
    }

    // Get user based on stored email (in real app, this would be from token)
    const storedEmail = localStorage.getItem('mock_user_email');
    if (!storedEmail) {
      throw new Error('No user email stored');
    }

    const user = mockUsers.find(u => u.email === storedEmail);

    if (!user) {
      throw new Error('User not found');
    }

    return user;
  },

  // Employees
  getEmployees: async (): Promise<Employee[]> => {
    await delay(800);
    return mockEmployees;
  },

  getEmployeeById: async (id: number): Promise<Employee> => {
    await delay(500);
    const employee = mockEmployees.find(emp => emp.id === id);
    if (!employee) {
      throw new Error('Employee not found');
    }
    return employee;
  },

  // Departments
  getDepartments: async (): Promise<Department[]> => {
    await delay(500);
    return [
      {
        id: 1,
        name: 'Human Resources',
        description: 'HR Department',
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
      },
      {
        id: 2,
        name: 'Engineering',
        description: 'Engineering Department',
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
      },
      {
        id: 3,
        name: 'Design',
        description: 'Design Department',
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
      },
    ];
  },

  // Leave Applications
  getLeaveApplications: async (): Promise<LeaveApplication[]> => {
    await delay(600);
    return [
      {
        id: 1,
        employee: 2,
        employee_name: 'John Doe',
        leave_type: 'annual',
        start_date: '2024-02-15',
        end_date: '2024-02-20',
        days_requested: 5,
        reason: 'Family vacation',
        status: 'approved',
        approved_by: 1,
        approved_by_name: 'Admin User',
        applied_date: '2024-02-01',
      },
      {
        id: 2,
        employee: 3,
        employee_name: 'Maria Cotton',
        leave_type: 'sick',
        start_date: '2024-02-10',
        end_date: '2024-02-12',
        days_requested: 3,
        reason: 'Medical appointment',
        status: 'pending',
        applied_date: '2024-02-08',
      },
    ];
  },

  // Dashboard Stats
  getDashboardStats: async () => {
    await delay(700);
    return {
      total_employees: mockEmployees.length,
      total_departments: 3,
      pending_leaves: 1,
      total_salary: mockEmployees.reduce((sum, emp) => sum + (emp.salary || 0), 0),
      recent_activities: [
        {
          id: 1,
          type: 'leave' as const,
          description: 'Maria Cotton applied for sick leave',
          user: 'Maria Cotton',
          timestamp: new Date().toISOString(),
        },
        {
          id: 2,
          type: 'attendance' as const,
          description: 'John Doe checked in',
          user: 'John Doe',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
        },
      ],
    };
  },

  // Logout
  logout: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('mock_user_email');
    }
  },
};

// Helper function to store user email for mock authentication
export const setMockUserEmail = (email: string) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('mock_user_email', email);
  }
};

export const clearMockUserEmail = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('mock_user_email');
  }
};
