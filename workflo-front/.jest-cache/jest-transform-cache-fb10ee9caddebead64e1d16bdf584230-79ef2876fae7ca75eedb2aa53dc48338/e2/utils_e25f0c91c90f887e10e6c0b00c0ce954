46e45b96d009016add800bd9d0949d4c
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    calculateAge: function() {
        return calculateAge;
    },
    calculateDaysBetween: function() {
        return calculateDaysBetween;
    },
    capitalizeWords: function() {
        return capitalizeWords;
    },
    cn: function() {
        return cn;
    },
    debounce: function() {
        return debounce;
    },
    deepClone: function() {
        return deepClone;
    },
    formatCurrency: function() {
        return formatCurrency;
    },
    formatDate: function() {
        return formatDate;
    },
    formatFileSize: function() {
        return formatFileSize;
    },
    formatPhoneNumber: function() {
        return formatPhoneNumber;
    },
    generateAvatarColor: function() {
        return generateAvatarColor;
    },
    generateEmployeeId: function() {
        return generateEmployeeId;
    },
    getBusinessDays: function() {
        return getBusinessDays;
    },
    getInitials: function() {
        return getInitials;
    },
    getStatusColor: function() {
        return getStatusColor;
    },
    isEmpty: function() {
        return isEmpty;
    },
    isValidEmail: function() {
        return isValidEmail;
    },
    isValidKenyanPhone: function() {
        return isValidKenyanPhone;
    },
    isWeekend: function() {
        return isWeekend;
    },
    sleep: function() {
        return sleep;
    },
    truncateText: function() {
        return truncateText;
    }
});
const _clsx = require("clsx");
const _datefns = require("date-fns");
function cn(...inputs) {
    return (0, _clsx.clsx)(inputs);
}
function formatDate(dateString, formatStr = 'MMM dd, yyyy') {
    try {
        const date = (0, _datefns.parseISO)(dateString);
        if (!(0, _datefns.isValid)(date)) {
            return 'Invalid Date';
        }
        return (0, _datefns.format)(date, formatStr);
    } catch (error) {
        return 'Invalid Date';
    }
}
function formatCurrency(amount, currency = 'KSH') {
    const locale = currency === 'KSH' || currency === 'KES' ? 'en-KE' : 'en-US';
    return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency
    }).format(amount);
}
function formatPhoneNumber(phone) {
    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    // Format as +254 XXX XXX XXX for Kenyan numbers
    if (cleaned.startsWith('254') && cleaned.length === 12) {
        return `+${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 9)} ${cleaned.slice(9)}`;
    }
    // Format as 0XXX XXX XXX for local numbers
    if (cleaned.startsWith('0') && cleaned.length === 10) {
        return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;
    }
    return phone;
}
function calculateDaysBetween(startDate, endDate) {
    const start = (0, _datefns.parseISO)(startDate);
    const end = (0, _datefns.parseISO)(endDate);
    if (!(0, _datefns.isValid)(start) || !(0, _datefns.isValid)(end)) {
        return 0;
    }
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
}
function getInitials(firstName, lastName) {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
}
function capitalizeWords(str) {
    return str.replace(/\w\S*/g, (txt)=>txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
}
function generateAvatarColor(name) {
    const colors = [
        'bg-red-500',
        'bg-blue-500',
        'bg-green-500',
        'bg-yellow-500',
        'bg-purple-500',
        'bg-pink-500',
        'bg-indigo-500',
        'bg-teal-500'
    ];
    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
}
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function isValidKenyanPhone(phone) {
    const cleaned = phone.replace(/\D/g, '');
    // Check for +254XXXXXXXXX or 254XXXXXXXXX format
    if (cleaned.startsWith('254') && cleaned.length === 12) {
        return true;
    }
    // Check for 0XXXXXXXXX format
    if (cleaned.startsWith('0') && cleaned.length === 10) {
        return true;
    }
    return false;
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.slice(0, maxLength) + '...';
}
function getStatusColor(status) {
    const statusColors = {
        active: 'text-green-600 bg-green-100',
        inactive: 'text-red-600 bg-red-100',
        pending: 'text-yellow-600 bg-yellow-100',
        approved: 'text-green-600 bg-green-100',
        rejected: 'text-red-600 bg-red-100',
        completed: 'text-blue-600 bg-blue-100',
        in_progress: 'text-orange-600 bg-orange-100',
        not_started: 'text-gray-600 bg-gray-100'
    };
    return statusColors[status] || 'text-gray-600 bg-gray-100';
}
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = [
        'Bytes',
        'KB',
        'MB',
        'GB'
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
function generateEmployeeId(departmentCode, sequence) {
    const year = new Date().getFullYear().toString().slice(-2);
    const paddedSequence = sequence.toString().padStart(3, '0');
    return `${departmentCode}${year}${paddedSequence}`;
}
function calculateAge(dateOfBirth) {
    const today = new Date();
    const birthDate = (0, _datefns.parseISO)(dateOfBirth);
    if (!(0, _datefns.isValid)(birthDate)) {
        return 0;
    }
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || monthDiff === 0 && today.getDate() < birthDate.getDate()) {
        age--;
    }
    return age;
}
function isWeekend(date) {
    const day = (0, _datefns.parseISO)(date).getDay();
    return day === 0 || day === 6; // Sunday = 0, Saturday = 6
}
function getBusinessDays(startDate, endDate) {
    const start = (0, _datefns.parseISO)(startDate);
    const end = (0, _datefns.parseISO)(endDate);
    if (!(0, _datefns.isValid)(start) || !(0, _datefns.isValid)(end)) {
        return 0;
    }
    let count = 0;
    const current = new Date(start);
    while(current <= end){
        const dayOfWeek = current.getDay();
        if (dayOfWeek !== 0 && dayOfWeek !== 6) {
            count++;
        }
        current.setDate(current.getDate() + 1);
    }
    return count;
}
function sleep(ms) {
    return new Promise((resolve)=>setTimeout(resolve, ms));
}
function deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
}
function isEmpty(obj) {
    if (obj == null) return true;
    if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
    if (typeof obj === 'object') return Object.keys(obj).length === 0;
    return false;
}

//# sourceMappingURL=data:application/json;base64,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