import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, mockUser, mockAdminUser, mockUnauthenticatedAuthContextValue, mockLoadingAuthContextValue } from '@/__tests__/utils/test-utils';
import RoleGuard, { AdminGuard, StaffGuard, useRoleCheck } from '@/components/auth/RoleGuard';

// Mock useRouter
const mockPush = jest.fn();
const mockReplace = jest.fn();
const mockBack = jest.fn();

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: mockReplace,
    back: mockBack,
  }),
}));

describe('RoleGuard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('when user is authenticated and has required role', () => {
    it('renders children for staff role', () => {
      render(
        <RoleGuard allowedRoles={['staff']}>
          <div>Staff Content</div>
        </RoleGuard>
      );

      expect(screen.getByText('Staff Content')).toBeInTheDocument();
    });

    it('renders children for admin role when user is HR', () => {
      render(
        <RoleGuard allowedRoles={['admin']}>
          <div>Admin Content</div>
        </RoleGuard>,
        {
          authContextValue: {
            ...mockUnauthenticatedAuthContextValue,
            user: mockAdminUser,
            isAuthenticated: true,
            isLoading: false,
          }
        }
      );

      expect(screen.getByText('Admin Content')).toBeInTheDocument();
    });
  });

  describe('when user is not authenticated', () => {
    it('shows authentication required message', () => {
      render(
        <RoleGuard allowedRoles={['staff']}>
          <div>Protected Content</div>
        </RoleGuard>,
        {
          authContextValue: mockUnauthenticatedAuthContextValue
        }
      );

      expect(screen.getByText('Authentication Required')).toBeInTheDocument();
      expect(screen.getByText('Please log in to access this page.')).toBeInTheDocument();
    });

    it('redirects to login when redirectTo is provided', () => {
      render(
        <RoleGuard allowedRoles={['staff']} redirectTo="/login">
          <div>Protected Content</div>
        </RoleGuard>,
        {
          authContextValue: mockUnauthenticatedAuthContextValue
        }
      );

      expect(mockReplace).toHaveBeenCalledWith('/login');
    });

    it('navigates to login when login button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <RoleGuard allowedRoles={['staff']}>
          <div>Protected Content</div>
        </RoleGuard>,
        {
          authContextValue: mockUnauthenticatedAuthContextValue
        }
      );

      const loginButton = screen.getByText('Go to Login');
      await user.click(loginButton);

      expect(mockPush).toHaveBeenCalledWith('/login');
    });
  });

  describe('when user lacks required role', () => {
    it('shows access denied message', () => {
      render(
        <RoleGuard allowedRoles={['admin']}>
          <div>Admin Content</div>
        </RoleGuard>
      );

      expect(screen.getByText('Access Denied')).toBeInTheDocument();
      expect(screen.getByText(/You don't have permission to access this page/)).toBeInTheDocument();
      expect(screen.getByText(/Your current role \(employee\) is not authorized/)).toBeInTheDocument();
    });

    it('shows custom fallback when provided', () => {
      const customFallback = <div>Custom Access Denied</div>;
      
      render(
        <RoleGuard allowedRoles={['admin']} fallback={customFallback}>
          <div>Admin Content</div>
        </RoleGuard>
      );

      expect(screen.getByText('Custom Access Denied')).toBeInTheDocument();
      expect(screen.queryByText('Access Denied')).not.toBeInTheDocument();
    });

    it('navigates back when go back button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <RoleGuard allowedRoles={['admin']}>
          <div>Admin Content</div>
        </RoleGuard>
      );

      const backButton = screen.getByText('Go Back');
      await user.click(backButton);

      expect(mockBack).toHaveBeenCalled();
    });

    it('navigates to staff dashboard for employee role', async () => {
      const user = userEvent.setup();
      
      render(
        <RoleGuard allowedRoles={['admin']}>
          <div>Admin Content</div>
        </RoleGuard>
      );

      const dashboardButton = screen.getByText('Go to Dashboard');
      await user.click(dashboardButton);

      // Should redirect to staff dashboard for employee role
      expect(mockPush).toHaveBeenCalledWith('/staff');
    });
  });

  describe('when loading', () => {
    it('shows loading state', () => {
      render(
        <RoleGuard allowedRoles={['staff']}>
          <div>Protected Content</div>
        </RoleGuard>,
        {
          authContextValue: mockLoadingAuthContextValue
        }
      );

      expect(screen.getByText('Checking permissions...')).toBeInTheDocument();
    });
  });
});

describe('AdminGuard', () => {
  it('allows access for HR user', () => {
    render(
      <AdminGuard>
        <div>Admin Content</div>
      </AdminGuard>,
      {
        authContextValue: {
          ...mockUnauthenticatedAuthContextValue,
          user: mockAdminUser,
          isAuthenticated: true,
          isLoading: false,
        }
      }
    );

    expect(screen.getByText('Admin Content')).toBeInTheDocument();
  });

  it('denies access for employee user', () => {
    render(
      <AdminGuard>
        <div>Admin Content</div>
      </AdminGuard>
    );

    expect(screen.getByText('Access Denied')).toBeInTheDocument();
  });
});

describe('StaffGuard', () => {
  it('allows access for employee user', () => {
    render(
      <StaffGuard>
        <div>Staff Content</div>
      </StaffGuard>
    );

    expect(screen.getByText('Staff Content')).toBeInTheDocument();
  });

  it('denies access for HR user', () => {
    render(
      <StaffGuard>
        <div>Staff Content</div>
      </StaffGuard>,
      {
        authContextValue: {
          ...mockUnauthenticatedAuthContextValue,
          user: mockAdminUser,
          isAuthenticated: true,
          isLoading: false,
        }
      }
    );

    expect(screen.getByText('Access Denied')).toBeInTheDocument();
  });
});

describe('useRoleCheck', () => {
  const TestComponent: React.FC = () => {
    const { hasRole, isAdmin, isStaff, isHR, userRole } = useRoleCheck();

    return (
      <div>
        <div data-testid="has-admin-role">{hasRole(['admin']).toString()}</div>
        <div data-testid="has-staff-role">{hasRole(['staff']).toString()}</div>
        <div data-testid="is-admin">{isAdmin().toString()}</div>
        <div data-testid="is-staff">{isStaff().toString()}</div>
        <div data-testid="is-hr">{isHR().toString()}</div>
        <div data-testid="user-role">{userRole}</div>
      </div>
    );
  };

  it('returns correct role checks for employee user', () => {
    render(<TestComponent />);

    expect(screen.getByTestId('has-admin-role')).toHaveTextContent('false');
    expect(screen.getByTestId('has-staff-role')).toHaveTextContent('true');
    expect(screen.getByTestId('is-admin')).toHaveTextContent('false');
    expect(screen.getByTestId('is-staff')).toHaveTextContent('true');
    expect(screen.getByTestId('is-hr')).toHaveTextContent('false');
    expect(screen.getByTestId('user-role')).toHaveTextContent('employee');
  });

  it('returns correct role checks for HR user', () => {
    render(<TestComponent />, {
      authContextValue: {
        ...mockUnauthenticatedAuthContextValue,
        user: mockAdminUser,
        isAuthenticated: true,
        isLoading: false,
      }
    });

    expect(screen.getByTestId('has-admin-role')).toHaveTextContent('true');
    expect(screen.getByTestId('has-staff-role')).toHaveTextContent('false');
    expect(screen.getByTestId('is-admin')).toHaveTextContent('true');
    expect(screen.getByTestId('is-staff')).toHaveTextContent('false');
    expect(screen.getByTestId('is-hr')).toHaveTextContent('true');
    expect(screen.getByTestId('user-role')).toHaveTextContent('hr');
  });

  it('returns null values when user is not authenticated', () => {
    render(<TestComponent />, {
      authContextValue: mockUnauthenticatedAuthContextValue
    });

    expect(screen.getByTestId('has-admin-role')).toHaveTextContent('false');
    expect(screen.getByTestId('has-staff-role')).toHaveTextContent('false');
    expect(screen.getByTestId('is-admin')).toHaveTextContent('false');
    expect(screen.getByTestId('is-staff')).toHaveTextContent('false');
    expect(screen.getByTestId('is-hr')).toHaveTextContent('false');
    expect(screen.getByTestId('user-role')).toHaveTextContent('');
  });
});
